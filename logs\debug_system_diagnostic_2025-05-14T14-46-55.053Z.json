{"timestamp": "2025-05-14T14:46:55.053Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 381902848, "heapTotal": 91099136, "heapUsed": 69264440, "external": 6886756, "arrayBuffers": 98802}, "uptime": 1.909270582, "cpuUsage": {"user": 2487121, "system": 337194}, "resourceUsage": {"userCPUTime": 2487176, "systemCPUTime": 337194, "maxRSS": 372952, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100749, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 216, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6800, "involuntaryContextSwitches": 6226}}