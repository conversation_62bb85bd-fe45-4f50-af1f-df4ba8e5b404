{"timestamp": "2025-05-23T00:59:02.415Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397471744, "heapTotal": 115122176, "heapUsed": 74311936, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.066295611, "cpuUsage": {"user": 3053812, "system": 371384}, "resourceUsage": {"userCPUTime": 3053863, "systemCPUTime": 371384, "maxRSS": 388156, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103904, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8601, "involuntaryContextSwitches": 6451}}