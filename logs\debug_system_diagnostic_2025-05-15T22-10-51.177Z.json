{"timestamp": "2025-05-15T22:10:51.176Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403009536, "heapTotal": 116432896, "heapUsed": 72564920, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.806220433, "cpuUsage": {"user": 2964320, "system": 362045}, "resourceUsage": {"userCPUTime": 2964373, "systemCPUTime": 362052, "maxRSS": 393564, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102312, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7285, "involuntaryContextSwitches": 8540}}