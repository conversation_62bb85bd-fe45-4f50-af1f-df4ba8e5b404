import { useState, useEffect } from "react";
import { Layout } from "@/components/Layout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { pt } from "date-fns/locale";
import { format, parseISO, isValid } from "date-fns";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, CalendarIcon, Clock, MapPin, Tag, User, HelpCircle } from "lucide-react";

// Esquema para validação do formulário
const eventoSchema = z.object({
  titulo: z.string().min(3, "O título deve ter pelo menos 3 caracteres"),
  descricao: z.string().optional(),
  data: z.string().refine((val) => isValid(parseISO(val)), {
    message: "Data inválida",
  }),
  horaInicio: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Formato inválido: HH:MM"),
  horaFim: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Formato inválido: HH:MM"),
  local: z.string().optional(),
  tipo: z.string().min(1, "O tipo é obrigatório"),
  prioridade: z.enum(["baixa", "media", "alta"]).default("media"),
  horseId: z.number().optional(),
  manejoId: z.number().optional(),
  userId: z.number(),
});

// Tipagem dos dados do formulário
type EventoFormData = z.infer<typeof eventoSchema>;

// Tipagem para os cavalos
interface Cavalo {
  id: number;
  nome: string;
}

// Tipagem para os manejos
interface Manejo {
  id: number;
  tipo: string;
  descricao: string;
  horseId: number;
}

// Tipagem para os eventos
interface Evento {
  id: number;
  titulo: string;
  descricao?: string;
  data: string;
  horaInicio: string;
  horaFim: string;
  local?: string;
  tipo: string;
  prioridade: string;
  horseId?: number;
  manejoId?: number;
  userId: number;
  createdAt: string;
  cavaloNome?: string; // Nome do cavalo associado
  manejoTipo?: string; // Tipo do manejo associado
}

function Agenda() {
  // Estado para armazenar os eventos
  const [eventos, setEventos] = useState<Evento[]>([]);
  const [cavalos, setCavalos] = useState<Cavalo[]>([]);
  const [manejos, setManejos] = useState<Manejo[]>([]);
  const [loading, setLoading] = useState(true);
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentTab, setCurrentTab] = useState("todos");

  // Configuração do formulário
  const form = useForm<EventoFormData>({
    resolver: zodResolver(eventoSchema),
    defaultValues: {
      titulo: "",
      descricao: "",
      data: format(new Date(), "yyyy-MM-dd"),
      horaInicio: "08:00",
      horaFim: "09:00",
      local: "",
      tipo: "geral",
      prioridade: "media",
    },
  });

  const { toast } = useToast();

  // Formatar data para exibição no formato brasileiro
  const formatDisplayDate = (dateStr: string) => {
    try {
      const parsedDate = parseISO(dateStr);
      if (isValid(parsedDate)) {
        return format(parsedDate, "dd/MM/yyyy", { locale: pt });
      }
      return dateStr;
    } catch (error) {
      console.error("Erro ao formatar data:", error);
      return dateStr;
    }
  };

  // Obter uma string de prioridade formatada com a cor adequada
  const getPriorityClass = (priority: string) => {
    switch (priority) {
      case "alta":
        return "text-red-500 font-bold";
      case "media":
        return "text-yellow-500 font-semibold";
      case "baixa":
        return "text-green-500";
      default:
        return "";
    }
  };
  
  // Retorna a cor de fundo e ícone baseado no tipo de evento
  const getEventTypeStyle = (tipo: string): { bgColor: string, borderColor: string, icon: JSX.Element } => {
    switch (tipo) {
      case 'veterinario':
        return { 
          bgColor: 'bg-red-50', 
          borderColor: 'border-red-200',
          icon: <svg className="h-4 w-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
        };
      case 'ferrageamento':
        return { 
          bgColor: 'bg-amber-50', 
          borderColor: 'border-amber-200',
          icon: <svg className="h-4 w-4 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
        };
      case 'odontologia':
        return { 
          bgColor: 'bg-purple-50', 
          borderColor: 'border-purple-200',
          icon: <svg className="h-4 w-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
        };
      case 'treinamento':
        return { 
          bgColor: 'bg-blue-50', 
          borderColor: 'border-blue-200',
          icon: <svg className="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
        };
      case 'competicao':
        return { 
          bgColor: 'bg-green-50', 
          borderColor: 'border-green-200',
          icon: <svg className="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
        };
      case 'exposicao':
        return { 
          bgColor: 'bg-pink-50', 
          borderColor: 'border-pink-200',
          icon: <svg className="h-4 w-4 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
        };
      default:
        return { 
          bgColor: 'bg-gray-50', 
          borderColor: 'border-gray-200',
          icon: <svg className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
        };
    }
  };

  const fetchEventos = async () => {
    setLoading(true);
    try {
      const userId = localStorage.getItem("userId");
      if (!userId) {
        throw new Error("Usuário não autenticado");
      }

      const response = await fetch("/api/eventos", {
        headers: {
          "user-id": userId,
        },
      });

      if (!response.ok) {
        throw new Error("Falha ao carregar eventos");
      }

      const data = await response.json();
      
      // Buscar nomes de cavalos e tipos de manejos para exibição
      setEventos(data);
    } catch (error) {
      console.error("Erro ao buscar eventos:", error);
      toast({
        title: "Erro ao carregar eventos",
        description: "Não foi possível carregar os eventos. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchCavalos = async () => {
    try {
      const userId = localStorage.getItem("userId");
      if (!userId) {
        throw new Error("Usuário não autenticado");
      }

      const response = await fetch("/api/cavalos", {
        headers: {
          "user-id": userId,
        },
      });

      if (!response.ok) {
        throw new Error("Falha ao carregar cavalos");
      }

      const data = await response.json();
      setCavalos(data);
    } catch (error) {
      console.error("Erro ao buscar cavalos:", error);
    }
  };

  const fetchManejos = async () => {
    try {
      const userId = localStorage.getItem("userId");
      if (!userId) {
        throw new Error("Usuário não autenticado");
      }

      const response = await fetch("/api/manejos", {
        headers: {
          "user-id": userId,
        },
      });

      if (!response.ok) {
        throw new Error("Falha ao carregar manejos");
      }

      const data = await response.json();
      setManejos(data);
    } catch (error) {
      console.error("Erro ao buscar manejos:", error);
    }
  };

  const getCavaloNome = (horseId?: number) => {
    if (!horseId) return "N/A";
    const cavalo = cavalos.find(c => c.id === horseId);
    return cavalo ? cavalo.nome : "Desconhecido";
  };

  const getManejoDescricao = (manejoId?: number) => {
    if (!manejoId) return null;
    const manejo = manejos.find(m => m.id === manejoId);
    return manejo ? manejo.tipo : "Desconhecido";
  };

  const onSubmit = async (data: EventoFormData) => {
    try {
      const userId = localStorage.getItem("userId");
      if (!userId) {
        throw new Error("Usuário não autenticado");
      }

      // Garantir que horaFim não está vazio
      if (!data.horaFim) {
        toast({
          title: "Erro ao criar evento",
          description: "O campo Hora Fim é obrigatório.",
          variant: "destructive",
        });
        return;
      }

      console.log("Enviando dados:", data); // Adicionar log para depuração

      const eventoData = {
        ...data,
        userId: parseInt(userId),
      };

      const response = await fetch("/api/eventos", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "user-id": userId,
        },
        body: JSON.stringify(eventoData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Erro na API:", errorText);
        throw new Error(`Falha ao criar evento: ${errorText}`);
      }

      toast({
        title: "Evento criado com sucesso",
        description: `O evento "${data.titulo}" foi agendado para ${formatDisplayDate(data.data)}.`,
      });

      setIsModalOpen(false);
      form.reset();
      fetchEventos();
    } catch (error) {
      console.error("Erro ao criar evento:", error);
      toast({
        title: "Erro ao criar evento",
        description: "Não foi possível criar o evento. Tente novamente mais tarde.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveEvento = async (id: number) => {
    if (!confirm("Tem certeza que deseja excluir este evento?")) {
      return;
    }

    try {
      const userId = localStorage.getItem("userId");
      if (!userId) {
        throw new Error("Usuário não autenticado");
      }

      const response = await fetch(`/api/eventos/${id}`, {
        method: "DELETE",
        headers: {
          "user-id": userId,
        },
      });

      if (!response.ok) {
        throw new Error("Falha ao excluir evento");
      }

      toast({
        title: "Evento excluído com sucesso",
        description: "O evento foi removido da agenda.",
      });

      fetchEventos();
    } catch (error) {
      console.error("Erro ao excluir evento:", error);
      toast({
        title: "Erro ao excluir evento",
        description: "Não foi possível excluir o evento. Tente novamente mais tarde.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchEventos();
    fetchCavalos();
    fetchManejos();
  }, []);

  // Filtrar eventos com base na data selecionada e na tab atual
  const filteredEventos = eventos.filter((evento) => {
    // Filtro por data
    const selectedDate = date ? format(date, "yyyy-MM-dd") : null;
    const eventDate = evento.data;
    
    const dateMatches = !selectedDate || eventDate === selectedDate;
    
    // Filtro por tipo
    const typeMatches = 
      currentTab === "todos" || 
      (currentTab === "veterinario" && evento.tipo === "veterinario") ||
      (currentTab === "ferrageamento" && evento.tipo === "ferrageamento") ||
      (currentTab === "odontologia" && evento.tipo === "odontologia") ||
      (currentTab === "treinamento" && evento.tipo === "treinamento") ||
      (currentTab === "outros" && !["veterinario", "ferrageamento", "odontologia", "treinamento"].includes(evento.tipo));
    
    return dateMatches && typeMatches;
  });

  // Renderização do componente de evento
  const renderEvento = (evento: Evento) => {
    const typeStyle = getEventTypeStyle(evento.tipo);
    return (
      <Card 
        key={evento.id} 
        className={`mb-4 overflow-hidden border-l-4 ${typeStyle.borderColor}`}
      >
        <div className={`flex flex-col md:flex-row ${typeStyle.bgColor}`}>
          <div className="p-4 md:p-6 flex-1">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                {typeStyle.icon}
                <h3 className="text-lg font-semibold">{evento.titulo}</h3>
              </div>
              <span className={`text-xs ${getPriorityClass(evento.prioridade)} uppercase bg-gray-100 px-2 py-1 rounded-md`}>
                {evento.prioridade}
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3 text-sm">
              <div className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                <span>{formatDisplayDate(evento.data)}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>
                  {evento.horaInicio}
                  {evento.horaFim ? ` - ${evento.horaFim}` : ""}
                </span>
              </div>
              
              {evento.local && (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{evento.local}</span>
                </div>
              )}
              
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <span className="capitalize">{evento.tipo}</span>
              </div>
            </div>
            
            {evento.descricao && (
              <p className="text-sm text-gray-600 mt-2">{evento.descricao}</p>
            )}
            
            <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-500">
              {evento.horseId && (
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  <span>Cavalo: {getCavaloNome(evento.horseId)}</span>
                </div>
              )}
              
              {evento.manejoId && (
                <div className="flex items-center gap-1">
                  <HelpCircle className="h-3 w-3" />
                  <span>Manejo: {getManejoDescricao(evento.manejoId)}</span>
                </div>
              )}
            </div>
          </div>
          
          <div className="p-4 flex md:flex-col justify-end items-center gap-2 border-t md:border-t-0 md:border-l bg-gray-50">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => {
                // Implementar edição
                toast({
                  title: "Recurso em desenvolvimento",
                  description: "A edição de eventos será implementada em breve.",
                });
              }}
            >
              Editar
            </Button>
            <Button 
              variant="destructive" 
              size="sm"
              onClick={() => handleRemoveEvento(evento.id)}
            >
              Remover
            </Button>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <Layout pageTitle="Agenda">
      <div className="container mx-auto p-4">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Calendário e filtros */}
          <div className="w-full md:w-1/3">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-bold">Calendário</CardTitle>
              </CardHeader>
              <CardContent>
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  className="border rounded-md"
                  locale={pt}
                  modifiers={{
                    eventDay: eventos.map(evento => new Date(evento.data))
                  }}
                  modifiersClassNames={{
                    eventDay: "bg-green-100 text-green-800 font-medium relative before:absolute before:bottom-1 before:left-1/2 before:-translate-x-1/2 before:w-1 before:h-1 before:bg-green-500 before:rounded-full"
                  }}
                />
                
                <div className="mt-4">
                  <h3 className="text-sm font-semibold mb-2">Data Selecionada</h3>
                  <div className="flex items-center gap-2 text-sm">
                    <CalendarIcon className="h-4 w-4" />
                    <span>
                      {date ? format(date, "dd 'de' MMMM 'de' yyyy", { locale: pt }) : "Nenhuma data selecionada"}
                    </span>
                  </div>
                  
                  {date && (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => setDate(undefined)}
                    >
                      Limpar seleção
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
            
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="text-xl font-bold">Adicionar Evento</CardTitle>
              </CardHeader>
              <CardContent>
                <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                  <DialogTrigger asChild>
                    <Button className="w-full">Novo Evento</Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[500px]">
                    <DialogHeader>
                      <DialogTitle>Adicionar Novo Evento</DialogTitle>
                    </DialogHeader>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                          control={form.control}
                          name="titulo"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Título*</FormLabel>
                              <FormControl>
                                <Input placeholder="Ex: Consulta veterinária" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="descricao"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Descrição</FormLabel>
                              <FormControl>
                                <Textarea 
                                  placeholder="Detalhes do evento" 
                                  {...field} 
                                  value={field.value || ""}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="data"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Data*</FormLabel>
                                <FormControl>
                                  <Input 
                                    type="date" 
                                    {...field} 
                                    value={field.value || format(new Date(), "yyyy-MM-dd")}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="horaInicio"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Hora Início*</FormLabel>
                                <FormControl>
                                  <Input type="time" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="horaFim"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Hora Fim*</FormLabel>
                                <FormControl>
                                  <Input 
                                    type="time" 
                                    {...field} 
                                    value={field.value || "09:00"}
                                    required
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="local"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Local</FormLabel>
                                <FormControl>
                                  <Input 
                                    placeholder="Local do evento" 
                                    {...field} 
                                    value={field.value || ""}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="tipo"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Tipo*</FormLabel>
                                <Select 
                                  onValueChange={field.onChange} 
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione o tipo" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="geral">Geral</SelectItem>
                                    <SelectItem value="veterinario">Veterinário</SelectItem>
                                    <SelectItem value="ferrageamento">Ferrageamento</SelectItem>
                                    <SelectItem value="odontologia">Odontologia</SelectItem>
                                    <SelectItem value="treinamento">Treinamento</SelectItem>
                                    <SelectItem value="competicao">Competição</SelectItem>
                                    <SelectItem value="exposicao">Exposição</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="prioridade"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Prioridade</FormLabel>
                                <Select 
                                  onValueChange={field.onChange} 
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione a prioridade" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="baixa">Baixa</SelectItem>
                                    <SelectItem value="media">Média</SelectItem>
                                    <SelectItem value="alta">Alta</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <FormField
                          control={form.control}
                          name="horseId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Cavalo</FormLabel>
                              <Select 
                                onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)} 
                                defaultValue={field.value?.toString()}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Selecione um cavalo (opcional)" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {cavalos.map((cavalo) => (
                                    <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                                      {cavalo.nome}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <DialogFooter>
                          <Button type="submit">Salvar</Button>
                        </DialogFooter>
                      </form>
                    </Form>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          </div>
          
          {/* Lista de eventos */}
          <div className="w-full md:w-2/3">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-bold">Eventos</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="todos" onValueChange={setCurrentTab}>
                  <TabsList className="mb-4">
                    <TabsTrigger value="todos">Todos</TabsTrigger>
                    <TabsTrigger value="veterinario">Veterinário</TabsTrigger>
                    <TabsTrigger value="ferrageamento">Ferrageamento</TabsTrigger>
                    <TabsTrigger value="odontologia">Odontologia</TabsTrigger>
                    <TabsTrigger value="treinamento">Treinamento</TabsTrigger>
                    <TabsTrigger value="outros">Outros</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="todos" className="space-y-4">
                    {loading ? (
                      <div className="flex justify-center items-center py-10">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        <span className="ml-2">Carregando eventos...</span>
                      </div>
                    ) : filteredEventos.length > 0 ? (
                      filteredEventos.map(evento => renderEvento(evento))
                    ) : (
                      <div className="text-center py-10 text-gray-500">
                        <p>Nenhum evento encontrado para os filtros selecionados.</p>
                        {date && (
                          <p className="mt-2">
                            Tente selecionar outra data ou limpar os filtros.
                          </p>
                        )}
                      </div>
                    )}
                  </TabsContent>
                  
                  {/* Renderizamos o mesmo padrão para todas as tabs */}
                  {["veterinario", "ferrageamento", "odontologia", "treinamento", "outros"].map((tab) => (
                    <TabsContent key={tab} value={tab} className="space-y-4">
                      {loading ? (
                        <div className="flex justify-center items-center py-10">
                          <Loader2 className="h-8 w-8 animate-spin text-primary" />
                          <span className="ml-2">Carregando eventos...</span>
                        </div>
                      ) : filteredEventos.length > 0 ? (
                        filteredEventos.map(evento => renderEvento(evento))
                      ) : (
                        <div className="text-center py-10 text-gray-500">
                          <p>Nenhum evento encontrado para os filtros selecionados.</p>
                          {date && (
                            <p className="mt-2">
                              Tente selecionar outra data ou limpar os filtros.
                            </p>
                          )}
                        </div>
                      )}
                    </TabsContent>
                  ))}
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default Agenda;