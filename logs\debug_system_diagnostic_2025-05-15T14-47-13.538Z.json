{"timestamp": "2025-05-15T14:47:13.537Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 271466496, "heapTotal": 96604160, "heapUsed": 68869776, "external": 6867053, "arrayBuffers": 60485}, "uptime": 2.053558925, "cpuUsage": {"user": 2151013, "system": 349509}, "resourceUsage": {"userCPUTime": 2151065, "systemCPUTime": 349517, "maxRSS": 272788, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 98116, "majorPageFault": 0, "swappedOut": 0, "fsRead": 21864, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6821, "involuntaryContextSwitches": 5371}}