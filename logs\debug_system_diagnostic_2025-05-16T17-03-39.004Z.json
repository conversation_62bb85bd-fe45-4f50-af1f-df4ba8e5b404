{"timestamp": "2025-05-16T17:03:39.004Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 411074560, "heapTotal": 118546432, "heapUsed": 89736624, "external": 8453143, "arrayBuffers": 243725}, "uptime": 3.048174385, "cpuUsage": {"user": 3082305, "system": 368733}, "resourceUsage": {"userCPUTime": 3082382, "systemCPUTime": 368733, "maxRSS": 401440, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105407, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 160, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7470, "involuntaryContextSwitches": 7813}}