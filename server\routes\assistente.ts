import { Router } from 'express';
import { authenticateUser } from '../auth';
import { z } from 'zod';
import { generateChatResponse } from '../services/openai';

// Schema para validação da mensagem
const messageSchema = z.object({
  message: z.string().min(1, "A mensagem não pode ser vazia").optional(),
  messages: z.array(
    z.object({
      role: z.enum(["user", "assistant", "system"]),
      content: z.string()
    })
  ).optional(),
  horseId: z.string().optional()
});

// Router para o assistente virtual
const assistenteRouter = Router();

// Rota para processar mensagens do assistente
assistenteRouter.post('/mensagem', authenticateUser, async (req, res) => {
  try {
    // Estrutura esperada do corpo da requisição: 
    // { message: string, messages?: ChatMessage[], horseId?: string }
    const { message, messages = [], horseId } = req.body;
    
    // Validar o formato da mensagem
    const validationResult = messageSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: true, 
        message: 'Dados inválidos',
        details: validationResult.error.errors
      });
    }
    
    // Se a mensagem direta for fornecida, adicioná-la ao array de mensagens
    let allMessages = [...messages];
    if (message) {
      allMessages.push({ role: 'user', content: message });
    }
    
    // Verificar se temos mensagens para processar
    if (allMessages.length === 0) {
      return res.status(400).json({ 
        error: true, 
        message: 'Nenhuma mensagem fornecida para processamento.' 
      });
    }
    
    // Verificar se temos a chave da API OpenAI
    if (!process.env.OPENAI_API_KEY) {
      return res.status(500).json({ 
        error: true, 
        message: 'Serviço de assistente virtual indisponível. Chave de API ausente.' 
      });
    }
    
    // Processar a resposta com OpenAI
    const horseIdNum = horseId ? parseInt(horseId) : null;
    console.log("[OpenAI] Processando mensagem com horseId:", horseIdNum);
    const response = await generateChatResponse(allMessages, horseIdNum);
    console.log("[OpenAI] Resposta recebida do serviço:", response);
    
    res.json({ content: response });
  } catch (error) {
    console.error('Erro no assistente virtual:', error);
    
    let errorMessage = 'Não foi possível processar sua solicitação ao assistente virtual.';
    
    // Tentar extrair a mensagem de erro de diferentes tipos de erro
    if (error) {
      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object') {
        try {
          errorMessage = JSON.stringify(error);
        } catch (e) {
          errorMessage = "Erro desconhecido";
        }
      }
    }
    
    console.error('[OpenAI] Erro detalhado:', errorMessage);
    
    res.status(500).json({ 
      error: true, 
      message: errorMessage
    });
  }
});

// Rota para detectar intenção
assistenteRouter.post('/detectar-intencao', authenticateUser, async (req, res) => {
  try {
    const { message } = req.body;
    
    if (!message || typeof message !== 'string') {
      return res.status(400).json({ 
        error: true, 
        message: 'Mensagem inválida ou ausente' 
      });
    }
    
    // Implementar a detecção de intenção (simulado por enquanto)
    const intent = {
      name: "resposta_informacao",
      confidence: 0.92,
      entities: []
    };
    
    res.json({ intent });
  } catch (error) {
    console.error('Erro na detecção de intenção:', error);
    res.status(500).json({ 
      error: true, 
      message: error instanceof Error ? error.message : 'Erro na detecção de intenção'
    });
  }
});

// Rota para extrair entidades
assistenteRouter.post('/extrair-entidades', authenticateUser, async (req, res) => {
  try {
    const { message } = req.body;
    
    if (!message || typeof message !== 'string') {
      return res.status(400).json({ 
        error: true, 
        message: 'Mensagem inválida ou ausente' 
      });
    }
    
    // Implementar a extração de entidades (simulado por enquanto)
    const entities = [
      {
        entity: "cavalo",
        value: "Pegasus",
        start: message.indexOf("Pegasus"),
        end: message.indexOf("Pegasus") + 7,
        confidence: 0.95
      }
    ];
    
    res.json({ entities });
  } catch (error) {
    console.error('Erro na extração de entidades:', error);
    res.status(500).json({ 
      error: true, 
      message: error instanceof Error ? error.message : 'Erro na extração de entidades'
    });
  }
});

export default assistenteRouter;