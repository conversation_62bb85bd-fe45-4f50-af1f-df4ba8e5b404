{"timestamp": "2025-05-15T21:47:07.412Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 400531456, "heapTotal": 111714304, "heapUsed": 72507216, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.969983957, "cpuUsage": {"user": 2826928, "system": 387572}, "resourceUsage": {"userCPUTime": 2826977, "systemCPUTime": 387578, "maxRSS": 391144, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104572, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 48, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8787, "involuntaryContextSwitches": 2943}}