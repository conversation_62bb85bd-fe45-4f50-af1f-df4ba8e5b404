{"dados": "preview_1747438772196", "logs": "{\n  \"dados\": {\n    \"cavalo\": {\n      \"nome\": \"HONESTA 305 DA VENDRAMIN\",\n      \"registro\": \"B388719\",\n      \"rp\": \"305\",\n      \"sexo\": \"FÊMEA\",\n      \"nascimento\": \"05/12/2011\",\n      \"pelagem\": \"COLORAD<PERSON>\",\n      \"criador\": \"ALDO VENDRAMIN\",\n      \"proprietario\": \"CAMILA WEBER\",\n      \"inspetor\": \"MARCOS GOMES ANTUNES\"\n    },\n    \"pai\": {\n      \"nome\": \"SÃO JOÃO DO JUNCAL PANDEMÔNIO\",\n      \"registro\": \"B190224\"\n    },\n    \"mae\": {\n      \"nome\": \"LUIZA DO CARRACHI\",\n      \"registro\": \"B279306\"\n    },\n    \"avoPai\": {\n      \"nome\": \"BT JURADO\"\n    },\n    \"avaMae\": {\n      \"nome\": \"MARAVILHA 1138 DA TRADIÇÃO\"\n    },\n    \"avoPai2\": {\n      \"nome\": \"LA INVERNADA PASCUERO\"\n    },\n    \"avaMae2\": {\n      \"nome\": \"BELA DONA DO RETIRO DO OURO\"\n    }\n  },\n  \"log\": \"[2025-05-16T23:39:28.663Z] [INFO] Iniciando processamento com OpenAI\\n[2025-05-16T23:39:32.195Z] [DEBUG] Conteúdo bruto da resposta: {\\\"cavalo_principal\\\": {\\\"nome\\\": \\\"HONESTA 305 DA VENDRAMIN\\\", \\\"registro\\\": \\\"B388719\\\", \\\"rp\\\": \\\"305\\\", \\\"sexo\\\": \\\"FEMEA\\\", \\\"nascimento\\\": \\\"05/12/2011\\\", \\\"pelagem\\\": \\\"COLORADA\\\", \\\"proprietario\\\": \\\"CAMILA WEBER\\\", \\\"criador\\\": \\\"ALDO VENDRAMIN\\\", \\\"inspetor_tecnico\\\": \\\"MARCOS GOMES ANTUNES\\\"}, \\\"pai\\\": {\\\"nome\\\": \\\"SÃO JOÃO DO JUNCAL PANDEMÔNIO\\\", \\\"registro\\\": \\\"B190224\\\"}, \\\"mae\\\": {\\\"nome\\\": \\\"LUIZA DO CARRACHI\\\", \\\"registro\\\": \\\"B279306\\\"}, \\\"avo_paterno\\\": {\\\"nome\\\": \\\"BT JURADO\\\", \\\"registro\\\": null}, \\\"ava_paterna\\\": {\\\"nome\\\": \\\"MARAVILHA 1138 DA TRADIÇÃO\\\", \\\"registro\\\": null}, \\\"avo_materno\\\": {\\\"nome\\\": \\\"LA INVERNADA PASCUERO\\\", \\\"registro\\\": null}, \\\"ava_materna\\\": {\\\"nome\\\": \\\"BELA DONA DO RETIRO DO OURO\\\", \\\"registro\\\": null}}\\n[2025-05-16T23:39:32.195Z] [INFO] Recebido resposta da OpenAI e parseado com sucesso\\n[2025-05-16T23:39:32.195Z] [DEBUG] Dados extraídos: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"HONESTA 305 DA VENDRAMIN\\\",\\n    \\\"registro\\\": \\\"B388719\\\",\\n    \\\"rp\\\": \\\"305\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"05/12/2011\\\",\\n    \\\"pelagem\\\": \\\"COLORADA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"ALDO VENDRAMIN\\\",\\n    \\\"inspetor_tecnico\\\": \\\"MARCOS GOMES ANTUNES\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"SÃO JOÃO DO JUNCAL PANDEMÔNIO\\\",\\n    \\\"registro\\\": \\\"B190224\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"LUIZA DO CARRACHI\\\",\\n    \\\"registro\\\": \\\"B279306\\\"\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"BT JURADO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"MARAVILHA 1138 DA TRADIÇÃO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"LA INVERNADA PASCUERO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"BELA DONA DO RETIRO DO OURO\\\",\\n    \\\"registro\\\": null\\n  }\\n}\\n[2025-05-16T23:39:32.195Z] [INFO] Dados do cavalo principal extraídos: {\\n  \\\"nome\\\": \\\"HONESTA 305 DA VENDRAMIN\\\",\\n  \\\"registro\\\": \\\"B388719\\\",\\n  \\\"rp\\\": \\\"305\\\",\\n  \\\"sexo\\\": \\\"FEMEA\\\",\\n  \\\"nascimento\\\": \\\"05/12/2011\\\",\\n  \\\"pelagem\\\": \\\"COLORADA\\\",\\n  \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n  \\\"criador\\\": \\\"ALDO VENDRAMIN\\\",\\n  \\\"inspetor_tecnico\\\": \\\"MARCOS GOMES ANTUNES\\\"\\n}\\n[2025-05-16T23:39:32.196Z] [INFO] Dados do cavalo principal extraídos: HONESTA 305 DA VENDRAMIN (B388719)\\n[2025-05-16T23:39:32.196Z] [INFO] Pai: SÃO JOÃO DO JUNCAL PANDEMÔNIO (B190224)\\n[2025-05-16T23:39:32.196Z] [INFO] Mãe: LUIZA DO CARRACHI (B279306)\\n[2025-05-16T23:39:32.196Z] [INFO] Dados convertidos com sucesso\"\n}", "timestamp": "2025-05-16T23:39:32.196Z"}