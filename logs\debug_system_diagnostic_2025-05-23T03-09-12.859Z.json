{"timestamp": "2025-05-23T03:09:12.859Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 290197504, "heapTotal": 154525696, "heapUsed": 150287480, "external": 13651028, "arrayBuffers": 1078518}, "uptime": 696.926566559, "cpuUsage": {"user": 20544284, "system": 1516598}, "resourceUsage": {"userCPUTime": 20544353, "systemCPUTime": 1516603, "maxRSS": 721440, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 288559, "majorPageFault": 0, "swappedOut": 0, "fsRead": 11752, "fsWrite": 1264, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 32135, "involuntaryContextSwitches": 26718}}