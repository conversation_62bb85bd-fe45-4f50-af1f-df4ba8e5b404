-- Script SQL para popular o banco de dados do EquiGestor AI com dados completos
-- Autor: EquiGestor Team
-- Data: 09/04/2025

-- Configurações iniciais
SET client_encoding = 'UTF8';

-- Limpar dados existentes (opcional - comentar se não quiser limpar)
-- DELETE FROM nutricao;
-- DELETE FROM procedimentos_vet;
-- DELETE FROM reproducao;
-- DELETE FROM medidas_fisicas;
-- DELETE FROM sugestoes_cruzamento;
-- DELETE FROM desempenho_historico;
-- DELETE FROM morfologia;
-- DELETE FROM genealogia;
-- DELETE FROM arquivos;
-- DELETE FROM eventos;
-- DELETE FROM manejos;
-- ALTER SEQUENCE cavalos_id_seq RESTART WITH 1;
-- DELETE FROM cavalos;

-- =============================
-- 1. INSERIR CAVALOS COMPLETOS
-- =============================

-- <PERSON><PERSON><PERSON><PERSON><PERSON>
INSERT INTO cavalos (name, breed, birth_date, sexo, peso, altura, notes, pai, mae, avo_paterno, avo_materno, status, data_entrada, user_id)
VALUES 
('Trovão Negro', 'Mangalarga Marchador', '2019-05-15', 'Macho', 485, 1.58, 'Garanhão premiado em diversas competições de marcha. Temperamento dócil e boa morfologia.', 'Relâmpago', 'Estrela', 'Tempestade', 'Aurora', 'ativo', '2020-01-10', 1),
('Vento Forte', 'Quarto de Milha', '2018-08-22', 'Macho', 520, 1.62, 'Garanhão com excelente linhagem para provas de velocidade. Musculatura bem desenvolvida.', 'Furacão', 'Brisa', 'Tufão', 'Ventania', 'ativo', '2019-11-05', 1),
('Diamante Negro', 'Puro Sangue Inglês', '2017-03-10', 'Macho', 510, 1.64, 'Garanhão de elite com grande potencial para corridas. Descendente de linhagem campeã.', 'Rubi', 'Safira', 'Esmeralda', 'Pérola', 'ativo', '2018-09-15', 1),
('Luar de Prata', 'Andaluz', '2020-01-18', 'Macho', 490, 1.59, 'Garanhão jovem com grande potencial para adestramento. Movimentos elegantes e precisos.', 'Sol Dourado', 'Noite Estrelada', 'Raio de Sol', 'Lua Cheia', 'ativo', '2021-06-12', 1),
('Thor', 'Árabe', '2016-11-05', 'Macho', 450, 1.55, 'Garanhão de alto desempenho em provas de resistência. Excelente capacidade cardiorrespiratória.', 'Odin', 'Freya', 'Loki', 'Hela', 'ativo', '2018-02-25', 1);

-- Éguas
INSERT INTO cavalos (name, breed, birth_date, sexo, peso, altura, notes, pai, mae, avo_paterno, avo_materno, status, data_entrada, user_id)
VALUES 
('Lua Prateada', 'Mangalarga Marchador', '2020-04-12', 'Fêmea', 450, 1.52, 'Égua de marcha picada perfeita. Ótima conformação e temperamento excelente para reprodução.', 'Luar', 'Estrela Guia', 'Sol Nascente', 'Via Láctea', 'ativo', '2021-08-20', 1),
('Brisa Suave', 'Quarto de Milha', '2019-09-30', 'Fêmea', 480, 1.54, 'Égua com ótima musculatura e performance em provas de três tambores. Muito versátil.', 'Tornado', 'Tempestade', 'Furacão', 'Ventania', 'ativo', '2020-11-15', 1),
('Pérola Negra', 'Puro Sangue Inglês', '2018-06-18', 'Fêmea', 475, 1.56, 'Égua com excelente linhagem e desempenho em corridas. Já produziu potros premiados.', 'Diamante', 'Esmeralda', 'Rubi', 'Safira', 'ativo', '2019-05-30', 1),
('Aurora Dourada', 'Andaluz', '2020-12-05', 'Fêmea', 460, 1.53, 'Égua jovem com grande potencial para adestramento clássico. Movimentos precisos e elegantes.', 'Sol Poente', 'Manhã Clara', 'Horizonte', 'Alvorada', 'ativo', '2022-03-15', 1),
('Tormenta', 'Árabe', '2017-07-20', 'Fêmea', 430, 1.50, 'Égua de grande resistência e velocidade. Excelente para enduro e provas de longa distância.', 'Vendaval', 'Tempestade', 'Ciclone', 'Borrasca', 'ativo', '2018-10-25', 1);

-- Potros e Potrancas
INSERT INTO cavalos (name, breed, birth_date, sexo, peso, altura, notes, pai, mae, avo_paterno, avo_materno, status, data_entrada, user_id)
VALUES 
('Relâmpago Jr', 'Mangalarga Marchador', '2022-03-25', 'Macho', 350, 1.40, 'Potro com excelente desenvolvimento e potencial para marcha. Filho de Trovão Negro.', 'Trovão Negro', 'Lua Prateada', 'Relâmpago', 'Estrela Guia', 'ativo', '2022-03-25', 1),
('Ventania', 'Quarto de Milha', '2022-08-14', 'Fêmea', 320, 1.35, 'Potranca com ótimas linhas e desenvolvimento muscular precoce. Filha de Vento Forte.', 'Vento Forte', 'Brisa Suave', 'Furacão', 'Tempestade', 'ativo', '2022-08-14', 1),
('Cristal', 'Puro Sangue Inglês', '2023-01-10', 'Fêmea', 290, 1.30, 'Potranca jovem com excelente estrutura óssea e desenvolvimento. Filha de Diamante Negro.', 'Diamante Negro', 'Pérola Negra', 'Rubi', 'Esmeralda', 'ativo', '2023-01-10', 1),
('Raio de Sol', 'Andaluz', '2023-04-18', 'Macho', 280, 1.28, 'Potro jovem com movimentos elegantes desde o nascimento. Filho de Luar de Prata.', 'Luar de Prata', 'Aurora Dourada', 'Sol Dourado', 'Manhã Clara', 'ativo', '2023-04-18', 1),
('Tempestade', 'Árabe', '2022-10-05', 'Fêmea', 310, 1.34, 'Potranca com excelente estrutura e temperamento. Filha de Thor.', 'Thor', 'Tormenta', 'Odin', 'Tempestade', 'ativo', '2022-10-05', 1);

-- Cavalos castrados
INSERT INTO cavalos (name, breed, birth_date, sexo, peso, altura, notes, pai, mae, avo_paterno, avo_materno, status, data_entrada, user_id)
VALUES 
('Galope', 'Mangalarga Marchador', '2018-09-12', 'Macho (Castrado)', 470, 1.57, 'Cavalo de sela com excelente andamento. Ideal para passeios e lazer.', 'Trovador', 'Melodia', 'Violeiro', 'Cantora', 'ativo', '2019-12-10', 1),
('Veloz', 'Quarto de Milha', '2017-11-20', 'Macho (Castrado)', 510, 1.60, 'Cavalo de trabalho com gado. Muito ágil e obediente.', 'Relâmpago', 'Tempestade', 'Trovão', 'Ventania', 'ativo', '2018-08-15', 1),
('Valente', 'Crioulo', '2016-05-18', 'Macho (Castrado)', 480, 1.55, 'Cavalo resistente e versátil. Excelente para provas funcionais.', 'Intrépido', 'Coragem', 'Destemido', 'Audácia', 'ativo', '2017-06-20', 1),
('Imperador', 'Lusitano', '2015-08-30', 'Macho (Castrado)', 495, 1.58, 'Cavalo de adestramento com movimentos elegantes e precisos.', 'Rei', 'Rainha', 'Príncipe', 'Princesa', 'ativo', '2016-10-15', 1),
('Guerreiro', 'Bretão', '2014-10-15', 'Macho (Castrado)', 650, 1.70, 'Cavalo de tração forte e resistente. Temperamento dócil apesar do porte.', 'Titã', 'Força', 'Atlas', 'Potência', 'ativo', '2015-12-10', 1);

-- =============================
-- 2. INSERIR DADOS DE GENEALOGIA
-- =============================

INSERT INTO genealogia (horse_id, pai, mae, avo_paterno, avo_materno, bisavo_paterno_1, bisavo_paterno_2, bisavo_materno_1, bisavo_materno_2, coeficiente_consanguinidade, observacoes, user_id)
VALUES
-- Garanhões
(1, 'Relâmpago', 'Estrela', 'Tempestade', 'Aurora', 'Raio', 'Faísca', 'Sol', 'Lua', 0.0625, 'Linhagem forte com baixa consanguinidade. Excelente para reprodução.', 1),
(2, 'Furacão', 'Brisa', 'Tufão', 'Ventania', 'Ciclone', 'Tornado', 'Vento', 'Tempestade', 0.0312, 'Linhagem de velocidade com cruzamentos bem planejados.', 1),
(3, 'Rubi', 'Safira', 'Esmeralda', 'Pérola', 'Diamante', 'Topázio', 'Ametista', 'Jade', 0.125, 'Linhagem de elite para corridas, com moderada consanguinidade.', 1),
(4, 'Sol Dourado', 'Noite Estrelada', 'Raio de Sol', 'Lua Cheia', 'Amanhecer', 'Poente', 'Céu', 'Galáxia', 0.0, 'Linhagem pura sem consanguinidade nas últimas 4 gerações.', 1),
(5, 'Odin', 'Freya', 'Loki', 'Hela', 'Thor', 'Heimdall', 'Sif', 'Frigga', 0.1875, 'Linhagem compacta com consanguinidade moderada para fixação de características.', 1),

-- Éguas
(6, 'Luar', 'Estrela Guia', 'Sol Nascente', 'Via Láctea', 'Horizonte', 'Alvorada', 'Cometa', 'Nebulosa', 0.0156, 'Linhagem materna forte com baixa consanguinidade.', 1),
(7, 'Tornado', 'Tempestade', 'Furacão', 'Ventania', 'Ciclone', 'Tufão', 'Brisa', 'Borrasca', 0.0937, 'Linhagem cruzada com ascendentes de alto desempenho.', 1),
(8, 'Diamante', 'Esmeralda', 'Rubi', 'Safira', 'Brilhante', 'Cristal', 'Jade', 'Ônix', 0.0781, 'Linhagem de puro sangue clássica com consanguinidade controlada.', 1),
(9, 'Sol Poente', 'Manhã Clara', 'Horizonte', 'Alvorada', 'Amanhecer', 'Crepúsculo', 'Aurora', 'Nascente', 0.0312, 'Linhagem elegante com cruzamentos para adestramento clássico.', 1),
(10, 'Vendaval', 'Tempestade', 'Ciclone', 'Borrasca', 'Tufão', 'Furacão', 'Ventania', 'Tornado', 0.1562, 'Linhagem árabe tradicional com consanguinidade para resistência.', 1),

-- Potros e potrancas (genealogia direta dos pais acima)
(11, 'Trovão Negro', 'Lua Prateada', 'Relâmpago', 'Luar', 'Tempestade', 'Sol Nascente', 'Estrela', 'Estrela Guia', 0.0312, 'Primeira geração do plantel. Potencial para marcha picada.', 1),
(12, 'Vento Forte', 'Brisa Suave', 'Furacão', 'Tornado', 'Tufão', 'Furacão', 'Brisa', 'Tempestade', 0.1875, 'Primeira geração com consanguinidade calculada para velocidade.', 1),
(13, 'Diamante Negro', 'Pérola Negra', 'Rubi', 'Diamante', 'Esmeralda', 'Rubi', 'Safira', 'Esmeralda', 0.25, 'Consanguinidade elevada para fixação de características de corrida.', 1),
(14, 'Luar de Prata', 'Aurora Dourada', 'Sol Dourado', 'Sol Poente', 'Raio de Sol', 'Horizonte', 'Noite Estrelada', 'Manhã Clara', 0.0156, 'Cruzamento planejado para movimentos de adestramento.', 1),
(15, 'Thor', 'Tormenta', 'Odin', 'Vendaval', 'Loki', 'Ciclone', 'Freya', 'Tempestade', 0.0312, 'Linhagem cruzada para resistência e velocidade.', 1);

-- =============================
-- 3. INSERIR DADOS DE MORFOLOGIA
-- =============================

-- Garanhões
INSERT INTO morfologia (horse_id, data_medicao, altura_cernelha, altura_dorso, altura_garupa, comprimento_corpo, comprimento_pescoco, largura_peito, perimetro_toracico, perimetro_pescoco, perimetro_canela, pontuacao_cabeca, pontuacao_pescoco, pontuacao_espalda, pontuacao_dorso, pontuacao_garupa, pontuacao_membros, pontuacao_aprumos, pontuacao_andamento, pontuacao_harmonia, pontuacao_total, observacoes, responsavel_medicao, user_id)
VALUES
(1, '2024-09-15', 1.58, 1.49, 1.56, 1.67, 0.72, 0.48, 1.82, 0.92, 0.21, 9, 9, 8, 9, 8, 8, 8, 9, 9, 8.7, 'Morfologia excelente. Destaque para andamento e estrutura de pescoço.', 'Dr. Carlos Santos', 1),
(1, '2025-03-10', 1.58, 1.49, 1.56, 1.68, 0.73, 0.49, 1.84, 0.94, 0.21, 9, 9, 8, 9, 8, 8, 8, 9, 9, 8.7, 'Manutenção da excelente morfologia, com ganho de perímetro torácico.', 'Dr. Paulo Oliveira', 1),
(2, '2024-11-20', 1.62, 1.52, 1.60, 1.70, 0.74, 0.51, 1.90, 0.96, 0.22, 8, 8, 9, 8, 9, 9, 8, 8, 8, 8.5, 'Morfologia robusta, típica de Quarto de Milha. Excelente musculatura.', 'Dr. Carlos Santos', 1),
(3, '2025-01-05', 1.64, 1.54, 1.62, 1.73, 0.76, 0.50, 1.92, 0.97, 0.22, 9, 8, 9, 9, 9, 9, 8, 8, 9, 8.8, 'Morfologia refinada com excelentes linhas. Modelo para corrida.', 'Dra. Marta Lima', 1),
(4, '2025-02-18', 1.59, 1.49, 1.57, 1.69, 0.75, 0.49, 1.86, 0.95, 0.21, 9, 9, 8, 8, 9, 8, 9, 9, 9, 8.8, 'Morfologia elegante com destaque para pescoço e andamento.', 'Dr. Paulo Oliveira', 1),
(5, '2024-12-10', 1.55, 1.46, 1.53, 1.64, 0.70, 0.47, 1.78, 0.90, 0.20, 9, 8, 8, 8, 8, 9, 8, 9, 8, 8.4, 'Morfologia compacta e equilibrada. Típico exemplar árabe.', 'Dra. Marta Lima', 1);

-- Éguas
INSERT INTO morfologia (horse_id, data_medicao, altura_cernelha, altura_dorso, altura_garupa, comprimento_corpo, comprimento_pescoco, largura_peito, perimetro_toracico, perimetro_pescoco, perimetro_canela, pontuacao_cabeca, pontuacao_pescoco, pontuacao_espalda, pontuacao_dorso, pontuacao_garupa, pontuacao_membros, pontuacao_aprumos, pontuacao_andamento, pontuacao_harmonia, pontuacao_total, observacoes, responsavel_medicao, user_id)
VALUES
(6, '2025-02-22', 1.52, 1.43, 1.50, 1.62, 0.68, 0.45, 1.76, 0.87, 0.19, 9, 9, 8, 8, 9, 8, 8, 9, 9, 8.7, 'Morfologia feminina refinada. Excelente estrutura para reprodução.', 'Dr. Carlos Santos', 1),
(7, '2025-01-15', 1.54, 1.45, 1.52, 1.64, 0.69, 0.47, 1.80, 0.88, 0.20, 8, 8, 9, 8, 9, 9, 8, 8, 8, 8.4, 'Boa musculatura e estrutura para provas. Garupa potente.', 'Dr. Paulo Oliveira', 1),
(8, '2024-11-30', 1.56, 1.47, 1.54, 1.66, 0.71, 0.46, 1.82, 0.88, 0.20, 9, 8, 8, 9, 9, 8, 8, 8, 9, 8.6, 'Morfologia elegante e equilibrada. Excelente para corridas.', 'Dra. Marta Lima', 1),
(9, '2025-03-05', 1.53, 1.44, 1.51, 1.63, 0.70, 0.45, 1.77, 0.86, 0.19, 9, 9, 8, 8, 8, 8, 9, 9, 9, 8.7, 'Morfologia muito refinada. Movimentos elegantes naturais.', 'Dr. Paulo Oliveira', 1),
(10, '2024-10-25', 1.50, 1.41, 1.48, 1.60, 0.67, 0.44, 1.73, 0.85, 0.19, 9, 8, 8, 8, 8, 9, 8, 9, 8, 8.4, 'Estrutura compacta e resistente. Típica morfologia árabe.', 'Dr. Carlos Santos', 1);

-- Potros e Potrancas (medidas de desenvolvimento)
INSERT INTO morfologia (horse_id, data_medicao, altura_cernelha, altura_dorso, altura_garupa, comprimento_corpo, comprimento_pescoco, largura_peito, perimetro_toracico, perimetro_pescoco, perimetro_canela, pontuacao_cabeca, pontuacao_pescoco, pontuacao_espalda, pontuacao_dorso, pontuacao_garupa, pontuacao_membros, pontuacao_aprumos, pontuacao_andamento, pontuacao_harmonia, pontuacao_total, observacoes, responsavel_medicao, user_id)
VALUES
(11, '2025-03-25', 1.40, 1.31, 1.38, 1.45, 0.60, 0.39, 1.54, 0.75, 0.18, 8, 8, 8, 8, 8, 8, 7, 8, 8, 7.9, 'Desenvolvimento adequado para a idade. Bom potencial para marcha.', 'Dr. Carlos Santos', 1),
(12, '2025-02-14', 1.35, 1.26, 1.33, 1.40, 0.57, 0.37, 1.48, 0.72, 0.17, 8, 8, 8, 7, 8, 8, 7, 8, 8, 7.8, 'Desenvolvimento muscular precoce. Boas linhas de crescimento.', 'Dra. Marta Lima', 1),
(13, '2025-03-10', 1.30, 1.21, 1.28, 1.35, 0.54, 0.35, 1.40, 0.68, 0.16, 8, 7, 7, 7, 8, 7, 7, 7, 8, 7.4, 'Desenvolvimento normal para a idade. Bom potencial de crescimento.', 'Dr. Paulo Oliveira', 1),
(14, '2025-04-18', 1.28, 1.19, 1.26, 1.32, 0.52, 0.34, 1.36, 0.65, 0.15, 8, 7, 7, 7, 7, 7, 7, 8, 8, 7.3, 'Desenvolvimento inicial com bons indicadores. Elegância já aparente.', 'Dr. Carlos Santos', 1),
(15, '2025-03-05', 1.34, 1.25, 1.32, 1.38, 0.56, 0.36, 1.45, 0.70, 0.17, 8, 8, 7, 7, 8, 8, 7, 8, 8, 7.7, 'Bom desenvolvimento para a idade. Estrutura compacta árabe aparente.', 'Dra. Marta Lima', 1);

-- =============================
-- 4. INSERIR DADOS DE DESEMPENHO
-- =============================

-- Garanhões
INSERT INTO desempenho_historico (horse_id, data, tipo_evento, nome_evento, categoria, resultado, observacoes, premiacao, desempenho_detalhes, user_id)
VALUES
(1, '2024-06-20', 'Competição', 'Campeonato Brasileiro de Marcha', 'Mangalarga Marchador Senior', '1º Lugar', 'Excelente apresentação, com marcha consistente e regular', 'Troféu, Medalha e R$ 8.000,00', 'Pontuação: 9.8/10. Juiz destacou qualidade da marcha picada, dissociação perfeita e temperamento exemplar durante toda a apresentação.', 1),
(1, '2024-10-15', 'Exposição', 'Exposição Nacional Mangalarga', 'Garanhões 5-7 anos', '2º Lugar', 'Ótima apresentação morfológica e funcional', 'Troféu e Medalha', 'Pontuação: 9.5/10. Pequena perda em aprumos, mas excelente avaliação geral.', 1),
(1, '2025-03-05', 'Competição', 'Copa Marchas do Interior', 'Marcha Picada Elite', '1º Lugar', 'Desempenho impecável, confirmando qualidade da marcha', 'Troféu, Medalha e R$ 5.000,00', 'Pontuação: 9.9/10. Melhor desempenho da competição, com excelente técnica e regularidade.', 1),
(2, '2024-07-18', 'Competição', 'Prova dos Três Tambores', 'Aberta Senior', '1º Lugar', 'Tempo excepcional, demonstrando agilidade e precisão', 'Troféu, Medalha e R$ 10.000,00', 'Tempo: 16.2 segundos. Melhor marca da competição, com curvas precisas e excelente aceleração.', 1),
(2, '2024-11-25', 'Competição', 'ABQM Grand Prix', 'Velocidade Elite', '3º Lugar', 'Bom desempenho contra competidores de alto nível', 'Medalha e R$ 3.000,00', 'Tempo: 16.5 segundos. Pequena perda na primeira curva, mas boa recuperação.', 1),
(3, '2024-09-10', 'Competição', 'Grande Prêmio Nacional', 'Corrida 1600m', '2º Lugar', 'Excelente corrida, perdendo por apenas meio corpo', 'Troféu e R$ 15.000,00', 'Tempo: 1:34.5. Ótima largada e ritmo, mas superado no sprint final.', 1),
(3, '2025-02-28', 'Competição', 'Copa Ouro de Velocidade', 'Corrida 2000m', '1º Lugar', 'Impressionante desempenho, dominando toda a prova', 'Troféu, Medalha e R$ 25.000,00', 'Tempo: 2:01.3. Novo recorde da pista, com excelente estratégia de corrida.', 1),
(4, '2024-08-15', 'Competição', 'Campeonato de Adestramento', 'Série Intermediária', '1º Lugar', 'Movimentos precisos e elegantes em todos os exercícios', 'Troféu, Medalha e R$ 7.000,00', 'Pontuação: 74.5%. Destaque para piruetas e passagens laterais perfeitas.', 1),
(4, '2025-01-20', 'Exposição', 'Exposição Nacional Equina', 'Garanhões Espanhóis', '1º Lugar', 'Apresentação impecável, destacando-se pela morfologia e movimentos', 'Troféu e R$ 5.000,00', 'Pontuação: 92/100. Juízes destacaram elegância e presença na pista.', 1),
(5, '2024-10-05', 'Competição', 'Enduro Equestre Nacional', '80 km Elite', '1º Lugar', 'Excelente estratégia e condicionamento físico', 'Troféu, Medalha e R$ 8.000,00', 'Tempo: 4:20:15. Excelente recuperação cardíaca nos check points e ótimo ritmo constante.', 1),
(5, '2025-03-15', 'Competição', 'Copa Desert Challenge', '120 km', '2º Lugar', 'Ótima resistência em condições climáticas adversas', 'Troféu e R$ 6.000,00', 'Tempo: 6:40:22. Excelente desempenho, superado apenas no último trecho.', 1);

-- Éguas
INSERT INTO desempenho_historico (horse_id, data, tipo_evento, nome_evento, categoria, resultado, observacoes, premiacao, desempenho_detalhes, user_id)
VALUES
(6, '2024-07-10', 'Competição', 'Campeonato Estadual de Marcha', 'Éguas 3-5 anos', '1º Lugar', 'Marcha picada perfeita com excelente regularidade', 'Troféu, Medalha e R$ 6.000,00', 'Pontuação: 9.7/10. Juízes destacaram a dissociação dos membros e suavidade do andamento.', 1),
(6, '2024-11-20', 'Exposição', 'Exposição Nacional Mangalarga', 'Éguas Jovens', '2º Lugar', 'Excelente conformação e apresentação', 'Troféu e Medalha', 'Pontuação: 9.4/10. Pequena perda em cabeça e pescoço, mas excelente avaliação.', 1),
(7, '2024-08-25', 'Competição', 'Três Tambores Regional', 'Feminino Aberto', '1º Lugar', 'Tempo impressionante, demonstrando agilidade excepcional', 'Troféu, Medalha e R$ 7.000,00', 'Tempo: 16.8 segundos. Excelente controle nas curvas e aceleração final.', 1),
(7, '2025-02-10', 'Competição', 'Prova Team Penning', 'Misto Profissional', '2º Lugar', 'Excelente trabalho em equipe com ótima demonstração de manejo de gado', 'Troféu e R$ 5.000,00', 'Tempo: 55.4 segundos com 9 cabeças. Excelente controle e agilidade.', 1),
(8, '2024-09-05', 'Competição', 'Grande Prêmio Feminino', 'Corrida 1400m', '1º Lugar', 'Vitória impressionante, dominando toda a prova', 'Troféu, Medalha e R$ 20.000,00', 'Tempo: 1:23.8. Largada explosiva e manutenção de ritmo até o final.', 1),
(8, '2025-01-15', 'Competição', 'Copa Velocidade', 'Éguas 5-7 anos', '3º Lugar', 'Boa performance contra competidoras mais experientes', 'Medalha e R$ 5.000,00', 'Tempo: 1:24.5. Boa corrida, perdendo posição apenas no final.', 1),
(9, '2024-10-20', 'Competição', 'Festival de Adestramento', 'Série Elementar', '1º Lugar', 'Execução precisa e elegante de todos os movimentos', 'Troféu, Medalha e R$ 5.000,00', 'Pontuação: 72.8%. Destaque para transições suaves e circle perfeitos.', 1),
(9, '2025-03-10', 'Exposição', 'Mostra Equestre Ibérica', 'Éguas Puro Sangue', '1º Lugar', 'Apresentação destacada pela elegância e presença', 'Troféu e R$ 4.000,00', 'Pontuação: 91/100. Destaque para morfologia refinada e movimentos elegantes.', 1),
(10, '2024-11-10', 'Competição', 'Enduro Equestre Regional', '60 km', '1º Lugar', 'Estratégia perfeita, mantendo ritmo constante', 'Troféu, Medalha e R$ 6.000,00', 'Tempo: 3:10:25. Excelente recuperação cardíaca e constância no ritmo.', 1),
(10, '2025-02-25', 'Competição', 'Copa Resistência', '80 km', '2º Lugar', 'Excelente resistência em terreno acidentado', 'Troféu e R$ 4.000,00', 'Tempo: 4:25:18. Ótimo desempenho em condições difíceis.', 1);

-- =============================
-- 5. INSERIR SUGESTÕES DE CRUZAMENTO
-- =============================

INSERT INTO sugestoes_cruzamento (horse_id_base, horse_id_sugerido, data_sugestao, objetivo, pontuacao_compatibilidade, analise_consanguinidade, analise_desempenho, analise_morfologia, prev_potencial_morfologico, prev_potencial_desempenho, prev_potencial_temperamento, justificativa, recomendacoes, user_id)
VALUES
-- Cruzamentos para éguas
(6, 1, '2025-04-07', 'morfologia', 92.5, 
'Análise de consanguinidade: 7.8%. Valor dentro da faixa ideal para manutenção das características desejáveis sem riscos genéticos significativos.', 
'Compatibilidade de desempenho excepcional. Ambos têm histórico de premiações em competições de marcha, com pontuações acima de 9.5/10.',
'Excelente compatibilidade morfológica. Complemento perfeito: o garanhão tem pontuação 9 em pescoço e andamento, enquanto a égua tem 9 em cabeça e harmonia.',
9.2, 9.0, 8.5, 
'Este cruzamento tem potencial para produzir descendentes com marcha excepcional e morfologia refinada. A baixa consanguinidade mantém a diversidade genética enquanto fixa as características desejáveis da raça Mangalarga Marchador.',
'Cruzamento altamente recomendado. Planejar para época ideal de reprodução (setembro-outubro) quando a égua estiver no melhor momento do ciclo. Acompanhamento veterinário especializado é essencial.',
1),

(7, 2, '2025-04-07', 'desempenho', 90.0, 
'Análise de consanguinidade: 9.4%. Este valor está dentro da faixa aceitável para o objetivo de melhoramento de desempenho atlético.',
'Compatibilidade de desempenho excelente. Ambos são vencedores em provas de velocidade e agilidade, com tempos recordes.',
'Boa compatibilidade morfológica. O garanhão contribui com forte estrutura muscular nas pernas, enquanto a égua adiciona flexibilidade e agilidade.',
8.5, 9.5, 8.7, 
'Este cruzamento visa produzir animais de alta performance para provas de Três Tambores e outras competições western. A combinação genética promete velocidade, agilidade e capacidade atlética superior.',
'Cruzamento fortemente recomendado para objetivos de performance. Sugerimos acompanhamento especializado no desenvolvimento do potro, com programa de treinamento específico a partir dos 18 meses.',
1),

(8, 3, '2025-04-07', 'velocidade', 94.0, 
'Análise de consanguinidade: 8.2%. Valor ideal para manutenção da linhagem de corrida sem comprometer a diversidade genética.',
'Compatibilidade de desempenho excepcional. Combinação de vencedores de provas de velocidade com tempos recordes em pistas oficiais.',
'Excelente compatibilidade morfológica. Estrutura óssea e muscular ideal para velocidade, com complementaridade nas características físicas.',
8.8, 9.7, 8.6, 
'Este cruzamento tem potencial para produzir um animal excepcional para corridas. A genealogia de ambos mostra consistência em produzir vencedores de Grandes Prêmios.',
'Cruzamento premium recomendado. Sugerimos acompanhamento nutricional especializado desde a gestação, com suplementação específica para desenvolvimento ósseo e muscular adequados.',
1),

(9, 4, '2025-04-07', 'adestramento', 93.5, 
'Análise de consanguinidade: 6.2%. Valor baixo que favorece a diversidade genética enquanto mantém características desejáveis.',
'Compatibilidade de desempenho excelente. Ambos têm premiações em provas de adestramento com movimentos precisos e elegantes.',
'Compatibilidade morfológica superior. A combinação promete estrutura refinada, com destaque para pescoço alongado e movimentos amplos.',
9.4, 9.2, 9.0, 
'Este cruzamento é ideal para produzir um animal de adestramento de alto nível. A combinação genética favorece elegância, inteligência e capacidade de aprendizado.',
'Cruzamento altamente recomendado para adestramento clássico. Programa de treinamento específico recomendado a partir dos 24 meses, com foco em flexibilidade e obediência.',
1),

(10, 5, '2025-04-07', 'resistência', 91.5, 
'Análise de consanguinidade: 10.5%. Valor aceitável para linhagens árabes focadas em resistência.',
'Compatibilidade de desempenho superior. Ambos são vencedores em provas de enduro e resistência em condições adversas.',
'Boa compatibilidade morfológica. A estrutura compacta e eficiente energeticamente de ambos favorece provas de longa distância.',
8.7, 9.6, 9.2, 
'Este cruzamento visa produzir um animal excepcional para provas de enduro e resistência. A combinação genética favorece eficiência cardiovascular e estrutura óssea resistente.',
'Cruzamento fortemente recomendado para objetivos de enduro. Sugerimos programa nutricional específico com monitoramento cardiorrespiratório desde os primeiros meses.',
1),

-- Cruzamentos para garanhões
(1, 6, '2025-04-07', 'morfologia', 92.5, 
'Análise de consanguinidade: 7.8%. Valor dentro da faixa ideal para manutenção das características desejáveis sem riscos genéticos significativos.',
'Compatibilidade de desempenho excepcional. Ambos têm histórico de premiações em competições de marcha, com pontuações acima de 9.5/10.',
'Excelente compatibilidade morfológica. Complemento perfeito: o garanhão tem pontuação 9 em pescoço e andamento, enquanto a égua tem 9 em cabeça e harmonia.',
9.2, 9.0, 8.5, 
'Este cruzamento tem potencial para produzir descendentes com marcha excepcional e morfologia refinada. A baixa consanguinidade mantém a diversidade genética enquanto fixa as características desejáveis da raça Mangalarga Marchador.',
'Cruzamento altamente recomendado. Planejar para época ideal de reprodução (setembro-outubro) quando a égua estiver no melhor momento do ciclo. Acompanhamento veterinário especializado é essencial.',
1),

(2, 7, '2025-04-07', 'desempenho', 90.0, 
'Análise de consanguinidade: 9.4%. Este valor está dentro da faixa aceitável para o objetivo de melhoramento de desempenho atlético.',
'Compatibilidade de desempenho excelente. Ambos são vencedores em provas de velocidade e agilidade, com tempos recordes.',
'Boa compatibilidade morfológica. O garanhão contribui com forte estrutura muscular nas pernas, enquanto a égua adiciona flexibilidade e agilidade.',
8.5, 9.5, 8.7, 
'Este cruzamento visa produzir animais de alta performance para provas de Três Tambores e outras competições western. A combinação genética promete velocidade, agilidade e capacidade atlética superior.',
'Cruzamento fortemente recomendado para objetivos de performance. Sugerimos acompanhamento especializado no desenvolvimento do potro, com programa de treinamento específico a partir dos 18 meses.',
1),

(3, 8, '2025-04-07', 'velocidade', 94.0, 
'Análise de consanguinidade: 8.2%. Valor ideal para manutenção da linhagem de corrida sem comprometer a diversidade genética.',
'Compatibilidade de desempenho excepcional. Combinação de vencedores de provas de velocidade com tempos recordes em pistas oficiais.',
'Excelente compatibilidade morfológica. Estrutura óssea e muscular ideal para velocidade, com complementaridade nas características físicas.',
8.8, 9.7, 8.6, 
'Este cruzamento tem potencial para produzir um animal excepcional para corridas. A genealogia de ambos mostra consistência em produzir vencedores de Grandes Prêmios.',
'Cruzamento premium recomendado. Sugerimos acompanhamento nutricional especializado desde a gestação, com suplementação específica para desenvolvimento ósseo e muscular adequados.',
1),

(4, 9, '2025-04-07', 'adestramento', 93.5, 
'Análise de consanguinidade: 6.2%. Valor baixo que favorece a diversidade genética enquanto mantém características desejáveis.',
'Compatibilidade de desempenho excelente. Ambos têm premiações em provas de adestramento com movimentos precisos e elegantes.',
'Compatibilidade morfológica superior. A combinação promete estrutura refinada, com destaque para pescoço alongado e movimentos amplos.',
9.4, 9.2, 9.0, 
'Este cruzamento é ideal para produzir um animal de adestramento de alto nível. A combinação genética favorece elegância, inteligência e capacidade de aprendizado.',
'Cruzamento altamente recomendado para adestramento clássico. Programa de treinamento específico recomendado a partir dos 24 meses, com foco em flexibilidade e obediência.',
1),

(5, 10, '2025-04-07', 'resistência', 91.5, 
'Análise de consanguinidade: 10.5%. Valor aceitável para linhagens árabes focadas em resistência.',
'Compatibilidade de desempenho superior. Ambos são vencedores em provas de enduro e resistência em condições adversas.',
'Boa compatibilidade morfológica. A estrutura compacta e eficiente energeticamente de ambos favorece provas de longa distância.',
8.7, 9.6, 9.2, 
'Este cruzamento visa produzir um animal excepcional para provas de enduro e resistência. A combinação genética favorece eficiência cardiovascular e estrutura óssea resistente.',
'Cruzamento fortemente recomendado para objetivos de enduro. Sugerimos programa nutricional específico com monitoramento cardiorrespiratório desde os primeiros meses.',
1);

-- =============================
-- 6. INSERIR MEDIDAS FÍSICAS
-- =============================

-- Garanhões (histórico de medidas físicas)
INSERT INTO medidas_fisicas (horse_id, data, peso, altura, condicao_corporal, observacoes, user_id)
VALUES
-- Trovão Negro - ID 1
(1, '2024-04-15', 470, 1.58, 3, 'Início da temporada de competições. Condição física boa, mas pode melhorar.', 1),
(1, '2024-07-20', 478, 1.58, 4, 'Melhora significativa após período de treinamento intensivo. Condição física ideal para competições.', 1),
(1, '2024-10-10', 485, 1.58, 4, 'Manutenção da excelente condição física durante período competitivo.', 1),
(1, '2025-01-15', 490, 1.58, 5, 'Peso ligeiramente acima do ideal após período de descanso. Iniciar programa de exercícios.', 1),
(1, '2025-04-05', 480, 1.58, 4, 'Retorno à condição física ideal após ajustes no programa de exercícios e alimentação.', 1),

-- Vento Forte - ID 2
(2, '2024-05-10', 510, 1.62, 3, 'Condição física adequada, mas com potencial para desenvolvimento muscular.', 1),
(2, '2024-08-15', 518, 1.62, 4, 'Excelente desenvolvimento muscular após programa específico de treinamento.', 1),
(2, '2024-11-20', 525, 1.62, 5, 'Peso ligeiramente acima do ideal para competições. Recomendar ajuste na alimentação.', 1),
(2, '2025-02-25', 520, 1.62, 4, 'Retorno à condição física ideal para competições.', 1),
(2, '2025-04-05', 515, 1.62, 4, 'Manutenção da condição física ideal com pequena redução controlada de peso.', 1),

-- Éguas (histórico de medidas físicas)
-- Lua Prateada - ID 6
(6, '2024-04-20', 440, 1.52, 3, 'Condição física adequada. Iniciar preparação para temporada reprodutiva.', 1),
(6, '2024-07-25', 448, 1.52, 4, 'Excelente condição física para reprodução.', 1),
(6, '2024-10-15', 455, 1.52, 5, 'Peso ligeiramente acima do ideal. Recomendado ajuste alimentar.', 1),
(6, '2025-01-20', 450, 1.52, 4, 'Retorno à condição física ideal após ajustes na alimentação.', 1),
(6, '2025-04-05', 445, 1.52, 4, 'Manutenção da condição física ideal para reprodução.', 1),

-- Brisa Suave - ID 7
(7, '2024-05-15', 470, 1.54, 3, 'Condição física adequada, mas com potencial para melhora.', 1),
(7, '2024-08-20', 478, 1.54, 4, 'Excelente condição física após período de treinamento específico.', 1),
(7, '2024-11-25', 485, 1.54, 5, 'Peso ligeiramente acima do ideal para provas. Ajustar alimentação.', 1),
(7, '2025-03-01', 480, 1.54, 4, 'Retorno à condição física ideal para competições.', 1),
(7, '2025-04-05', 475, 1.54, 4, 'Manutenção da excelente condição física para a temporada de provas.', 1),

-- Potros (desenvolvimento físico)
-- Relâmpago Jr - ID 11
(11, '2024-06-25', 300, 1.32, 3, '6 meses: Desenvolvimento dentro do esperado para a idade.', 1),
(11, '2024-09-25', 320, 1.36, 3, '9 meses: Bom ganho de altura e peso. Desenvolvimento alinhado com as expectativas.', 1),
(11, '2024-12-25', 335, 1.38, 4, '12 meses: Excelente desenvolvimento para o primeiro ano. Condição física muito boa.', 1),
(11, '2025-03-25', 350, 1.40, 4, '15 meses: Desenvolvimento consistente e contínuo. Altura e peso ideais para a idade.', 1),

-- Ventania - ID 12
(12, '2024-08-14', 285, 1.30, 3, '6 meses: Desenvolvimento dentro do esperado para a idade e raça.', 1),
(12, '2024-11-14', 300, 1.33, 3, '9 meses: Bom ganho de altura e desenvolvimento muscular.', 1),
(12, '2025-02-14', 315, 1.35, 4, '12 meses: Desenvolvimento muito bom para o primeiro ano completo.', 1),
(12, '2025-04-05', 320, 1.36, 4, '14 meses: Manutenção da boa taxa de crescimento. Desenvolvimento muscular excelente.', 1);

-- =============================
-- 7. INSERIR NUTRICAO
-- =============================

-- Garanhões
INSERT INTO nutricao (horse_id, data, tipo_alimentacao, nome_alimento, quantidade, unidade_medida, frequencia_diaria, horarios, observacoes, custo_unitario, custo_mensal, fornecedor, recomendacao, status, user_id)
VALUES
-- Trovão Negro - ID 1 (Dieta de competição)
(1, '2025-04-01', 'Concentrado', 'Ração Premium Performance', 4.0, 'kg', 2, '07:00, 17:00', 'Ração específica para cavalos atletas em período de competição', 8.50, 510.00, 'Nutrição Equina Brasil', 'Recomendado pelo Dr. Carlos Santos para período competitivo', 'ativo', 1),
(1, '2025-04-01', 'Volumoso', 'Feno de Alfafa Premium', 8.0, 'kg', 2, '06:30, 16:30', 'Feno de alta qualidade com excelente valor nutricional', 3.20, 768.00, 'Fazenda Verde', 'Base da alimentação para saúde digestiva', 'ativo', 1),
(1, '2025-04-01', 'Suplemento', 'Suplemento Vitamínico Mineral para Atletas', 0.05, 'kg', 1, '07:15', 'Suplemento específico com alto teor de vitamina E e selênio', 120.00, 180.00, 'VetEquinos', 'Suporte para recuperação muscular e performance', 'ativo', 1),
(1, '2025-04-01', 'Suplemento', 'Óleo de Linhaça', 0.1, 'l', 1, '17:15', 'Suplementação energética e fonte de ômega 3', 45.00, 135.00, 'NutriEquinos', 'Energia de liberação lenta e saúde da pelagem', 'ativo', 1),

-- Vento Forte - ID 2 (Dieta de competição)
(2, '2025-04-01', 'Concentrado', 'Ração High Performance Energy', 4.5, 'kg', 3, '06:00, 12:00, 18:00', 'Ração com alto teor energético para cavalos de prova', 9.20, 621.00, 'ProEqui Nutrição', 'Formulada para alta demanda energética', 'ativo', 1),
(2, '2025-04-01', 'Volumoso', 'Feno de Tifton', 9.0, 'kg', 3, '05:30, 11:30, 17:30', 'Feno de qualidade para base da alimentação', 2.80, 756.00, 'Fazenda Verde', 'Essencial para funcionamento intestinal adequado', 'ativo', 1),
(2, '2025-04-01', 'Suplemento', 'Eletrólitos Performance', 0.03, 'kg', 1, '18:15', 'Reposição eletrolítica após exercícios intensos', 85.00, 76.50, 'VetEquinos', 'Fundamental para recuperação pós-treino', 'ativo', 1),
(2, '2025-04-01', 'Suplemento', 'Suplemento Articular', 0.04, 'kg', 1, '06:15', 'Proteção articular para cavalos atletas', 150.00, 180.00, 'EquiSaúde', 'Prevenção de problemas articulares em cavalos de prova', 'ativo', 1),

-- Éguas reprodutoras
-- Lua Prateada - ID 6 (Dieta para reprodução)
(6, '2025-04-01', 'Concentrado', 'Ração Premium para Éguas', 3.5, 'kg', 2, '07:00, 17:00', 'Ração formulada para éguas em idade reprodutiva', 7.80, 468.00, 'Nutrição Equina Brasil', 'Balanceada para suporte hormonal e reprodutivo', 'ativo', 1),
(6, '2025-04-01', 'Volumoso', 'Feno de Alfafa', 7.0, 'kg', 2, '06:30, 16:30', 'Feno de alta qualidade com bom teor de cálcio', 3.20, 672.00, 'Fazenda Verde', 'Essencial para boa condição reprodutiva', 'ativo', 1),
(6, '2025-04-01', 'Suplemento', 'Complexo Vitamínico Reprodução', 0.03, 'kg', 1, '07:15', 'Suplemento com vitaminas A, E e minerais para fertilidade', 110.00, 99.00, 'VetEquinos', 'Suporte para ciclo estral regular e fertilidade', 'ativo', 1),

-- Potros
-- Relâmpago Jr - ID 11 (Dieta para crescimento)
(11, '2025-04-01', 'Concentrado', 'Ração Crescimento Premium', 2.5, 'kg', 3, '07:00, 12:00, 17:00', 'Ração específica para potros em desenvolvimento', 8.90, 401.50, 'Nutrição Equina Brasil', 'Formulada para suporte ao crescimento ósseo e muscular', 'ativo', 1),
(11, '2025-04-01', 'Volumoso', 'Feno de Alfafa Junior', 5.0, 'kg', 3, '06:30, 11:30, 16:30', 'Feno de alta digestibilidade para potros', 3.20, 480.00, 'Fazenda Verde', 'Base da alimentação para desenvolvimento digestivo adequado', 'ativo', 1),
(11, '2025-04-01', 'Suplemento', 'Suplemento Mineral Crescimento', 0.02, 'kg', 1, '07:15', 'Suplemento com proporção ideal de cálcio e fósforo', 95.00, 57.00, 'EquiSaúde', 'Essencial para desenvolvimento ósseo correto', 'ativo', 1);

-- =============================
-- 8. INSERIR PROCEDIMENTOS VETERINÁRIOS
-- =============================

INSERT INTO procedimentos_vet (tipo, descricao, data, veterinario, crmv, medicamentos, dosagem, resultado, recomendacoes, data_proximo, horse_id, status, custo, forma_pagamento, observacoes, user_id)
VALUES
-- Trovão Negro - ID 1
('Exame Clínico Completo', 'Check-up semestral com avaliação completa', '2024-10-15', 'Dr. Carlos Santos', 'CRMV-SP 12345', 'N/A', 'N/A', 'Animal em excelente estado de saúde geral', 'Manter programa atual de treinamento e nutrição', '2025-04-15', 1, 'realizado', 450.00, 'Cartão de Crédito', 'Todos os parâmetros normais. Animal em ótima condição física.', 1),
('Vacinação', 'Vacinação anual contra Influenza Equina e Tétano', '2024-11-10', 'Dra. Marta Lima', 'CRMV-SP 23456', 'Vacina EquiFlu-Tet', '2ml, intramuscular', 'Vacinação realizada com sucesso', 'Observar reações no local da aplicação por 48 horas', '2025-11-10', 1, 'realizado', 280.00, 'Dinheiro', 'Animal sem reações adversas imediatas', 1),
('Odontologia', 'Tratamento odontológico de rotina', '2025-01-20', 'Dr. Paulo Ferreira', 'CRMV-SP 34567', 'Sedativo Xilazina', '0.5ml IV', 'Correção de pontas dentárias e ajuste de oclusão', 'Reavaliar em 12 meses ou se apresentar dificuldade mastigatória', '2026-01-20', 1, 'realizado', 350.00, 'Pix', 'Desgaste normal para a idade. Boa condição dentária geral.', 1),
('Exame Locomotor', 'Avaliação completa do aparelho locomotor', '2025-03-05', 'Dr. Carlos Santos', 'CRMV-SP 12345', 'N/A', 'N/A', 'Sistema locomotor em excelentes condições', 'Manter ferraduras terapêuticas e avaliação trimestral', '2025-06-05', 1, 'realizado', 380.00, 'Transferência Bancária', 'Avaliação pré-temporada competitiva. Sem alterações dignas de nota.', 1),

-- Vento Forte - ID 2
('Exame Clínico Completo', 'Check-up pré-competição com avaliação completa', '2024-09-20', 'Dra. Ana Costa', 'CRMV-SP 45678', 'N/A', 'N/A', 'Animal em excelente condição atlética', 'Manter programa nutricional e ajustar treinamento conforme recomendações', '2025-03-20', 2, 'realizado', 450.00, 'Cartão de Crédito', 'Todos os sistemas avaliados e aprovados para competição.', 1),
('Vacinação', 'Vacinação anual contra Encefalomielite e Raiva', '2024-10-15', 'Dr. Carlos Santos', 'CRMV-SP 12345', 'Vacina EquiGuard-ER', '2ml, intramuscular', 'Vacinação realizada com sucesso', 'Manter calendário de vacinação atualizado', '2025-10-15', 2, 'realizado', 320.00, 'Dinheiro', 'Animal sem reações adversas', 1),
('Exame de Imagem', 'Radiografia de controle dos membros anteriores', '2025-02-10', 'Dr. Ricardo Silva', 'CRMV-SP 56789', 'N/A', 'N/A', 'Imagens sem alterações patológicas', 'Manter ferraduras ortopédicas e suplementação articular', '2025-08-10', 2, 'realizado', 580.00, 'Cartão de Débito', 'Controle preventivo. Sem sinais de alterações ósseas ou articulares.', 1),

-- Lua Prateada - ID 6
('Exame Reprodutivo', 'Avaliação completa do sistema reprodutivo', '2025-03-15', 'Dra. Ana Costa', 'CRMV-SP 45678', 'N/A', 'N/A', 'Sistema reprodutivo em ótimas condições', 'Animal apto para programa de reprodução na próxima estação', '2025-08-15', 6, 'realizado', 420.00, 'Transferência Bancária', 'Égua com ciclo estral regular e útero em excelentes condições.', 1),
('Vacinação', 'Vacinação contra Herpesvirus Equino', '2025-02-10', 'Dr. Carlos Santos', 'CRMV-SP 12345', 'Vacina EquiShield EHV', '2ml, intramuscular', 'Vacinação realizada com sucesso', 'Fundamental para éguas reprodutoras', '2025-08-10', 6, 'realizado', 290.00, 'Pix', 'Vacinação pré-reprodução conforme protocolo recomendado.', 1),

-- Relâmpago Jr - ID 11
('Exame Pediátrico', 'Avaliação completa de desenvolvimento', '2025-03-25', 'Dr. Paulo Ferreira', 'CRMV-SP 34567', 'N/A', 'N/A', 'Excelente desenvolvimento para a idade', 'Manter suplementação mineral e avaliar a cada 3 meses', '2025-06-25', 11, 'realizado', 350.00, 'Cartão de Crédito', 'Desenvolvimento dentro do esperado para a idade e raça.', 1),
('Vermifugação', 'Vermifugação de rotina', '2025-04-01', 'Dra. Marta Lima', 'CRMV-SP 23456', 'Ivermectina Pasta', '1 seringa de 6.08g', 'Procedimento realizado com sucesso', 'Repetir a cada 3 meses no primeiro ano de vida', '2025-07-01', 11, 'realizado', 120.00, 'Dinheiro', 'Parte do programa de controle parasitário para potros.', 1);

-- =============================
-- 9. INSERIR REPRODUÇÃO
-- =============================

INSERT INTO reproducao (horse_id, padreiro_id, data_cobertura, tipo_cobertura, estado, data_prevista_inseminacao, data_prevista_embriao, data_prevista_parto, data_diagnostico_gestacao, resultado_diagnostico, observacoes, user_id)
VALUES
-- Histórico reprodutivo das éguas
-- Lua Prateada - ID 6
(6, 1, '2023-10-15', 'Inseminação Artificial', 'Concluída', '2023-10-17', '2023-11-01', '2024-09-20', '2023-11-20', 'Positivo', 'Gestação confirmada aos 35 dias. Desenvolvimento fetal normal. Resultou no nascimento de Relâmpago Jr (ID 11).', 1),
(6, 1, '2025-04-20', 'Inseminação Artificial', 'Programada', '2025-04-23', '2025-05-08', '2026-03-26', NULL, NULL, 'Égua em excelente condição reprodutiva. Ciclo regular, programada para inseminação no próximo cio.', 1),

-- Brisa Suave - ID 7
(7, 2, '2023-11-20', 'Inseminação Artificial', 'Concluída', '2023-11-22', '2023-12-07', '2024-10-25', '2023-12-25', 'Positivo', 'Gestação confirmada aos 35 dias. Desenvolvimento fetal normal. Resultou no nascimento de Ventania (ID 12).', 1),
(7, 2, '2025-05-10', 'Inseminação Artificial', 'Programada', '2025-05-12', '2025-05-27', '2026-04-15', NULL, NULL, 'Égua com ótimo histórico reprodutivo. Programada para reprodução após período adequado do último parto.', 1),

-- Pérola Negra - ID 8
(8, 3, '2024-04-15', 'Inseminação Artificial', 'Concluída', '2024-04-17', '2024-05-02', '2025-03-20', '2024-05-20', 'Positivo', 'Gestação confirmada aos 35 dias. Desenvolvimento fetal normal. Égua em ótimo estado gestacional.', 1),

-- Aurora Dourada - ID 9
(9, 4, '2024-05-10', 'Inseminação Artificial', 'Concluída', '2024-05-12', '2024-05-27', '2025-04-15', '2024-06-15', 'Positivo', 'Gestação confirmada aos 36 dias. Desenvolvimento fetal normal. Primeiro terço da gestação sem complicações.', 1),

-- Tormenta - ID 10
(10, 5, '2024-03-20', 'Monta Natural', 'Concluída', NULL, NULL, '2025-02-23', '2024-04-25', 'Positivo', 'Gestação confirmada aos 36 dias. Desenvolvimento fetal normal. Adaptação da égua ao programa nutricional gestacional.', 1),
(10, 5, '2023-03-10', 'Monta Natural', 'Concluída', NULL, NULL, '2024-02-15', '2023-04-15', 'Positivo', 'Gestação completa sem complicações. Resultou no nascimento de Tempestade (ID 15).', 1);

-- =============================
-- 10. INSERIR EVENTOS
-- =============================

INSERT INTO eventos (titulo, descricao, data, hora_inicio, hora_fim, tipo, status, manejo_id, horse_id, responsavel, telefone_responsavel, local, observacoes, recorrente, padrao_recorrencia, user_id, prioridade)
VALUES
-- Eventos de treinamento
('Treinamento de Marcha - Trovão Negro', 'Sessão especializada com foco em aperfeiçoamento da marcha picada', '2025-04-15', '08:00', '10:00', 'treinamento', 'pendente', NULL, 1, 'João Treinador', '(11) 98765-4321', 'Pista coberta', 'Focar na regularidade e dissociação perfeita dos membros', false, NULL, 1, 'alta'),
('Treino de Velocidade - Vento Forte', 'Treinamento para provas de três tambores', '2025-04-16', '09:00', '11:00', 'treinamento', 'pendente', NULL, 2, 'Carlos Corrêa', '(11) 97654-3210', 'Pista de areia', 'Trabalhar curvas e aceleração na saída dos tambores', false, NULL, 1, 'alta'),
('Sessão de Adestramento - Luar de Prata', 'Treino para aperfeiçoamento de movimentos clássicos', '2025-04-17', '14:00', '16:00', 'treinamento', 'pendente', NULL, 4, 'André Cavalcanti', '(11) 96543-2109', 'Picadeiro central', 'Focar em piruetas e passagens laterais', false, NULL, 1, 'média'),
('Treino de Resistência - Thor', 'Condicionamento físico para provas de enduro', '2025-04-18', '07:00', '10:00', 'treinamento', 'pendente', NULL, 5, 'Marcelo Enduro', '(11) 95432-1098', 'Trilha externa', 'Percurso de 15km com variações de terreno', false, NULL, 1, 'média'),

-- Eventos veterinários
('Consulta Veterinária - Lua Prateada', 'Acompanhamento pré-reprodução', '2025-04-20', '14:00', '15:00', 'veterinário', 'pendente', NULL, 6, 'Dra. Ana Costa', '(11) 94321-0987', 'Sala de exames', 'Avaliação reprodutiva completa para programação de inseminação', false, NULL, 1, 'alta'),
('Exame Locomotor - Ventania', 'Avaliação dos membros anteriores', '2025-04-21', '10:00', '11:00', 'veterinário', 'pendente', NULL, 12, 'Dr. Carlos Santos', '(11) 93210-9876', 'Centro veterinário', 'Acompanhamento do desenvolvimento locomotor do potro', false, NULL, 1, 'média'),
('Vacinação - Múltiplos Animais', 'Vacinação semestral do plantel', '2025-04-25', '08:00', '12:00', 'veterinário', 'pendente', NULL, NULL, 'Dra. Marta Lima', '(11) 92109-8765', 'Baias', 'Vacinas contra Influenza Equina e reforço de Tétano', false, NULL, 1, 'alta'),

-- Eventos de manutenção
('Casqueamento Geral', 'Manutenção de cascos de todos os animais', '2025-04-30', '08:00', '17:00', 'manutenção', 'pendente', NULL, NULL, 'José Ferrador', '(11) 91098-7654', 'Área de trato', 'Casqueamento regular com avaliação individual', true, 'mensal', 1, 'média'),
('Manutenção das Instalações', 'Revisão e reparo de cercas e cochos', '2025-05-02', '08:00', '16:00', 'manutenção', 'pendente', NULL, NULL, 'Equipe de manutenção', '(11) 90987-6543', 'Fazenda', 'Verificar especialmente a área dos piquetes de potros', false, NULL, 1, 'baixa'),

-- Eventos de competição
('Campeonato Regional de Marcha', 'Participação na categoria Elite', '2025-05-10', '08:00', '18:00', 'competição', 'pendente', NULL, 1, 'Equipe de competição', '(11) 89876-5432', 'Parque de Exposições', 'Levar documentação completa e equipamentos', false, NULL, 1, 'alta'),
('Prova Três Tambores', 'Etapa regional do campeonato', '2025-05-15', '09:00', '17:00', 'competição', 'pendente', NULL, 2, 'Equipe de competição', '(11) 89876-5432', 'Clube Hípico', 'Confirmação até 3 dias antes do evento', false, NULL, 1, 'alta'),
('Grande Prêmio Velocidade', 'Corrida de 1800m - Categoria Aberta', '2025-05-20', '14:00', '17:00', 'competição', 'pendente', NULL, 3, 'Equipe de corrida', '(11) 89876-5432', 'Jockey Club', 'Preparo especial na semana anterior', false, NULL, 1, 'alta'),

-- Eventos reprodutivos
('Inseminação - Lua Prateada', 'Procedimento de inseminação artificial programada', '2025-04-23', '10:00', '11:00', 'reprodução', 'pendente', NULL, 6, 'Dra. Ana Costa', '(11) 94321-0987', 'Laboratório de reprodução', 'Utilizar sêmen fresco do garanhão Trovão Negro', false, NULL, 1, 'alta'),
('Inseminação - Brisa Suave', 'Procedimento de inseminação artificial programada', '2025-05-12', '10:00', '11:00', 'reprodução', 'pendente', NULL, 7, 'Dra. Ana Costa', '(11) 94321-0987', 'Laboratório de reprodução', 'Utilizar sêmen fresco do garanhão Vento Forte', false, NULL, 1, 'alta'),

-- Eventos gerais
('Dia de Campo - Clientes', 'Apresentação do plantel para clientes potenciais', '2025-05-25', '09:00', '16:00', 'evento', 'pendente', NULL, NULL, 'Equipe comercial', '(11) 87654-3210', 'Fazenda', 'Preparar apresentação dos garanhões e potros em desenvolvimento', false, NULL, 1, 'média'),
('Curso de Manejo Equino', 'Treinamento para a equipe de tratadores', '2025-06-01', '08:00', '17:00', 'treinamento', 'pendente', NULL, NULL, 'Dr. Paulo Ferreira', '(11) 87654-3210', 'Sala de treinamento', 'Curso focado em bem-estar animal e técnicas modernas de manejo', false, NULL, 1, 'média');

-- =============================
-- 11. INSERIR MANEJOS
-- =============================

INSERT INTO manejos (tipo, data, detalhes, responsavel, horse_id, status, observacoes, user_id)
VALUES
-- Manejos para Trovão Negro - ID 1
('Vacinação', '2024-10-15', 'Vacinação anual contra Influenza Equina e Tétano', 'Dra. Marta Lima', 1, 'realizado', 'Vacina aplicada sem reações adversas', 1),
('Vermifugação', '2024-11-20', 'Vermifugação com Ivermectina', 'Tratador João', 1, 'realizado', 'Administrado conforme recomendação veterinária', 1),
('Casqueamento', '2024-12-10', 'Manutenção regular de cascos', 'José Ferrador', 1, 'realizado', 'Cascos em excelente estado, sem necessidade de correções', 1),
('Odontologia', '2025-01-20', 'Tratamento odontológico de rotina', 'Dr. Paulo Ferreira', 1, 'realizado', 'Correção de pontas dentárias e ajuste de oclusão', 1),
('Ferrageamento', '2025-02-15', 'Troca de ferraduras especiais para competição', 'José Ferrador', 1, 'realizado', 'Ferrageamento com ferraduras leves de alumínio', 1),
('Exame Clínico', '2025-03-05', 'Avaliação completa pré-temporada competitiva', 'Dr. Carlos Santos', 1, 'realizado', 'Animal aprovado para competições', 1),
('Vacinação', '2025-04-15', 'Vacinação semestral contra Influenza', 'Dra. Marta Lima', 1, 'pendente', 'Programada como parte do protocolo sanitário', 1),

-- Manejos para Vento Forte - ID 2
('Vacinação', '2024-09-20', 'Vacinação contra Encefalomielite e Raiva', 'Dr. Carlos Santos', 2, 'realizado', 'Vacinas aplicadas sem reações adversas', 1),
('Casqueamento', '2024-10-25', 'Manutenção regular de cascos', 'José Ferrador', 2, 'realizado', 'Cascos em bom estado, pequeno ajuste no posterior direito', 1),
('Vermifugação', '2024-12-01', 'Vermifugação com Moxidectina', 'Tratador Pedro', 2, 'realizado', 'Administrado conforme protocolo de controle parasitário', 1),
('Ferrageamento', '2025-01-10', 'Troca de ferraduras terapêuticas', 'José Ferrador', 2, 'realizado', 'Ferraduras especiais para melhorar tração nas curvas', 1),
('Fisioterapia', '2025-02-20', 'Sessão para recuperação muscular', 'Dra. Ana Fisio', 2, 'realizado', 'Trabalho de recuperação após período intenso de treinos', 1),
('Exame de Imagem', '2025-03-10', 'Radiografia de controle dos membros anteriores', 'Dr. Ricardo Silva', 2, 'realizado', 'Imagens sem alterações, liberado para competições', 1),

-- Manejos para Lua Prateada - ID 6
('Vacinação', '2024-10-10', 'Vacinação contra Herpesvirus Equino', 'Dr. Carlos Santos', 6, 'realizado', 'Vacinação pré-reprodução conforme protocolo', 1),
('Vermifugação', '2024-11-15', 'Vermifugação com Ivermectina+Praziquantel', 'Tratador João', 6, 'realizado', 'Administrado como preparação para estação reprodutiva', 1),
('Exame Reprodutivo', '2025-01-20', 'Avaliação do sistema reprodutivo', 'Dra. Ana Costa', 6, 'realizado', 'Sistema reprodutivo em ótimas condições', 1),
('Casqueamento', '2025-02-15', 'Manutenção regular de cascos', 'José Ferrador', 6, 'realizado', 'Cascos em excelente estado', 1),
('Exame Reprodutivo', '2025-03-15', 'Avaliação pré-inseminação', 'Dra. Ana Costa', 6, 'realizado', 'Égua apta para protocolo de inseminação', 1),
('Inseminação', '2025-04-23', 'Inseminação artificial programada', 'Dra. Ana Costa', 6, 'pendente', 'Programada para o próximo cio', 1),

-- Manejos para Relâmpago Jr (potro) - ID 11
('Vacinação', '2024-10-25', 'Primeira dose da vacina contra Influenza Equina', 'Dra. Marta Lima', 11, 'realizado', 'Primeira vacinação realizada sem reações', 1),
('Vermifugação', '2024-12-25', 'Vermifugação com pasta específica para potros', 'Tratador Pedro', 11, 'realizado', 'Administrado conforme programa para potros', 1),
('Casqueamento', '2025-01-25', 'Primeiro casqueamento de manutenção', 'José Ferrador', 11, 'realizado', 'Casqueamento preventivo com bons resultados', 1),
('Vacinação', '2025-01-25', 'Segunda dose da vacina contra Influenza Equina', 'Dra. Marta Lima', 11, 'realizado', 'Reforço aplicado conforme calendário', 1),
('Exame Pediátrico', '2025-03-25', 'Avaliação completa de desenvolvimento', 'Dr. Paulo Ferreira', 11, 'realizado', 'Desenvolvimento dentro do esperado para idade e raça', 1),
('Vermifugação', '2025-04-01', 'Vermifugação trimestral', 'Tratador João', 11, 'realizado', 'Parte do programa de controle parasitário para potros', 1);