{"timestamp": "2025-05-14T15:03:38.503Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 389042176, "heapTotal": 103157760, "heapUsed": 62292464, "external": 6815481, "arrayBuffers": 60485}, "uptime": 2.024009921, "cpuUsage": {"user": 2604175, "system": 296931}, "resourceUsage": {"userCPUTime": 2604235, "systemCPUTime": 296931, "maxRSS": 379924, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101309, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 96, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6595, "involuntaryContextSwitches": 5693}}