{"timestamp": "2025-05-15T21:29:20.261Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403189760, "heapTotal": 113549312, "heapUsed": 72520968, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.74084558, "cpuUsage": {"user": 2708698, "system": 348632}, "resourceUsage": {"userCPUTime": 2708749, "systemCPUTime": 348638, "maxRSS": 393740, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103177, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8330, "involuntaryContextSwitches": 3299}}