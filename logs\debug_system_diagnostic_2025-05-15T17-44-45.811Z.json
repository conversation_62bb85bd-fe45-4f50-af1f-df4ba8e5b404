{"timestamp": "2025-05-15T17:44:45.811Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 401666048, "heapTotal": 112762880, "heapUsed": 72459688, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.063573011, "cpuUsage": {"user": 2963650, "system": 377746}, "resourceUsage": {"userCPUTime": 2963707, "systemCPUTime": 377746, "maxRSS": 392252, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104312, "majorPageFault": 0, "swappedOut": 0, "fsRead": 4024, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8359, "involuntaryContextSwitches": 2832}}