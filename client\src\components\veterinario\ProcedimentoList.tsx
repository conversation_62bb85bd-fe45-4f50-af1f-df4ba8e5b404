import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Eye, Edit, Calendar, Trash2 } from 'lucide-react';
import { ProcedimentoVet } from '@shared/schema';
import { StatusBadge } from './StatusBadge';

interface ProcedimentoListProps {
  procedimentos: ProcedimentoVet[];
  getCavaloName: (id: number) => string;
  onView: (id: number) => void;
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onSchedule?: (id: number) => void;
  formatarData: (data: string) => string;
  getStatus?: (procedimento: ProcedimentoVet) => string;
  statusType?: 'vacinacao' | 'vermifugacao' | 'exame' | 'procedimento';
  truncarTexto: (texto: string, tamanhoMaximo: number) => string;
  colunas?: string[];
}

/**
 * Componente para listar procedimentos veterinários em formato de tabela
 * 
 * Este componente exibe uma tabela de procedimentos veterinários com
 * colunas configuráveis e ações para visualizar, editar, agendar e
 * excluir procedimentos.
 */
export const ProcedimentoList: React.FC<ProcedimentoListProps> = ({
  procedimentos,
  getCavaloName,
  onView,
  onEdit,
  onDelete,
  onSchedule,
  formatarData,
  getStatus,
  statusType = 'procedimento',
  truncarTexto,
  colunas = ['data', 'cavalo', 'tipo', 'descricao', 'veterinario', 'proximaData', 'status', 'acoes']
}) => {
  return (
    <div className="border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            {colunas.includes('data') && (
              <TableHead>Data</TableHead>
            )}
            {colunas.includes('cavalo') && (
              <TableHead>Animal</TableHead>
            )}
            {colunas.includes('tipo') && (
              <TableHead>Tipo</TableHead>
            )}
            {colunas.includes('descricao') && (
              <TableHead>Descrição</TableHead>
            )}
            {colunas.includes('medicamento') && (
              <TableHead>Medicamento</TableHead>
            )}
            {colunas.includes('dosagem') && (
              <TableHead>Dosagem</TableHead>
            )}
            {colunas.includes('veterinario') && (
              <TableHead>Veterinário</TableHead>
            )}
            {colunas.includes('resultado') && (
              <TableHead>Resultado</TableHead>
            )}
            {colunas.includes('proximaData') && (
              <TableHead>Próxima Data</TableHead>
            )}
            {colunas.includes('status') && (
              <TableHead>Status</TableHead>
            )}
            {colunas.includes('custo') && (
              <TableHead>Custo</TableHead>
            )}
            {colunas.includes('acoes') && (
              <TableHead className="text-right">Ações</TableHead>
            )}
          </TableRow>
        </TableHeader>
        <TableBody>
          {procedimentos.map((procedimento) => {
            const status = getStatus ? getStatus(procedimento) : (procedimento.status || 'realizado');
            const highligthBackground = status === 'vencida' || status === 'atrasado' ? 'bg-red-50' : '';
            
            return (
              <TableRow key={procedimento.id} className={highligthBackground}>
                {colunas.includes('data') && (
                  <TableCell>{formatarData(procedimento.data)}</TableCell>
                )}
                {colunas.includes('cavalo') && (
                  <TableCell>{getCavaloName(procedimento.horseId)}</TableCell>
                )}
                {colunas.includes('tipo') && (
                  <TableCell>{procedimento.tipo}</TableCell>
                )}
                {colunas.includes('descricao') && (
                  <TableCell>{truncarTexto(procedimento.descricao, 50)}</TableCell>
                )}
                {colunas.includes('medicamento') && (
                  <TableCell>{procedimento.medicamentos || 'N/A'}</TableCell>
                )}
                {colunas.includes('dosagem') && (
                  <TableCell>{procedimento.dosagem || 'N/A'}</TableCell>
                )}
                {colunas.includes('veterinario') && (
                  <TableCell>{procedimento.veterinario || 'N/A'}</TableCell>
                )}
                {colunas.includes('resultado') && (
                  <TableCell>{truncarTexto(procedimento.resultado || 'N/A', 30)}</TableCell>
                )}
                {colunas.includes('proximaData') && (
                  <TableCell>
                    {procedimento.dataProximoProcedimento 
                      ? formatarData(procedimento.dataProximoProcedimento) 
                      : 'Não especificada'}
                  </TableCell>
                )}
                {colunas.includes('status') && (
                  <TableCell>
                    <StatusBadge status={status} type={statusType} />
                  </TableCell>
                )}
                {colunas.includes('custo') && (
                  <TableCell>
                    {procedimento.custo ? `R$ ${procedimento.custo.toFixed(2)}` : 'N/A'}
                  </TableCell>
                )}
                {colunas.includes('acoes') && (
                  <TableCell className="text-right">
                    <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => onView(procedimento.id)}>
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => onEdit(procedimento.id)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    {onSchedule && (
                      <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => onSchedule(procedimento.id)}>
                        <Calendar className="h-4 w-4" />
                      </Button>
                    )}
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8" 
                      onClick={() => {
                        if (window.confirm("Tem certeza que deseja excluir este procedimento?")) {
                          onDelete(procedimento.id);
                        }
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};