{"timestamp": "2025-05-15T01:29:12.023Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 431325184, "heapTotal": 296845312, "heapUsed": 220268816, "external": 13200037, "arrayBuffers": 1455072}, "uptime": 35.537536242, "cpuUsage": {"user": 16512155, "system": 1021601}, "resourceUsage": {"userCPUTime": 16512163, "systemCPUTime": 1021601, "maxRSS": 849032, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 245554, "majorPageFault": 0, "swappedOut": 0, "fsRead": 40, "fsWrite": 464, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 18416, "involuntaryContextSwitches": 33988}}