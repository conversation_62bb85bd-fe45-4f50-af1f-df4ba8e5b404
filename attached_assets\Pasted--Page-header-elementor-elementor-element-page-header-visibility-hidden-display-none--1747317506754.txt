/* Page header */
.elementor .elementor-element .page-header{
	visibility:hidden;
	display:none;
}

#hero-slider .splide__track{
	border-bottom-right-radius:30px;
	border-bottom-left-radius:30px;
	border-top-right-radius:30px;
	border-top-left-radius:30px;
}

.elementor .header .row{
	display: no;
}

/* Image */
.navbar .d-flex img{
	visibility:hidden;
}

.navbar .d-flex a{
	visibility:hidden;
	padding-top:5px;
	padding-bottom:5px;
}

/* Cabealho inicial com fundo transparente */
.header nav{
	background-color:rgba(255,255,255,0);
	transform:translatex(0px) translatey(0px);
	box-shadow:none;
	padding-bottom:31px;
}

.navbar-nav .menu-item a{
	border-style:none;
}

/* Column 3/12 */
.navbar .d-flex{
	top: 2px;
	z-index: 91;
	visibility: visible;
	background-color: transparent;
	background-image: url("https://tecnomor.com.br/novo2025/wp-content/uploads/2024/12/logo-1.png");
	background-size: contain;
	background-position: center;
	background-repeat: no-repeat;
	transform: translateX(0px) translateY(0px);
	min-width: 111px;
	height: 42px;
	min-height: 50px;
	transition: background-image 0.3s ease, background-color 0.3s ease;
}

.navbar.scrolled .d-flex{
	background-image: url("https://tecnomor.com.br/novo2025/wp-content/themes/tecnomor/assets/img/logo.webp");
	background-color: white;
}

/* Column 3/12 */
.elementor .elementor-element .e-con-inner .elementor-widget-shortcode .elementor-widget-container .elementor-shortcode .header .row .navbar .container .d-flex{
	width:106px !important;
}

/* Item */
.e-lazyloaded .elementor-nav-menu--main .menu-item:nth-child(6) .elementor-item{
	color:#ffffff;
	border-top-left-radius:18px;
	border-top-right-radius:18px;
	border-bottom-left-radius:18px;
	border-bottom-right-radius:18px;
	padding-left:8px;
	padding-right:8px;
	padding-top:2px;
	padding-bottom:2px;
	font-size:12px;
}

/* Item */
.elementor-location-header .e-lazyloaded .e-con-inner .elementor-nav-menu--dropdown-tablet .elementor-widget-container .elementor-nav-menu--main .elementor-nav-menu .menu-item .elementor-item{
	line-height:1em !important;
}

.elementor-location-header .elementor-widget-image .elementor-widget-container{
	padding-top:3px !important;
	margin-top:-1px !important;
}

.elementor .elementor-element .elementor-widget-media-carousel{
	top:-16px;
}

/* Paragraph */
.elementor-posts--thumbnail-left .elementor-grid-item p{
	text-align:justify;
	padding-right:18px;
}

/* Link */
.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__text .elementor-post__read-more-wrapper a{
	text-align:right;
	background-color:#1e8549;
	background-clip:padding-box;
	padding-left:8px;
	padding-right:8px;
	padding-top:5px;
	padding-bottom:24px;
	color:#ffffff;
	border-top-left-radius:10px;
	border-top-right-radius:10px;
	border-bottom-left-radius:10px;
	border-bottom-right-radius:10px;
	display:inline-block;
	height:24px;
	position:relative;
	top:-3px;
}

/* Division */
.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__read-more-wrapper{
	text-align:right;
	padding-right:2px;
	padding-top:0px;
	display:block;
	float:right;
	clear:none;
	margin-bottom:34px;
	margin-right:55px;
}

/* Link */
.elementor-posts--thumbnail-left .elementor-post__title a{
	font-size:15px;
	font-weight:600;
}

/* Heading */
.e-con-inner .elementor-element .elementor-widget-heading .elementor-widget-container .elementor-heading-title{
	font-size:60px;
}

/* Link */
.elementkit-tab-nav .elementkit-nav-item a{
	text-align:center !important;
}

.elementkit-tab-wraper .active h2{
	font-size:35px !important;
	color:#154a30 !important;
	left:0px !important;
}

/* Paragraph */
.elementor .elementor-element .e-con-inner .elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item .elementor-post__text .elementor-post__excerpt p{
	width:92% !important;
}

/* Division */
.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__excerpt{
	margin-bottom:9px !important;
}

.active .animated .widgetarea_warper_editable .elementor-widget-container .elementor .elementor-element .e-con-inner .elementor-element .elementor-widget-heading .elementor-widget-container h2{
	width:144% !important;
}

/* Span Tag */
.elementor-nav-menu--main .menu-item span{
	position:relative;
	left:-7px;
	width:21px;
	height:0px;
}

.elementor-nav-menu--main span .icon{
	font-size:10px;
	font-weight:600;
}

/* Paragraph */
#txt p{
	line-height:1.4em;
}

/* Element */
.elementor-location-header > .elementor-element{
	background-color:transparent !important;
}

/* Swiper slide inner */
.elementor-slides .swiper-slide .swiper-slide-inner{
	border-top-left-radius:0px;
	border-top-right-radius:0px;
	border-bottom-left-radius:0px;
	border-bottom-right-radius:0px;
}

.elementor-widget-slides .elementor-slides .swiper-slide{
	border-top-left-radius:20px;
	border-top-right-radius:20px;
	border-bottom-left-radius:20px;
	border-bottom-right-radius:20px;
}

.elementor-widget-elementskit-tab .ekit-wid-con ul{
	text-align:left;
}

.elementkit-tab-wraper .elementkit-tab-nav li{
	margin-right:42px !important;
}

/* Span Tag */
.elementkit-tab-nav .active span{
	font-weight:700;
}

/* Span Tag */
.elementkit-tab-nav .elementkit-nav-item span{
	font-weight:700;
}

/* Division */
.elementor .elementor-element .e-con-inner > .elementor-widget-text-editor .elementor-widget-container{
	transform:translatex(0px) translatey(0px);
}

.elementor .elementor-element hr{
	padding-top:10px;
	padding-bottom:5px;
}

/* Paragraph */
.elementkit-tab-wraper .active p{
	line-height:1.3em;
	font-size:20px;
}

/* Division */
#txt .elementor-widget-container{
	transform:translatex(0px) translatey(0px);
}

.elementkit-tab-wraper .active .elementor-widget-text-editor .elementor-widget-container{
	font-size:20px;
	line-height:1.3em;
	transform:translatex(0px) translatey(0px);
}

/* Element */
.elementor > .elementor-element{
	display:inline-block;
}

/* Division */
.elementor .swiper-wrapper .swiper-slide:nth-child(3) .elementor-testimonial__footer{
	margin-top:40px !important;
}

#menu-lang{
	top:px;
	width:100% !important;
}

/* Paragraph */
.slider .elementor-element p{
	line-height:0.95em;
}

.elementor > .elementor-element > .e-con-inner .elementor-widget-text-editor .elementor-widget-container p{
	font-size:18px;
	line-height:29px;
}

/* Post Division */
.elementor-posts--thumbnail-left .elementor-grid-item{
	transform:translatex(0px) translatey(0px);
}

/* Division */
.elementor-main-swiper .swiper-slide .elementor-testimonial{
	float:none;
}

/* Division */
.elementor-main-swiper .swiper-slide:nth-child(1) .elementor-testimonial__text{
	position:relative;
	top:67px;
}

/* Division */
.elementor-main-swiper .swiper-slide:nth-child(2) .elementor-testimonial__footer{
	position:relative;
	top:-95px;
}

/* Division */
.elementor-main-swiper .swiper-wrapper .swiper-slide:nth-child(2) .elementor-testimonial .elementor-testimonial__footer{
	top:-95px !important;
}

/* Division */
.elementor-main-swiper .swiper-slide:nth-child(2) .elementor-testimonial__text{
	position:relative;
	top:66px;
}

/* Division */
.elementor-main-swiper .swiper-slide:nth-child(3) .elementor-testimonial__text{
	position:relative;
	top:66px;
}

/* Division */
.elementor-main-swiper .swiper-slide:nth-child(3) .elementor-testimonial__footer{
	position:relative;
	top:-156px !important;
}

/* Heading */
.elementor .elementor-element .e-con-inner .elementor-element .elementor-widget-heading .elementor-widget-container .elementor-heading-title{
	width:89%;
}

/* Division */
.elementor-main-swiper .swiper-wrapper .swiper-slide .elementor-testimonial .elementor-testimonial__footer{
	top:-145px;
}

/* Elementor */
.elementor{
	transform:translatex(0px) translatey(0px) !important;
	z-index:1;
	position:relative;
}

/* Con inner */
.slider .elementor-element .e-con-inner{
	top:-12px;
	z-index:1;
	position:relative;
	left:-33px;
	right:-103px;
	padding-left:10px;
}

/* Menu conteiner */
#menu-conteiner{
	margin-top:0px;
	z-index:23;
	position:relative;
	bottom:-45px;
	top:-58px;
}

/* Heading */
.e-con-inner > .elementor-widget-heading .elementor-heading-title{
	padding-top:0px;
}

/* Element */
.elementor .slider > .elementor-element{
	margin-top:-97px;
	position:fixed;
	top:12px;
	z-index:1;
	padding-left:30px;
}

/* Slider */
.elementor .slider{
	position:static;
	height:544px;
}

/* Division */
.elementor-location-header{
	z-index:20;
	top:45px;
}

/* Con inner */
.elementor > .elementor-element > .e-con-inner{
	padding-top:5px;
}

/* Thumbnail Image */
#menu-conteiner .elementor-widget-image img{
	position:relative;
	top:32px;
}

/* 1199px and smaller screen sizes */
@media (max-width:1199px){

	/* Swiper wrapper */
	.elementor-swiper .elementor-main-swiper .swiper-wrapper{
		max-height:505px;
	}
	
	.elementor .swiper-wrapper .swiper-slide a{
		height:566px !important;
	}
	
	/* Link */
	.elementor .elementor-element .elementor-widget-media-carousel .elementor-widget-container .elementor-swiper .elementor-main-swiper .swiper-wrapper .swiper-slide a{
		display:inline-block !important;
	}
	
	/* Link */
	.elementor-widget-container .elementor-swiper .elementor-main-swiper .swiper-wrapper .swiper-slide a{
		display:inline-block !important;
	}
	
}

/* 640px and smaller screen sizes */
@media (max-width:640px){

	/* Span Tag */
	p > span > span{
		font-size:20px !important;
	}
	
	/* Span Tag */
	.elementor p strong span{
		font-size:20px !important;
	}
	
	.elementor > .elementor-element > .e-con-inner > .elementor-widget-text-editor p{
		line-height:20px;
		font-size:15px;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-slide .elementor-testimonial__content{
		position:relative;
		top:-24px;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-wrapper .swiper-slide .elementor-testimonial .elementor-testimonial__footer{
		top:-122px !important;
	}
	
	/* Post Division */
	.elementor-posts--thumbnail-left .elementor-grid-item{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Paragraph */
	.e-con-inner .elementor-element .elementor-element .qq .elementor-widget-container p{
		font-size:14px;
		line-height:1.6em;
	}
	
	/* Division */
	.elementor .e-n-accordion-item:nth-child(6) .elementor-button-wrapper{
		position:relative;
		top:-8px;
		bottom:-37px;
		margin-bottom:31px;
	}
	
	/* Paragraph */
	.elementor > .elementor-element > .e-con-inner > .elementor-element > .elementor-widget-heading p.elementor-heading-title{
		font-size:25px;
		line-height:1em;
	}
	
}

/* 575px and smaller screen sizes */
@media (max-width:575px){

	/* Paragraph */
	.elementor > .elementor-element > .e-con-inner > .elementor-element > .elementor-widget-heading p.elementor-heading-title{
		padding-top:27px;
		padding-bottom:41px;
	}
	
	/* Paragraph */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner .elementor-element .ttt .elementor-widget-container .elementor-heading-title{
		font-size:20px;
		width:284px;
	}
	
	/* Element */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner > .elementor-element{
		padding-bottom:75px;
	}
	
	/* Division */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner .elementor-element .qq .elementor-widget-container{
		font-size:14px;
	}
	
	/* Paragraph */
	#de .ttt p{
		font-size:20px;
		width:239px;
	}
	
	/* Division */
	#de .qqq .elementor-widget-container{
		font-size:14px;
	}
	
	/* Element */
	#de > .elementor-element{
		padding-left:24px;
	}
	
	/* Element */
	.e-n-accordion .e-n-accordion-item > .elementor-element{
		padding-left:23px !important;
	}
	
	/* Element */
	#de .elementor-element .e-con-inner > .elementor-element > .elementor-element{
		padding-bottom:46px;
	}
	
	/* Division */
	.e-con-inner .elementor-element .elementor-element .qq .elementor-widget-container{
		font-size:20px;
	}
	
	/* Paragraph */
	.elementor-element .e-con-inner .elementor-element .elementor-element .ttt .elementor-widget-container .elementor-heading-title{
		font-size:20px !important;
	}
	
}

/* 479px and smaller screen sizes */
@media (max-width:479px){

	/* Span Tag */
	.elementor-widget-container p span span span{
		font-size:20px !important;
	}
	
	/* Paragraph */
	.e-lazyloaded > .e-con-inner > .elementor-widget-text-editor p{
		font-size:15px !important;
		line-height:17px !important;
	}
	
	/* Swiper slide inner */
	.elementor-image-carousel a .swiper-slide-inner{
		position:relative;
		top:30px;
	}
	
	/* Division */
	.elementor .e-lazyloaded .swiper-slide:nth-child(1) .elementor-testimonial__footer{
		top:-147px !important;
	}
	
	#rodape .elementor-element img{
		top:-36px;
	}
	
	/* Paragraph */
	.elementor-posts--thumbnail-left .elementor-grid-item p{
		height:136px;
	}
	
	/* Division */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__text{
		padding-left:0px !important;
		padding-right:37px !important;
		transform:translatex(0px) translatey(0px);
	}
	
	/* Link */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__thumbnail__link{
		margin-right:25px !important;
		transform:translatex(0px) translatey(0px);
	}
	
	/* Division */
	.elementor .e-lazyloaded > .e-con-inner > .elementor-element > .elementor-widget-heading{
		height:128px;
		margin-top:50px;
	}
	
	/* Element */
	.elementor .e-lazyloaded > .e-con-inner > .elementor-element{
		transform:translatex(0px) translatey(0px) !important;
	}
	
	/* Con inner */
	.elementor .e-lazyloaded:nth-child(7) .e-con-inner{
		height:auto !important;
		max-width:none;
	}
	
	/* Paragraph */
	.e-lazyloaded > .e-con-inner > .elementor-element > .elementor-widget-heading p{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Body */
	body{
		height:1817px;
	}
	
	/* Elementor */
	.elementor{
		height:auto !important;
	}
	
	/* Division */
	.elementor .elementor-element .elementor-element .elementor-widget-heading{
		line-height:15px;
	}
	
	/* Paragraph */
	.elementor .elementor-element p.elementor-heading-title{
		height:33px !important;
	}
	
	/* Division */
	.elementor > .elementor-element > .e-con-inner > .elementor-element > .elementor-widget-heading .elementor-widget-container{
		transform:translatex(0px) translatey(0px) !important;
		height:227px !important;
	}
	
	/* Paragraph */
	.elementor > .elementor-element > .e-con-inner > .elementor-element > .elementor-widget-heading p.elementor-heading-title{
		height:76px;
		font-size:25px;
		line-height:20px;
	}
	
	/* Division */
	.elementor-element .e-con-inner .elementor-element .elementor-element .ttt{
		height:30px !important;
	}
	
	/* Con inner */
	.elementor .elementor-element .e-con-inner{
		transform:translatex(0px) translatey(0px) !important;
	}
	
	/* Division */
	.elementor > .elementor-element > .e-con-inner > .elementor-element > .elementor-widget-heading{
		height:113px !important;
	}
	
	/* Element */
	.elementor > .elementor-element{
		top:-62px !important;
	}
	
	/* Heading */
	.e-con-inner > .elementor-widget-heading .elementor-heading-title{
		position:relative;
		top:8px;
	}
	
	/* Division */
	.elementor-widget-testimonial-carousel .swiper-wrapper .swiper-slide .elementor-testimonial__text{
		top:77px !important;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-slide .elementor-testimonial{
		position:relative;
		top:21px;
	}
	
	/* Link */
	.elementor .elementor-element .elementor-post__title a{
		font-size:14px !important;
	}
	
	/* Post Division */
	.elementor-posts--thumbnail-left .elementor-grid-item{
		border-top-right-radius:0px !important;
		border-top-left-radius:0px !important;
		border-bottom-left-radius:0px !important;
		border-bottom-right-radius:0px !important;
		transform:translatex(0px) translatey(0px);
	}
	
	/* Span Tag */
	.elementor-posts--thumbnail-left .elementor-grid-item span{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Span Tag */
	.elementor .elementor-element .e-con-inner .elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item .elementor-post__text span{
		width:93% !important;
	}
	
	/* Element */
	#rodape .e-con-inner > .elementor-element > .elementor-element{
		padding-left:0px;
	}
	
	/* Division */
	#rod .elementor-widget-text-editor .elementor-widget-container{
		margin-top:46px;
	}
	
	/* Division */
	.elementor .swiper-wrapper .swiper-slide:nth-child(3) .elementor-testimonial__footer{
		top:-155px !important;
	}
	
}

/* 425px and smaller screen sizes */
@media (max-width:425px){

	/* Link */
	.elementor-posts-container .elementor-grid-item:nth-child(1) .elementor-post__read-more{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Division */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__read-more-wrapper{
		left:16px;
		top:-10px;
	}
	
	/* Division */
	.elementor-posts--thumbnail-left .elementor-posts-container{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Post Division */
	.elementor-posts--thumbnail-left .elementor-grid-item{
		border-top-left-radius:0px !important;
		border-top-right-radius:0px !important;
		border-bottom-left-radius:0px !important;
		border-bottom-right-radius:0px !important;
	}
	
	/* Link */
	.elementor-posts--thumbnail-left .elementor-post__title a{
		width:99% !important;
	}
	
	/* Paragraph */
	.elementor-posts--thumbnail-left .elementor-grid-item p{
		height:196px;
		width:277px !important;
	}
	
	/* Paragraph */
	.elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item .elementor-post__text .elementor-post__excerpt p{
		width:276px !important;
	}
	
	/* Division */
	.elementor .elementor-element .e-con-inner .elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item .elementor-post__text{
		width:300px !important;
	}
	
	/* Division */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__excerpt{
		width:280px;
	}
	
}

/* 400px and smaller screen sizes */
@media (max-width:400px){

	.ekit-wid-con .elementkit-tab-wraper ul{
		width:75%;
		transform:translatex(0px) translatey(0px);
		top:-62px;
		padding-right:21px !important;
		padding-left:40px !important;
		height:559px !important;
	}
	
	.elementkit-tab-wraper .elementkit-tab-nav li{
		padding-top:14px;
	}
	
	/* Con inner */
	.elementor .elementor-element .e-con-inner{
		padding-bottom:57px;
		padding-top:34px;
		padding-left:0px;
		padding-right:6px;
	}
	
	.elementor .elementor-element .e-con-inner > .elementor-widget-text-editor{
		display:inline-block;
		transform:translatex(0px) translatey(0px) !important;
	}
	
	.elementor .elementor-widget-text-editor .elementor-widget-container{
		margin-top:0px !important;
	}
	
	.elementor-posts--thumbnail-left .elementor-grid-item .wp-image-3925{
		width:124%;
	}
	
	/* Link */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__thumbnail__link{
		width:121px !important;
		margin-right:8px !important;
	}
	
	.elementkit-tab-wraper .active .widgetarea_warper_editable{
		transform:translatex(0px) translatey(0px);
	}
	
	.active .animated .widgetarea_warper_editable .elementor-widget-container .elementor .elementor-element .e-con-inner > .elementor-element{
		padding-top:3px;
		transform:translatex(0px) translatey(0px);
		padding-left:0px;
		padding-right:0px;
	}
	
	.ekit-wid-con .elementkit-tab-wraper .elementkit-tab-content .active .animated .widgetarea_warper_editable .elementor-widget-container .elementor .elementor-element .e-con-inner .elementor-element .elementor-widget-heading{
		width:100% !important;
	}
	
	.elementkit-tab-wraper .active h2{
		margin-bottom:0px !important;
	}
	
	.elementkit-tab-wraper .active .elementor-widget-text-editor .elementor-widget-container{
		margin-top:7px !important;
	}
	
	.ekit-wid-con .elementkit-tab-wraper .elementkit-tab-content{
		margin-top:-50px;
	}
	
	.elementor .e-grid{
		margin-top:-77px;
	}
	
	.header .navbar .container{
		padding-top:0px;
		width:73%;
		margin-left:0px;
		margin-right:43px;
		background-color:#0c6833;
		padding-left:18px !important;
	}
	
	/* Link */
	.elementkit-tab-nav .elementkit-nav-item a{
		margin-right:80px !important;
	}
	
	/* Link */
	.ekit-wid-con .elementkit-tab-wraper .elementkit-tab-nav .elementkit-nav-item a{
		width:200% !important;
	}
	
	/* Element */
	.elementor > .elementor-element{
		padding-left:19px;
		transform:translatex(0px) translatey(0px) !important;
		padding-right:8px;
	}
	
	/* Con inner */
	.elementor-location-header .elementor-element .e-con-inner{
		padding-bottom:0px;
		padding-top:0px;
		padding-left:13px;
	}
	
	#menu-lang{
		width:100% !important;
		transform:translatex(0px) translatey(0px);
		height:9px;
		padding-bottom:0px;
		padding-top:0px;
		z-index:94;
		bottom:0px;
		top:41px;
	}
	
	#img-logo{
		width:100% !important;
		max-width:180px;
		top:37px;
	}
	
	/* Menu conteiner */
	#menu-conteiner{
		position:relative;
		margin-top:-50px;
		height:203px;
		min-height:50px;
		padding-top:0px;
		padding-bottom:0px;
		transform:translatex(0px) translatey(0px);
		bottom:37px;
	}
	
	/* Con inner */
	.e-n-accordion .elementor-element .elementor-element .e-con-inner{
		padding-top:10px;
		padding-bottom:50px;
		transform:translatex(0px) translatey(0px);
		position:relative;
		left:-8px;
	}
	
	/* Division */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner .elementor-element .ttt .elementor-widget-container{
		padding-bottom:0px;
		transform:translatex(0px) translatey(0px);
		width:300px;
	}
	
	/* Paragraph */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner .elementor-element .qq .elementor-widget-container p{
		position:relative;
		top:-16px;
		line-height:15px;
		margin-top:14px;
	}
	
	/* Link */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner .elementor-element .elementor-widget-button .elementor-widget-container .elementor-button-wrapper .elementor-button-link{
		position:relative;
		top:-29px;
	}
	
	/* Paragraph */
	.elementor > .elementor-element > .e-con-inner > .elementor-element > .elementor-widget-heading p.elementor-heading-title{
		height:87px;
		position:relative;
		left:34px;
		margin-bottom:0px;
	}
	
	/* Post Division */
	.elementor-posts--thumbnail-left .elementor-grid-item{
		border-top-left-radius:0px !important;
		border-top-right-radius:0px !important;
		border-bottom-left-radius:0px !important;
		border-bottom-right-radius:0px !important;
		width:100%;
		transform:translatex(0px) translatey(0px);
		height:228px;
	}
	
	/* Paragraph */
	.elementor .elementor-element .e-con-inner .elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item .elementor-post__text .elementor-post__excerpt p{
		height:auto !important;
		width:98% !important;
	}
	
	/* Division */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__text{
		width:300px;
		height:200px;
		padding-left:0px !important;
		padding-right:0px !important;
		display:inline-block;
		transform:translatex(0px) translatey(0px) !important;
		font-size:14px;
		line-height:14px;
	}
	
	#rodape .elementor-element img{
		position:relative;
		top:-20px;
	}
	
	/* Division */
	#de .qqq .elementor-widget-container{
		font-size:14px;
		transform:translatex(0px) translatey(0px);
		margin-left:0px;
		text-align:justify;
		line-height:1.64em;
		margin-top:-67px !important;
	}
	
	.elementor-widget-n-accordion .e-n-accordion-item:nth-child(4) .qq .elementor-widget-container{
		font-size:14px;
		position:relative;
		top:-29px;
	}
	
	.elementor-widget-n-accordion .e-n-accordion-item:nth-child(5) .ttt .elementor-heading-title{
		font-size:30px !important;
	}
	
	.elementor-widget-n-accordion .e-n-accordion-item:nth-child(5) .elementor-heading-title{
		text-align:left;
	}
	
	.elementor-widget-n-accordion .e-n-accordion-item:nth-child(5) .qq .elementor-widget-container{
		font-size:14px;
		position:relative;
		top:-11px;
	}
	
	.elementor-widget-n-accordion .e-n-accordion .wp-image-9272{
		position:relative;
	}
	
	.elementor-widget-n-accordion .e-n-accordion-item:nth-child(6) .ttt .elementor-heading-title{
		font-size:30px !important;
	}
	
	/* Paragraph */
	.e-con-inner .elementor-element .elementor-element .qq .elementor-widget-container p{
		font-size:14px;
		position:relative;
		top:-53px;
		margin-top:-10px;
		line-height:15px;
	}
	
	.elementor-widget-n-accordion .e-n-accordion .wp-image-8833{
		padding-top:10px;
	}
	
	.elementor-widget-n-accordion .e-n-accordion-item:nth-child(6) .elementor-button-link{
		position:relative;
		top:-11px;
	}
	
	.elementor-widget-n-accordion .e-n-accordion-item:nth-child(7) .elementor-heading-title{
		font-size:30px !important;
	}
	
	.elementor-widget-n-accordion .e-n-accordion-item:nth-child(7) .qq .elementor-widget-container{
		font-size:14px;
		position:relative;
		top:-21px;
	}
	
	.elementor-widget-n-accordion .e-n-accordion-item:nth-child(7) .e-con-inner{
		padding-left:10px;
		padding-top:20px;
	}
	
	.elementor-widget-n-accordion .e-n-accordion-item:nth-child(7) .elementor-widget-button{
		top:-14px;
	}
	
	/* Paragraph */
	.elementor-posts-container .elementor-grid-item:nth-child(1) p{
		padding-right:0px;
	}
	
	.elementor .elementor-element .e-con-inner .elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item:nth-child(2) .elementor-post__text .elementor-post__excerpt p{
		width:100% !important;
	}
	
	.elementor .elementor-element .e-con-inner .elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item:nth-child(3) .elementor-post__text .elementor-post__excerpt p{
		width:100% !important;
	}
	
	.elementor-posts-container .elementor-grid-item:nth-child(3) p{
		transform:translatex(0px) translatey(0px);
	}
	
	.elementor-posts-container .elementor-grid-item:nth-child(2) p{
		height:65px;
		transform:translatex(0px) translatey(0px);
	}
	
	.elementor .elementor-posts--thumbnail-left .elementor-grid-item:nth-child(2){
		height:230px;
	}
	
	/* Division */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__read-more-wrapper{
		display:flex;
		flex:0 1 auto;
		position:absolute;
		margin-left:101px;
		top:130px !important;
		left:7px !important;
	}
	
	/* Division */
	#rod .elementor-widget-text-editor .elementor-widget-container{
		position:relative;
		top:-2px;
		margin-top:27px !important;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-slide:nth-child(1) .elementor-testimonial__footer{
		position:relative;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-slide:nth-child(1) .elementor-testimonial__text{
		top:90px;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-slide:nth-child(2) .elementor-testimonial__content{
		top:-21px;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-slide:nth-child(3) .elementor-testimonial__text{
		top:90px;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-slide:nth-child(2) .elementor-testimonial__text{
		top:90px;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-wrapper .swiper-slide:nth-child(2) .elementor-testimonial .elementor-testimonial__footer{
		top:-115px !important;
	}
	
	/* Division */
	.elementor .e-n-accordion-item:nth-child(2) .qq .elementor-widget-container{
		font-size:14px;
		padding-bottom:20px;
	}
	
	.e-n-accordion .elementor-element .wp-image-8820{
		padding-left:0px;
		margin-left:2px;
	}
	
	/* Paragraph */
	#de .ttt p{
		width:130% !important;
		padding-bottom:10px;
		margin-left:0px;
		padding-left:37px;
		position:relative;
		left:-4px;
		top:-16px;
	}
	
	/* Division */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner .elementor-element .qq .elementor-widget-container{
		text-align:justify;
		line-height:1.64em;
		top:-10px;
		margin-bottom:13px;
	}
	
	#de .elementor-element .elementor-button-wrapper{
		margin-bottom:31px;
	}
	
	#de .elementor-element img{
		margin-left:4px;
	}
	
	.e-n-accordion .elementor-element .wp-image-8826{
		left:3px;
		position:relative;
	}
	
	/* Division */
	.e-con-inner .elementor-element .elementor-element .qq .elementor-widget-container{
		text-align:justify;
		margin-top:-10px !important;
		padding-top:18px;
		transform:translatex(0px) translatey(0px);
		line-height:15px;
		top:-58px;
		left:-4px;
	}
	
	/* Division */
	.elementor .e-n-accordion-item:nth-child(5) .elementor-button-wrapper{
		margin-bottom:10px;
	}
	
	/* Thumbnail Image */
	.e-n-accordion .elementor-element .wp-image-9272{
		top:18px;
		left:3px;
	}
	
	.e-n-accordion .elementor-element .wp-image-8833{
		padding-left:6px;
	}
	
	/* Span Tag */
	p > span > span{
		font-size:25px !important;
		line-height:1em;
	}
	
	/* Span Tag */
	.elementor p strong span{
		font-size:25px !important;
	}
	
	/* Span Tag */
	.elementor-widget-container p span span span{
		font-size:25px !important;
	}
	
	/* Span Tag */
	.elementor span span span{
		line-height:1em;
	}
	
	/* Strong Tag */
	.elementor span strong{
		line-height:1em;
	}
	
	/* Swiper slide */
	.elementor-widget-image-carousel .elementor-image-carousel .swiper-slide{
		margin-top:-26px;
	}
	
	/* Paragraph */
	.elementor > .elementor-element > .e-con-inner > .elementor-element > .elementor-widget-heading .elementor-widget-container p.elementor-heading-title{
		font-size:30px !important;
		line-height:1em !important;
	}
	
	/* Link */
	.elementor-posts--thumbnail-left .elementor-post__title a{
		font-weight:700 !important;
		line-height:15px;
		width:98%;
	}
	
	/* Paragraph */
	.elementor-posts--thumbnail-left .elementor-grid-item p{
		padding-right:1px !important;
		transform:translatex(0px) translatey(0px);
		top:3px;
		font-size:12px !important;
		line-height:15px !important;
	}
	
	/* Division */
	.e-con-inner .elementor-element .elementor-element .ttt .elementor-widget-container{
		left:-37px;
		height:67px;
	}
	
	/* Paragraph */
	.e-con-inner .elementor-element .elementor-element .ttt .elementor-widget-container .elementor-heading-title{
		padding-left:0px !important;
		line-height:1em;
		margin-left:0px !important;
	}
	
	/* Paragraph */
	.elementor-hidden-laptop .elementor-widget-n-accordion .elementor-widget-container .e-n-accordion .e-n-accordion-item .elementor-element .e-con-inner .elementor-element .elementor-element .elementor-widget-heading .elementor-widget-container .elementor-heading-title{
		width:100% !important;
		font-size:20px !important;
	}
	
	/* Paragraph */
	.e-n-accordion-item .elementor-element .e-con-inner .elementor-element .elementor-element .elementor-widget-heading .elementor-widget-container .elementor-heading-title{
		margin-bottom:2px;
		margin-left:-10px;
	}
	
	.e-n-accordion .elementor-element .wp-image-9277{
		left:-7px;
		position:relative;
		top:33px;
	}
	
	/* Paragraph */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner .elementor-element .ttt .elementor-widget-container .elementor-heading-title{
		width:295px;
		font-size:20px;
	}
	
	/* Link */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__text .elementor-post__read-more-wrapper a{
		width:120px;
		left:-26px;
		top:64px !important;
	}
	
	/* Division */
	.elementor > .elementor-element > .e-con-inner > .elementor-element > .elementor-widget-heading .elementor-widget-container{
		transform:translatex(0px) translatey(0px) !important;
		padding-left:7px;
	}
	
	/* Paragraph */
	.elementor .elementor-element .e-con-inner .elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item:nth-child(1) .elementor-post__text .elementor-post__excerpt p{
		width:95%;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-wrapper .swiper-slide .elementor-testimonial .elementor-testimonial__footer{
		top:-161px !important;
	}
	
	/* Division */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__excerpt{
		padding-right:4px;
		min-height:185px;
		transform:translatex(0px) translatey(0px);
		font-size:34px;
		width:99%;
	}
	
	/* Lazyloaded */
	.elementor .e-lazyloaded:nth-child(2){
		top:-70px;
	}
	
	/* Con inner */
	.elementor .e-lazyloaded > .elementor-element > .e-con-inner{
		margin-top:47px;
		padding-bottom:24px;
	}
	
	.e-lazyloaded > .elementor-element > .e-con-inner p{
		line-height:1em;
	}
	
	/* Elementor */
	.elementor{
		transform:translatex(0px) translatey(0px) !important;
	}
	
	/* Lazyloaded */
	.elementor .e-lazyloaded:nth-child(3){
		padding-bottom:0px !important;
		transform:translatex(0px) translatey(0px);
		padding-top:0px;
		top:-29px;
		margin-top:-33px;
	}
	
	/* Lazyloaded */
	.elementor .e-lazyloaded:nth-child(4){
		margin-bottom:0px;
	}
	
	#rodape .elementor-element .elementor-icon-list-text{
		color: white;
		text-decoration: none;
	}
	
	.elementor .e-lazyloaded .elementor-background-video-embed{
		border-top-right-radius:10px;
		border-bottom-left-radius:10px;
		border-bottom-right-radius:10px;
		border-top-left-radius:3px;
	}
	
	/* Element */
	.elementor:nth-child(3) .e-lazyloaded:nth-child(1) .elementor-element:nth-child(1){
		top:-8px;
		z-index:1;
	}
	
	/* Elementor */
	.elementor:nth-child(3){
		margin-top:-50px;
		z-index:0;
	}
	
	/* Division */
	.elementor-location-header{
		padding-top:16px;
		margin-bottom:-50px;
		z-index:1;
		height:162px;
		position:relative;
	}
	
	/* Lazyloaded */
	.elementor:nth-child(3) .e-lazyloaded:nth-child(1){
		padding-top:0px;
		padding-bottom:0px;
		min-height:318px;
		height:318px;
		margin-bottom:16px;
		top:-12px;
	}
	
	.elementor-image-carousel a img{
		width:100%;
	}
	
	.elementor .e-lazyloaded .e-con-inner .elementor-widget-image-carousel .elementor-widget-container .elementor-image-carousel-wrapper .elementor-image-carousel .swiper-slide a .swiper-slide-inner img{
		height:auto !important;
	}
	
	/* Division */
	.elementor .elementor-element:nth-child(7) .elementor-widget-heading .elementor-widget-container{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Division */
	.elementor .elementor-element:nth-child(7) .elementor-widget-heading{
		height:112px !important;
	}
	
	/* Element */
	.elementor .slider > .elementor-element{
		height:316px;
	}
	
	/* Paragraph */
	.slider .elementor-element p{
		font-size:12px;
	}
	
	/* Con inner */
	.elementor .elementor-element:nth-child(7) .e-con-inner{
		height:auto !important;
	}
	
	/* Division */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner .elementor-element .ttt{
		height:30px !important;
	}
	
	/* Paragraph */
	#de .elementor-element .ttt p{
		font-size:20px !important;
	}
	
	/* Element */
	#de .elementor-element .e-con-inner > .elementor-element > .elementor-element{
		left:-8px;
	}
	
	/* Element */
	.e-n-accordion-item > .elementor-element > .e-con-inner > .elementor-element > .elementor-element{
		left:-12px !important;
	}
	
	/* Division */
	.e-n-accordion-item .elementor-element .e-con-inner .elementor-element .elementor-element .elementor-widget-heading{
		height:76px !important;
	}
	
	/* Element */
	#de .elementor-element .elementor-element .elementor-element{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Division */
	#de .elementor-element .e-con-inner .elementor-element .elementor-element .qqq .elementor-widget-container{
		margin-top:-65px !important;
	}
	
	/* Paragraph */
	.elementor .e-n-accordion-item:nth-child(6) .elementor-heading-title{
		position:relative;
		top:-8px;
	}
	
	/* Link */
	.elementor .elementor-element .elementor-post__title a{
		font-size:13px !important;
	}
	
	/* Element */
	.elementor > .elementor-element > .e-con-inner > .elementor-element{
		transform:translatex(0px) translatey(0px) !important;
	}
	
	/* Division */
	.elementor .elementor-element .elementor-element > .elementor-widget-heading{
		top:-33px;
	}
	
}

/* 368px and smaller screen sizes */
@media (max-width:368px){

	/* Division */
	.elementor-swiper .elementor-main-swiper .swiper-wrapper .swiper-slide .elementor-testimonial .elementor-testimonial__footer{
		top:-200px !important;
	}
	
	/* Division */
	.elementor .e-lazyloaded .e-con-inner .elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item .elementor-post__text{
		width:100% !important;
	}
	
	/* Division */
	.elementor-location-header{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Elementor */
	.elementor{
		top:57px;
	}
	
	/* Menu conteiner */
	#menu-conteiner{
		top:-70px !important;
		transform:translatex(0px) translatey(0px);
	}
	
	/* Element */
	#menu-conteiner > .elementor-element{
		margin-top:-38px;
	}
	
	/* Slider */
	.elementor .slider{
		min-height:215px !important;
		height:215px !important;
	}
	
	/* Division */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__excerpt{
		width:99% !important;
	}
	
	/* Division */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__read-more-wrapper{
		padding-bottom:38px;
		padding-top:0px;
	}
	
	/* Paragraph */
	.elementor .elementor-element .e-con-inner .elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item .elementor-post__text .elementor-post__excerpt p{
		height:214px !important;
	}
	
	/* Link */
	.elementor-posts--thumbnail-left .elementor-post__title a{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Paragraph */
	.elementor-posts--thumbnail-left .elementor-grid-item p{
		font-size:12px;
		transform:translatex(0px) translatey(0px);
	}
	
	/* Paragraph */
	.elementor .elementor-element .elementor-grid-item p{
		line-height:0.9em !important;
		width:88% !important;
	}
	
	/* Paragraph */
	.elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item .elementor-post__text .elementor-post__excerpt p{
		width:90% !important;
	}
	
	/* Division */
	.elementor .elementor-element .elementor-element > .elementor-widget-heading{
		
	}
	
}

/* 320px and smaller screen sizes */
@media (max-width:320px){

	/* Con inner */
	.elementor-location-header .elementor-element .e-con-inner{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Division */
	.elementor-location-header{
		transform:translatex(0px) translatey(0px);
	}
	
	#img-logo{
		padding-left:0px;
		width:50%;
	}
	
	#imglogo{
		right:0px;
		width:100%;
	}
	
	#menu-lang{
		width:50%;
		text-align:right;
	}
	
	/* Division */
	.elementor .swiper-wrapper .swiper-slide:nth-child(1) .elementor-testimonial__footer{
		top:-231px !important;
	}
	
	/* Division */
	.elementor .swiper-wrapper .swiper-slide:nth-child(2) .elementor-testimonial__footer{
		top:-153px !important;
	}
	
	/* Division */
	.elementor .swiper-wrapper .swiper-slide:nth-child(3) .elementor-testimonial__footer{
		top:-206px !important;
	}
	
	/* Paragraph */
	.elementor .elementor-element .elementor-grid-item p{
		font-size:10px !important;
	}
	
	/* Paragraph */
	.elementor-posts--thumbnail-left .elementor-grid-item p{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Link */
	.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__text .elementor-post__read-more-wrapper a{
		left:-75px;
	}
	
	/* Division */
	.elementor-swiper .elementor-main-swiper .swiper-wrapper .swiper-slide .elementor-testimonial .elementor-testimonial__footer{
		top:-234px !important;
	}
	
}

/* 768px and larger screen sizes */
@media (min-width:768px){

	#imglogo .elementor-widget-container{
		padding-top:7px !important;
	}
	
}