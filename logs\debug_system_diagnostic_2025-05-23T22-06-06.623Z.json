{"timestamp": "2025-05-23T22:06:06.622Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 406724608, "heapTotal": 119996416, "heapUsed": 97958904, "external": 8730790, "arrayBuffers": 298886}, "uptime": 1.907537489, "cpuUsage": {"user": 2815242, "system": 355986}, "resourceUsage": {"userCPUTime": 2815292, "systemCPUTime": 355992, "maxRSS": 397192, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107043, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 88, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7926, "involuntaryContextSwitches": 1554}}