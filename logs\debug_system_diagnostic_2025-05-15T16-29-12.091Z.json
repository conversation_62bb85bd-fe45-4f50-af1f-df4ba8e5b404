{"timestamp": "2025-05-15T16:29:12.091Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 292655104, "heapTotal": 105254912, "heapUsed": 66032872, "external": 7016254, "arrayBuffers": 60485}, "uptime": 2.806846366, "cpuUsage": {"user": 2508582, "system": 410246}, "resourceUsage": {"userCPUTime": 2508638, "systemCPUTime": 410246, "maxRSS": 285796, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101508, "majorPageFault": 0, "swappedOut": 0, "fsRead": 20336, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7600, "involuntaryContextSwitches": 8730}}