{"timestamp": "2025-05-15T13:58:43.745Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 386920448, "heapTotal": 102895616, "heapUsed": 62298992, "external": 6815481, "arrayBuffers": 60485}, "uptime": 12.771642612, "cpuUsage": {"user": 2253697, "system": 339648}, "resourceUsage": {"userCPUTime": 2253757, "systemCPUTime": 339657, "maxRSS": 377852, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102765, "majorPageFault": 6, "swappedOut": 0, "fsRead": 42120, "fsWrite": 416, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6354, "involuntaryContextSwitches": 1573}}