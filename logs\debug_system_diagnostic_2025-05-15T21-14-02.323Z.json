{"timestamp": "2025-05-15T21:14:02.322Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397422592, "heapTotal": 114860032, "heapUsed": 72471272, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.968660777, "cpuUsage": {"user": 2862243, "system": 355607}, "resourceUsage": {"userCPUTime": 2862309, "systemCPUTime": 355607, "maxRSS": 388108, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103482, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7654, "involuntaryContextSwitches": 2979}}