{"timestamp": "2025-05-23T20:09:10.892Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 415846400, "heapTotal": 120320000, "heapUsed": 90423176, "external": 8632325, "arrayBuffers": 620669}, "uptime": 4.593246169, "cpuUsage": {"user": 3529432, "system": 418155}, "resourceUsage": {"userCPUTime": 3529440, "systemCPUTime": 418156, "maxRSS": 406100, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106983, "majorPageFault": 0, "swappedOut": 0, "fsRead": 8, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 10054, "involuntaryContextSwitches": 7274}}