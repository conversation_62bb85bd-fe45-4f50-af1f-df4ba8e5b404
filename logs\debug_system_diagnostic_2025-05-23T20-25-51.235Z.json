{"timestamp": "2025-05-23T20:25:51.235Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 411090944, "heapTotal": 118812672, "heapUsed": 98884520, "external": 8432925, "arrayBuffers": 273850}, "uptime": 2.829386793, "cpuUsage": {"user": 3252014, "system": 372381}, "resourceUsage": {"userCPUTime": 3252086, "systemCPUTime": 372390, "maxRSS": 401456, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105852, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8839, "involuntaryContextSwitches": 11052}}