{"timestamp": "2025-05-14T14:12:42.725Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 331489280, "heapTotal": 149196800, "heapUsed": 122667416, "external": 8546984, "arrayBuffers": 85061}, "uptime": 1.767912743, "cpuUsage": {"user": 3045208, "system": 406224}, "resourceUsage": {"userCPUTime": 3045271, "systemCPUTime": 406233, "maxRSS": 323720, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 111626, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 13115, "involuntaryContextSwitches": 2869}}