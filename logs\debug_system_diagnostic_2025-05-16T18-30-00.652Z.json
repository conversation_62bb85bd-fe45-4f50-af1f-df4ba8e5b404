{"timestamp": "2025-05-16T18:30:00.652Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404819968, "heapTotal": 116432896, "heapUsed": 72494816, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.163082106, "cpuUsage": {"user": 3028068, "system": 383432}, "resourceUsage": {"userCPUTime": 3028122, "systemCPUTime": 383432, "maxRSS": 395332, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105382, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 56, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8427, "involuntaryContextSwitches": 4622}}