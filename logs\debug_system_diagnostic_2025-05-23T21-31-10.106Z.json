{"timestamp": "2025-05-23T21:31:10.105Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 398323712, "heapTotal": 117743616, "heapUsed": 74773480, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.346148686, "cpuUsage": {"user": 3236007, "system": 372743}, "resourceUsage": {"userCPUTime": 3236062, "systemCPUTime": 372743, "maxRSS": 388988, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104284, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8339, "involuntaryContextSwitches": 5498}}