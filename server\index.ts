import express from "express";
import cors from "cors";
import { createServer } from "http";
import { addApiRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { requestLogger } from "./middlewares/logging";
import { metricsMiddleware } from "./metrics/prometheus";
import { apiLimiter } from "./middlewares/rate-limit";
import { healthcheck } from "./health/healthcheck";
import { metricsHandler } from "./metrics/prometheus";

const app = express();
const port = 5000;

// Configuração básica
app.use(cors({ origin: '*', credentials: true }));
app.use(express.json({ limit: '2mb' }));
app.use(express.urlencoded({ extended: false, limit: '2mb' }));

// Middlewares de monitoramento e segurança
app.use(requestLogger);
app.use(metricsMiddleware);
app.use('/api', apiLimiter);

// Rotas de monitoramento
app.get('/healthz', healthcheck);
app.get('/metrics', metricsHandler);

// Health check legacy (manter compatibilidade)
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'EquiGestor AI Online!',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(process.uptime())
  });
});

const server = createServer(app);

(async () => {
  try {
    // Configurar rotas da API
    await addApiRoutes(app);

    // Configurar Vite para desenvolvimento
    const nodeEnv = process.env.NODE_ENV || "development";
    if (nodeEnv === "development") {
      await setupVite(app, server);
    } else {
      serveStatic(app);
    }

    // Iniciar servidor
    server.listen(port, "0.0.0.0", () => {
      console.log(`🎉 EquiGestor AI com sistema de login funcionando na porta ${port}`);
      log(`serving on port ${port}`);
    });
  } catch (error) {
    console.error('Erro ao inicializar:', error);
  }
})();
