{"timestamp": "2025-05-17T01:11:41.318Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 250064896, "heapTotal": 107151360, "heapUsed": 83001816, "external": 8276453, "arrayBuffers": 249274}, "uptime": 3.222863849, "cpuUsage": {"user": 2943529, "system": 478321}, "resourceUsage": {"userCPUTime": 2943619, "systemCPUTime": 478321, "maxRSS": 331544, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108369, "majorPageFault": 0, "swappedOut": 0, "fsRead": 22088, "fsWrite": 88, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8912, "involuntaryContextSwitches": 10635}}