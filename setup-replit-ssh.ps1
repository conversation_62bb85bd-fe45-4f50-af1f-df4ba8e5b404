# Script para configurar SSH para Replit
# Gera chave SSH ed25519 e copia a chave pública para a área de transferência

param(
    [string]$KeyName = "replit",
    [switch]$Force
)

# Cores para output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Cyan = "Cyan"

Write-Host "🔑 Configurando SSH para Replit..." -ForegroundColor $Cyan
Write-Host ""

# Definir caminhos
$SshDir = "$env:USERPROFILE\.ssh"
$PrivateKeyPath = "$SshDir\$KeyName"
$PublicKeyPath = "$SshDir\$KeyName.pub"

# Criar diretório .ssh se não existir
if (-not (Test-Path $SshDir)) {
    Write-Host "📁 Criando diretório .ssh..." -ForegroundColor $Yellow
    New-Item -ItemType Directory -Path $SshDir -Force | Out-Null
    
    # Definir permissões corretas no diretório .ssh (apenas para o usuário atual)
    $acl = Get-Acl $SshDir
    $acl.SetAccessRuleProtection($true, $false)
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule($env:USERNAME, "FullControl", "Allow")
    $acl.SetAccessRule($accessRule)
    Set-Acl -Path $SshDir -AclObject $acl
}

# Verificar se a chave já existe
if ((Test-Path $PublicKeyPath) -and -not $Force) {
    Write-Host "✅ Chave SSH '$KeyName' já existe!" -ForegroundColor $Green
    Write-Host "📍 Localização: $PublicKeyPath" -ForegroundColor $Yellow
    
    $choice = Read-Host "Deseja usar a chave existente? (s/N)"
    if ($choice -notmatch '^[sS]$') {
        Write-Host "❌ Operação cancelada." -ForegroundColor $Red
        exit 1
    }
} else {
    # Gerar nova chave SSH
    Write-Host "🔐 Gerando nova chave SSH ed25519..." -ForegroundColor $Yellow
    
    # Verificar se ssh-keygen está disponível
    try {
        $null = Get-Command ssh-keygen -ErrorAction Stop
    } catch {
        Write-Host "❌ ssh-keygen não encontrado. Instale o OpenSSH ou Git for Windows." -ForegroundColor $Red
        Write-Host "💡 Você pode instalar via: winget install Git.Git" -ForegroundColor $Yellow
        exit 1
    }
    
    # Remover chaves existentes se Force foi especificado
    if ($Force) {
        if (Test-Path $PrivateKeyPath) { Remove-Item $PrivateKeyPath -Force }
        if (Test-Path $PublicKeyPath) { Remove-Item $PublicKeyPath -Force }
    }
    
    # Gerar chave
    $sshKeygenArgs = @(
        "-t", "ed25519",
        "-f", $PrivateKeyPath,
        "-q",
        "-N", '""',
        "-C", "replit-key-$(Get-Date -Format 'yyyy-MM-dd')"
    )
    
    try {
        & ssh-keygen @sshKeygenArgs
        if ($LASTEXITCODE -ne 0) {
            throw "ssh-keygen falhou com código $LASTEXITCODE"
        }
        Write-Host "✅ Chave SSH gerada com sucesso!" -ForegroundColor $Green
    } catch {
        Write-Host "❌ Erro ao gerar chave SSH: $_" -ForegroundColor $Red
        exit 1
    }
}

# Verificar se a chave pública existe
if (-not (Test-Path $PublicKeyPath)) {
    Write-Host "❌ Chave pública não encontrada: $PublicKeyPath" -ForegroundColor $Red
    exit 1
}

# Ler conteúdo da chave pública
try {
    $PublicKeyContent = Get-Content $PublicKeyPath -Raw
    $PublicKeyContent = $PublicKeyContent.Trim()
    
    if ([string]::IsNullOrWhiteSpace($PublicKeyContent)) {
        throw "Chave pública está vazia"
    }
    
    Write-Host "📋 Chave pública:" -ForegroundColor $Cyan
    Write-Host $PublicKeyContent -ForegroundColor $Yellow
    Write-Host ""
    
} catch {
    Write-Host "❌ Erro ao ler chave pública: $_" -ForegroundColor $Red
    exit 1
}

# Copiar para área de transferência
try {
    $PublicKeyContent | Set-Clipboard
    Write-Host "✅ Chave pública copiada para a área de transferência!" -ForegroundColor $Green
} catch {
    Write-Host "⚠️  Não foi possível copiar para área de transferência: $_" -ForegroundColor $Yellow
    Write-Host "💡 Você pode copiar manualmente a chave acima." -ForegroundColor $Yellow
}

Write-Host ""
Write-Host "🎯 Próximos passos:" -ForegroundColor $Cyan
Write-Host "1. Acesse https://replit.com/account#ssh-keys" -ForegroundColor $White
Write-Host "2. Clique em 'Add SSH Key'" -ForegroundColor $White
Write-Host "3. Cole a chave (Ctrl+V) no campo 'Key'" -ForegroundColor $White
Write-Host "4. Dê um nome para a chave (ex: 'Meu PC')" -ForegroundColor $White
Write-Host "5. Clique em 'Add key'" -ForegroundColor $White
Write-Host ""
Write-Host "📁 Arquivos criados:" -ForegroundColor $Cyan
Write-Host "   Chave privada: $PrivateKeyPath" -ForegroundColor $White
Write-Host "   Chave pública:  $PublicKeyPath" -ForegroundColor $White
Write-Host ""
Write-Host "🔒 IMPORTANTE: Mantenha a chave privada segura e nunca a compartilhe!" -ForegroundColor $Red

# Abrir página do Replit automaticamente
$openBrowser = Read-Host "Deseja abrir a página de configuração SSH do Replit agora? (S/n)"
if ($openBrowser -notmatch '^[nN]$') {
    try {
        Start-Process "https://replit.com/account#ssh-keys"
        Write-Host "🌐 Abrindo página do Replit..." -ForegroundColor $Green
    } catch {
        Write-Host "⚠️  Não foi possível abrir o navegador automaticamente." -ForegroundColor $Yellow
        Write-Host "💡 Acesse manualmente: https://replit.com/account#ssh-keys" -ForegroundColor $Yellow
    }
}

Write-Host ""
Write-Host "✨ Configuração concluída!" -ForegroundColor $Green
