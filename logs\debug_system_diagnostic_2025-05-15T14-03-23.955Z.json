{"timestamp": "2025-05-15T14:03:23.954Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 245481472, "heapTotal": 100012032, "heapUsed": 62221400, "external": 6815481, "arrayBuffers": 60485}, "uptime": 2.846833996, "cpuUsage": {"user": 2294744, "system": 342723}, "resourceUsage": {"userCPUTime": 2294823, "systemCPUTime": 342723, "maxRSS": 284660, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 98440, "majorPageFault": 1, "swappedOut": 0, "fsRead": 22256, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5841, "involuntaryContextSwitches": 4879}}