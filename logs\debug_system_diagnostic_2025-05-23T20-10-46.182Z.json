{"timestamp": "2025-05-23T20:10:46.181Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 270753792, "heapTotal": 118681600, "heapUsed": 91708704, "external": 8496020, "arrayBuffers": 261044}, "uptime": 2.330387888, "cpuUsage": {"user": 3019264, "system": 358977}, "resourceUsage": {"userCPUTime": 3019326, "systemCPUTime": 358985, "maxRSS": 328336, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106621, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8722, "involuntaryContextSwitches": 5630}}