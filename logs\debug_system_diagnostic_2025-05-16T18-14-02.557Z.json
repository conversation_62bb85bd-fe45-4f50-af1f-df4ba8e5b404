{"timestamp": "2025-05-16T18:14:02.556Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 273395712, "heapTotal": 122355712, "heapUsed": 97439280, "external": 8702549, "arrayBuffers": 282977}, "uptime": 1.698753648, "cpuUsage": {"user": 2665137, "system": 333550}, "resourceUsage": {"userCPUTime": 2665137, "systemCPUTime": 333598, "maxRSS": 296300, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107205, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 56, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8638, "involuntaryContextSwitches": 1831}}