import { db } from "./db";
import { eq, and, asc, desc, gte, lte } from "drizzle-orm";
import { medidasMorfologicas, InsertMedidasMorfologicas, MedidasMorfologicas } from "../shared/schema_medidas_morfologicas";

// Storage para medidas morfológicas
export class MedidasMorfologicasStorage {
  // Listar todas as medidas morfológicas de um usuário
  async listarTodas(userId: number): Promise<MedidasMorfologicas[]> {
    return db.select()
      .from(medidasMorfologicas)
      .where(eq(medidasMorfologicas.userId, userId))
      .orderBy(desc(medidasMorfologicas.dataMedicao));
  }
  
  // Listar medidas morfológicas por cavalo
  async listarPorCavalo(horseId: number): Promise<MedidasMorfologicas[]> {
    return db.select()
      .from(medidasMorfologicas)
      .where(eq(medidasMorfologicas.horseId, horseId))
      .orderBy(desc(medidasMorfologicas.dataMedicao));
  }
  
  // Obter uma medida específica
  async obter(id: number): Promise<MedidasMorfologicas | undefined> {
    const [medida] = await db.select()
      .from(medidasMorfologicas)
      .where(eq(medidasMorfologicas.id, id));
    return medida;
  }
  
  // Criar uma nova medida morfológica
  async criar(dados: InsertMedidasMorfologicas): Promise<MedidasMorfologicas> {
    try {
      console.log('Tentando inserir medidas morfológicas:', dados);
      
      // Garantir que todos os números são do tipo float
      const dadosProcessados = {
        ...dados,
        alturaCernelha: dados.alturaCernelha ? parseFloat(String(dados.alturaCernelha)) : null,
        comprimentoCorpo: dados.comprimentoCorpo ? parseFloat(String(dados.comprimentoCorpo)) : null,
        perimetroToracico: dados.perimetroToracico ? parseFloat(String(dados.perimetroToracico)) : null,
        comprimentoGarupa: dados.comprimentoGarupa ? parseFloat(String(dados.comprimentoGarupa)) : null,
        perimetroCanela: dados.perimetroCanela ? parseFloat(String(dados.perimetroCanela)) : null,
        comprimentoCabeca: dados.comprimentoCabeca ? parseFloat(String(dados.comprimentoCabeca)) : null,
        larguraCabeca: dados.larguraCabeca ? parseFloat(String(dados.larguraCabeca)) : null,
        temperatura: dados.temperatura ? parseFloat(String(dados.temperatura)) : null,
        indiceRelativo: dados.indiceRelativo ? parseFloat(String(dados.indiceRelativo)) : null,
        indiceRobustez: dados.indiceRobustez ? parseFloat(String(dados.indiceRobustez)) : null,
        indiceTorax: dados.indiceTorax ? parseFloat(String(dados.indiceTorax)) : null
      };

      const [medida] = await db
        .insert(medidasMorfologicas)
        .values(dadosProcessados)
        .returning();

      console.log('Medidas morfológicas inseridas com sucesso:', medida);
      return medida;
    } catch (error) {
      console.error('Erro ao inserir medidas morfológicas:', error);
      throw new Error('Falha ao salvar as medidas morfológicas no banco de dados');
    }
  }

  // Atualizar uma medida morfológica
  async atualizar(id: number, userId: number, dados: Partial<InsertMedidasMorfologicas>): Promise<MedidasMorfologicas | undefined> {
    const [medida] = await db
      .update(medidasMorfologicas)
      .set(dados)
      .where(and(eq(medidasMorfologicas.id, id), eq(medidasMorfologicas.userId, userId)))
      .returning();
    return medida;
  }

  // Excluir uma medida morfológica
  async excluir(id: number, userId: number): Promise<boolean> {
    const resultado = await db
      .delete(medidasMorfologicas)
      .where(and(eq(medidasMorfologicas.id, id), eq(medidasMorfologicas.userId, userId)))
      .returning();
    return resultado.length > 0;
  }

  // Obter última medida de um cavalo
  async obterUltimaMedida(horseId: number): Promise<MedidasMorfologicas | undefined> {
    const [medida] = await db.select()
      .from(medidasMorfologicas)
      .where(eq(medidasMorfologicas.horseId, horseId))
      .orderBy(desc(medidasMorfologicas.dataMedicao))
      .limit(1);
    return medida;
  }

  // Obter medidas dentro de um período
  async obterMedidasPorPeriodo(horseId: number, dataInicio: string, dataFim: string): Promise<MedidasMorfologicas[]> {
    return db.select()
      .from(medidasMorfologicas)
      .where(and(
        eq(medidasMorfologicas.horseId, horseId),
        gte(medidasMorfologicas.dataMedicao, new Date(dataInicio)),
        lte(medidasMorfologicas.dataMedicao, new Date(dataFim))
      ))
      .orderBy(asc(medidasMorfologicas.dataMedicao));
  }

  // Verificar conexão com o banco de dados
  async checkConnection(): Promise<void> {
    try {
      // Usar uma consulta genérica para verificar a conexão
      await db.execute(sql`SELECT 1`); 
      console.log('Conexão com o banco de dados (Neon/PostgreSQL) verificada com sucesso.');
    } catch (error) {
      console.error('Erro ao verificar conexão com o banco de dados (Neon/PostgreSQL):', error);
      throw new Error('Falha na conexão com o banco de dados');
    }
  }
}