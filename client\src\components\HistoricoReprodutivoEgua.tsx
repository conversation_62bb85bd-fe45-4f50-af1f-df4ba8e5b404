import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { format, parseISO, isValid, differenceInDays, differenceInMonths } from 'date-fns';
import { pt } from 'date-fns/locale';
import { AreaChart, Bar, BarChart, LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, Responsive<PERSON><PERSON>r, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { AlertCircle, Heart, Calendar, Activity, ChartBar } from 'lucide-react';
import { Link } from 'wouter';

/**
 * Componente de Histórico Reprodutivo de Égua
 * 
 * Este componente exibe o histórico completo de reprodução de uma égua específica,
 * incluindo todos os eventos reprodutivos, estatísticas e gráficos.
 */
interface HistoricoReprodutivoEguaProps {
  horseId: number;
  horseName?: string;
}

interface ReproducaoDetalhada {
  id: number;
  horseId: number;
  padreiroId: number | null;
  padreiro?: { name: string; breed: string; } | null;
  dataCobertura: string;
  tipoCobertura: string;
  estado: string;
  dataPrevistaInseminacao: string | null;
  dataPrevistaEmbriao: string | null;
  dataPrevistaParto: string | null;
  dataDiagnosticoGestacao: string | null;
  resultadoDiagnostico: string | null;
  observacoes: string | null;
  procedimentoVetId: number | null;
  eventoId: number | null;
  userId: number;
  createdAt: string;
}

interface ProcedimentoVet {
  id: number;
  tipo: string;
  descricao: string;
  data: string;
  veterinario: string | null;
  crmv: string | null;
  horseId: number;
  userId: number;
}

// Cores para gráficos
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

export function HistoricoReprodutivoEgua({ horseId, horseName }: HistoricoReprodutivoEguaProps) {
  const [user, setUser] = useState<any>(null);
  const [estatisticas, setEstatisticas] = useState({
    totalReproducoes: 0,
    sucessos: 0,
    fracassos: 0,
    emAndamento: 0,
    taxaSucesso: 0,
    tiposCobertura: [] as {name: string, value: number}[],
    tempoGestacao: [] as {data: string, dias: number}[],
  });

  // Carregar usuário do localStorage
  useEffect(() => {
    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
    } catch (error) {
      console.error("Erro ao carregar usuário:", error);
    }
  }, []);
  
  // Consulta para buscar reproduções da égua específica
  const { 
    data: reproducoes = [],
    isLoading: isLoadingReproducoes
  } = useQuery({
    queryKey: [`/api/cavalos/${horseId}/reproducao`],
    queryFn: async () => {
      try {
        let user = null;
        try {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            user = JSON.parse(userStr);
          }
        } catch (e) {
          console.error("Erro ao obter usuário do localStorage:", e);
        }
        
        const userId = user?.id;
        
        if (!userId) {
          throw new Error("Usuário não autenticado. Faça login novamente.");
        }

        const response = await fetch(`/api/cavalos/${horseId}/reproducao`, {
          headers: {
            'user-id': userId.toString()
          }
        });

        if (!response.ok) {
          throw new Error('Erro ao buscar histórico reprodutivo');
        }

        return await response.json();
      } catch (error) {
        console.error('Erro ao buscar histórico reprodutivo:', error);
        return [];
      }
    },
    enabled: !!user && !!horseId
  });
  
  // Consulta para buscar procedimentos veterinários associados
  const { 
    data: procedimentos = [],
    isLoading: isLoadingProcedimentos
  } = useQuery({
    queryKey: [`/api/cavalos/${horseId}/procedimentos-veterinarios`],
    queryFn: async () => {
      try {
        let user = null;
        try {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            user = JSON.parse(userStr);
          }
        } catch (e) {
          console.error("Erro ao obter usuário do localStorage:", e);
        }
        
        const userId = user?.id;
        
        if (!userId) {
          throw new Error("Usuário não autenticado. Faça login novamente.");
        }

        const response = await fetch(`/api/cavalos/${horseId}/procedimentos-veterinarios`, {
          headers: {
            'user-id': userId.toString()
          }
        });

        if (!response.ok) {
          throw new Error('Erro ao buscar procedimentos veterinários');
        }

        return await response.json();
      } catch (error) {
        console.error('Erro ao buscar procedimentos veterinários:', error);
        return [];
      }
    },
    enabled: !!user && !!horseId
  });

  // Funções auxiliares
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    
    try {
      const date = parseISO(dateString);
      if (!isValid(date)) return 'Data inválida';
      return format(date, 'dd/MM/yyyy', { locale: pt });
    } catch (error) {
      return 'Data inválida';
    }
  };

  const getEstadoReproducaoLabel = (value: string) => {
    const estados: {[key: string]: string} = {
      'pre_cobertura': 'Pré-Cobertura',
      'em_cobertura': 'Em Cobertura',
      'gestante': 'Gestante',
      'parto_realizado': 'Parto Realizado',
      'aborto': 'Aborto',
      'falha_embriao': 'Falha de Embrião',
      'nao_prenhe': 'Não Prenhe'
    };
    return estados[value] || value;
  };

  const getTipoCoberturaLabel = (value: string) => {
    const tipos: {[key: string]: string} = {
      'monta_natural': 'Monta Natural',
      'inseminacao': 'Inseminação Artificial',
      'transferencia_embriao': 'Transferência de Embrião'
    };
    return tipos[value] || value;
  };

  // Calcular estatísticas quando os dados forem carregados
  useEffect(() => {
    if (reproducoes.length > 0) {
      // Estatísticas básicas
      const sucessos = reproducoes.filter((r: any) => 
        r.estado === 'gestante' || r.estado === 'parto_realizado'
      ).length;
      
      const fracassos = reproducoes.filter((r: any) => 
        r.estado === 'aborto' || r.estado === 'falha_embriao' || r.estado === 'nao_prenhe'
      ).length;
      
      const emAndamento = reproducoes.filter((r: any) => 
        r.estado === 'pre_cobertura' || r.estado === 'em_cobertura'
      ).length;
      
      // Taxa de sucesso (porcentagem)
      const taxaSucesso = reproducoes.length > 0 
        ? Math.round((sucessos / (sucessos + fracassos)) * 100) 
        : 0;
      
      // Distribuição por tipo de cobertura
      const tiposContagem: {[key: string]: number} = {};
      reproducoes.forEach((r: any) => {
        const tipo = r.tipoCobertura || 'desconhecido';
        tiposContagem[tipo] = (tiposContagem[tipo] || 0) + 1;
      });
      
      const tiposCobertura = Object.keys(tiposContagem).map(key => ({
        name: getTipoCoberturaLabel(key),
        value: tiposContagem[key]
      }));
      
      // Tempo de gestação (para partos realizados)
      const tempoGestacao = reproducoes
        .filter((r: any) => r.estado === 'parto_realizado' && r.dataCobertura && r.dataParto)
        .map((r: any) => {
          const dataCobertura = parseISO(r.dataCobertura);
          const dataParto = parseISO(r.dataParto);
          const dias = differenceInDays(dataParto, dataCobertura);
          return {
            data: formatDate(r.dataCobertura),
            dias: dias
          };
        });
      
      setEstatisticas({
        totalReproducoes: reproducoes.length,
        sucessos,
        fracassos,
        emAndamento,
        taxaSucesso,
        tiposCobertura,
        tempoGestacao
      });
    }
  }, [reproducoes]);

  // Combinar procedimentos veterinários com reproduções
  const reproducoesProcedimentos = reproducoes.map((reproducao: any) => {
    let procedimentoAssociado = null;
    if (reproducao.procedimentoVetId) {
      procedimentoAssociado = procedimentos.find((p: any) => p.id === reproducao.procedimentoVetId);
    }
    return {
      ...reproducao,
      procedimento: procedimentoAssociado
    };
  });

  if (isLoadingReproducoes || isLoadingProcedimentos) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        <span className="ml-2">Carregando histórico reprodutivo...</span>
      </div>
    );
  }

  if (!horseId) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Erro</AlertTitle>
        <AlertDescription>
          Nenhuma égua selecionada para visualizar o histórico reprodutivo.
        </AlertDescription>
      </Alert>
    );
  }

  if (reproducoes.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Histórico Reprodutivo - {horseName || `Égua #${horseId}`}</CardTitle>
          <CardDescription>
            Não há registros reprodutivos para esta égua.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center py-12">
            <Alert>
              <Heart className="h-4 w-4" />
              <AlertTitle>Nenhum registro encontrado</AlertTitle>
              <AlertDescription>
                Esta égua ainda não possui histórico reprodutivo registrado no sistema.
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col md:flex-row md:justify-between md:items-center">
          <div>
            <CardTitle className="text-xl flex items-center">
              <Heart className="mr-2 h-5 w-5 text-pink-500" />
              Histórico Reprodutivo - {horseName || `Égua #${horseId}`}
            </CardTitle>
            <CardDescription>
              Visualize o histórico completo de reprodução desta égua
            </CardDescription>
          </div>
          <div className="mt-4 md:mt-0 flex space-x-2">
            <Badge variant="outline" className="px-3 py-1">
              Total: {estatisticas.totalReproducoes}
            </Badge>
            <Badge variant="outline" className="px-3 py-1 bg-green-50 text-green-700 border-green-200">
              Sucessos: {estatisticas.sucessos}
            </Badge>
            <Badge variant="outline" className="px-3 py-1 bg-red-50 text-red-700 border-red-200">
              Fracassos: {estatisticas.fracassos}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="timeline" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="timeline">Linha do Tempo</TabsTrigger>
            <TabsTrigger value="stats">Estatísticas</TabsTrigger>
            <TabsTrigger value="vet">Procedimentos Vet.</TabsTrigger>
          </TabsList>
          
          {/* Tab: Linha do Tempo */}
          <TabsContent value="timeline" className="mt-4">
            <Table>
              <TableCaption>Histórico de eventos reprodutivos em ordem cronológica</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>Data</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Garanhão</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Diagn. Gestação</TableHead>
                  <TableHead>Resultado</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reproducoesProcedimentos
                  .sort((a: any, b: any) => {
                    // Ordenar por data (mais recente primeiro)
                    return new Date(b.dataCobertura).getTime() - new Date(a.dataCobertura).getTime();
                  })
                  .map((item: any) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{formatDate(item.dataCobertura)}</TableCell>
                      <TableCell>{getTipoCoberturaLabel(item.tipoCobertura)}</TableCell>
                      <TableCell>{item.padreiroId ? `#${item.padreiroId}` : 'N/A'}</TableCell>
                      <TableCell>
                        <Badge 
                          variant="outline"
                          className={`${
                            item.estado === 'gestante' || item.estado === 'parto_realizado' 
                              ? 'bg-green-50 text-green-700 border-green-200' 
                              : item.estado === 'aborto' || item.estado === 'falha_embriao' || item.estado === 'nao_prenhe'
                                ? 'bg-red-50 text-red-700 border-red-200'
                                : 'bg-blue-50 text-blue-700 border-blue-200'
                          }`}
                        >
                          {getEstadoReproducaoLabel(item.estado)}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatDate(item.dataDiagnosticoGestacao)}</TableCell>
                      <TableCell>{item.resultadoDiagnostico || '-'}</TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TabsContent>
          
          {/* Tab: Estatísticas */}
          <TabsContent value="stats" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Taxa de Sucesso</CardTitle>
                  <CardDescription>
                    Porcentagem de coberturas que resultaram em prenhez
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-[300px]">
                  {estatisticas.sucessos + estatisticas.fracassos > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={[
                            { name: 'Sucesso', value: estatisticas.sucessos },
                            { name: 'Fracasso', value: estatisticas.fracassos }
                          ]}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          <Cell fill="#4ade80" />
                          <Cell fill="#f87171" />
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-gray-500">Dados insuficientes para gerar estatísticas</p>
                    </div>
                  )}
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Tipos de Cobertura</CardTitle>
                  <CardDescription>
                    Distribuição dos métodos utilizados
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-[300px]">
                  {estatisticas.tiposCobertura.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        width={500}
                        height={300}
                        data={estatisticas.tiposCobertura}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="value" name="Quantidade" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-gray-500">Dados insuficientes para gerar estatísticas</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          {/* Tab: Procedimentos Veterinários */}
          <TabsContent value="vet" className="mt-4">
            {procedimentos.length > 0 ? (
              <Table>
                <TableCaption>Procedimentos veterinários relacionados à reprodução</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Data</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead>Procedimento</TableHead>
                    <TableHead>Veterinário</TableHead>
                    <TableHead>Resultado</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {procedimentos
                    .filter((p: any) => {
                      // Filtrar apenas procedimentos reprodutivos ou associados a uma reprodução
                      const isReproducao = p.tipo.toLowerCase().includes('reprod') || 
                                           p.descricao.toLowerCase().includes('reprod') ||
                                           p.tipo.toLowerCase().includes('gestac') ||
                                           p.descricao.toLowerCase().includes('gestac');
                                           
                      const isAssociado = reproducoes.some((r: any) => r.procedimentoVetId === p.id);
                      
                      return isReproducao || isAssociado;
                    })
                    .sort((a: any, b: any) => {
                      // Ordenar por data (mais recente primeiro)
                      return new Date(b.data).getTime() - new Date(a.data).getTime();
                    })
                    .map((item: any) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{formatDate(item.data)}</TableCell>
                        <TableCell>{item.tipo}</TableCell>
                        <TableCell>{item.descricao}</TableCell>
                        <TableCell>{item.veterinario || '-'}</TableCell>
                        <TableCell>{item.resultado || '-'}</TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Sem procedimentos</AlertTitle>
                <AlertDescription>
                  Não há procedimentos veterinários reprodutivos registrados para esta égua.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Link href={`/reproducao/estatisticas?horseId=${horseId}`}>
          <Button variant="default" className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700">
            <ChartBar size={16} />
            Ver Estatísticas Detalhadas
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}