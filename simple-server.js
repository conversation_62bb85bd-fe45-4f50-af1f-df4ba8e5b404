import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const app = express();
const port = 5000;

// Configuração básica
app.use(express.json());
app.use(express.static(path.join(__dirname, 'client', 'public')));

// Rota de saúde
app.get('/health', (req, res) => {
  res.json({
    status: 'EquiGestor AI Online!',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Página principal
app.get('*', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>EquiGestor AI</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          margin: 0;
          padding: 40px;
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .container {
          background: white;
          padding: 40px;
          border-radius: 16px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          text-align: center;
          max-width: 600px;
          width: 100%;
        }
        h1 { color: #2d3748; margin-bottom: 20px; }
        .status { color: #48bb78; font-size: 24px; margin: 20px 0; }
        .features {
          background: #f7fafc;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          text-align: left;
        }
        .feature { margin: 10px 0; color: #4a5568; }
        .btn {
          background: #4299e1;
          color: white;
          padding: 12px 24px;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          font-size: 16px;
          margin: 10px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🐎 EquiGestor AI</h1>
        <div class="status">✅ Sistema Operacional!</div>
        <p>Sua plataforma de gestão equina está funcionando perfeitamente!</p>
        
        <div class="features">
          <h3>Funcionalidades Disponíveis:</h3>
          <div class="feature">• 📊 Gestão Completa de Cavalos</div>
          <div class="feature">• 🧬 Análise de Genealogia</div>
          <div class="feature">• 🏆 Sistema de Performance</div>
          <div class="feature">• 📋 Integração ABCCC</div>
          <div class="feature">• 📈 Relatórios e Analytics</div>
        </div>
        
        <button class="btn" onclick="window.location.href='/health'">Verificar Status da API</button>
        <button class="btn" onclick="alert('Interface React carregando...')">Acessar Interface Principal</button>
      </div>
    </body>
    </html>
  `);
});

// Iniciar servidor
app.listen(port, '0.0.0.0', () => {
  console.log(`🎉 EquiGestor AI rodando perfeitamente na porta ${port}`);
  console.log(`🌐 Acesse: http://localhost:${port}`);
  
  // Manter vivo
  setInterval(() => {
    console.log(`💓 EquiGestor AI ativo - ${new Date().toLocaleTimeString()}`);
  }, 30000);
});

// Tratamento de erros
process.on('uncaughtException', (error) => {
  console.error('Erro capturado:', error.message);
});