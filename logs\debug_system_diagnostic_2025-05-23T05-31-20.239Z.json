{"timestamp": "2025-05-23T05:31:20.239Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 418299904, "heapTotal": 135413760, "heapUsed": 109318328, "external": 8863959, "arrayBuffers": 636540}, "uptime": 4.475692067, "cpuUsage": {"user": 3416266, "system": 405981}, "resourceUsage": {"userCPUTime": 3416273, "systemCPUTime": 405982, "maxRSS": 408496, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 115438, "majorPageFault": 0, "swappedOut": 0, "fsRead": 8, "fsWrite": 112, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 10449, "involuntaryContextSwitches": 1646}}