Estou desenvolvendo um sistema chamado EquiGestor, inspirado nas telas do sistema EquinoGestor. Quero que você me ajude a criar a estrutura do sistema com React no frontend, Node.js com Express no backend, banco de dados local (SQLite ou JSON) e armazenamento local de arquivos.

O visual e a navegação devem seguir o padrão mostrado nas imagens: menu lateral à esquerda com ícones agrupados, layout com abas por seção (cadastro, complemento, genealogia, etc), barras superiores com botões de salvar, excluir, imprimir, etc.

📦 Estrutura esperada do projeto:
Frontend em React com React Router e TailwindCSS para o estilo (ou CSS organizado e responsivo)

Backend em Node.js com Express

Banco de dados local (usar SQLite ou JSON)

Armazenamento local de arquivos (pasta /uploads) com multer

Organização modular: controllers, services, rotas, componentes

🔹 Módulos e Funcionalidades
1. Cadastro de Animais
Layout com abas: Cadastro | Associação | Complemento | Genealogia | Dados de Venda | Website

Campos principais:

Nome, raça, nascimento, idade, sexo, pelagem, temperamento

Criador, proprietário, valor da diária, status (ativo/vendido/falecido)

Foto de capa com botão “Adicionar Foto” (salvar imagem em /uploads)

Exibição de idade formatada (ex: 4 anos, 5 meses e 20 dias)

Identificação por chip, código de controle do criador

Genealogia: exibir estrutura em grade para inserir pai, mãe, avôs, receptora, com campos clicáveis (“clique para adicionar”)

Complemento:

Animal segurado: sim/não, passaporte e validade

Método de concepção: IA, Monta Natural, etc.

Desmame: sim/não, data

Upload de documentos com validade

2. Procedimentos e Controle Sanitário
Manejos como: vacinações, vermifugações, exames, tratamentos, ferrageamentos

Categorias: geral, clínico, odontológico, reprodução, cirurgia, etc.

Campos:

Tipo, animal, data, observações, documento anexo (PDF ou imagem), responsável

Interface com filtros por tipo, mês, ano

Alerta visual:

Vencido = vermelho

Próximo vencimento (3 dias) = amarelo

3. Dados Biométricos
Layout com ilustração lateral do cavalo + campos ao lado com as medidas:

Altura na cernelha, altura da garupa, perímetro torácico, comprimento do dorso, perímetro de canela, cabeça, pescoço, etc.

Calcular e exibir os índices corporais automaticamente:

IC = D / J * 100 (índice corporal)

IM = média de 3 medidas (mesoscópico)

IDT = MP / 100 (índice dígito-torácico)

Salvar histórico por data

4. Reprodução e Cobrições
Controle de cobrições, inseminações, coleta de sêmen

Campos:

Macho, fêmea, método de concepção, data, situação (aguardando, confirmada), anotação

Registro de coleta de sêmen: animal, doses, destinatário, data limite, lembrete em agenda

5. Veterinário
Agrupamento de:

Procedimentos realizados, registros clínicos, exames laboratoriais, vacinação, vermifugação, ferrageamentos

Filtros de busca por tipo, animal, mês, ano

Upload de laudos ou arquivos médicos

6. Estoque e Nutrição
Itens agrupados por categoria: antibióticos, suplementos, ferramentas, alimentos, sêmen fresco/congelado

Módulo de:

Entradas

Saídas

Relatórios de consumo

7. Agenda e Alertas
Sistema simples de agenda com lembretes por animal, tarefa ou reprodução

Visual em calendário mensal ou lista

Integração com procedimentos para gerar lembrete automático

8. Relatórios e Estatísticas
Relatórios disponíveis:

Animais do plantel

Curva de crescimento e peso

Entrada e saída de animais

Reprodutivo e serviços veterinários

Estatísticas no dashboard:

Total de cavalos, ativos, vendidos

Procedimentos feitos no mês

Médias de peso, índice corporal

Top animais com mais registros

🔧 Especificações Técnicas
Upload de arquivos: use multer no backend e salve arquivos em /uploads

Rotas REST no backend: /api/animais, /api/procedimentos, /api/biometria, etc.

Banco local: usar SQLite com better-sqlite3 ou JSON simples com lowdb

Layout responsivo (priorize tablets e desktop) com organização limpa

Menus laterais com ícones e seções colapsáveis (como nas imagens)

Componentes reutilizáveis para formulários e uploads

Código bem estruturado, separado por pastas e comentado

Se possível, gere:

A estrutura do projeto completa com arquivos separados

Alguns exemplos de dados mock (ex: 2 animais cadastrados)

Rotas de backend prontas para uso com Postman ou fetch

💬 Extras:
Se a IA puder, peça para:

Me entregar primeiro os arquivos iniciais do frontend com layout e rotas básicas + o backend com rotas de animais funcionando, e depois vamos expandindo por módulo.