import crypto from 'crypto';
import bcrypt from 'bcrypt';

/**
 * Módulo de segurança para o EquiGestor
 * 
 * Contém funções úteis para criptografia, geração de tokens
 * e verificação de permissões.
 */

// Número de rodadas para o algoritmo bcrypt (maior = mais seguro, mas mais lento)
const SALT_ROUNDS = 10;

// Duração padrão do token (24 horas em milissegundos)
const TOKEN_DURATION = 24 * 60 * 60 * 1000;

/**
 * Gera um hash seguro para uma senha usando bcrypt
 * 
 * @param password Senha em texto plano
 * @returns Senha hasheada com bcrypt
 */
export const hashPassword = async (password: string): Promise<string> => {
  return await bcrypt.hash(password, SALT_ROUNDS);
};

/**
 * Compara uma senha em texto plano com uma senha hasheada
 * 
 * @param plain Senha em texto plano
 * @param hashed Senha hasheada para comparação
 * @returns True se a senha for v<PERSON>lid<PERSON>, false caso contrário
 */
export const comparePassword = async (plain: string, hashed: string): Promise<boolean> => {
  // Para compatibilidade com senhas antigas
  if (!hashed.includes('.')) {
    return plain === hashed;
  }
  
  return await bcrypt.compare(plain, hashed);
};

/**
 * Gera um token de autenticação seguro
 * 
 * @returns Token de autenticação (string hexadecimal)
 */
export const generateToken = (): string => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * Verifica se um token é válido com base no seu tamanho
 * 
 * @param token Token a ser verificado
 * @returns True se o token for válido, false caso contrário
 */
export const isValidToken = (token: string | undefined): boolean => {
  // Verificação básica - em produção, verificaríamos em um banco de dados
  return Boolean(token && token.length >= 32);
};

/**
 * Calcula a data de expiração de um token com base na duração padrão
 * 
 * @returns Timestamp de expiração (em milissegundos)
 */
export const calculateTokenExpiration = (): number => {
  return Date.now() + TOKEN_DURATION;
};

/**
 * Verifica se um timestamp de expiração ainda é válido
 * 
 * @param expiration Timestamp de expiração a verificar
 * @returns True se o token ainda for válido, false se expirado
 */
export const isTokenExpired = (expiration: number): boolean => {
  return Date.now() > expiration;
};

/**
 * Formatar um objeto de resposta para autenticação bem-sucedida
 * 
 * @param user Objeto do usuário (sem a senha)
 * @returns Objeto formatado com user, token e informações de segurança para resposta HTTP
 */
export const formatAuthResponse = (user: any): { 
  user: any, 
  token: string, 
  expiration: number,
  expirationFormatted: string,
  security: {
    lastLogin?: string,
    passwordExpiration?: string,
    sessionInfo: {
      ip?: string,
      userAgent?: string,
      location?: string
    },
    securityTips: string[]
  }
} => {
  // Remover a senha do objeto de usuário
  const { password, ...userWithoutPassword } = user;
  
  // Gerar um novo token de autenticação
  const token = generateToken();
  
  // Calcular a expiração do token
  const expiration = calculateTokenExpiration();
  
  // Formatar a data de expiração para exibição amigável
  const expirationDate = new Date(expiration);
  const expirationFormatted = expirationDate.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
  
  // Calcular a expiração da senha (90 dias a partir de agora)
  const passwordExpirationDate = new Date();
  passwordExpirationDate.setDate(passwordExpirationDate.getDate() + 90);
  const passwordExpiration = passwordExpirationDate.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
  
  // Dicas de segurança rotativas
  const securityTips = [
    "Nunca compartilhe seu login e senha com terceiros.",
    "Use senhas diferentes para cada serviço que você utiliza.",
    "Ative a autenticação de dois fatores sempre que possível.",
    "Verifique regularmente as atividades de sua conta para detectar acessos não autorizados.",
    "Troque sua senha regularmente para aumentar a segurança de sua conta.",
    "Desconfie de e-mails solicitando dados pessoais ou financeiros.",
    "Mantenha seu sistema operacional e navegador sempre atualizados."
  ];
  
  // Escolher duas dicas aleatórias
  const randomTips = [];
  for (let i = 0; i < 2; i++) {
    const randomIndex = Math.floor(Math.random() * securityTips.length);
    randomTips.push(securityTips[randomIndex]);
    // Remover a dica escolhida para não repetir
    securityTips.splice(randomIndex, 1);
  }
  
  return {
    user: {
      ...userWithoutPassword,
      accessLevel: user.accessLevel || 'user',
      isActive: user.isActive !== false
    },
    token,
    expiration,
    expirationFormatted,
    security: {
      lastLogin: user.lastLogin ? new Date(user.lastLogin).toLocaleString('pt-BR') : undefined,
      passwordExpiration,
      sessionInfo: {
        // Em uma implementação real, capturamos estas informações da requisição
        ip: "Informação disponível na versão completa",
        userAgent: "Informação disponível na versão completa",
        location: "Informação disponível na versão completa"
      },
      securityTips: randomTips
    }
  };
};