{"timestamp": "2025-05-23T21:45:11.686Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 407986176, "heapTotal": 123142144, "heapUsed": 103925392, "external": 8972453, "arrayBuffers": 307553}, "uptime": 2.010176507, "cpuUsage": {"user": 2912486, "system": 379158}, "resourceUsage": {"userCPUTime": 2912536, "systemCPUTime": 379164, "maxRSS": 398424, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105872, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8616, "involuntaryContextSwitches": 3032}}