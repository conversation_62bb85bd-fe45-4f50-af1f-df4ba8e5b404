{"timestamp": "2025-05-16T13:03:36.615Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 408530944, "heapTotal": 120889344, "heapUsed": 72565920, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.981453528, "cpuUsage": {"user": 2860318, "system": 342754}, "resourceUsage": {"userCPUTime": 2860378, "systemCPUTime": 342754, "maxRSS": 398956, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104833, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7384, "involuntaryContextSwitches": 2794}}