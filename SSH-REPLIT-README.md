# 🔑 Scripts para Configuração SSH do Replit

Este repositório contém scripts automatizados para gerar e configurar chaves SSH para uso com o Replit.

## 📁 Arquivos Disponíveis

### 1. `setup-replit-ssh.ps1` (PowerShell - Recomendado)
Script completo em PowerShell com todas as funcionalidades:
- ✅ Verificação de dependências
- ✅ Criação segura de diretórios
- ✅ Geração de chave SSH ed25519
- ✅ Cópia automática para área de transferência
- ✅ Abertura automática do navegador
- ✅ Tratamento de erros robusto
- ✅ Interface colorida e amigável

### 2. `setup-replit-ssh.bat` (Batch)
Versão em batch para compatibilidade:
- ✅ Funciona em sistemas com restrições PowerShell
- ✅ Mesmas funcionalidades principais
- ✅ Interface simples

### 3. `replit-ssh-quick.bat` (Batch Simplificado)
Versão rápida baseada no comando original:
- ✅ Execução rápida
- ✅ Baseado no comando fornecido
- ✅ Mínimo de interações

## 🚀 Como Usar

### Opção 1: PowerShell (Recomendado)
```powershell
# Executar com permissões padrão
.\setup-replit-ssh.ps1

# Forçar regeneração de chave existente
.\setup-replit-ssh.ps1 -Force

# Usar nome personalizado para a chave
.\setup-replit-ssh.ps1 -KeyName "minha-chave-replit"
```

### Opção 2: Batch
```cmd
# Executar script completo
setup-replit-ssh.bat

# Ou executar versão rápida
replit-ssh-quick.bat
```

### Opção 3: Comando Original Melhorado
```cmd
# Baseado no seu comando original, mas melhorado
if not exist "%USERPROFILE%\.ssh\replit.pub" ssh-keygen -t ed25519 -f "%USERPROFILE%\.ssh\replit" -q -N "" && type "%USERPROFILE%\.ssh\replit.pub" | clip
```

## 📋 Pré-requisitos

- **Windows 10/11**
- **OpenSSH** ou **Git for Windows** instalado
  - Instalar via: `winget install Git.Git`
  - Ou baixar do: https://git-scm.com/download/win

## 🎯 Processo Automático

1. **Executa o script** → Gera chave SSH ed25519
2. **Copia a chave** → Área de transferência automaticamente
3. **Abre o navegador** → Página de configuração SSH do Replit
4. **Cole a chave** → No campo "Key" do Replit
5. **Pronto!** → SSH configurado

## 🔒 Segurança

- ✅ Chaves geradas localmente
- ✅ Chave privada nunca é compartilhada
- ✅ Permissões corretas no diretório `.ssh`
- ✅ Algoritmo ed25519 (mais seguro)

## 📍 Localização dos Arquivos

Após execução, os arquivos ficam em:
```
%USERPROFILE%\.ssh\
├── replit      (chave privada - MANTER SEGURA)
└── replit.pub  (chave pública - para o Replit)
```

## 🛠️ Solução de Problemas

### Erro: "ssh-keygen não encontrado"
```cmd
# Instalar Git for Windows
winget install Git.Git

# Ou instalar OpenSSH
Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******
```

### Erro: "Não foi possível copiar para área de transferência"
- Copie manualmente a chave mostrada no terminal
- A chave pública está em: `%USERPROFILE%\.ssh\replit.pub`

### Chave já existe
- Use a opção `-Force` no PowerShell para regenerar
- Ou delete manualmente os arquivos em `%USERPROFILE%\.ssh\replit*`

## 🌐 Links Úteis

- **Configurar SSH no Replit**: https://replit.com/account#ssh-keys
- **Documentação SSH Replit**: https://docs.replit.com/programming-ide/using-git-on-replit
- **Git for Windows**: https://git-scm.com/download/win

## 💡 Dicas

1. **Mantenha a chave privada segura** - nunca compartilhe o arquivo `replit`
2. **Use nomes descritivos** - ao adicionar no Replit, use nomes como "Meu PC", "Notebook Trabalho"
3. **Backup das chaves** - considere fazer backup seguro das chaves importantes
4. **Uma chave por dispositivo** - gere chaves diferentes para cada computador

---

**✨ Criado para facilitar a configuração SSH com Replit!**
