{"timestamp": "2025-05-15T16:20:05.075Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 392486912, "heapTotal": 106364928, "heapUsed": 65827064, "external": 7016254, "arrayBuffers": 60485}, "uptime": 2.907845703, "cpuUsage": {"user": 2619806, "system": 375370}, "resourceUsage": {"userCPUTime": 2619861, "systemCPUTime": 375370, "maxRSS": 383288, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102173, "majorPageFault": 0, "swappedOut": 0, "fsRead": 21872, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8146, "involuntaryContextSwitches": 7885}}