{"timestamp": "2025-05-15T22:55:58.907Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 263512064, "heapTotal": 116170752, "heapUsed": 72411496, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.743329384, "cpuUsage": {"user": 2686284, "system": 374800}, "resourceUsage": {"userCPUTime": 2686357, "systemCPUTime": 374800, "maxRSS": 295748, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104125, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 112, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9186, "involuntaryContextSwitches": 3418}}