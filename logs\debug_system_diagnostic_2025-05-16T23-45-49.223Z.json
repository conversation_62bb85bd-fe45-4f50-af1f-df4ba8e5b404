{"timestamp": "2025-05-16T23:45:49.222Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 401420288, "heapTotal": 113287168, "heapUsed": 73563032, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.696641246, "cpuUsage": {"user": 2800175, "system": 310830}, "resourceUsage": {"userCPUTime": 2800235, "systemCPUTime": 310837, "maxRSS": 392012, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104233, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8130, "involuntaryContextSwitches": 1942}}