{"timestamp": "2025-05-19T17:53:24.466Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 307019776, "heapTotal": 165429248, "heapUsed": 144395568, "external": 13469988, "arrayBuffers": 897478}, "uptime": 297.495099238, "cpuUsage": {"user": 17495780, "system": 1222981}, "resourceUsage": {"userCPUTime": 17495786, "systemCPUTime": 1222981, "maxRSS": 724872, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 276244, "majorPageFault": 0, "swappedOut": 0, "fsRead": 296, "fsWrite": 1312, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 27340, "involuntaryContextSwitches": 13304}}