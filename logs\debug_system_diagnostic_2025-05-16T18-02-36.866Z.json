{"timestamp": "2025-05-16T18:02:36.866Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 407097344, "heapTotal": 114614272, "heapUsed": 89365000, "external": 8444702, "arrayBuffers": 257466}, "uptime": 4.809243348, "cpuUsage": {"user": 3225899, "system": 452957}, "resourceUsage": {"userCPUTime": 3225965, "systemCPUTime": 452957, "maxRSS": 397556, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106487, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 160, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8154, "involuntaryContextSwitches": 15468}}