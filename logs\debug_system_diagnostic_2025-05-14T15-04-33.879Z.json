{"timestamp": "2025-05-14T15:04:33.879Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 380411904, "heapTotal": 103157760, "heapUsed": 62275104, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.878598213, "cpuUsage": {"user": 2449889, "system": 314590}, "resourceUsage": {"userCPUTime": 2449947, "systemCPUTime": 314597, "maxRSS": 371496, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99357, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6589, "involuntaryContextSwitches": 5601}}