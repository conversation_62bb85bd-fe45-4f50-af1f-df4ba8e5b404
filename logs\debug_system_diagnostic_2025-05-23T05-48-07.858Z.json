{"timestamp": "2025-05-23T05:48:07.857Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 308293632, "heapTotal": 164024320, "heapUsed": 154670416, "external": 13925032, "arrayBuffers": 1352154}, "uptime": 849.991358093, "cpuUsage": {"user": 30902794, "system": 2306162}, "resourceUsage": {"userCPUTime": 30902881, "systemCPUTime": 2306162, "maxRSS": 613560, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 336191, "majorPageFault": 6, "swappedOut": 0, "fsRead": 104144, "fsWrite": 3296, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 45158, "involuntaryContextSwitches": 35297}}