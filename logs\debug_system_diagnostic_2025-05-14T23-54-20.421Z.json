{"timestamp": "2025-05-14T23:54:20.421Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 387796992, "heapTotal": 101863424, "heapUsed": 72363592, "external": 6840274, "arrayBuffers": 60485}, "uptime": 1.848644663, "cpuUsage": {"user": 2470615, "system": 344978}, "resourceUsage": {"userCPUTime": 2470685, "systemCPUTime": 344978, "maxRSS": 378708, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99748, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6577, "involuntaryContextSwitches": 4054}}