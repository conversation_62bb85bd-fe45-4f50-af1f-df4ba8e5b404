{"timestamp": "2025-05-16T17:27:39.074Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 391045120, "heapTotal": 102801408, "heapUsed": 81240808, "external": 8281151, "arrayBuffers": 265658}, "uptime": 1.747307985, "cpuUsage": {"user": 2741633, "system": 310093}, "resourceUsage": {"userCPUTime": 2741692, "systemCPUTime": 310093, "maxRSS": 381880, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103096, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8685, "involuntaryContextSwitches": 2622}}