{"timestamp": "2025-05-15T21:28:26.766Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 283353088, "heapTotal": 110927872, "heapUsed": 72495888, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.890000056, "cpuUsage": {"user": 2693320, "system": 389104}, "resourceUsage": {"userCPUTime": 2693361, "systemCPUTime": 389110, "maxRSS": 276712, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101952, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9043, "involuntaryContextSwitches": 4306}}