{"timestamp": "2025-05-15T18:13:54.381Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 330399744, "heapTotal": 192077824, "heapUsed": 169611936, "external": 14599513, "arrayBuffers": 1633051}, "uptime": 74.295710277, "cpuUsage": {"user": 16709292, "system": 1035420}, "resourceUsage": {"userCPUTime": 16709298, "systemCPUTime": 1035420, "maxRSS": 898956, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 262854, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 640, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 24352, "involuntaryContextSwitches": 12516}}