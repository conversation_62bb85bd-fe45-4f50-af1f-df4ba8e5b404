{"timestamp": "2025-05-15T21:30:50.411Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404000768, "heapTotal": 115384320, "heapUsed": 72372168, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.75514814, "cpuUsage": {"user": 2723693, "system": 370868}, "resourceUsage": {"userCPUTime": 2723761, "systemCPUTime": 370868, "maxRSS": 394532, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102976, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8646, "involuntaryContextSwitches": 2637}}