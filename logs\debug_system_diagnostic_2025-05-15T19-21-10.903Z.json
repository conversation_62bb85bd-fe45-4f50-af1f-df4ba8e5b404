{"timestamp": "2025-05-15T19:21:10.902Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404455424, "heapTotal": 114397184, "heapUsed": 72135768, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.753953134, "cpuUsage": {"user": 3053638, "system": 374156}, "resourceUsage": {"userCPUTime": 3053694, "systemCPUTime": 374163, "maxRSS": 394976, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103555, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8351, "involuntaryContextSwitches": 9807}}