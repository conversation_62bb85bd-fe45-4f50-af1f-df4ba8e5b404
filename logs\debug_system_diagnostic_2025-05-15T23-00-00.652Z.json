{"timestamp": "2025-05-15T23:00:00.651Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 411090944, "heapTotal": 118546432, "heapUsed": 92569144, "external": 8490123, "arrayBuffers": 243725}, "uptime": 1.844848789, "cpuUsage": {"user": 2692027, "system": 380740}, "resourceUsage": {"userCPUTime": 2692068, "systemCPUTime": 380746, "maxRSS": 401456, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107071, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 144, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8124, "involuntaryContextSwitches": 3502}}