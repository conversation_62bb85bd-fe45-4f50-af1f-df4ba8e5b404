{"timestamp": "2025-05-15T21:18:08.034Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403537920, "heapTotal": 115646464, "heapUsed": 72419024, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.879810418, "cpuUsage": {"user": 2821042, "system": 369685}, "resourceUsage": {"userCPUTime": 2821098, "systemCPUTime": 369692, "maxRSS": 394080, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103319, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8406, "involuntaryContextSwitches": 4178}}