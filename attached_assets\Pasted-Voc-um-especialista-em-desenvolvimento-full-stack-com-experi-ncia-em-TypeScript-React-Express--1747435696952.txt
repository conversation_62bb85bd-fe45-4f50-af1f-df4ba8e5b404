Você é um especialista em desenvolvimento full-stack com experiência em TypeScript, React, Express, e PostgreSQL. Tenho um sistema para gerenciamento de dados de cavalos, focado em registros genealógicos da ABCCC (Associação Brasileira de Criadores de Cavalos Crioulos). O sistema inclui cinco componentes principais:

1. **M<PERSON><PERSON>lo de Scraping (abccc-scraper.ts)**: Um módulo TypeScript que realiza web scraping no site da ABCCC usando axios e cheerio para extrair dados de cavalos com base em números de registro.
2. **Componente React (ImportadorPdfABCCC.tsx)**: Um componente React para upload, pré-visualização e importação de PDFs de registros genealógicos, integrado com React Query e componentes UI (Shadcn/UI).
3. **Rotas da API Express (routes.ts)**: Um backend Express com endpoints para gerenciamento de dados, upload de arquivos, e importação de PDFs, usando Drizzle ORM e Multer.
4. **Hook React Query (useABCCCImport.ts)**: Um hook personalizado que faz chamadas à API para buscar dados de cavalos da ABCCC, com notificações via toast.
5. **Módulo de Importação de PDFs (abccc-import-service.ts)**: Um módulo referenciado (não fornecido diretamente) que processa PDFs e integra dados com o banco.

Fiz uma análise detalhada desses componentes e identifiquei pontos fortes, problemas e sugestões de melhorias. Sua tarefa é revisar o código fornecido, corrigir os problemas identificados e implementar as melhorias sugeridas. Abaixo, listo os problemas e melhorias para cada componente, seguidos por instruções gerais para o sistema. Por favor, modifique o código existente, adicione novos arquivos se necessário, e forneça explicações claras para cada alteração. Use TypeScript, siga boas práticas, e garanta que as alterações sejam compatíveis com a arquitetura existente.

---

### **1. Módulo de Scraping (abccc-scraper.ts)**

**Problemas Identificados:**
- Dependência de seletores HTML específicos, vulnerável a mudanças no site da ABCCC.
- Tokens hardcoded para acesso ao endpoint de busca, que podem expirar.
- Timeout fixo de 15 segundos, sem configuração dinâmica.
- Ausência de cache para resultados de scraping, causando requisições repetidas.
- URLs hardcoded, que podem ficar obsoletas.

**Melhorias Solicitadas:**
1. Implemente um sistema de cache usando `node-cache` para armazenar resultados de scraping por 24 horas.
2. Adicione testes automatizados para verificar a validade dos seletores HTML e implemente um fallback para scraping por palavras-chave.
3. Permita configuração dinâmica do timeout via variável de ambiente (`SCRAPING_TIMEOUT`).
4. Substitua tokens hardcoded por um mecanismo de renovação automática ou configuração via banco de dados.
5. Adicione rate limiting interno (ex.: máximo de 5 requisições por segundo) usando uma biblioteca como `bottleneck`.
6. Retorne dados parciais com aviso se a ficha detalhada falhar.

**Tarefas Específicas:**
- Modifique `abccc-scraper.ts` para incluir cache com `node-cache`.
- Crie um arquivo de testes (`abccc-scraper.test.ts`) com Jest para validar seletores.
- Adicione uma variável de ambiente para timeout e configure-a no `axios`.
- Crie uma tabela `tokens` no banco para gerenciar tokens dinamicamente e atualize a lógica de busca.
- Integre `bottleneck` para limitar requisições.
- Atualize `extrairDadosFichaDetalhada` para retornar dados parciais com um campo `partial: true` se a extração completa falhar.

---

### **2. Componente React (ImportadorPdfABCCC.tsx)**

**Problemas Identificados:**
- Uso de `localStorage` para obter `userId`, que é inseguro.
- Falta de validação client-side para dados retornados pela API.
- Não limpa o arquivo selecionado após importação bem-sucedida.
- Mensagens de erro genéricas para timeouts.
- Acessibilidade limitada (falta de ARIA e suporte a teclado).

**Melhorias Solicitadas:**
1. Substitua `localStorage` por um contexto de autenticação (`AuthContext`) usando JWT.
2. Adicione validação com Zod para `dadosPreVisualizacao` e `dadosExtraidos` antes de renderizar.
3. Resete o estado `selectedFile` após importação bem-sucedida.
4. Exiba mensagens específicas para erros de timeout (ex.: "A busca demorou muito. Tente novamente.").
5. Adicione atributos ARIA (ex.: `aria-label`) e suporte a navegação por teclado nos botões e tabelas.
6. Implemente paginação na tabela de familiares usando `react-table` para PDFs com muitos registros.
7. Adicione um diálogo de confirmação antes da importação final, mostrando um resumo dos dados.

**Tarefas Específicas:**
- Crie um `AuthContext.ts` para gerenciar o `userId` e integre-o no componente.
- Adicione um schema Zod para `ResumoImportacao` e valide os dados em `onSuccess` das mutações.
- Modifique o `onSuccess` de `uploadMutation` para resetar `selectedFile`.
- Atualize o tratamento de erros em `previewMutation` e `uploadMutation` para mensagens específicas de timeout.
- Adicione atributos ARIA (ex.: `aria-label="Pré-visualizar arquivo"`) nos botões e `role="grid"` na tabela.
- Integre `react-table` com paginação na tabela de familiares.
- Adicione um componente `<Dialog>` para confirmação antes de chamar `iniciarProcessamento`.

---

### **3. Rotas da API Express (routes.ts)**

**Problemas Identificados:**
- Arquivos enviados não são removidos após importação, ocupando espaço.
- Inconsistência na obtenção de `userId` (corpo vs. cabeçalho).
- Uso de SQL direto em algumas rotas, em vez de Drizzle ORM.
- Falta de rate limiting, permitindo abusos.
- Referência obsoleta ao erro 406 para registros específicos.
- Rota de logs expõe dados sensíveis sem restrição de acesso.

**Melhorias Solicitadas:**
1. Implemente uma política de limpeza de arquivos temporários (ex.: apagar PDFs após 24 horas ou importação bem-sucedida) usando `node-cron`.
2. Padronize a obtenção de `userId` exclusivamente do token JWT no middleware `authenticateUser`.
3. Migre consultas SQL diretas (ex.: `/api/pelagens`) para Drizzle ORM.
4. Adicione rate limiting com `express-rate-limit` para rotas sensíveis (ex.: `/api/abccc/cavalo/:registro`).
5. Remova a lógica relacionada ao erro 406, já que a solução é robusta para todos os registros.
6. Restrinja a rota `/api/importar-pdf-crioulo/logs` a usuários administrativos verificando uma role no JWT.
7. Adicione paginação com `limit` e `offset` em rotas que retornam listas (ex.: `/api/cavalos`).
8. Crie uma tabela de auditoria para registrar ações críticas (ex.: importação, exclusão).

**Tarefas Específicas:**
- Adicione `node-cron` para agendar a exclusão de PDFs em `abcccPdfsDir` após 24 horas.
- Modifique `authenticateUser` para extrair `userId` do JWT e remova verificações de `req.body.userId`.
- Converta a consulta em `/api/pelagens` para usar Drizzle ORM com a tabela `pelagens`.
- Integre `express-rate-limit` com limite de 100 requisições por 15 minutos para rotas de scraping.
- Remova referências ao erro 406 em `/api/abccc/cavalo/:registro`.
- Adicione verificação de `role: 'admin'` no JWT para a rota de logs.
- Atualize rotas como `/api/cavalos` para aceitar query params `limit` e `offset`.
- Crie uma tabela `audit_logs` e registre ações em rotas críticas (ex.: `POST /api/importar-pdf-crioulo`).

---

### **4. Hook React Query (useABCCCImport.ts)**

**Problemas Identificados:**
- Cabeçalho `User-Id: '1'` hardcoded, inseguro para múltiplos usuários.
- Referência obsoleta ao erro 406.
- Ausência de retry para falhas temporárias.
- Logs excessivos com `console.log` em produção.
- Falta de validação robusta para a estrutura de `genealogia`.

**Melhorias Solicitadas:**
1. Obtenha o `userId` de um contexto de autenticação (`AuthContext`).
2. Remova a lógica de tratamento do erro 406.
3. Configure retry em `useMutation` para erros temporários (ex.: 2 tentativas com delay de 1 segundo).
4. Substitua `console.log` por um logger configurável (ex.: `pino`) que seja desativado em produção.
5. Use Zod para validar a estrutura de `ABCCCCavaloData` antes de retornar os dados.
6. Aproveite o cache do React Query para evitar chamadas repetidas ao mesmo registro.

**Tarefas Específicas:**
- Integre o `userId` do `AuthContext` no cabeçalho da requisição.
- Remova o tratamento de erro 406 no `mutationFn`.
- Adicione `{ retry: 2, retryDelay: 1000 }` às opções do `useMutation`.
- Substitua `console.log` por `pino` com configuração para desativar logs em `NODE_ENV=production`.
- Crie um schema Zod para `ABCCCCavaloData` e valide os dados em `mutationFn`.
- Configure a chave de cache do React Query com `[registro]` para reutilizar resultados.

---

### **5. Módulo de Importação de PDFs (abccc-import-service.ts)**

**Nota**: O código completo não foi fornecido, mas é referenciado como `importarRegistroABCCC` e `previsualizarRegistroABCCC`. Presuma que ele usa uma biblioteca como `pdf-parse` para extrair dados e integra com `abccc-scraper`.

**Problemas Identificados (Inferidos):**
- Dependência de bibliotecas externas para parsing de PDFs, que podem falhar com documentos mal formatados.
- Possível falta de validação robusta para dados extraídos.
- Inconsistência na extração devido a variações no formato dos PDFs.
- Falta de suporte para PDFs corrompidos.

**Melhorias Solicitadas:**
1. Adicione validação com Zod para o objeto `ResumoImportacao` retornado.
2. Implemente um fallback para PDFs não legíveis (ex.: tentativa de OCR com `tesseract.js`).
3. Adicione logging detalhado com `pino` para cada etapa do processamento (ex.: número de páginas, erros de parsing).
4. Crie testes unitários com Jest para simular PDFs com diferentes formatos.
5. Cacheie dados enriquecidos do scraper usando `node-cache` para evitar chamadas repetidas.

**Tarefas Específicas:**
- Crie um schema Zod para `ResumoImportacao` e valide os dados em `importarRegistroABCCC` e `previsualizarRegistroABCCC`.
- Adicione suporte a OCR com `tesseract.js` como fallback em caso de falha com `pdf-parse`.
- Integre `pino` para registrar detalhes do processo (ex.: `logger.info("Processando página 1 do PDF")`).
- Crie um arquivo `abccc-import-service.test.ts` com testes para PDFs simulados.
- Use `node-cache` para armazenar resultados do scraper chamados dentro do serviço.

---

### **Melhorias Gerais do Sistema**

1. **Cache Distribuído**: Configure Redis para cachear resultados de scraping e dados frequentemente acessados (ex.: lista de cavalos).
2. **Autenticação Robusta**: Implemente JWT com refresh tokens e armazene o `userId` em um `AuthContext` no frontend.
3. **Monitoramento**: Adicione Prometheus e Grafana para monitorar falhas no scraping e importação de PDFs.
4. **Testes End-to-End**: Crie testes com Cypress para simular o fluxo completo (upload de PDF, scraping, salvamento).
5. **Documentação**: Gere documentação da API com OpenAPI/Swagger e crie um guia para lidar com falhas no scraping.
6. **Resiliência**: Use BullMQ para processar importações de PDFs assincronamente, evitando sobrecarga no servidor.

**Tarefas Gerais:**
- Configure Redis e integre-o com `abccc-scraper` e `routes.ts` para cache.
- Implemente um sistema de autenticação com `@nestjs/jwt` ou `jsonwebtoken` e atualize o frontend/backend.
- Configure Prometheus e Grafana, exportando métricas para falhas de scraping e importação.
- Crie um teste Cypress (`cypress/e2e/import-pdf.cy.ts`) para o fluxo de importação.
- Gere documentação OpenAPI em `docs/api.yaml` e um guia em `docs/scraping-failures.md`.
- Integre BullMQ para filas em `routes.ts` para a rota `/api/importar-pdf-crioulo`.

---

### **Instruções Adicionais**

- **Código Existente**: Use os trechos fornecidos como base. Para `abccc-import-service.ts`, crie um esboço com base nas funções referenciadas (`importarRegistroABCCC`, `previsualizarRegistroABCCC`) e presuma o uso de `pdf-parse`.
- **Boas Práticas**: Siga convenções de código limpo, use TypeScript estrito, e adicione JSDoc para funções críticas.
- **Explicações**: Para cada arquivo modificado, inclua um comentário explicando as mudanças (ex.: `// Adicionado cache com node-cache para evitar requisições repetidas`).
- **Novos Arquivos**: Crie arquivos adicionais (ex.: `AuthContext.ts`, `abccc-scraper.test.ts`) conforme necessário.
- **Testes**: Adicione testes unitários com Jest para backend e testes de integração com Cypress para frontend.
- **Segurança**: Garanta que dados sensíveis (ex.: logs, tokens) sejam protegidos e que a autenticação seja robusta.
- **Performance**: Priorize otimizações como cache, paginação, e processamento assíncrono.

**Saída Esperada**:
- Código modificado para cada arquivo (`abccc-scraper.ts`, `ImportadorPdfABCCC.tsx`, `routes.ts`, `useABCCCImport.ts`, `abccc-import-service.ts`).
- Novos arquivos para testes, contexto de autenticação, e documentação.
- Um resumo das alterações feitas, explicando como cada problema foi resolvido.
- Instruções para rodar os testes e configurar dependências adicionais (ex.: Redis, BullMQ).

**Prazo**: Forneça a solução completa, pronta para revisão, com todas as implementações e testes funcionando.

Por favor, comece a implementação e me avise se precisar de mais detalhes sobre algum componente ou preferência de configuração!