{"timestamp": "2025-05-26T20:11:27.769Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 257998848, "heapTotal": 111190016, "heapUsed": 74584328, "external": 8219482, "arrayBuffers": 243725}, "uptime": 8.991205897, "cpuUsage": {"user": 3283421, "system": 472026}, "resourceUsage": {"userCPUTime": 3283481, "systemCPUTime": 472035, "maxRSS": 289996, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105823, "majorPageFault": 10, "swappedOut": 0, "fsRead": 24248, "fsWrite": 944, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9191, "involuntaryContextSwitches": 11309}}