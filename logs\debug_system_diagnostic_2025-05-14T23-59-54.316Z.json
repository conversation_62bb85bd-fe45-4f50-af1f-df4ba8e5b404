{"timestamp": "2025-05-14T23:59:54.316Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 795889664, "heapTotal": 463892480, "heapUsed": 432558384, "external": 19433948, "arrayBuffers": 7688983}, "uptime": 16.778363758, "cpuUsage": {"user": 14846522, "system": 868228}, "resourceUsage": {"userCPUTime": 14846530, "systemCPUTime": 868228, "maxRSS": 777236, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 209165, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 312, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 17198, "involuntaryContextSwitches": 19621}}