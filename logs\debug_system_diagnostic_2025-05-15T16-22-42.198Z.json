{"timestamp": "2025-05-15T16:22:42.197Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 251785216, "heapTotal": 102129664, "heapUsed": 80632016, "external": 7066130, "arrayBuffers": 90610}, "uptime": 1.752188823, "cpuUsage": {"user": 2313749, "system": 370168}, "resourceUsage": {"userCPUTime": 2313822, "systemCPUTime": 370168, "maxRSS": 326644, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101209, "majorPageFault": 0, "swappedOut": 0, "fsRead": 3368, "fsWrite": 48, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7252, "involuntaryContextSwitches": 4022}}