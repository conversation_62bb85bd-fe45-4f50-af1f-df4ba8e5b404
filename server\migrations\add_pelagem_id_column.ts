/**
 * Migração para adicionar a coluna pelagem_id na tabela cavalos
 */
import { db } from '../db';
import { sql } from 'drizzle-orm';
import logger, { getModuleLogger } from '../logger';
import { QueryResult } from '@neondatabase/serverless';

const migrationLogger = getModuleLogger('migration-pelagem-id');

export async function addPelagemIdColumn() {
  migrationLogger.info('Iniciando migração para adicionar coluna pelagem_id na tabela cavalos');
  
  try {
    // Usar uma abordagem mais robusta para verificar se a coluna existe
    const checkColumnSql = `
      SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'cavalos' AND column_name = 'pelagem_id'
      ) AS column_exists;
    `;
    
    const result = await db.execute(sql.raw(checkColumnSql));
    const columnExists = result.rows?.[0]?.column_exists === true || 
                         result.rows?.[0]?.column_exists === 't' ||
                         result.rows?.[0]?.column_exists === 'true';
    
    if (columnExists) {
      migrationLogger.info('Coluna pelagem_id já existe. Pulando migração.');
      return true;
    }
    
    // Adiciona a coluna pelagem_id como referência à tabela pelagens
    await db.execute(sql`
      ALTER TABLE cavalos 
      ADD COLUMN pelagem_id INTEGER REFERENCES pelagens(id)
    `);
    
    migrationLogger.info('Coluna pelagem_id adicionada com sucesso.');
    return true;
  } catch (error: any) {
    migrationLogger.error(`Erro ao adicionar coluna pelagem_id: ${error.message}`);
    return false;
  }
}