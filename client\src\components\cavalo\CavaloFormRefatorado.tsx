import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { cavaloFormSchema, converterFormParaBanco } from "@shared/schema";
import { useAuth } from "@/context/AuthContext";
import { format, parse, isValid } from "date-fns";

// Schema baseado no sistema PDF funcionante
const formularioSchema = cavaloFormSchema.extend({
  userId: z.number().optional(), // Será preenchido automaticamente
});

type FormularioData = z.infer<typeof formularioSchema>;

interface CavaloFormRefatoradoProps {
  onSuccess?: (cavalo: any) => void;
  onCancel?: () => void;
  isModal?: boolean;
  dadosIniciais?: Partial<FormularioData>;
}

export function CavaloFormRefatorado({
  onSuccess,
  onCancel,
  isModal = false,
  dadosIniciais
}: CavaloFormRefatoradoProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const userId = user?.id;
  const [loading, setLoading] = useState(false);

  const form = useForm<FormularioData>({
    resolver: zodResolver(formularioSchema),
    defaultValues: {
      nome: dadosIniciais?.nome || "",
      breed: dadosIniciais?.breed || "",
      birthDate: dadosIniciais?.birthDate || null,
      peso: dadosIniciais?.peso || null,
      altura: dadosIniciais?.altura || null,
      sexo: dadosIniciais?.sexo || "nao_informado",
      pelagem: dadosIniciais?.pelagem || null,
      status: dadosIniciais?.status || "ativo",
      dataEntrada: dadosIniciais?.dataEntrada || null,
      dataSaida: dadosIniciais?.dataSaida || null,
      motivoSaida: dadosIniciais?.motivoSaida || null,
      paiNome: dadosIniciais?.paiNome || null,
      maeNome: dadosIniciais?.maeNome || null,
      numeroRegistro: dadosIniciais?.numeroRegistro || null,
      origem: dadosIniciais?.origem || null,
      criador: dadosIniciais?.criador || null,
      proprietario: dadosIniciais?.proprietario || null,
      inspetor: dadosIniciais?.inspetor || null,
      valorCompra: dadosIniciais?.valorCompra || null,
      dataCompra: dadosIniciais?.dataCompra || null,
      observacoes: dadosIniciais?.observacoes || null,
    },
  });

  const handleSubmit = async (data: FormularioData) => {
    if (!userId) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      // Converter dados do formulário para formato do banco (igual ao sistema PDF)
      const dadosBanco = converterFormParaBanco(data, userId);

      const response = await fetch("/api/cavalos", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "user-id": userId.toString(),
        },
        body: JSON.stringify(dadosBanco),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erro ao cadastrar cavalo");
      }

      const cavalo = await response.json();

      toast({
        title: "Sucesso",
        description: "Cavalo cadastrado com sucesso!",
        variant: "default",
      });

      if (onSuccess) {
        onSuccess(cavalo);
      }

      // Limpar formulário se não for modal
      if (!isModal) {
        form.reset();
      }
    } catch (error: any) {
      console.error("Erro ao cadastrar cavalo:", error);
      toast({
        title: "Erro",
        description: error.message || "Erro inesperado ao cadastrar cavalo",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Cadastro de Cavalo</CardTitle>
        <CardDescription>
          Preencha os dados do cavalo. Apenas nome e raça são obrigatórios.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Dados Básicos */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="nome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome *</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome do cavalo" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="breed"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Raça *</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: Crioulo" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="birthDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Nascimento</FormLabel>
                    <FormControl>
                      <Input 
                        type="date" 
                        {...field} 
                        value={field.value || ""} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sexo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sexo</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o sexo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="nao_informado">Não informado</SelectItem>
                        <SelectItem value="Macho">Macho</SelectItem>
                        <SelectItem value="Fêmea">Fêmea</SelectItem>
                        <SelectItem value="Castrado">Castrado</SelectItem>
                        <SelectItem value="Garanhão">Garanhão</SelectItem>
                        <SelectItem value="Égua">Égua</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pelagem"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pelagem</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Ex: Tordilho" 
                        {...field} 
                        value={field.value || ""} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="numeroRegistro"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Número de Registro</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Ex: ABCCC 12345" 
                        {...field} 
                        value={field.value || ""} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Dados Físicos */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="peso"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Peso (kg)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="Ex: 450" 
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="altura"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Altura (m)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.01"
                        placeholder="Ex: 1.45" 
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Genealogia */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="paiNome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome do Pai</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Nome do pai" 
                        {...field} 
                        value={field.value || ""} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="maeNome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Mãe</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Nome da mãe" 
                        {...field} 
                        value={field.value || ""} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Proprietário e Criador */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="criador"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Criador</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Nome do criador" 
                        {...field} 
                        value={field.value || ""} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="proprietario"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Proprietário</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Nome do proprietário" 
                        {...field} 
                        value={field.value || ""} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Observações */}
            <FormField
              control={form.control}
              name="observacoes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Observações</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Observações sobre o cavalo..." 
                      {...field} 
                      value={field.value || ""} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Botões */}
            <div className="flex justify-end space-x-4">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancelar
                </Button>
              )}
              <Button type="submit" disabled={loading}>
                {loading ? "Cadastrando..." : "Cadastrar Cavalo"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}