{"timestamp": "2025-05-19T19:36:41.141Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 250671104, "heapTotal": 110555136, "heapUsed": 83299072, "external": 8289376, "arrayBuffers": 235533}, "uptime": 3.296969522, "cpuUsage": {"user": 2797363, "system": 434993}, "resourceUsage": {"userCPUTime": 2797407, "systemCPUTime": 434995, "maxRSS": 291304, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107232, "majorPageFault": 2, "swappedOut": 0, "fsRead": 52088, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8466, "involuntaryContextSwitches": 6431}}