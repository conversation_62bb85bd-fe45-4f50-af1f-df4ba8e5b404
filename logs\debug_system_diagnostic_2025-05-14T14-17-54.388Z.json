{"timestamp": "2025-05-14T14:17:54.388Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 317579264, "heapTotal": 136671232, "heapUsed": 112266328, "external": 8981326, "arrayBuffers": 76869}, "uptime": 1.762659113, "cpuUsage": {"user": 2979748, "system": 432806}, "resourceUsage": {"userCPUTime": 2979748, "systemCPUTime": 432908, "maxRSS": 310136, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 109535, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 13013, "involuntaryContextSwitches": 2051}}