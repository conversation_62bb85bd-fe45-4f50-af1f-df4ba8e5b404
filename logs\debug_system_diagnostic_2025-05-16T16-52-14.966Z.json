{"timestamp": "2025-05-16T16:52:14.965Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 407805952, "heapTotal": 120102912, "heapUsed": 72574376, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.39447571, "cpuUsage": {"user": 3134467, "system": 408531}, "resourceUsage": {"userCPUTime": 3134510, "systemCPUTime": 408531, "maxRSS": 398248, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107195, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 144, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9267, "involuntaryContextSwitches": 6508}}