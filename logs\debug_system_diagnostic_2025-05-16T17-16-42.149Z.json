{"timestamp": "2025-05-16T17:16:42.148Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 287973376, "heapTotal": 111714304, "heapUsed": 72468648, "external": 8209316, "arrayBuffers": 233559}, "uptime": 2.171585231, "cpuUsage": {"user": 2989057, "system": 381422}, "resourceUsage": {"userCPUTime": 2989126, "systemCPUTime": 381422, "maxRSS": 281224, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102919, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 176, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8556, "involuntaryContextSwitches": 5550}}