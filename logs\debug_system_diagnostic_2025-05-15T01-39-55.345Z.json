{"timestamp": "2025-05-15T01:39:55.345Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 379469824, "heapTotal": 91099136, "heapUsed": 70238496, "external": 6890744, "arrayBuffers": 106994}, "uptime": 1.365040486, "cpuUsage": {"user": 2169623, "system": 267728}, "resourceUsage": {"userCPUTime": 2169679, "systemCPUTime": 267735, "maxRSS": 370576, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 97533, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5888, "involuntaryContextSwitches": 1847}}