{"timestamp": "2025-05-16T23:19:12.442Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403480576, "heapTotal": 116957184, "heapUsed": 73417224, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.772890538, "cpuUsage": {"user": 2771973, "system": 360341}, "resourceUsage": {"userCPUTime": 2772008, "systemCPUTime": 360346, "maxRSS": 394024, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104071, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7942, "involuntaryContextSwitches": 1994}}