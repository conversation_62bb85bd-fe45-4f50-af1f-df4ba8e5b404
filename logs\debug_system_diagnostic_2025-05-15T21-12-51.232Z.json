{"timestamp": "2025-05-15T21:12:51.232Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 267341824, "heapTotal": 120102912, "heapUsed": 72282120, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.252270299, "cpuUsage": {"user": 2943545, "system": 407049}, "resourceUsage": {"userCPUTime": 2943604, "systemCPUTime": 407049, "maxRSS": 275528, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104919, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 64, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8979, "involuntaryContextSwitches": 8845}}