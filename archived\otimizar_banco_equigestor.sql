-- Script SQL para otimização do banco de dados do EquiGestor AI
-- Autor: EquiGestor Team
-- Data: 09/04/2025

-- <PERSON>ste script cria índices para melhorar a performance das consultas mais frequentes no sistema

-- =============================
-- 1. ÍNDICES PARA CAVALOS
-- =============================

-- Índice para pesquisa por nome (autocomplete)
CREATE INDEX IF NOT EXISTS idx_cavalos_name ON cavalos (name);

-- Índice para filtrar por raça
CREATE INDEX IF NOT EXISTS idx_cavalos_breed ON cavalos (breed);

-- Índice para ordenar por data de nascimento
CREATE INDEX IF NOT EXISTS idx_cavalos_birth_date ON cavalos (birth_date);

-- Índice para buscar por sexo
CREATE INDEX IF NOT EXISTS idx_cavalos_sexo ON cavalos (sexo);

-- Índice para filtrar cavalos ativos/inativos
CREATE INDEX IF NOT EXISTS idx_cavalos_status ON cavalos (status);

-- Índice composto para buscas de características físicas
CREATE INDEX IF NOT EXISTS idx_cavalos_fisicas ON cavalos (sexo, breed, peso, altura);

-- Índice composto para genealogia rápida
CREATE INDEX IF NOT EXISTS idx_cavalos_genealogia ON cavalos (pai, mae);

-- =============================
-- 2. ÍNDICES PARA MANEJOS
-- =============================

-- Índice para buscar manejos por tipo
CREATE INDEX IF NOT EXISTS idx_manejos_tipo ON manejos (tipo);

-- Índice para ordenar por data
CREATE INDEX IF NOT EXISTS idx_manejos_data ON manejos (data);

-- Índice para filtrar por status
CREATE INDEX IF NOT EXISTS idx_manejos_status ON manejos (status);

-- Índice para filtrar por cavalo
CREATE INDEX IF NOT EXISTS idx_manejos_horse_id ON manejos (horse_id);

-- Índice composto para buscas combinadas frequentes
CREATE INDEX IF NOT EXISTS idx_manejos_combinado ON manejos (tipo, data, horse_id);

-- =============================
-- 3. ÍNDICES PARA EVENTOS
-- =============================

-- Índice para ordenar por data (importante para calendário)
CREATE INDEX IF NOT EXISTS idx_eventos_data ON eventos (data);

-- Índice para filtrar por tipo
CREATE INDEX IF NOT EXISTS idx_eventos_tipo ON eventos (tipo);

-- Índice para filtrar por status
CREATE INDEX IF NOT EXISTS idx_eventos_status ON eventos (status);

-- Índice para filtrar por cavalo
CREATE INDEX IF NOT EXISTS idx_eventos_horse_id ON eventos (horse_id);

-- Índice para filtrar por prioridade
CREATE INDEX IF NOT EXISTS idx_eventos_prioridade ON eventos (prioridade);

-- Índice composto para buscas de calendário
CREATE INDEX IF NOT EXISTS idx_eventos_calendario ON eventos (data, hora_inicio);

-- Índice composto para buscas por cavalo e data
CREATE INDEX IF NOT EXISTS idx_eventos_cavalo_data ON eventos (horse_id, data);

-- =============================
-- 4. ÍNDICES PARA MÓDULO DE GENÉTICA
-- =============================

-- Índice para morfologia por cavalo
CREATE INDEX IF NOT EXISTS idx_morfologia_horse_id ON morfologia (horse_id);

-- Índice para ordenar avaliações morfológicas por data
CREATE INDEX IF NOT EXISTS idx_morfologia_data ON morfologia (data_medicao);

-- Índice para buscar desempenho por cavalo
CREATE INDEX IF NOT EXISTS idx_desempenho_horse_id ON desempenho_historico (horse_id);

-- Índice para ordenar desempenho por data
CREATE INDEX IF NOT EXISTS idx_desempenho_data ON desempenho_historico (data);

-- Índice para filtrar desempenho por tipo de evento
CREATE INDEX IF NOT EXISTS idx_desempenho_tipo ON desempenho_historico (tipo_evento);

-- Índice para genealogia por cavalo
CREATE INDEX IF NOT EXISTS idx_genealogia_horse_id ON genealogia (horse_id);

-- Índice para sugestões de cruzamento por cavalo base
CREATE INDEX IF NOT EXISTS idx_sugestoes_base ON sugestoes_cruzamento (horse_id_base);

-- Índice para sugestões de cruzamento por cavalo sugerido
CREATE INDEX IF NOT EXISTS idx_sugestoes_sugerido ON sugestoes_cruzamento (horse_id_sugerido);

-- Índice para filtrar sugestões por objetivo
CREATE INDEX IF NOT EXISTS idx_sugestoes_objetivo ON sugestoes_cruzamento (objetivo);

-- Índice para ordenar sugestões por pontuação
CREATE INDEX IF NOT EXISTS idx_sugestoes_pontuacao ON sugestoes_cruzamento (pontuacao_compatibilidade DESC);

-- =============================
-- 5. ÍNDICES PARA NUTRIÇÃO
-- =============================

-- Índice para nutrição por cavalo
CREATE INDEX IF NOT EXISTS idx_nutricao_horse_id ON nutricao (horse_id);

-- Índice para nutrição por tipo
CREATE INDEX IF NOT EXISTS idx_nutricao_tipo ON nutricao (tipo_alimentacao);

-- Índice composto para busca de histórico alimentar
CREATE INDEX IF NOT EXISTS idx_nutricao_historico ON nutricao (horse_id, data);

-- =============================
-- 6. ÍNDICES PARA PROCEDIMENTOS VETERINÁRIOS
-- =============================

-- Índice para procedimentos por cavalo
CREATE INDEX IF NOT EXISTS idx_procedimentos_horse_id ON procedimentos_vet (horse_id);

-- Índice para ordenar procedimentos por data
CREATE INDEX IF NOT EXISTS idx_procedimentos_data ON procedimentos_vet (data);

-- Índice para filtrar por tipo de procedimento
CREATE INDEX IF NOT EXISTS idx_procedimentos_tipo ON procedimentos_vet (tipo);

-- Índice para filtrar por status
CREATE INDEX IF NOT EXISTS idx_procedimentos_status ON procedimentos_vet (status);

-- Índice composto para veterinário e data
CREATE INDEX IF NOT EXISTS idx_procedimentos_vet_data ON procedimentos_vet (veterinario, data);

-- =============================
-- 7. ÍNDICES PARA REPRODUÇÃO
-- =============================

-- Índice para reprodução por égua
CREATE INDEX IF NOT EXISTS idx_reproducao_egua ON reproducao (horse_id);

-- Índice para reprodução por padreador
CREATE INDEX IF NOT EXISTS idx_reproducao_padreador ON reproducao (padreiro_id);

-- Índice para ordenar por data de cobertura
CREATE INDEX IF NOT EXISTS idx_reproducao_data ON reproducao (data_cobertura);

-- Índice para filtrar por estado da reprodução
CREATE INDEX IF NOT EXISTS idx_reproducao_estado ON reproducao (estado);

-- Índice composto para histórico reprodutivo
CREATE INDEX IF NOT EXISTS idx_reproducao_historico ON reproducao (horse_id, data_cobertura);

-- =============================
-- 8. ÍNDICES PARA MEDIDAS FÍSICAS
-- =============================

-- Índice para medidas por cavalo
CREATE INDEX IF NOT EXISTS idx_medidas_horse_id ON medidas_fisicas (horse_id);

-- Índice para ordenar medidas por data
CREATE INDEX IF NOT EXISTS idx_medidas_data ON medidas_fisicas (data);

-- Índice composto para histórico de medidas
CREATE INDEX IF NOT EXISTS idx_medidas_historico ON medidas_fisicas (horse_id, data);

-- =============================
-- 9. ESTATÍSTICAS DO BANCO DE DADOS
-- =============================

-- Atualizar estatísticas do banco para otimizar o planejador de consultas
ANALYZE;

-- Confirmar criação dos índices
SELECT 
    tablename, 
    indexname, 
    indexdef
FROM 
    pg_indexes
WHERE 
    schemaname = 'public'
ORDER BY 
    tablename, 
    indexname;