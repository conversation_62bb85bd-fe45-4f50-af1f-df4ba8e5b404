Você é um(a) engenheiro(a) full-stack sênior especializado(a) em TypeScript, React, Node.js (Express) e Drizzle ORM.
O repositório atual (EquiGestor) já possui cadastro de cavalos, saúde, manejo, reprodução, agenda etc. Há um módulo básico de nutrição, porém sua logística é limitada. Seu objetivo é adicionar, sem quebrar nada existente, um módulo completo de Logística de Nutrição conforme as especificações abaixo.

⸻

🎯 1. Requisitos Funcionais
	1.	Templates de Dieta
	•	CRUD para feed_templates com campos
id, category (“manutenção”, “atleta_leve”, “gestacao”, “lactacao”, “potro”),
forage_min_pct_ms, concentrate_max_kg_per_100kg, notes, created_by.
	•	Seeds iniciais com as 5 categorias e valores-padrão.
	2.	Plano de Alimentação Diário
	•	Tabela feed_plan_items
id, animal_id, template_id, forage_kg, concentrate_kg, date, status (“pending”, “done”, “leftover”), leftover_pct.
	•	Endpoint GET /api/feeding/today → lista planos do dia filtrados por usuário.
	•	Endpoint PATCH /api/feeding/:id/mark → marca “done” ou registra sobra.
	3.	Estoque & Compras
	•	Tabela stock_batches
id, item, quantity_kg, unit_cost, expiry, created_at.
	•	Cron diário 00:05 (node-cron) que:
	•	Consome estoque previsto para o dia anterior.
	•	Calcula dias restantes de cada item; se <7 → cria notificação.
	•	Endpoint GET /api/stock/alerts.
	4.	Dashboards Front-end (React + Tailwind)
	•	FeedingDashboard.tsx
	•	Cards: “Tratos pendentes”, “Desperdício médio”, “Custo/dia/animal”.
	•	Tabela responsiva com baia, cavalo, template, forage kg, concentrate kg, botão “OK”.
	•	StockAlerts.tsx
	•	Lista de itens críticos com badge vermelho/amarelo.
	5.	Hooks & Context
	•	useFeedingPlan() – chama /feeding/today, usa SWR ou React-Query.
	•	useStockAlerts() – chama /stock/alerts.
	6.	Indicadores (relatório mensal)
	•	API /api/reports/nutrition?from=YYYY-MM-DD&to=... que devolve:
avg_cost_per_animal, avg_body_score, avg_waste_pct.

⸻

🛠️ 2. Requisitos Não-Funcionais
	•	Não alterar lógica existente; apenas adicionar arquivos e, quando necessário, importar novos módulos em pontos bem isolados.
	•	Type-safety total (strict mode on).
	•	Migrations Drizzle geradas em drizzle/2025-mm-dd-add-nutrition.ts.
	•	Autorização: rotas novas devem respeitar o middleware já usado nos demais módulos (verifyFirebaseAuth + RBAC).
	•	Testes:
	•	Unitários (Vitest) para serviços de cálculo de dieta e cron.
	•	E2E (Supertest) para endpoints principais.
	•	Commits semânticos (feat(nutrition): add feed templates crud etc.).
	•	Documentação: atualizar docs/openapi.yaml e README.md.

3. Lógica de Cálculo de Dieta (utilize estes utilitários)

/** Cálculo de volumoso (kg MS) = peso vivo * pct_MS */
export const calcForageKg = (weightKg: number, pctMS: number) =>
  +(weightKg * pctMS).toFixed(2);

/** Concent. máx (kg) = peso vivo / 100 * kg/100kg */
export const calcConcentrateKg = (
  weightKg: number,
  kgPer100: number
) => +((weightKg / 100) * kgPer100).toFixed(2);

4. Passo a Passo de Implementação (siga na ordem)
	1.	Crie branch feat/nutrition-module.
	2.	Gerar migrations para as 3 tabelas.
	3.	Seed feed_templates.
	4.	Services:
	•	nutrition.service.ts (gera planos, calcula sobras).
	•	stock.service.ts (dias de estoque, alertas).
	5.	Cron job em jobs/recalculateStock.ts.
	6.	Endpoints em routes/nutrition.ts e routes/stock.ts; adicione no app.ts.
	7.	Front-end:
	•	Páginas em src/pages/Nutrition/*.
	•	Componentes e hooks novos.
	8.	Tests em tests/nutrition/*.test.ts.
	9.	Atualize docs e faça PR.

⸻

✅ 5. Critérios de Aceite
	•	Roteiro diário carrega em < 500 ms para 100 cavalos.
	•	Sobra >10 % por 2 dias gera sugestão de ajuste no próximo plano.
	•	Estoque crítico dispara notificação (já aparece em StockAlerts) sem falhas.
	•	Todos os testes passam (npm run test verde).
	•	Linter (ESLint + Prettier) sem erros.

⸻

🔒 6. Restrições e Boas Práticas
	•	Não remover nem renomear pastas existentes.
	•	Evite modificar componentes compartilhados; se precisar, crie variantes.
	•	Use zod para validação de payloads das novas rotas.
	•	Internacionalização: textos novos em i18n/pt.json e i18n/en.json.
	•	Opt-in PWA: se usar notificações push, siga o serviço já existente.

⸻

📣 7. Saída Esperada da IA
	1.	Link para PR + resumo das mudanças.
	2.	Log de testes passando.
	3.	Instruções rápidas para revisão (migrations, env vars extras).

IMPORTANTE: Não altere nada fora do escopo descrito e sempre mantenha backup antes de executar migrações.