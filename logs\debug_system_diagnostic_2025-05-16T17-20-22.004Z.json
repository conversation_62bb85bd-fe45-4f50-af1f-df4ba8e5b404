{"timestamp": "2025-05-16T17:20:22.003Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397328384, "heapTotal": 116170752, "heapUsed": 72593592, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.676491692, "cpuUsage": {"user": 2703414, "system": 325980}, "resourceUsage": {"userCPUTime": 2703462, "systemCPUTime": 325986, "maxRSS": 388016, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103664, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7970, "involuntaryContextSwitches": 1371}}