{"timestamp": "2025-05-15T22:50:41.228Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 419627008, "heapTotal": 115384320, "heapUsed": 72553232, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.086066237, "cpuUsage": {"user": 2987394, "system": 386132}, "resourceUsage": {"userCPUTime": 2987470, "systemCPUTime": 386132, "maxRSS": 409792, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 109396, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 112, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8795, "involuntaryContextSwitches": 4343}}