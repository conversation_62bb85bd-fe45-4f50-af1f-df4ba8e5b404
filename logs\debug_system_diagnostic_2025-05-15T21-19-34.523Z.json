{"timestamp": "2025-05-15T21:19:34.522Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 395218944, "heapTotal": 108044288, "heapUsed": 72525048, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.830156259, "cpuUsage": {"user": 2892260, "system": 323004}, "resourceUsage": {"userCPUTime": 2892327, "systemCPUTime": 323004, "maxRSS": 385956, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104050, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 40, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8240, "involuntaryContextSwitches": 2639}}