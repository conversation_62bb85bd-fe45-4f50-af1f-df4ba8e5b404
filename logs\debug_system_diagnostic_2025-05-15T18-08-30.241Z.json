{"timestamp": "2025-05-15T18:08:30.240Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 410112000, "heapTotal": 116695040, "heapUsed": 72428744, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.591099454, "cpuUsage": {"user": 3012004, "system": 430535}, "resourceUsage": {"userCPUTime": 3012071, "systemCPUTime": 430535, "maxRSS": 400500, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104556, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8968, "involuntaryContextSwitches": 9361}}