{"timestamp": "2025-05-15T01:14:47.948Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 378806272, "heapTotal": 91623424, "heapUsed": 70236296, "external": 6890744, "arrayBuffers": 106994}, "uptime": 1.540232622, "cpuUsage": {"user": 2279058, "system": 294876}, "resourceUsage": {"userCPUTime": 2279123, "systemCPUTime": 294876, "maxRSS": 369928, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 97672, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6336, "involuntaryContextSwitches": 2500}}