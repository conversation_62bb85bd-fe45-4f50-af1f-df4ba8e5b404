import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { Cavalo } from '@shared/schema';

// Schema de validação para o formulário de procedimento veterinário
const procedimentoFormSchema = z.object({
  tipo: z.string().min(1, { message: "Selecione o tipo de procedimento" }),
  descricao: z.string().min(1, { message: "Descreva o procedimento" }),
  data: z.string().min(1, { message: "Selecione a data" }),
  veterinario: z.string().min(1, { message: "Informe o nome do veterinário" }),
  crmv: z.string().optional(),
  medicamentos: z.string().optional(),
  dosagem: z.string().optional(),
  resultado: z.string().optional(),
  recomendacoes: z.string().optional(),
  dataProximoProcedimento: z.string().optional(),
  horseId: z.number({ required_error: "Selecione um animal" }),
  observacoes: z.string().optional(),
  custo: z.coerce.number().optional(),
  formaPagamento: z.string().optional(),
  status: z.string().default("realizado"),
  userId: z.number(),
});

export type ProcedimentoFormValues = z.infer<typeof procedimentoFormSchema>;

interface ProcedimentoVetFormProps {
  onSubmit: (values: ProcedimentoFormValues) => void;
  defaultValues?: Partial<ProcedimentoFormValues>;
  isSubmitting: boolean;
  cavalos: Cavalo[];
  userId: number;
  procedimentoTipo?: 'vacinacao' | 'vermifugacao' | 'exame' | 'outro';
}

/**
 * Componente de formulário para procedimentos veterinários
 * 
 * Este componente é usado para criar e editar procedimentos veterinários,
 * incluindo vacinações, vermifugações, exames e outros tipos de procedimentos.
 * Os campos exibidos são adaptados ao tipo de procedimento selecionado.
 */
export const ProcedimentoVetForm: React.FC<ProcedimentoVetFormProps> = ({
  onSubmit,
  defaultValues,
  isSubmitting,
  cavalos,
  userId,
  procedimentoTipo = 'outro',
}) => {
  // Definir valores iniciais com base no tipo de procedimento
  const getTipoInicial = () => {
    switch (procedimentoTipo) {
      case 'vacinacao': return 'Vacinação';
      case 'vermifugacao': return 'Vermifugação';
      case 'exame': return 'Exame clínico';
      default: return '';
    }
  };

  // Formulário com validação Zod
  const form = useForm<ProcedimentoFormValues>({
    resolver: zodResolver(procedimentoFormSchema),
    defaultValues: {
      tipo: getTipoInicial(),
      descricao: '',
      data: format(new Date(), 'yyyy-MM-dd'),
      veterinario: '',
      crmv: '',
      medicamentos: '',
      dosagem: '',
      resultado: '',
      recomendacoes: '',
      dataProximoProcedimento: '',
      horseId: 0,
      observacoes: '',
      custo: 0,
      formaPagamento: '',
      status: 'realizado',
      userId,
      ...defaultValues
    },
  });

  // Tipos de procedimentos disponíveis
  const tiposProcedimento = [
    // Vacinações
    { value: 'Vacinação', label: 'Vacinação' },
    { value: 'Reforço de vacina', label: 'Reforço de vacina' },
    
    // Vermifugações
    { value: 'Vermifugação', label: 'Vermifugação' },
    { value: 'Controle parasitário', label: 'Controle parasitário' },
    
    // Exames e consultas
    { value: 'Exame clínico', label: 'Exame clínico' },
    { value: 'Consulta veterinária', label: 'Consulta veterinária' },
    { value: 'Exame laboratorial', label: 'Exame laboratorial' },
    { value: 'Raio-X', label: 'Raio-X' },
    { value: 'Ultrassom', label: 'Ultrassom' },
    { value: 'Endoscopia', label: 'Endoscopia' },
    
    // Outros procedimentos
    { value: 'Cirurgia', label: 'Cirurgia' },
    { value: 'Tratamento dentário', label: 'Tratamento dentário' },
    { value: 'Casqueamento', label: 'Casqueamento' },
    { value: 'Fisioterapia', label: 'Fisioterapia' },
    { value: 'Acupuntura', label: 'Acupuntura' },
    { value: 'Outro', label: 'Outro procedimento' },
  ];

  // Verificar se o tipo atual é uma vacinação
  const isVacinacao = form.watch('tipo').toLowerCase().includes('vacina');
  
  // Verificar se o tipo atual é uma vermifugação
  const isVermifugacao = form.watch('tipo').toLowerCase().includes('vermifug');
  
  // Verificar se o tipo atual é um exame
  const isExame = form.watch('tipo').toLowerCase().includes('exame');

  // Handler para o envio do formulário
  const handleSubmit = (values: ProcedimentoFormValues) => {
    onSubmit(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <input 
          type="hidden" 
          {...form.register("userId", { valueAsNumber: true })}
          value={userId}
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="horseId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Animal</FormLabel>
                <Select 
                  onValueChange={(value) => field.onChange(parseInt(value))} 
                  defaultValue={field.value?.toString() || ""}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um animal" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {cavalos.map((cavalo) => (
                      <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                        {cavalo.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="tipo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tipo de Procedimento</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tipo" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {tiposProcedimento.map((tipo) => (
                      <SelectItem key={tipo.value} value={tipo.value}>
                        {tipo.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="data"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Data do Procedimento</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="veterinario"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Veterinário</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="crmv"
            render={({ field }) => (
              <FormItem>
                <FormLabel>CRMV (opcional)</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="custo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Custo (R$)</FormLabel>
                <FormControl>
                  <Input type="number" step="0.01" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="descricao"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Descrição do Procedimento</FormLabel>
              <FormControl>
                <Textarea {...field} rows={3} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        {(isVacinacao || isVermifugacao) && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="medicamentos"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{isVacinacao ? 'Vacina' : 'Vermífugo'}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormDescription>
                    {isVacinacao 
                      ? 'Nome comercial ou tipo da vacina aplicada' 
                      : 'Nome comercial ou princípio ativo do vermífugo'}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="dosagem"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Dosagem</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormDescription>
                    Quantidade administrada (ex: "5ml", "2 comprimidos")
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}
        
        {isExame && (
          <FormField
            control={form.control}
            name="resultado"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Resultado do Exame</FormLabel>
                <FormControl>
                  <Textarea {...field} rows={3} />
                </FormControl>
                <FormDescription>
                  Registre o resultado obtido no exame
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
        
        <FormField
          control={form.control}
          name="recomendacoes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Recomendações (opcional)</FormLabel>
              <FormControl>
                <Textarea {...field} rows={2} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="dataProximoProcedimento"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Data do Próximo Procedimento (opcional)</FormLabel>
              <FormControl>
                <Input type="date" {...field} />
              </FormControl>
              <FormDescription>
                {isVacinacao 
                  ? 'Para quando está programada a próxima dose ou reforço' 
                  : isVermifugacao 
                    ? 'Data recomendada para a próxima vermifugação'
                    : 'Quando o próximo procedimento deve ser realizado'}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="observacoes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Observações Adicionais (opcional)</FormLabel>
              <FormControl>
                <Textarea {...field} rows={3} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex justify-end space-x-2">
          <Button 
            type="submit" 
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Salvando...' : 'Salvar'}
          </Button>
        </div>
      </form>
    </Form>
  );
};