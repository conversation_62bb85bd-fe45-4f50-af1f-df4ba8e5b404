{"timestamp": "2025-05-15T00:52:09.293Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 384749568, "heapTotal": 101060608, "heapUsed": 62334912, "external": 6815481, "arrayBuffers": 60485}, "uptime": 6.628553051, "cpuUsage": {"user": 2149218, "system": 309787}, "resourceUsage": {"userCPUTime": 2149281, "systemCPUTime": 309787, "maxRSS": 375732, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102817, "majorPageFault": 6, "swappedOut": 0, "fsRead": 42232, "fsWrite": 376, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6391, "involuntaryContextSwitches": 1404}}