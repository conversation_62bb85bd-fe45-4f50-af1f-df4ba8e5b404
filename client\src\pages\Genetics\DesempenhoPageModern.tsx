import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useGeneticsContext } from "@/contexts/GeneticsContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Loader2, 
  PlusCircle, 
  Medal, 
  Trophy,
  Calendar,
  MapPin,
  Clock,
  Target,
  TrendingUp,
  Save,
  Star,
  Edit,
  Trash2,
  MoreHorizontal
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { apiRequest } from '@/lib/queryClient';

interface RegistroDesempenho {
  id?: number;
  horseId: number;
  evento: string;
  local: string;
  data: string;
  modalidade: string;
  categoria: string;
  posicao?: number;
  tempo?: string;
  pontuacao?: number;
  observacoes?: string;
  premiacao?: string;
}

// Modalidades comuns nos campeonatos brasileiros
const MODALIDADES = [
  'Salto',
  'Adestramento',
  'CCE (Concurso Completo de Equitação)',
  'Enduro Equestre',
  'Rédeas',
  'Tambor',
  'Baliza',
  'Maneabilidade',
  'Apartação',
  'Freio de Ouro',
  'Três Tambores',
  'Corrida de Velocidade',
  'Team Penning',
  'Vaquejada',
  'Laço em Dupla',
  'Laço Comprido',
  'Hipismo Rural',
  'Polo',
  'Volteio',
  'Atrelagem'
];

// Categorias por modalidade (principais no Brasil)
const CATEGORIAS: Record<string, string[]> = {
  'Salto': [
    'Iniciante (0,60m)',
    'Preliminar (0,80m)',
    'Pré-novatos (1,00m)',
    'Novatos (1,10m)',
    'Juvenil (1,20m)',
    'Amador (1,30m)',
    'Open (1,40m)',
    'Grand Prix (1,50m+)'
  ],
  'Adestramento': [
    'Preliminar A',
    'Preliminar B',
    'Elementar A',
    'Elementar B',
    'Médio I',
    'Médio II',
    'Avançado I',
    'Avançado II',
    'Grand Prix'
  ],
  'CCE (Concurso Completo de Equitação)': [
    'Iniciante',
    'Preliminar',
    'Intermediário',
    'Avançado',
    'CCI*',
    'CCI**',
    'CCI***',
    'CCI****'
  ],
  'Enduro Equestre': [
    'CEI* (20-40km)',
    'CEI** (40-80km)',
    'CEI*** (80-120km)',
    'CEN (120-160km)',
    'Iniciante (20km)',
    'Intermediário (40km)',
    'Avançado (80km)'
  ],
  'Rédeas': [
    'Iniciante',
    'Amador',
    'Não Pro',
    'Profissional',
    'Open',
    'Futurity 3 anos',
    'Maturity 4/5 anos',
    'Derby 6+ anos'
  ],
  'Tambor': [
    'Mirim',
    'Infantil',
    'Juvenil',
    'Amador',
    'Profissional',
    'Open',
    'Sênior'
  ],
  'Baliza': [
    'Iniciante',
    'Amador',
    'Profissional',
    'Open',
    'Feminino',
    'Masculino'
  ],
  'Vaquejada': [
    'Aspirante',
    'Amador',
    'Profissional',
    'Categoria A',
    'Categoria B',
    'Sênior'
  ]
};

export default function DesempenhoPageModern() {
  const { selectedHorseId } = useGeneticsContext();
  const [showForm, setShowForm] = useState(false);
  const [editingRecord, setEditingRecord] = useState<RegistroDesempenho | null>(null);
  const [selectedModalidade, setSelectedModalidade] = useState('');
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    evento: '',
    local: '',
    data: new Date().toISOString().split('T')[0],
    modalidade: '',
    categoria: '',
    posicao: '',
    tempo: '',
    pontuacao: '',
    observacoes: '',
    premiacao: ''
  });

  // Buscar cavalos
  const { data: cavalos, isLoading: isLoadingCavalos } = useQuery<any[]>({
    queryKey: ['/api/cavalos'],
  });

  // Buscar dados reais de desempenho do cavalo selecionado
  const { data: desempenhoData, isLoading: isLoadingDesempenho, refetch } = useQuery<RegistroDesempenho[]>({
    queryKey: [`/api/cavalos/${selectedHorseId}/desempenho`],
    enabled: selectedHorseId !== null,
  });

  const selectedHorse = cavalos?.find(cavalo => cavalo.id === selectedHorseId);

  // Funções para edição e exclusão
  const handleEdit = (record: RegistroDesempenho) => {
    setEditingRecord(record);
    
    // Extrair dados dos campos corretos do banco
    const modalidadeValue = record.modalidade || record.desempenhoDetalhes?.match(/Modalidade:\s*([^,]+)/)?.[1] || '';
    const tempoValue = record.desempenhoDetalhes?.match(/Tempo:\s*([^,]+)/)?.[1] || '';
    const pontuacaoValue = record.desempenhoDetalhes?.match(/Pontuação:\s*([^,]+)/)?.[1] || '';
    const posicaoValue = record.resultado?.match(/(\d+)/)?.[1] || '';
    
    setFormData({
      evento: record.nomeEvento || '',
      local: record.local || '',
      data: record.data || new Date().toISOString().split('T')[0],
      modalidade: modalidadeValue,
      categoria: record.categoria || '',
      posicao: posicaoValue,
      tempo: tempoValue,
      pontuacao: pontuacaoValue,
      observacoes: record.observacoes || '',
      premiacao: record.premiacao || ''
    });
    setSelectedModalidade(modalidadeValue);
    setShowForm(true);
  };

  const handleDelete = async (recordId: number) => {
    try {
      await apiRequest('DELETE', `/api/desempenho/${recordId}`, { userId: 1 });
      toast({
        title: "Sucesso!",
        description: "Registro excluído com sucesso",
      });
      refetch();
    } catch (error) {
      console.error('Erro ao excluir:', error);
      toast({
        title: "Erro",
        description: "Erro ao excluir registro",
        variant: "destructive",
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingRecord(null);
    setSelectedModalidade('');
    setFormData({
      evento: '',
      local: '',
      data: new Date().toISOString().split('T')[0],
      modalidade: '',
      categoria: '',
      posicao: '',
      tempo: '',
      pontuacao: '',
      observacoes: '',
      premiacao: ''
    });
    setShowForm(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedHorseId) {
      toast({
        title: "Erro",
        description: "Selecione um cavalo primeiro",
        variant: "destructive",
      });
      return;
    }

    try {
      // Dados com os nomes corretos que o backend espera
      const cleanData = {
        horseId: selectedHorseId,
        userId: 1,
        tipoEvento: 'Competição',
        nomeEvento: formData.evento?.trim(),
        data: formData.data,
        categoria: formData.categoria,
        resultado: formData.posicao ? `${formData.posicao}º lugar` : null,
        observacoes: formData.observacoes?.trim() || null,
        premiacao: formData.premiacao?.trim() || null,
        modalidade: formData.modalidade,
        desempenhoDetalhes: `Modalidade: ${formData.modalidade}${formData.tempo ? `, Tempo: ${formData.tempo}` : ''}${formData.pontuacao ? `, Pontuação: ${formData.pontuacao}` : ''}`,
      };

      console.log('Enviando dados:', cleanData);

      const isEditing = editingRecord !== null;
      
      if (isEditing) {
        await apiRequest('PUT', `/api/desempenho/${editingRecord.id}`, cleanData);
      } else {
        await apiRequest('POST', '/api/desempenho', cleanData);
      }
      
      toast({
        title: "Sucesso!",
        description: `Registro ${isEditing ? 'atualizado' : 'salvo'} com sucesso`,
      });
      
      // Atualizar dados
      refetch();
      
      handleCancelEdit(); // Reset form
      
    } catch (error) {
      console.error('Erro completo:', error);
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      toast({
        title: "Erro ao salvar",
        description: `Detalhes: ${errorMessage}`,
        variant: "destructive",
      });
    }
  };

  const getBadgeVariant = (posicao?: number) => {
    if (!posicao) return "outline";
    if (posicao === 1) return "default";
    if (posicao === 2) return "secondary";  
    if (posicao === 3) return "outline";
    return "outline";
  };

  const getBadgeIcon = (posicao?: number) => {
    if (posicao === 1) return <Trophy className="h-3 w-3" />;
    if (posicao === 2) return <Medal className="h-3 w-3" />;
    if (posicao === 3) return <Star className="h-3 w-3" />;
    return null;
  };

  // Dados para gráficos
  const chartData = (desempenhoData || []).map((registro, index) => ({
    evento: `Evento ${index + 1}`,
    pontuacao: registro.pontuacao || 0,
    posicao: registro.posicao || 0,
    data: new Date(registro.data).toLocaleDateString()
  })).reverse();

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Análise de Desempenho</CardTitle>
              <CardDescription>
                {selectedHorseId && selectedHorse 
                  ? `Histórico de performance de ${selectedHorse.name}`
                  : "Selecione um cavalo no topo da página para começar"
                }
              </CardDescription>
            </div>
            <Button 
              onClick={() => setShowForm(!showForm)} 
              disabled={!selectedHorseId}
              className="flex items-center gap-2"
            >
              <PlusCircle className="h-4 w-4" />
              {showForm ? 'Cancelar' : 'Novo Registro'}
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Formulário de novo registro */}
      {showForm && selectedHorse && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingRecord ? `Editando Registro - ${selectedHorse.name}` : `Novo Registro de Desempenho - ${selectedHorse.name}`}
            </CardTitle>
            <CardDescription>
              {editingRecord ? 'Modifique os dados da competição' : 'Registre a participação em competições e eventos'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Dados do evento */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="evento">Nome do Evento</Label>
                  <Input
                    id="evento"
                    value={formData.evento}
                    onChange={(e) => setFormData({...formData, evento: e.target.value})}
                    placeholder="Copa Nacional de Saltos"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="local">Local</Label>
                  <Input
                    id="local"
                    value={formData.local}
                    onChange={(e) => setFormData({...formData, local: e.target.value})}
                    placeholder="Hipódromo da Gávea - RJ"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="data">Data do Evento</Label>
                  <Input
                    id="data"
                    type="date"
                    value={formData.data}
                    onChange={(e) => setFormData({...formData, data: e.target.value})}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="modalidade">Modalidade</Label>
                  <Select 
                    value={formData.modalidade} 
                    onValueChange={(value) => {
                      setFormData({...formData, modalidade: value, categoria: ''});
                      setSelectedModalidade(value);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a modalidade" />
                    </SelectTrigger>
                    <SelectContent>
                      {MODALIDADES.map((modalidade) => (
                        <SelectItem key={modalidade} value={modalidade}>
                          {modalidade}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Resultados */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="categoria">Categoria</Label>
                  <Select 
                    value={formData.categoria} 
                    onValueChange={(value) => setFormData({...formData, categoria: value})}
                    disabled={!selectedModalidade}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={selectedModalidade ? "Selecione a categoria" : "Primeiro selecione uma modalidade"} />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedModalidade && CATEGORIAS[selectedModalidade]?.map((categoria) => (
                        <SelectItem key={categoria} value={categoria}>
                          {categoria}
                        </SelectItem>
                      ))}
                      {/* Opção para categorias não listadas */}
                      <SelectItem value="Outros">Outros (categoria personalizada)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="posicao">Posição</Label>
                  <Input
                    id="posicao"
                    type="number"
                    min="1"
                    value={formData.posicao}
                    onChange={(e) => setFormData({...formData, posicao: e.target.value})}
                    placeholder="1"
                  />
                </div>
                <div>
                  <Label htmlFor="tempo">Tempo</Label>
                  <Input
                    id="tempo"
                    value={formData.tempo}
                    onChange={(e) => setFormData({...formData, tempo: e.target.value})}
                    placeholder="01:35.42"
                  />
                </div>
                <div>
                  <Label htmlFor="pontuacao">Pontuação</Label>
                  <Input
                    id="pontuacao"
                    type="number"
                    step="0.1"
                    value={formData.pontuacao}
                    onChange={(e) => setFormData({...formData, pontuacao: e.target.value})}
                    placeholder="85.5"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="premiacao">Premiação</Label>
                <Input
                  id="premiacao"
                  value={formData.premiacao}
                  onChange={(e) => setFormData({...formData, premiacao: e.target.value})}
                  placeholder="1º Lugar, Medalha de Prata..."
                />
              </div>

              <div>
                <Label htmlFor="observacoes">Observações</Label>
                <Textarea
                  id="observacoes"
                  value={formData.observacoes}
                  onChange={(e) => setFormData({...formData, observacoes: e.target.value})}
                  placeholder="Observações sobre a performance..."
                  rows={3}
                />
              </div>

              <div className="flex gap-4">
                <Button type="submit" className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Salvar Registro
                </Button>
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  Cancelar
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Visualização dos dados */}
      {selectedHorseId && selectedHorse && (
        <Tabs defaultValue="historico" className="w-full">
          <TabsList>
            <TabsTrigger value="historico">Histórico</TabsTrigger>
            <TabsTrigger value="graficos">Gráficos</TabsTrigger>
            <TabsTrigger value="estatisticas">Estatísticas</TabsTrigger>
          </TabsList>

          <TabsContent value="historico" className="space-y-4">
            {isLoadingDesempenho ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Carregando registros...</span>
              </div>
            ) : (desempenhoData && desempenhoData.length > 0) ? (
              <Card>
                <CardHeader>
                  <CardTitle>Histórico de Competições</CardTitle>
                  <CardDescription>
                    Registros de participação em eventos e competições
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Evento</TableHead>
                        <TableHead>Data</TableHead>
                        <TableHead>Modalidade</TableHead>
                        <TableHead>Posição</TableHead>
                        <TableHead>Pontuação</TableHead>
                        <TableHead>Premiação</TableHead>
                        <TableHead className="text-right">Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {desempenhoData.map((registro) => (
                        <TableRow key={registro.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{registro.evento}</div>
                              <div className="text-sm text-gray-500 flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {registro.local}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {registro.data ? format(new Date(registro.data), "dd/MM/yyyy", { locale: ptBR }) : 'Data não informada'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{registro.modalidade}</div>
                              {registro.categoria && (
                                <div className="text-sm text-gray-500">{registro.categoria}</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {registro.posicao && (
                              <Badge variant={getBadgeVariant(registro.posicao)} className="gap-1">
                                {getBadgeIcon(registro.posicao)}
                                {registro.posicao}º
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {registro.pontuacao && (
                              <div className="flex items-center gap-1">
                                <Target className="h-3 w-3" />
                                {registro.pontuacao}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            {registro.premiacao && (
                              <Badge variant="outline">{registro.premiacao}</Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center gap-2 justify-end">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEdit(registro)}
                                className="h-8 w-8 p-0"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Tem certeza que deseja excluir este registro de desempenho? Esta ação não pode ser desfeita.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                    <AlertDialogAction 
                                      onClick={() => registro.id && handleDelete(registro.id)}
                                      className="bg-red-600 hover:bg-red-700"
                                    >
                                      Excluir
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Nenhum registro encontrado</h3>
                  <p className="text-gray-500 mb-4">
                    Este cavalo ainda não possui registros de desempenho.
                  </p>
                  <Button onClick={() => setShowForm(true)}>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Criar primeiro registro
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="graficos" className="space-y-4">
            {chartData.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Evolução das Pontuações</CardTitle>
                    <CardDescription>
                      Acompanhe o progresso ao longo das competições
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={chartData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="evento" />
                          <YAxis />
                          <Tooltip />
                          <Line type="monotone" dataKey="pontuacao" stroke="#3b82f6" strokeWidth={2} />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Posições por Evento</CardTitle>
                    <CardDescription>
                      Histórico de colocações nas competições
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={chartData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="evento" />
                          <YAxis reversed domain={[1, 10]} />
                          <Tooltip />
                          <Bar dataKey="posicao" fill="#f59e0b" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <BarChart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Sem dados para gráficos</h3>
                  <p className="text-gray-500">
                    Adicione registros de desempenho para visualizar os gráficos.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="estatisticas" className="space-y-4">
            {(desempenhoData && desempenhoData.length > 0) ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <Trophy className="h-8 w-8 text-yellow-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total de Competições</p>
                        <p className="text-2xl font-bold">{desempenhoData.length}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <Medal className="h-8 w-8 text-amber-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Primeiros Lugares</p>
                        <p className="text-2xl font-bold">
                          {desempenhoData.filter(r => r.posicao === 1).length}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <Target className="h-8 w-8 text-blue-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Pontuação Média</p>
                        <p className="text-2xl font-bold">
                          {(desempenhoData.reduce((acc, r) => acc + (r.pontuacao || 0), 0) / desempenhoData.filter(r => r.pontuacao).length || 0).toFixed(1)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <TrendingUp className="h-8 w-8 text-green-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Taxa de Pódio</p>
                        <p className="text-2xl font-bold">
                          {Math.round((desempenhoData.filter(r => r.posicao && r.posicao <= 3).length / desempenhoData.length) * 100)}%
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Sem dados estatísticos</h3>
                  <p className="text-gray-500">
                    Adicione registros de desempenho para ver as estatísticas.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}