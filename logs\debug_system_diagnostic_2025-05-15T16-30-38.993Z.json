{"timestamp": "2025-05-15T16:30:38.993Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 387731456, "heapTotal": 101122048, "heapUsed": 65747120, "external": 7016254, "arrayBuffers": 60485}, "uptime": 1.334813482, "cpuUsage": {"user": 2268203, "system": 295968}, "resourceUsage": {"userCPUTime": 2268245, "systemCPUTime": 295973, "maxRSS": 378644, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99586, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6493, "involuntaryContextSwitches": 839}}