#!/bin/bash

# Script de inicialização para produção no Replit
# Corrige o problema do comando Windows "set NODE_ENV=production"

echo "🚀 Iniciando EquiGestor AI em modo produção..."

# Definir variáveis de ambiente para Unix/Linux
export NODE_ENV=production
export PORT=${PORT:-5000}

echo "✅ NODE_ENV definido como: $NODE_ENV"
echo "✅ Porta configurada: $PORT"

# Verificar se o arquivo de build existe
if [ ! -f "dist/index.js" ]; then
    echo "❌ Erro: Arquivo de build não encontrado"
    echo "Execute 'npm run build' antes de iniciar em produção"
    exit 1
fi

echo "✅ Arquivo de build encontrado, iniciando aplicação..."

# Iniciar a aplicação
node dist/index.js