{"timestamp": "2025-05-23T05:34:04.793Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 260534272, "heapTotal": 111190016, "heapUsed": 74009728, "external": 8211290, "arrayBuffers": 235533}, "uptime": 6.926862811, "cpuUsage": {"user": 3065554, "system": 473775}, "resourceUsage": {"userCPUTime": 3065594, "systemCPUTime": 473782, "maxRSS": 321772, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104088, "majorPageFault": 6, "swappedOut": 0, "fsRead": 55512, "fsWrite": 968, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8936, "involuntaryContextSwitches": 3683}}