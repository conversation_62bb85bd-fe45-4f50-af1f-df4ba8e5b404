{"timestamp": "2025-05-16T17:20:28.153Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 409350144, "heapTotal": 117174272, "heapUsed": 84460752, "external": 8453631, "arrayBuffers": 480197}, "uptime": 7.826416418, "cpuUsage": {"user": 3125787, "system": 364218}, "resourceUsage": {"userCPUTime": 3125791, "systemCPUTime": 364219, "maxRSS": 399756, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105965, "majorPageFault": 0, "swappedOut": 0, "fsRead": 8, "fsWrite": 112, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9045, "involuntaryContextSwitches": 1481}}