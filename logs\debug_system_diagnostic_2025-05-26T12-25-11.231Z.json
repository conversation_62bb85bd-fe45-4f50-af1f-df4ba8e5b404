{"timestamp": "2025-05-26T12:25:11.231Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 408322048, "heapTotal": 116432896, "heapUsed": 74468392, "external": 8211290, "arrayBuffers": 235533}, "uptime": 8.590184707, "cpuUsage": {"user": 3332571, "system": 482146}, "resourceUsage": {"userCPUTime": 3332629, "systemCPUTime": 482155, "maxRSS": 398752, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107252, "majorPageFault": 7, "swappedOut": 0, "fsRead": 53936, "fsWrite": 992, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9170, "involuntaryContextSwitches": 7638}}