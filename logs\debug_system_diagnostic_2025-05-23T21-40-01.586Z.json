{"timestamp": "2025-05-23T21:40:01.585Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 296738816, "heapTotal": 119070720, "heapUsed": 92436432, "external": 8518699, "arrayBuffers": 235533}, "uptime": 2.084939784, "cpuUsage": {"user": 2831828, "system": 389782}, "resourceUsage": {"userCPUTime": 2831864, "systemCPUTime": 389787, "maxRSS": 289784, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104087, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9364, "involuntaryContextSwitches": 6007}}