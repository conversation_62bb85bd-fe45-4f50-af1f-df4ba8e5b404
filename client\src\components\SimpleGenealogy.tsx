import React, { useEffect, useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { GitBranchPlus } from "lucide-react";
import { apiRequest } from '@/lib/queryClient';
import { Cavalo } from '@shared/schema';

interface SimpleGenealogyProps {
  cavaloId: number;
}

/**
 * Componente simplificado que exibe a genealogia do cavalo
 * Suporta o sistema de genealogia dual (IDs e nomes)
 */
export function SimpleGenealogy({ cavaloId }: SimpleGenealogyProps) {
  const [cavalo, setCavalo] = useState<Cavalo | null>(null);
  const [pai, setPai] = useState<Cavalo | null>(null);
  const [mae, setMae] = useState<Cavalo | null>(null);
  const [loading, setLoading] = useState(true);

  // Função para obter informações do pai (sistema dual paiId/paiNome)
  const getPaiInfo = (cavalo: Cavalo | null) => {
    if (!cavalo) return { nome: 'Não informado', tipo: 'nenhum', id: null };
    
    // Verificar se tem referência a cavalo do sistema (paiId)
    if (cavalo.paiId) {
      return { 
        nome: pai?.name || 'Carregando...', 
        tipo: 'sistema',
        id: cavalo.paiId
      };
    }
    
    // Verificar se tem nome de cavalo externo (paiNome)
    if (cavalo.paiNome) {
      return { 
        nome: cavalo.paiNome, 
        tipo: 'externo',
        id: null 
      };
    }
    
    // Compatibilidade com campo legado pai
    if (cavalo.pai) {
      // Tentar entender se o campo pai é um ID ou um nome
      const isNumeric = /^\d+$/.test(cavalo.pai.toString());
      if (isNumeric) {
        return { 
          nome: pai?.name || 'Carregando...', 
          tipo: 'sistema',
          id: Number(cavalo.pai)
        };
      } else {
        return { 
          nome: cavalo.pai.toString(), 
          tipo: 'externo',
          id: null 
        };
      }
    }
    
    // Default - sem pai informado
    return { nome: 'Não informado', tipo: 'nenhum', id: null };
  };

  // Função para obter informações da mãe (sistema dual maeId/maeNome)
  const getMaeInfo = (cavalo: Cavalo | null) => {
    if (!cavalo) return { nome: 'Não informado', tipo: 'nenhum', id: null };
    
    // Verificar se tem referência a cavalo do sistema (maeId)
    if (cavalo.maeId) {
      return { 
        nome: mae?.name || 'Carregando...', 
        tipo: 'sistema',
        id: cavalo.maeId
      };
    }
    
    // Verificar se tem nome de cavalo externo (maeNome)
    if (cavalo.maeNome) {
      return { 
        nome: cavalo.maeNome, 
        tipo: 'externo',
        id: null 
      };
    }
    
    // Compatibilidade com campo legado mae
    if (cavalo.mae) {
      // Tentar entender se o campo mae é um ID ou um nome
      const isNumeric = /^\d+$/.test(cavalo.mae.toString());
      if (isNumeric) {
        return { 
          nome: mae?.name || 'Carregando...', 
          tipo: 'sistema',
          id: Number(cavalo.mae)
        };
      } else {
        return { 
          nome: cavalo.mae.toString(), 
          tipo: 'externo',
          id: null 
        };
      }
    }
    
    // Default - sem mãe informada
    return { nome: 'Não informado', tipo: 'nenhum', id: null };
  };

  // Carregar informações do cavalo e seus pais
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Obter o user ID do localStorage para autenticação
        const userJson = localStorage.getItem('user');
        const userId = userJson ? JSON.parse(userJson).id : null;
        
        if (!userId) {
          console.error('❌ [SimpleGenealogy] Usuário não encontrado no localStorage');
          setLoading(false);
          return;
        }
        
        // Adicionar cabeçalho de usuário para autenticação
        const options = {
          headers: {
            'user-id': userId.toString(),
            'Content-Type': 'application/json'
          }
        };
        
        // Buscar informações do cavalo
        const cavaloData = await apiRequest<Cavalo>('GET', `/api/cavalos/${cavaloId}`, undefined, options);
        setCavalo(cavaloData);
        
        // Depuração dos novos campos de genealogia dual
        console.log('🧬 [SimpleGenealogy] Valores genealogia nova (dual):', {
          paiId: cavaloData.paiId,
          paiNome: cavaloData.paiNome,
          maeId: cavaloData.maeId,
          maeNome: cavaloData.maeNome,
          // Campos legados
          pai: cavaloData.pai,
          mae: cavaloData.mae
        });
        
        // Buscar informações do pai, se houver paiId
        if (cavaloData.paiId) {
          try {
            const paiData = await apiRequest<Cavalo>(
              'GET', 
              `/api/cavalos/${cavaloData.paiId}`, 
              undefined, 
              options
            );
            setPai(paiData);
          } catch (error) {
            console.error('❌ [SimpleGenealogy] Erro ao buscar informações do pai:', error);
          }
        } 
        // Verificar campo legado pai (para compatibilidade)
        else if (cavaloData.pai && /^\d+$/.test(cavaloData.pai.toString())) {
          try {
            const paiId = Number(cavaloData.pai);
            const paiData = await apiRequest<Cavalo>(
              'GET', 
              `/api/cavalos/${paiId}`, 
              undefined, 
              options
            );
            setPai(paiData);
          } catch (error) {
            console.error('❌ [SimpleGenealogy] Erro ao buscar informações do pai (campo legado):', error);
          }
        }
        
        // Buscar informações da mãe, se houver maeId
        if (cavaloData.maeId) {
          try {
            const maeData = await apiRequest<Cavalo>(
              'GET', 
              `/api/cavalos/${cavaloData.maeId}`, 
              undefined, 
              options
            );
            setMae(maeData);
          } catch (error) {
            console.error('❌ [SimpleGenealogy] Erro ao buscar informações da mãe:', error);
          }
        }
        // Verificar campo legado mae (para compatibilidade)
        else if (cavaloData.mae && /^\d+$/.test(cavaloData.mae.toString())) {
          try {
            const maeId = Number(cavaloData.mae);
            const maeData = await apiRequest<Cavalo>(
              'GET', 
              `/api/cavalos/${maeId}`, 
              undefined, 
              options
            );
            setMae(maeData);
          } catch (error) {
            console.error('❌ [SimpleGenealogy] Erro ao buscar informações da mãe (campo legado):', error);
          }
        }
      } catch (error) {
        console.error('❌ [SimpleGenealogy] Erro ao buscar dados para genealogia:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [cavaloId]);

  // Obter informações formatadas de pai e mãe
  const paiInfo = getPaiInfo(cavalo);
  const maeInfo = getMaeInfo(cavalo);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-6">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex items-center gap-2 mb-4">
        <GitBranchPlus className="h-5 w-5 text-blue-600" />
        <h2 className="text-xl font-semibold text-gray-800">Árvore Genealógica Simples</h2>
      </div>

      <Card className="overflow-hidden border border-gray-200">
        <CardContent className="p-6">
          {/* O próprio cavalo */}
          <div className="text-center mb-6">
            <div className="inline-block border-2 border-blue-500 bg-blue-50 rounded p-4 text-center min-w-[200px]">
              <div className="font-semibold text-lg">{cavalo?.name || 'Carregando...'}</div>
              <div className="text-sm text-gray-500">{cavalo?.sexo || "Não informado"}</div>
            </div>
          </div>

          {/* Linha conectora */}
          <div className="h-6 w-0.5 bg-gray-300 mx-auto"></div>
          
          {/* Pais */}
          <div className="flex flex-col sm:flex-row justify-center gap-4 sm:gap-12 px-2 sm:px-4 mt-2">
            {/* Pai */}
            <div className="flex-1 sm:flex-none">
              <div className="text-center text-sm font-medium text-gray-500 mb-1">Pai:</div>
              <div className={`border rounded p-3 text-center w-full sm:min-w-[180px] ${
                paiInfo.tipo === 'sistema' 
                  ? "bg-green-50 border-green-200" 
                  : paiInfo.tipo === 'externo'
                    ? "bg-blue-50 border-blue-200"
                    : "bg-gray-50 border-gray-200"
              }`}>
                <div className="font-medium text-xs sm:text-sm">
                  {paiInfo.tipo === 'sistema' 
                    ? `ID: ${paiInfo.id}`
                    : paiInfo.tipo === 'externo'
                      ? "Cavalo Externo"
                      : "Não Informado"
                  }
                </div>
                <Separator className="my-2" />
                <div className="text-sm font-semibold break-words">{paiInfo.nome}</div>
                {pai && (
                  <div className="text-xs text-gray-500 mt-1">{pai.breed}</div>
                )}
                {paiInfo.tipo === 'externo' && (
                  <div className="text-xs text-blue-500 mt-1">Cadastrado externamente</div>
                )}
              </div>
            </div>

            {/* Mãe */}
            <div className="flex-1 sm:flex-none">
              <div className="text-center text-sm font-medium text-gray-500 mb-1">Mãe:</div>
              <div className={`border rounded p-3 text-center w-full sm:min-w-[180px] ${
                maeInfo.tipo === 'sistema' 
                  ? "bg-green-50 border-green-200" 
                  : maeInfo.tipo === 'externo'
                    ? "bg-blue-50 border-blue-200"
                    : "bg-gray-50 border-gray-200"
              }`}>
                <div className="font-medium text-xs sm:text-sm">
                  {maeInfo.tipo === 'sistema' 
                    ? `ID: ${maeInfo.id}`
                    : maeInfo.tipo === 'externo'
                      ? "Égua Externa"
                      : "Não Informada"
                  }
                </div>
                <Separator className="my-2" />
                <div className="text-sm font-semibold break-words">{maeInfo.nome}</div>
                {mae && (
                  <div className="text-xs text-gray-500 mt-1">{mae.breed}</div>
                )}
                {maeInfo.tipo === 'externo' && (
                  <div className="text-xs text-blue-500 mt-1">Cadastrada externamente</div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}