{"timestamp": "2025-05-17T00:25:59.052Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 408367104, "heapTotal": 121565184, "heapUsed": 94006304, "external": 8546758, "arrayBuffers": 269236}, "uptime": 1.677410901, "cpuUsage": {"user": 2784614, "system": 311317}, "resourceUsage": {"userCPUTime": 2784668, "systemCPUTime": 311323, "maxRSS": 398796, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105676, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8125, "involuntaryContextSwitches": 1260}}