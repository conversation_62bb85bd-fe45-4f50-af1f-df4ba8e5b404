{"timestamp": "2025-05-23T02:17:12.327Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 264564736, "heapTotal": 112918528, "heapUsed": 88637168, "external": 8622682, "arrayBuffers": 249274}, "uptime": 2.371534868, "cpuUsage": {"user": 2715365, "system": 373601}, "resourceUsage": {"userCPUTime": 2715418, "systemCPUTime": 373606, "maxRSS": 324272, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103352, "majorPageFault": 1, "swappedOut": 0, "fsRead": 26416, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7943, "involuntaryContextSwitches": 2272}}