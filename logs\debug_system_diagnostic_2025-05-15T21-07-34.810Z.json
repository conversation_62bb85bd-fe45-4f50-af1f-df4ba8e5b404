{"timestamp": "2025-05-15T21:07:34.810Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 286154752, "heapTotal": 112762880, "heapUsed": 72437488, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.612279852, "cpuUsage": {"user": 2599282, "system": 324891}, "resourceUsage": {"userCPUTime": 2599340, "systemCPUTime": 324898, "maxRSS": 279448, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101293, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8389, "involuntaryContextSwitches": 1162}}