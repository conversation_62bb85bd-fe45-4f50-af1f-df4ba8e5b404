{"timestamp": "2025-05-16T23:39:03.230Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 264957952, "heapTotal": 116432896, "heapUsed": 73539016, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.133869387, "cpuUsage": {"user": 3130418, "system": 397411}, "resourceUsage": {"userCPUTime": 3130462, "systemCPUTime": 397411, "maxRSS": 328152, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104327, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 136, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9351, "involuntaryContextSwitches": 6141}}