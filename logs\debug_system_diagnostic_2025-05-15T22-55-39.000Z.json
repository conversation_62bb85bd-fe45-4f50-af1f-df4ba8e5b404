{"timestamp": "2025-05-15T22:55:39.000Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 396967936, "heapTotal": 115122176, "heapUsed": 72501496, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.661914836, "cpuUsage": {"user": 2672428, "system": 325407}, "resourceUsage": {"userCPUTime": 2672475, "systemCPUTime": 325412, "maxRSS": 387664, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104569, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 16, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7813, "involuntaryContextSwitches": 1004}}