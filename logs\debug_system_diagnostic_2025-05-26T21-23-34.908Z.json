{"timestamp": "2025-05-26T21:23:34.907Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 262139904, "heapTotal": 115384320, "heapUsed": 74554760, "external": 8219482, "arrayBuffers": 243725}, "uptime": 6.611285615, "cpuUsage": {"user": 3285911, "system": 471237}, "resourceUsage": {"userCPUTime": 3285976, "systemCPUTime": 471246, "maxRSS": 292932, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104835, "majorPageFault": 6, "swappedOut": 0, "fsRead": 53872, "fsWrite": 992, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9203, "involuntaryContextSwitches": 8754}}