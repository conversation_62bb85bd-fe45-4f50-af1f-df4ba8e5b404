{"timestamp": "2025-05-23T21:43:53.769Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 423243776, "heapTotal": 130523136, "heapUsed": 106203040, "external": 8325511, "arrayBuffers": 249274}, "uptime": 2.598335952, "cpuUsage": {"user": 3160029, "system": 372011}, "resourceUsage": {"userCPUTime": 3160109, "systemCPUTime": 372011, "maxRSS": 413324, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106984, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7944, "involuntaryContextSwitches": 6701}}