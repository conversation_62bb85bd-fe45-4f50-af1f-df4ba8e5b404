import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ArrowLeft, Save } from 'lucide-react';
import { Cavalo } from '@shared/schema';
import { extractCleanName } from '@/utils/genealogy';

interface GenealogyFormData {
  paiId: number | null;
  maeId: number | null;
  avoPaterno: string;
  avoMaterno: string;
}

export default function EditarGenealogia() {
  const [location] = useLocation();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Extrair ID da URL
  const id = location.split('/')[2];
  
  // Estado para os dados de genealogia
  const [formData, setFormData] = useState<GenealogyFormData>({
    paiId: null,
    maeId: null,
    avoPaterno: '',
    avoMaterno: '',
  });

  // Buscar dados do cavalo
  const { data: cavalo, isLoading: loadingCavalo } = useQuery({
    queryKey: ['/api/cavalos', id],
    queryFn: async () => {
      const response = await fetch(`/api/cavalos/${id}`, {
        headers: {
          'User-ID': localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).id : '1',
        }
      });
      if (!response.ok) throw new Error('Erro ao buscar cavalo');
      return response.json();
    },
  });

  // Buscar lista de cavalos para seleção
  const { data: cavalos = [] } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      const response = await fetch('/api/cavalos', {
        headers: {
          'User-ID': localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).id : '1',
        }
      });
      if (!response.ok) throw new Error('Erro ao buscar cavalos');
      return response.json();
    },
  });

  // Filtrar cavalos por sexo
  const machos = (cavalos as Cavalo[]).filter((cavalo: Cavalo) => cavalo.sexo === 'Macho' || cavalo.sexo === 'Macho (Castrado)');
  const femeas = (cavalos as Cavalo[]).filter((cavalo: Cavalo) => cavalo.sexo === 'Fêmea');

  // Preencher dados do formulário quando os dados do cavalo carregarem
  useEffect(() => {
    if (!cavalo) return;

    console.log('Dados brutos do cavalo:', {
      avoPaterno: cavalo.avoPaterno,
      avoMaterno: cavalo.avoMaterno
    });

    const avoPaterno = extractCleanName(cavalo.avoPaterno);
    const avoMaterno = extractCleanName(cavalo.avoMaterno);

    console.log('Dados limpos:', {
      avoPaterno,
      avoMaterno
    });

    setFormData({
      paiId: cavalo.paiId ?? null,
      maeId: cavalo.maeId ?? null,
      avoPaterno, // LIMPO!
      avoMaterno, // LIMPO!
    });
  }, [cavalo]);

  // Função auxiliar para normalizar dados dos avós
  const normalizarAvos = (nome: string | null) =>
    nome
      ? JSON.stringify({
          tipo: "externo",
          cavaloSistemaId: null,
          cavaloNome: nome.trim(),
        })
      : null;

  // Mutation para salvar alterações
  const updateMutation = useMutation({
    mutationFn: async (data: GenealogyFormData) => {
      const body = {
        paiId: data.paiId,
        maeId: data.maeId,
        avoPaterno: normalizarAvos(data.avoPaterno),
        avoMaterno: normalizarAvos(data.avoMaterno),
      };

      const response = await fetch(`/api/cavalos/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'User-ID': localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).id : '1',
        },
        body: JSON.stringify(body),
      });
      if (!response.ok) throw new Error('Erro ao salvar');
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Genealogia atualizada!',
        description: 'As informações genealógicas foram salvas com sucesso.',
      });
      // Invalidar cache para recarregar os dados
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos', id] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      // Voltar para a página de edição
      setLocation(`/cavalo/${id}/editar`);
    },
    onError: (error: any) => {
      toast({
        title: 'Erro ao salvar',
        description: error.message || 'Ocorreu um erro ao salvar as alterações.',
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateMutation.mutate(formData);
  };

  const handleVoltar = () => {
    setLocation(`/cavalo/${id}/editar`);
  };

  if (loadingCavalo) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-300"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={handleVoltar}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar
        </Button>
        
        <h1 className="text-2xl font-bold">Editar Genealogia</h1>
        <p className="text-gray-600">Cavalo: {cavalo?.name}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Pai */}
          <Card>
            <CardHeader>
              <CardTitle>Pai</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="pai">Selecionar Pai</Label>
                  <Select 
                    value={formData.paiId?.toString() ?? "0"} 
                    onValueChange={(value) => 
                      setFormData(prev => ({ 
                        ...prev, 
                        paiId: value === "0" ? null : parseInt(value) 
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o pai" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Nenhum</SelectItem>
                      {machos.map((cavalo) => (
                        <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                          <div className="flex flex-col">
                            <span className="font-medium">{cavalo.name}</span>
                            <span className="text-xs text-gray-500">
                              {cavalo.breed && `${cavalo.breed}`}
                              {cavalo.cor && ` • ${cavalo.cor}`}
                              {cavalo.sexo && ` • ${cavalo.sexo}`}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Mãe */}
          <Card>
            <CardHeader>
              <CardTitle>Mãe</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="mae">Selecionar Mãe</Label>
                  <Select 
                    value={formData.maeId?.toString() ?? "0"} 
                    onValueChange={(value) => 
                      setFormData(prev => ({ 
                        ...prev, 
                        maeId: value === "0" ? null : parseInt(value) 
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a mãe" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Nenhuma</SelectItem>
                      {femeas.map((cavalo) => (
                        <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                          <div className="flex flex-col">
                            <span className="font-medium">{cavalo.name}</span>
                            <span className="text-xs text-gray-500">
                              {cavalo.breed && `${cavalo.breed}`}
                              {cavalo.cor && ` • ${cavalo.cor}`}
                              {cavalo.sexo && ` • ${cavalo.sexo}`}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Avô Paterno */}
          <Card>
            <CardHeader>
              <CardTitle>Avô Paterno</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="avoPaterno">Nome do Avô Paterno</Label>
                <Input
                  id="avoPaterno"
                  value={formData.avoPaterno}
                  onChange={(e) => 
                    setFormData(prev => ({ 
                      ...prev, 
                      avoPaterno: e.target.value 
                    }))
                  }
                  placeholder="Digite o nome do avô paterno"
                />
              </div>
            </CardContent>
          </Card>

          {/* Avô Materno */}
          <Card>
            <CardHeader>
              <CardTitle>Avô Materno</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="avoMaterno">Nome do Avô Materno</Label>
                <Input
                  id="avoMaterno"
                  value={formData.avoMaterno}
                  onChange={(e) => 
                    setFormData(prev => ({ 
                      ...prev, 
                      avoMaterno: e.target.value 
                    }))
                  }
                  placeholder="Digite o nome do avô materno"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Botões de ação */}
        <div className="flex gap-4 pt-6">
          <Button 
            type="submit" 
            disabled={updateMutation.isPending}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {updateMutation.isPending ? 'Salvando...' : 'Salvar Alterações'}
          </Button>
          
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleVoltar}
          >
            Cancelar
          </Button>
        </div>
      </form>
    </div>
  );
}