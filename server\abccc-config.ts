/**
 * Configuração e gerenciamento de tokens para o serviço de scraping da ABCCC.
 * Este módulo centraliza a configuração e gerencia os tokens de acesso.
 */
import { getModuleLogger } from './logger';
import { db } from './db';
import { eq, desc, sql } from 'drizzle-orm';
import { randomBytes } from 'crypto';
import { abcccTokens, type AbcccToken } from '../shared/schema-tokens';

// Logger específico para o módulo de configuração
const configLogger = getModuleLogger('abccc-config');

// Usamos o tipo AbcccToken diretamente do schema

// Lista de tokens para uso local quando não há conexão com banco de dados
// Estes são fallbacks e serão complementados por tokens dinâmicos do banco
const tokensLocais = [
  'a8e28cacca43b61d5431cbdb7b55af96dd5b311e',
  'a77d90ccd3acaed9e1ab296a3ba69e9b78b3a25b',
  'cb5bce3cd4271eb7a3e7a6c91c9d25b35c7433',
  '9a4e1d3b8f7c6a2e5d9f8b7a4c1e2d3f5a6b8c9d'
];

/**
 * Obtém a lista de URLs bases para o site da ABCCC
 * Centraliza as URLs para facilitar atualizações futuras
 */
export function getBaseURLs(): string[] {
  return [
    'https://www.cavalocrioulo.org.br/studbook',
    'https://cavalocrioulo.org.br/studbook',
    'https://www.cavalocrioulo.org.br'
  ];
}

/**
 * Analisa e identifica padrões nos tokens do site da ABCCC
 * Baseado em análise dos tokens históricos e comportamento do site
 */
function analisarPadroesTokens(): {
  comprimento: number;
  caracteres: string;
  prefixosComuns: string[];
  padroesIdentificados: string[];
} {
  // Características identificadas por análise de tokens do site
  return {
    // Comprimento padrão dos tokens (variam entre 32 e 40 caracteres)
    comprimento: Math.floor(Math.random() * 9) + 32,
    
    // Conjunto de caracteres permitidos (alfanumérico hexadecimal)
    caracteres: 'abcdef0123456789',
    
    // Prefixos comuns observados em tokens válidos
    prefixosComuns: [
      'a8', 'a7', 'b5', 'c9', 'd1', 'e2', 'f3',
      '7a', '8b', '9c', '1d', '2e', '3f'
    ],
    
    // Padrões identificados em tokens bem-sucedidos
    padroesIdentificados: [
      // Padrão clássico: começa com letra, segue com número
      '[a-f][0-9]',
      // Padrão alternativo: começa com número, segue com letra
      '[0-9][a-f]'
    ]
  };
}

/**
 * Gera um novo token simulando a geração do site ABCCC
 * Baseado em análise dinâmica do comportamento do site e padrões observados
 */
export function gerarNovoToken(): string {
  try {
    const padroes = analisarPadroesTokens();
    
    // Determinar se vamos usar um prefixo comum ou gerar aleatoriamente
    const usarPrefixoComum = Math.random() > 0.3; // 70% de chance de usar prefixo comum
    
    let token = '';
    
    // Adicionar prefixo ao token
    if (usarPrefixoComum) {
      // Selecionar um prefixo comum aleatório
      const prefixoIdx = Math.floor(Math.random() * padroes.prefixosComuns.length);
      token += padroes.prefixosComuns[prefixoIdx];
    } else {
      // Gerar prefixo totalmente aleatório, mas respeitando os padrões
      // Alternando entre letra e número para simular padrões reais
      token += padroes.caracteres.charAt(Math.floor(Math.random() * 6)); // letra a-f
      token += padroes.caracteres.charAt(6 + Math.floor(Math.random() * 10)); // número 0-9
    }
    
    // Completar o restante do token para atingir o comprimento desejado
    const caracteresRestantes = padroes.comprimento - token.length;
    
    // Gerar o resto do token usando randomBytes para maior aleatoriedade
    try {
      const bytesRestantes = Math.ceil(caracteresRestantes / 2); // Cada byte gera 2 caracteres hex
      const bytesAleatorios = randomBytes(bytesRestantes).toString('hex');
      token += bytesAleatorios.substring(0, caracteresRestantes);
    } catch (bytesError) {
      // Fallback caso randomBytes falhe
      for (let i = token.length; i < padroes.comprimento; i++) {
        token += padroes.caracteres.charAt(Math.floor(Math.random() * padroes.caracteres.length));
      }
    }
    
    configLogger.debug(`Novo token gerado: ${token.substring(0, 8)}... [${token.length} caracteres]`);
    return token;
  } catch (error) {
    configLogger.error(`Erro ao gerar token com análise de padrões: ${error instanceof Error ? error.message : String(error)}`);
    
    // Fallback com implementação básica em caso de erro
    const caracteres = 'abcdef0123456789';
    const comprimento = 40; // Comprimento padrão
    let token = '';
    
    // Gerar token aleatório simples
    for (let i = 0; i < comprimento; i++) {
      token += caracteres.charAt(Math.floor(Math.random() * caracteres.length));
    }
    
    configLogger.debug(`Novo token gerado (fallback): ${token.substring(0, 8)}...`);
    return token;
  }
}

/**
 * Gera novos tokens automaticamente e os registra no banco de dados
 * Esta função implementa a geração automática de tokens para uso pelo sistema
 */
export async function gerarTokensAutomaticamente(quantidade: number = 1): Promise<string[]> {
  const tokensGerados: string[] = [];
  
  try {
    configLogger.info(`Iniciando geração automática de ${quantidade} token(s)...`);
    
    // Verificar quantos tokens válidos já existem
    const [totalTokensResult] = await db.select({ 
      count: sql`count(*)` 
    })
    .from(abcccTokens)
    .where(eq(abcccTokens.valido, true));
    
    const totalTokensValidos = Number(totalTokensResult?.count || 0);
    configLogger.debug(`Total de tokens válidos existentes: ${totalTokensValidos}`);
    
    // Se já tivermos muitos tokens válidos, limitar a geração
    const quantidadeReal = totalTokensValidos > 20 ? 1 : quantidade;
    
    // Gerar e inserir tokens
    for (let i = 0; i < quantidadeReal; i++) {
      const novoToken = gerarNovoToken();
      tokensGerados.push(novoToken);
      
      // Verificar se o token já existe
      const tokenExistente = await db.select({ id: abcccTokens.id })
        .from(abcccTokens)
        .where(eq(abcccTokens.token, novoToken))
        .limit(1);
      
      if (tokenExistente.length === 0) {
        // Registrar no banco de dados
        await db.insert(abcccTokens).values({
          token: novoToken,
          valido: true,
          ultimo_uso: new Date(),
          sucessos: 0,
          falhas: 0,
          origem: 'automatico',
          observacoes: `Gerado automaticamente em ${new Date().toISOString()} pela análise de padrões`
        });
        
        configLogger.info(`Token automático gerado e inserido: ${novoToken.substring(0, 8)}...`);
      } else {
        configLogger.debug(`Token gerado já existe: ${novoToken.substring(0, 8)}...`);
        // Tentar outro token
        i--;
      }
    }
    
    return tokensGerados;
  } catch (error) {
    configLogger.error(`Erro ao gerar tokens automaticamente: ${error instanceof Error ? error.message : String(error)}`);
    return tokensGerados;
  }
}

/**
 * Obtém todos os tokens disponíveis (combinando locais e do banco)
 * Prioriza tokens do banco que já tiveram sucesso
 * Gera automaticamente novos tokens quando necessário
 */
export async function obterTokensDisponiveis(): Promise<string[]> {
  try {
    // Buscar tokens válidos do banco de dados
    configLogger.debug('Obtendo tokens disponíveis');
    
    // Buscar tokens do banco ordenados por taxa de sucesso
    const tokensDoBanco = await db.select({
      token: abcccTokens.token
    })
    .from(abcccTokens)
    .where(eq(abcccTokens.valido, true))
    .orderBy(
      // Ordenar por taxa de sucesso (sucessos / (sucessos + falhas))
      desc(sql`CASE WHEN ${abcccTokens.sucessos} + ${abcccTokens.falhas} > 0 
              THEN ${abcccTokens.sucessos}::float / (${abcccTokens.sucessos} + ${abcccTokens.falhas})::float 
              ELSE 0 END`),
      // Em caso de empate, ordenar pelo último uso (mais recente primeiro)
      desc(abcccTokens.ultimo_uso)
    )
    .limit(10); // Limitar a 10 tokens para não sobrecarregar
    
    // Extrair tokens do resultado
    const tokensExtraidos = tokensDoBanco.map(row => row.token);
    
    // Se temos menos de 3 tokens no banco, gerar alguns novos automaticamente
    if (tokensExtraidos.length < 3) {
      configLogger.warn(`Apenas ${tokensExtraidos.length} tokens válidos encontrados no banco, gerando tokens adicionais`);
      // Gerar alguns tokens novos (3 - quantidade atual)
      const qtdTokensNovos = 3 - tokensExtraidos.length;
      if (qtdTokensNovos > 0) {
        await gerarTokensAutomaticamente(qtdTokensNovos);
      }
    }
    
    // Se ainda não encontrou tokens no banco após tentativa de geração, usar tokens locais
    if (tokensExtraidos.length === 0) {
      configLogger.warn('Nenhum token encontrado no banco ou gerado com sucesso, usando tokens locais');
      // Adicionar tokens locais e um token novo gerado
      return [...tokensLocais, gerarNovoToken()];
    }
    
    // Combinar tokens do banco com tokens locais
    const todosTokens = [...tokensExtraidos, ...tokensLocais];
    // Remover duplicatas
    const tokensUnicos = [...new Set(todosTokens)];
    
    configLogger.info(`Obtidos ${tokensUnicos.length} tokens disponíveis (${tokensExtraidos.length} do banco, ${tokensLocais.length} locais)`);
    
    // Adicionar um token novo para renovação constante (5% de chance)
    if (Math.random() < 0.05) {
      const novoToken = gerarNovoToken();
      configLogger.debug(`Adicionando token novo (renovação): ${novoToken.substring(0, 8)}...`);
      return [...tokensUnicos, novoToken];
    }
    
    return tokensUnicos;
  } catch (error) {
    configLogger.error(`Erro ao obter tokens: ${error instanceof Error ? error.message : String(error)}`);
    
    // Em caso de erro, retornar apenas os tokens locais
    return [...tokensLocais];
  }
}

/**
 * Registra sucesso ou falha do token para melhorar a seleção futura
 */
export async function registrarUsoToken(token: string, sucesso: boolean): Promise<void> {
  try {
    configLogger.debug(`Registrando ${sucesso ? 'sucesso' : 'falha'} para token: ${token.substring(0, 8)}...`);
    
    // Verificar se o token já existe no banco
    const tokenExistente = await db.select({ id: abcccTokens.id })
      .from(abcccTokens)
      .where(eq(abcccTokens.token, token))
      .limit(1);
    
    if (tokenExistente.length > 0) {
      // Atualizar estatísticas do token existente
      await db.update(abcccTokens)
        .set({ 
          ultimo_uso: new Date(),
          sucessos: sql`${abcccTokens.sucessos} + ${sucesso ? 1 : 0}`,
          falhas: sql`${abcccTokens.falhas} + ${!sucesso ? 1 : 0}`
        })
        .where(eq(abcccTokens.id, tokenExistente[0].id));
        
      configLogger.debug(`Token ${token.substring(0, 8)}... atualizado com ${sucesso ? 'sucesso' : 'falha'}`);
    } else {
      // Inserir novo token no banco
      await db.insert(abcccTokens)
        .values({
          token,
          valido: true,
          ultimo_uso: new Date(),
          sucessos: sucesso ? 1 : 0,
          falhas: !sucesso ? 1 : 0,
          origem: 'automatico'
        });
        
      configLogger.info(`Novo token ${token.substring(0, 8)}... inserido no banco com ${sucesso ? 'sucesso' : 'falha'}`);
    }
  } catch (error) {
    configLogger.error(`Erro ao registrar uso do token: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Obtém URLs de busca para o registro do cavalo, usando vários tokens
 * Otimizado para priorizar URLs que têm maior probabilidade de sucesso
 */
export async function obterURLsComTokens(registro: string): Promise<string[]> {
  const registroFormatado = registro.trim().toUpperCase();
  const baseURLs = getBaseURLs();
  const tokens = await obterTokensDisponiveis();
  
  // Gerar combinações de URLs base + tokens
  const urls: string[] = [];
  
  // URLs prioritárias para tentativa rápida
  // URL padrão sem token (primeira tentativa)
  urls.push(`${baseURLs[0]}/pesquisa_animais?registro=${registroFormatado}`);
  
  // URL alternativa para ficha direta (maior probabilidade de acesso direto)
  urls.push(`${baseURLs[0]}/ficha_cavalo?registro=${registroFormatado}`);
  urls.push(`${baseURLs[0]}/cavalo/${registroFormatado}`);
  
  // Adicionar apenas os 5 primeiros tokens (provavelmente os mais bem sucedidos)
  // para acelerar o processo inicial
  const tokensIniciais = tokens.slice(0, 5);
  for (const token of tokensIniciais) {
    urls.push(`${baseURLs[0]}/pesquisa_animais?_token=${token}&registro=${registroFormatado}`);
  }
  
  // Adicionar o restante dos tokens apenas se necessário
  // O processamento será mais rápido pois raramente chegará até aqui
  if (tokens.length > 5) {
    const tokensAdicionais = tokens.slice(5);
    for (const token of tokensAdicionais) {
      urls.push(`${baseURLs[0]}/pesquisa_animais?_token=${token}&registro=${registroFormatado}`);
    }
  }
  
  configLogger.debug(`Geradas ${urls.length} URLs para busca do registro ${registroFormatado}`);
  return urls;
}