{"timestamp": "2025-05-15T21:27:01.116Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 263585792, "heapTotal": 115908608, "heapUsed": 72434544, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.78818249, "cpuUsage": {"user": 2738384, "system": 337965}, "resourceUsage": {"userCPUTime": 2738430, "systemCPUTime": 337970, "maxRSS": 329408, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102423, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8643, "involuntaryContextSwitches": 2544}}