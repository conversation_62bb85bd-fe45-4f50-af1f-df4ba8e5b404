import { useState, useEffect } from "react";
import { useLocation, Link } from "wouter";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { useLockBody } from "@/hooks/use-lock-body";
import { apiRequest } from "@/lib/queryClient";
import { <PERSON><PERSON>lo } from "@shared/schema";

// Lucide ícones - importar conforme necessário
import {
  Menu,
  X,
  Home,
  Stethoscope,
  Calendar,
  ClipboardList,
  LineChart,
  FileText,
  Settings,
  ShieldAlert as Shield,
  Bell,
  Users,
  Activity,
  Dna,
  ChevronDown,
  ChevronRight,
  HelpCircle,
  Heart,
  ShoppingCart,
  BarChart2,
  Send,
  MessageSquare,
  LucideMessageSquare,
  BellRing,
  User,
  DollarSign,
  Bot,
  PieChart,
  List,
  Key,
  Database,
  Terminal,
} from "lucide-react";

export interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
  submenu?: MenuItem[];
}

interface MenuSection {
  title: string;
  items: MenuItem[];
}

// Definir seções do menu
const menuSections: MenuSection[] = [
  {
    title: "Principal",
    items: [
      {
        id: "dashboard",
        label: "Dashboard",
        icon: <Home className="h-5 w-5" />,
        href: "/",
      },
      {
        id: "cavalos",
        label: "Cavalos",
        icon: <Stethoscope className="h-5 w-5" />,
        href: "/cavalos",
        submenu: [
          {
            id: "cadastro",
            label: "Cadastro",
            icon: <ClipboardList className="h-4 w-4" />,
            href: "/cavalos",
          },
          {
            id: "manejos",
            label: "Manejos",
            icon: <Activity className="h-4 w-4" />,
            href: "/manejos",
          },
          {
            id: "nutricao",
            label: "Nutrição",
            icon: <Heart className="h-4 w-4" />,
            href: "/cavalos/nutricao",
          },
          {
            id: "reproducao",
            label: "Reprodução",
            icon: <Users className="h-4 w-4" />,
            href: "/reproducao",
          },
        ],
      },
      {
        id: "veterinario",
        label: "Veterinária",
        icon: <Stethoscope className="h-5 w-5" />,
        href: "/veterinario",
        submenu: [
          {
            id: "registros",
            label: "Registros Clínicos",
            icon: <ClipboardList className="h-4 w-4" />,
            href: "/veterinario/registros",
          },
          {
            id: "vacinacoes",
            label: "Vacinações",
            icon: <Calendar className="h-4 w-4" />,
            href: "/veterinario/vacinacoes",
          },
          {
            id: "vermifugacoes",
            label: "Vermifugações",
            icon: <Activity className="h-4 w-4" />,
            href: "/veterinario/vermifugacoes",
          },
        ],
      },
      {
        id: "agenda",
        label: "Agenda",
        icon: <Calendar className="h-5 w-5" />,
        href: "/agenda",
      },
      {
        id: "eventos",
        label: "Eventos",
        icon: <Bell className="h-5 w-5" />,
        href: "/eventos",
      },
      {
        id: "arquivos",
        label: "Arquivos",
        icon: <FileText className="h-5 w-5" />,
        href: "/arquivos",
      },
      {
        id: "genetica",
        label: "Genética",
        icon: <Dna className="h-5 w-5" />,
        href: "/genetica",
        submenu: [
          {
            id: "morfologia",
            label: "Morfologia",
            icon: <ClipboardList className="h-4 w-4" />,
            href: "/genetica?tab=morfologia", 
          },
          {
            id: "genealogia",
            label: "Genealogia",
            icon: <Users className="h-4 w-4" />,
            href: "/genetica?tab=genealogia",
          },
          {
            id: "desempenho",
            label: "Desempenho",
            icon: <Activity className="h-4 w-4" />,
            href: "/genetica?tab=desempenho",
          },
          {
            id: "sugestoes",
            label: "Sugestões de Cruzamento",
            icon: <MessageSquare className="h-4 w-4" />,
            href: "/genetica?tab=sugestoes",
          },
        ],
      },
    ],
  },
  {
    title: "Gestão",
    items: [
      {
        id: "financeiro",
        label: "Financeiro",
        icon: <DollarSign className="h-5 w-5" />,
        href: "/financeiro",
      },
      {
        id: "relatorios",
        label: "Relatórios",
        icon: <BarChart2 className="h-5 w-5" />,
        href: "/relatorios",
      },
      {
        id: "usuarios",
        label: "Usuários",
        icon: <User className="h-5 w-5" />,
        href: "/usuarios",
      },
    ],
  },
  {
    title: "Sistema",
    items: [
      {
        id: "configuracoes",
        label: "Configurações",
        icon: <Settings className="h-5 w-5" />,
        href: "/configuracoes",
        submenu: [
          {
            id: "geral",
            label: "Geral",
            icon: <Settings className="h-4 w-4" />,
            href: "/configuracoes",
          },
          {
            id: "abccc-tokens",
            label: "Tokens ABCCC",
            icon: <Shield className="h-4 w-4" />,
            href: "/admin/abccc-tokens",
          },
        ],
      },
      {
        id: "admin",
        label: "Administração",
        icon: <Database className="h-5 w-5" />,
        href: "/admin",
        submenu: [
          {
            id: "abccc-tokens",
            label: "Tokens ABCCC",
            icon: <Key className="h-4 w-4" />,
            href: "/admin/abccc-tokens",
          },
          {
            id: "sistema",
            label: "Sistema",
            icon: <Terminal className="h-4 w-4" />,
            href: "/admin/sistema",
          },
        ],
      },
      {
        id: "assistente",
        label: "Assistente Virtual",
        icon: <Bot className="h-5 w-5" />,
        href: "/assistente",
      },
      {
        id: "ajuda",
        label: "Ajuda",
        icon: <HelpCircle className="h-5 w-5" />,
        href: "/ajuda",
      },
    ],
  },
];

// Componente para uma seção do menu
const MenuSectionComponent = ({
  section,
  isCollapsed,
  closeMobileMenu,
}: {
  section: MenuSection;
  isCollapsed: boolean;
  closeMobileMenu?: () => void;
}) => {
  const isMobile = useIsMobile();

  return (
    <div className="mt-5 first:mt-0">
      {!isCollapsed && (
        <div className="mb-2 px-4 text-xs uppercase text-blue-300/80 font-semibold tracking-wider">
          {section.title}
        </div>
      )}
      <div className={cn("px-2 space-y-1.5", isCollapsed && "mt-2")}>
        {section.items.map((item) => (
          <MenuItem
            key={item.id}
            item={item}
            isCollapsed={isCollapsed}
            closeMobileMenu={closeMobileMenu}
            isMobileView={isMobile}
          />
        ))}
      </div>
    </div>
  );
};

// Renderizar um item de menu
const MenuItem = ({
  item,
  isCollapsed,
  depth = 0,
  closeMobileMenu,
  isMobileView,
}: {
  item: MenuItem;
  isCollapsed: boolean;
  depth?: number;
  closeMobileMenu?: () => void;
  isMobileView?: boolean;
}) => {
  const [location, navigate] = useLocation();
  
  // Verificar se o item está ativo, considerando parâmetros de consulta
  const isActive = (() => {
    const currentPath = location.split('?')[0];
    const itemPath = item.href.split('?')[0];
    
    // Se o caminho base for o mesmo, considera ativo
    if (currentPath === itemPath) {
      // Se o item tiver parâmetros e o location também, verificar se são os mesmos
      if (item.href.includes('?') && location.includes('?')) {
        const itemParams = new URLSearchParams(item.href.split('?')[1]);
        const currentParams = new URLSearchParams(location.split('?')[1]);
        
        // Comparar o parâmetro 'tab' se existir
        const itemTab = itemParams.get('tab');
        const currentTab = currentParams.get('tab');
        
        if (itemTab && currentTab) {
          return itemTab === currentTab;
        }
      }
      return true;
    }
    
    // Ou se a URL completa for a mesma
    return location === item.href;
  })();
  
  const [isSubmenuOpen, setIsSubmenuOpen] = useState(false);
  const hasSubmenu = item.submenu && item.submenu.length > 0;
  const isMobile = useIsMobile();
  
  // No modo móvel, sempre mostramos os submenus expandidos
  const effectiveSubmenuOpen = isMobileView ? true : isSubmenuOpen;

  // Persistir estado de abertura do submenu no localStorage - apenas para desktop
  useEffect(() => {
    if (!isMobileView) {
      const storedMenuState = localStorage.getItem(`menu_${item.id}`);
      if (storedMenuState) {
        setIsSubmenuOpen(JSON.parse(storedMenuState));
      }
    }
  }, [item.id, isMobileView]);

  // Salvar estado do menu quando mudar - apenas para desktop
  useEffect(() => {
    if (hasSubmenu && !isMobileView) {
      localStorage.setItem(`menu_${item.id}`, JSON.stringify(isSubmenuOpen));
    }
  }, [hasSubmenu, isSubmenuOpen, item.id, isMobileView]);

  // Verificar se algum submenu está ativo
  const isSubmenuActive =
    hasSubmenu && item.submenu!.some((subItem) => {
      // Verificar se a URL base é a mesma, ignorando parâmetros de consulta
      const currentPath = location.split('?')[0];
      const subItemPath = subItem.href.split('?')[0];
      
      // Se a rota base for a mesma, considera ativo
      if (currentPath === subItemPath) return true;
      
      // Ou se a URL completa for a mesma
      return location === subItem.href;
    });

  // Abrir automaticamente submenu quando um item interno estiver ativo - apenas para desktop
  useEffect(() => {
    if (!isMobileView && isSubmenuActive && !isSubmenuOpen) {
      setIsSubmenuOpen(true);
    }
  }, [isSubmenuActive, isSubmenuOpen, isMobileView]);

  // Função para alternar o submenu
  const handleSubmenuToggle = () => {
    setIsSubmenuOpen(!isSubmenuOpen);
  };

  // Função para navegação
  const handleNavigate = (href: string) => {
    navigate(href);
    if (closeMobileMenu) {
      setTimeout(closeMobileMenu, 150);
    }
  };

  // Renderização para modo móvel
  if (isMobileView) {
    return (
      <div className="mb-2">
        <div 
          className={cn(
            "flex items-center w-full text-white px-4 py-3 rounded-md cursor-pointer",
            isActive ? "bg-blue-600" : "hover:bg-[#134282]",
            depth > 0 && "pl-8 text-sm border-l border-blue-400"
          )}
          onClick={() => {
            if (hasSubmenu) {
              // No mobile, clique no item com submenu apenas navega
              handleNavigate(item.href);
            } else {
              // Item sem submenu - navega normalmente
              handleNavigate(item.href);
            }
          }}
        >
          <span className="mr-3">{item.icon}</span>
          <span className="flex-1 text-left font-medium">{item.label}</span>
          {hasSubmenu && <ChevronRight className="h-4 w-4" />}
        </div>
        
        {/* Sempre mostrar submenus no modo móvel */}
        {hasSubmenu && (
          <div className="pl-4 mt-1 ml-3 border-l border-blue-500/50">
            {item.submenu!.map((subItem) => (
              <MenuItem
                key={subItem.id}
                item={subItem}
                isCollapsed={false}
                depth={depth + 1}
                closeMobileMenu={closeMobileMenu}
                isMobileView={true}
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  // Renderização para desktop
  return (
    <div>
      {hasSubmenu ? (
        // Item com submenu
        <div
          className={cn(
            "flex items-center w-full text-white cursor-pointer",
            isCollapsed ? "h-9 px-2" : "px-2 py-2 mb-1",
            depth > 0 && !isCollapsed && "pl-8 text-sm",
            isSubmenuActive
              ? "bg-blue-600 text-white hover:bg-blue-700"
              : "hover:bg-[#134282] hover:text-white",
            "rounded-md",
          )}
          onClick={() => {
            // No desktop, clique no item com submenu também navega
            handleNavigate(item.href);
            // E também abre/fecha o submenu
            handleSubmenuToggle();
          }}
        >
          <span className={cn("mr-2")}>{item.icon}</span>
          {!isCollapsed && (
            <>
              <span className="flex-1 text-left">{item.label}</span>
              {effectiveSubmenuOpen ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </>
          )}
        </div>
      ) : (
        // Item sem submenu
        <div 
          className={cn(
            "flex items-center w-full text-white cursor-pointer",
            isCollapsed ? "h-9 px-2" : "px-2 py-2 mb-1",
            depth > 0 && !isCollapsed && "pl-8 text-sm",
            isActive
              ? "bg-blue-600 text-white hover:bg-blue-700"
              : "hover:bg-[#134282] hover:text-white",
            "rounded-md",
          )}
          onClick={() => {
            // Para itens sem submenu, apenas navega
            handleNavigate(item.href);
          }}
        >
          <span className={cn("mr-2")}>{item.icon}</span>
          {!isCollapsed && (
            <span className="flex-1 text-left">{item.label}</span>
          )}
        </div>
      )}

      {/* Renderizar submenu se existir e estiver aberto */}
      {hasSubmenu && !isCollapsed && effectiveSubmenuOpen && (
        <div className="pl-2 mt-1 border-l border-blue-500/30">
          {item.submenu!.map((subItem) => (
            <MenuItem
              key={subItem.id}
              item={subItem}
              isCollapsed={isCollapsed}
              depth={depth + 1}
              closeMobileMenu={closeMobileMenu}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Componente principal Sidebar
export function Sidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const isMobile = useIsMobile();

  // Função para fechar o menu mobile
  const closeMobileMenu = () => {
    if (isMobile && isMobileMenuOpen) {
      console.log("Fechando menu mobile");
      setIsMobileMenuOpen(false);
    }
  };

  // Bloqueia o scroll quando o menu mobile estiver aberto
  useLockBody(isMobile && isMobileMenuOpen);

  // Para dispositivos móveis, collapse por padrão
  const isEffectivelyCollapsed = isMobile ? true : isCollapsed;

  // Ajustar o deslocamento da página quando o menu é aberto em dispositivos móveis
  useEffect(() => {
    if (isMobile) {
      const root = document.documentElement;
      if (isMobileMenuOpen) {
        root.classList.add("sidebar-mobile-open");
      } else {
        root.classList.remove("sidebar-mobile-open");
      }
    }

    return () => {
      document.documentElement.classList.remove("sidebar-mobile-open");
    };
  }, [isMobile, isMobileMenuOpen]);

  // Escuta o evento de navegação do menu mobile
  useEffect(() => {
    const handleMobileNavigationEvent = () => {
      if (isMobile && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    const handleSubmenuToggleEvent = () => {
      // Pode ser usado para criar comportamentos especiais quando
      // os submenus são alterados em dispositivos móveis
      console.log("Submenu toggle event detected");
    };

    window.addEventListener(
      "mobile-menu-navigate",
      handleMobileNavigationEvent,
    );
    window.addEventListener("submenu-toggle", handleSubmenuToggleEvent);

    return () => {
      window.removeEventListener(
        "mobile-menu-navigate",
        handleMobileNavigationEvent,
      );
      window.removeEventListener("submenu-toggle", handleSubmenuToggleEvent);
    };
  }, [isMobile, isMobileMenuOpen]);

  return (
    <>
      {/* Mobile Menu Button - fixo na parte superior para visibilidade e acessibilidade */}
      {isMobile && (
        <button
          type="button"
          className="fixed top-0 left-0 z-[110] h-16 w-16 bg-[#0A3364] text-white flex items-center justify-center border-r border-b border-blue-700"
          onClick={() => {
            console.log("Menu button clicked");
            setIsMobileMenuOpen(!isMobileMenuOpen);
          }}
          aria-label="Menu principal"
          aria-expanded={isMobileMenuOpen}
        >
          {isMobileMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </button>
      )}

      {/* Sidebar container */}
      <div
        className={cn(
          "fixed left-0 top-0 z-90 h-screen transition-all duration-300 bg-[#0A3364] text-white mobile-sidebar",
          isEffectivelyCollapsed ? "w-14" : "w-64",
          isMobile && !isMobileMenuOpen && "hidden",
        )}
      >
        {/* Logo área */}
        <div className="flex h-16 items-center px-3 bg-[#072c58]">
          {!isEffectivelyCollapsed && (
            <div className="flex items-center ml-5">
              <img
                src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWhvcnNlIj48cGF0aCBkPSJNMTcgMTd2LTJhMSAxIDAgMCAwLTEtMWgtNFY4YTEgMSAwIDAgMSAxLTFoMy41YTEgMSAwIDAgMSAxIDEgMiAyIDAgMCAxLTIgMmgtLjVhMSAxIDAgMCAwLTEgMXYxYTEgMSAwIDAgMCAxIDFoM2ExIDEgMCAwIDEgMSAxdjVhMyAzIDAgMCAxLTMgM0g0YTEgMSAwIDAgMS0xLTF2LTRhMSAxIDAgMCAxIDEtMWg0YTEgMSAwIDAgMCAxLTFWOWEzIDMgMCAwIDEgMy0zIi8+PHBhdGggZD0iTTE5IDdhNiA2IDAgMCAxLTQgNmEzIDMgMCAwIDE9MyAzIi8+PC9zdmc+"
                alt="Horse Icon"
                className="h-8 w-8 mr-2"
              />
              <span className="text-lg font-bold tracking-wide">
                RS Horse
              </span>
            </div>
          )}
          {isEffectivelyCollapsed && (
            <div className="mx-auto">
              <img
                src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWhvcnNlIj48cGF0aCBkPSJNMTcgMTd2LTJhMSAxIDAgMCAwLTEtMWgtNFY4YTEgMSAwIDAgMSAxLTFoMy41YTEgMSAwIDAgMSAxIDEgMiAyIDAgMCAxLTIgMmgtLjVhMSAxIDAgMCAwLTEgMXYxYTEgMSAwIDAgMCAxIDFoM2ExIDEgMCAwIDEgMSAxdjVhMyAzIDAgMCAxLTMgM0g0YTEgMSAwIDAgMS0xLTF2LTRhMSAxIDAgMCAxIDEtMWg0YTEgMSAwIDAgMCAxLTFWOWEzIDMgMCAwIDEgMy0zIi8+PHBhdGggZD0iTTE5IDdhNiA2IDAgMCAxLTQgNmEzIDMgMCAwIDE9MyAzIi8+PC9zdmc+"
                alt="Horse Icon"
                className="h-8 w-8"
              />
            </div>
          )}

          {/* Toggle button (not shown on mobile) */}
          {!isMobile && (
            <Button
              variant="ghost"
              size="icon"
              className="ml-auto h-8 w-8 text-white hover:bg-[#0F407C]"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>

        {/* Menu sections */}
        <div className="h-[calc(100vh-4rem)] overflow-y-auto pb-10 scrollbar-thin scrollbar-thumb-blue-400 scrollbar-track-blue-900 mt-2">
          {/* Menu para qualquer tipo de dispositivo: mobile ou desktop */}
          {menuSections.map((section) => (
            <MenuSectionComponent
              key={section.title}
              section={section}
              isCollapsed={isEffectivelyCollapsed}
              closeMobileMenu={closeMobileMenu}
            />
          ))}
        </div>
      </div>

      {/* Overlay para fechar o menu em dispositivos móveis */}
      {isMobile && isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-[80] bg-black/70 mobile-overlay"
          onClick={() => setIsMobileMenuOpen(false)}
          aria-label="Fechar menu"
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Escape' || e.key === 'Enter') {
              setIsMobileMenuOpen(false);
            }
          }}
        />
      )}
    </>
  );
}

export default Sidebar;