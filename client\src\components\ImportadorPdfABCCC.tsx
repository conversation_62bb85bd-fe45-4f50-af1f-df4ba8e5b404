/**
 * Componente para importação de PDFs de registro genealógico da ABCCC
 * 
 * Este componente permite o upload de PDFs da ABCCC, processamento
 * automático das informações e visualização dos resultados de extração.
 */

import React, { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import { 
  File, 
  Upload, 
  CheckCircle2, 
  AlertCircle, 
  ChevronRight, 
  Info, 
  ListTree, 
  FileText,
  Database,
  ArrowRight,
  UploadCloud,
  InfoIcon
} from 'lucide-react';

// Interface para o resumo da importação
interface ResumoImportacao {
  cavaloPrincipal: {
    nome: string;
    registro: string;
    novo: boolean;
    id?: number;
    sexo?: string;
    nascimento?: string;
    pelagem?: string;
    criador?: string;
    proprietario?: string;
    inspetor?: string;
  };
  familiares: {
    tipo: string;
    nome: string;
    registro?: string;
    novo: boolean;
    id?: number;
    sexo?: string;
    nascimento?: string;
    pelagem?: string;
    criador?: string;
    proprietario?: string;
    inspetor?: string;
  }[];
  detalhes: {
    nomeArquivo: string;
    dataImportacao: string;
    totalAnimais: number;
    novosAnimais: number;
    atualizados: number;
    erros: string[];
    dadosComplementares?: boolean;
  };
  logDebug: string;
  dadosOriginaisPdf?: any;
  dadosComplementares?: any;
}

interface ImportadorPdfABCCCProps {
  onImportacaoCompleta?: (dados: ResumoImportacao) => void;
  className?: string;
}

export function ImportadorPdfABCCC({ onImportacaoCompleta, className = '' }: ImportadorPdfABCCCProps) {
  const { toast } = useToast();
  
  // Estados
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isPreProcessing, setIsPreProcessing] = useState(false);
  const [processando, setProcessando] = useState(false);
  const [dadosExtraidos, setDadosExtraidos] = useState<ResumoImportacao | null>(null);
  const [dadosPreVisualizacao, setDadosPreVisualizacao] = useState<ResumoImportacao | null>(null);
  const [erroProcessamento, setErroProcessamento] = useState<string | null>(null);
  const [confirmarImportacao, setConfirmarImportacao] = useState(false);
  const [importacaoFinalizada, setImportacaoFinalizada] = useState(false);
  const [logMensagens, setLogMensagens] = useState<string[]>([]);
  const [familiarSelecionado, setFamiliarSelecionado] = useState<any>(null);
  const [detalhesAbertos, setDetalhesAbertos] = useState<boolean>(false);
  const [tabAtiva, setTabAtiva] = useState('upload');
  
  // Obter usuário do localStorage
  const [user] = useState(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch {
        return null;
      }
    }
    return null;
  });
  
  // Mutação para pré-visualização do PDF (sem importar para o banco)
  const previewMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', user?.id);
      
      adicionarLog('Iniciando upload do arquivo PDF para pré-visualização...');
      
      const response = await fetch('/api/importar-pdf-crioulo/preview', {
        method: 'POST',
        body: formData,
        headers: {
          'user-id': user?.id
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.mensagem || 'Erro ao processar o arquivo PDF para pré-visualização');
      }
      
      adicionarLog('Pré-visualização dos dados concluída com sucesso!');
      return await response.json();
    },
    onSuccess: (data) => {
      setIsPreProcessing(false);
      if (data.sucesso) {
        setDadosPreVisualizacao(data.dados);
        setTabAtiva('preview');
        
        toast({
          title: 'Pré-visualização concluída',
          description: `${data.dados.detalhes.totalAnimais} animais identificados, ${data.dados.detalhes.novosAnimais} serão adicionados.`,
          variant: 'default',
        });
      } else {
        setErroProcessamento(data.mensagem || 'Erro desconhecido ao processar o PDF');
      }
    },
    onError: (error: Error) => {
      setIsPreProcessing(false);
      setErroProcessamento(error.message);
      adicionarLog(`Erro: ${error.message}`);
      
      toast({
        title: 'Erro na pré-visualização',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Mutação para upload e processamento final do PDF
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', user?.id);
      
      adicionarLog('Iniciando upload do arquivo PDF...');
      
      const response = await fetch('/api/importar-pdf-crioulo', {
        method: 'POST',
        body: formData,
        headers: {
          'user-id': user?.id
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.mensagem || 'Erro ao processar o arquivo PDF');
      }
      
      adicionarLog('Upload e processamento concluídos com sucesso!');
      return await response.json();
    },
    onSuccess: (data) => {
      setProcessando(false);
      if (data.sucesso) {
        setDadosExtraidos(data.dados);
        setTabAtiva('resultado');
        
        toast({
          title: 'Importação concluída',
          description: `${data.dados.detalhes.totalAnimais} animais processados, ${data.dados.detalhes.novosAnimais} novos.`,
          variant: 'default',
        });
        
        // Invalidar queries que podem ter sido afetadas pela importação
        queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
        
        // Notificar o componente pai se necessário
        if (onImportacaoCompleta) {
          onImportacaoCompleta(data.dados);
        }
      } else {
        setErroProcessamento(data.mensagem || 'Erro desconhecido ao processar o PDF');
      }
    },
    onError: (error: Error) => {
      setProcessando(false);
      setErroProcessamento(error.message);
      adicionarLog(`Erro: ${error.message}`);
      
      toast({
        title: 'Erro na importação',
        description: error.message,
        variant: 'destructive',
      });
    }
  });
  
  // Handler para seleção de arquivo
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    setErroProcessamento(null);
    setDadosExtraidos(null);
    setConfirmarImportacao(false);
    
    if (file) {
      adicionarLog(`Arquivo selecionado: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`);
      
      // Verifica se o arquivo é um PDF
      if (file.type !== 'application/pdf') {
        setErroProcessamento('O arquivo selecionado não é um PDF. Por favor, selecione um arquivo PDF da ABCCC.');
        adicionarLog('Erro: O arquivo selecionado não é um PDF.');
        return;
      }
      
      // Arquivo está pronto para pré-visualização
      adicionarLog('Arquivo PDF verificado. Clique em "Pré-visualizar" para continuar.');
    }
  };
  
  // Pré-visualizar arquivo (primeiro passo)
  const iniciarPreVisualizacao = () => {
    if (!selectedFile) return;
    
    setIsPreProcessing(true);
    setLogMensagens([]);
    adicionarLog('Iniciando pré-processamento do arquivo para visualização...');
    
    // Usar a mutação que criamos para preview
    previewMutation.mutate(selectedFile);
  };
  
  // Confirmar e iniciar processamento final do arquivo
  const iniciarProcessamento = () => {
    if (!selectedFile || !dadosPreVisualizacao) return;
    
    setProcessando(true);
    adicionarLog('Iniciando importação final dos dados...');
    
    uploadMutation.mutate(selectedFile);
  };
  
  // Adicionar mensagem ao log
  const adicionarLog = (mensagem: string) => {
    setLogMensagens(logs => [...logs, `[${new Date().toLocaleTimeString()}] ${mensagem}`]);
  };
  
  return (
    <div className={`w-full ${className}`}>
      <Tabs value={tabAtiva} onValueChange={setTabAtiva} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="upload">Upload</TabsTrigger>
          <TabsTrigger 
            value="preview" 
            disabled={!dadosPreVisualizacao}
          >
            Pré-Visualização
          </TabsTrigger>
          <TabsTrigger 
            value="resultado" 
            disabled={!dadosExtraidos}
          >
            Resultado
          </TabsTrigger>
          <TabsTrigger 
            value="logs" 
            disabled={logMensagens.length === 0}
          >
            Logs
          </TabsTrigger>
        </TabsList>
        
        {/* Aba de Upload */}
        <TabsContent value="upload" className="border rounded-md p-4 mt-2">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <File className="h-5 w-5 text-blue-500" />
              <h2 className="text-xl font-semibold">Importar Registro Genealógico ABCCC</h2>
            </div>
            
            <p className="text-sm text-gray-500">
              Selecione um arquivo PDF do certificado de registro genealógico da ABCCC. 
              Após selecionar o arquivo, clique em "Pré-visualizar" para analisar os dados extraídos 
              antes de confirmar a importação final para o banco de dados.
            </p>
            
            {/* Input para seleção de arquivo */}
            <div className="space-y-2">
              <Label htmlFor="pdf-file">Certificado ABCCC (PDF)</Label>
              <Input 
                id="pdf-file" 
                type="file" 
                accept=".pdf,application/pdf" 
                onChange={handleFileChange}
                disabled={processando || isPreProcessing}
                className="cursor-pointer"
              />
              
              {selectedFile && (
                <p className="text-sm">
                  <span className="font-medium">Arquivo:</span> {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                </p>
              )}
            </div>
            
            {/* Alerta de erro */}
            {erroProcessamento && (
              <Alert variant="destructive" className="mt-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Erro</AlertTitle>
                <AlertDescription>{erroProcessamento}</AlertDescription>
              </Alert>
            )}
            
            {/* Progresso */}
            {(isPreProcessing || processando) && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{isPreProcessing ? 'Verificando arquivo...' : 'Processando...'}</span>
                  <span>{isPreProcessing ? '50%' : processando ? '75%' : '0%'}</span>
                </div>
                <Progress value={isPreProcessing ? 50 : processando ? 75 : 0} className="h-2" />
              </div>
            )}
            
            {/* Alerta sobre o arquivo selecionado */}
            {selectedFile && !processando && !erroProcessamento && (
              <Alert className="bg-blue-50 border-blue-200">
                <Info className="h-4 w-4 text-blue-500" />
                <AlertTitle className="text-blue-700">Arquivo selecionado</AlertTitle>
                <AlertDescription className="text-blue-600">
                  Clique em "Pré-visualizar" para analisar os dados do cavalo antes da importação final.
                </AlertDescription>
              </Alert>
            )}
            
            {/* Botões de ação */}
            <div className="flex gap-2 justify-end mt-4">
              <Button 
                variant="outline" 
                onClick={() => {
                  setSelectedFile(null);
                  setErroProcessamento(null);
                  setDadosExtraidos(null);
                  setDadosPreVisualizacao(null);
                  setConfirmarImportacao(false);
                  setLogMensagens([]);
                }}
                disabled={!selectedFile || processando}
              >
                Limpar
              </Button>
              
              <Button 
                onClick={iniciarPreVisualizacao}
                disabled={!selectedFile || processando || isPreProcessing}
                className="gap-2"
              >
                {isPreProcessing ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                    Processando...
                  </>
                ) : (
                  <>
                    <UploadCloud className="h-4 w-4" />
                    Pré-visualizar
                  </>
                )}
              </Button>
            </div>
          </div>
        </TabsContent>
        
        {/* Aba de Pré-Visualização */}
        <TabsContent value="preview" className="border rounded-md p-4 mt-2">
          {dadosPreVisualizacao && (
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                <h2 className="text-xl font-semibold">Pré-visualização dos Dados</h2>
              </div>
              
              <Alert className="bg-blue-50 border-blue-200">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-800">Verifique os dados antes de importar</AlertTitle>
                <AlertDescription className="text-blue-700">
                  Revise as informações extraídas abaixo e confirme se estão corretas antes de realizar a importação final.
                </AlertDescription>
              </Alert>
              
              {/* Detalhes do cavalo principal */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Cavalo Principal</CardTitle>
                  <CardDescription>
                    Detalhes do cavalo principal identificado no documento.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Nome</h4>
                      <p className="text-lg font-semibold">{dadosPreVisualizacao.cavaloPrincipal.nome}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Registro</h4>
                      <p className="text-lg font-semibold">{dadosPreVisualizacao.cavaloPrincipal.registro}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Status Previsto</h4>
                      <Badge variant={dadosPreVisualizacao.cavaloPrincipal.novo ? "default" : "secondary"}>
                        {dadosPreVisualizacao.cavaloPrincipal.novo ? 'Novo Cadastro' : 'Atualização'}
                      </Badge>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">ID no Sistema</h4>
                      <p className="text-base">{dadosPreVisualizacao.cavaloPrincipal.id || "-"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Sexo</h4>
                      <p className="text-base">{dadosPreVisualizacao.cavaloPrincipal.sexo || "-"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Pelagem</h4>
                      <p className="text-base">{dadosPreVisualizacao.cavaloPrincipal.pelagem || "-"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Data de Nascimento</h4>
                      <p className="text-base">{dadosPreVisualizacao.cavaloPrincipal.nascimento || "-"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Criador</h4>
                      <p className="text-base">{dadosPreVisualizacao.cavaloPrincipal.criador || "-"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Proprietário</h4>
                      <p className="text-base">{dadosPreVisualizacao.cavaloPrincipal.proprietario || "-"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-1">Inspetor Técnico</h4>
                      <p className="text-base">{dadosPreVisualizacao.cavaloPrincipal.inspetor || "-"}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Familiares */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Genealogia</CardTitle>
                  <CardDescription>
                    Familiares identificados no documento.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dadosPreVisualizacao.familiares.length > 0 ? (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Tipo</TableHead>
                            <TableHead>Nome</TableHead>
                            <TableHead>Registro</TableHead>
                            <TableHead>Informações</TableHead>
                            <TableHead>Status Previsto</TableHead>
                            <TableHead>Detalhes</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {dadosPreVisualizacao.familiares.map((familiar, index) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">{familiar.tipo}</TableCell>
                              <TableCell>{familiar.nome}</TableCell>
                              <TableCell>{familiar.registro || '-'}</TableCell>
                              <TableCell>
                                <div className="flex flex-wrap gap-2 text-xs">
                                  <Badge variant="outline" className="bg-gray-50">
                                    {familiar.sexo || "-"}
                                  </Badge>
                                  <Badge variant="outline" className="bg-gray-50">
                                    {familiar.pelagem || "-"}
                                  </Badge>
                                  <Badge variant="outline" className="bg-gray-50">
                                    Nasc: {familiar.nascimento || "-"}
                                  </Badge>
                                  <Badge variant="outline" className="bg-blue-50">
                                    Criador: {familiar.criador ? (familiar.criador.length > 15 ? familiar.criador.substring(0, 15) + '...' : familiar.criador) : "-"}
                                  </Badge>
                                  <Badge variant="outline" className="bg-blue-50">
                                    Prop: {familiar.proprietario ? (familiar.proprietario.length > 15 ? familiar.proprietario.substring(0, 15) + '...' : familiar.proprietario) : "-"}
                                  </Badge>
                                  <Badge variant="outline" className="bg-blue-50">
                                    Insp: {familiar.inspetor ? (familiar.inspetor.length > 15 ? familiar.inspetor.substring(0, 15) + '...' : familiar.inspetor) : "-"}
                                  </Badge>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant={familiar.novo ? "default" : "secondary"}>
                                  {familiar.novo ? 'Novo Cadastro' : 'Atualização'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  className="flex items-center gap-1"
                                  onClick={() => {
                                    setFamiliarSelecionado(familiar);
                                    setDetalhesAbertos(true);
                                  }}
                                >
                                  <Info className="h-4 w-4" />
                                  Detalhes
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    ) : (
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Nenhum familiar encontrado</AlertTitle>
                        <AlertDescription>
                          Não foi possível identificar dados de familiares no documento.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              {/* Botões de ação para pré-visualização */}
              <div className="flex justify-end gap-3 mt-6">
                <Button 
                  variant="outline" 
                  onClick={() => setTabAtiva('upload')}
                >
                  Voltar
                </Button>
                
                <Button
                  onClick={iniciarProcessamento}
                  disabled={processando}
                  className="gap-2"
                >
                  {processando ? (
                    <>
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                      Processando...
                    </>
                  ) : (
                    <>
                      <Database className="h-4 w-4" />
                      Confirmar e Importar
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </TabsContent>
        
        {/* Aba de Resultado */}
        <TabsContent value="resultado" className="border rounded-md p-4 mt-2">
          {dadosExtraidos && (
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-5 w-5 text-green-500" />
                <h2 className="text-xl font-semibold">Resultado da Importação</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Informações do Cavalo Principal */}
                <Card className="md:col-span-2">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      Cavalo Principal
                      {dadosExtraidos.cavaloPrincipal.novo && (
                        <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">
                          Novo
                        </span>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">Nome:</span>
                        <span>{dadosExtraidos.cavaloPrincipal.nome}</span>
                      </div>
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">Registro ABCCC:</span>
                        <span>{dadosExtraidos.cavaloPrincipal.registro || "Não especificado"}</span>
                      </div>
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">ID no sistema:</span>
                        <span>{dadosExtraidos.cavaloPrincipal.id || "Não disponível"}</span>
                      </div>
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">Sexo:</span>
                        <span>{dadosExtraidos.cavaloPrincipal.sexo || "Não informado"}</span>
                      </div>
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">Pelagem:</span>
                        <span>{dadosExtraidos.cavaloPrincipal.pelagem || "Não informada"}</span>
                      </div>
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">Data de Nascimento:</span>
                        <span>{dadosExtraidos.cavaloPrincipal.nascimento || "Não informada"}</span>
                      </div>
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">Criador:</span>
                        <span>{dadosExtraidos.cavaloPrincipal.criador || "Não informado"}</span>
                      </div>
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">Proprietário:</span>
                        <span>{dadosExtraidos.cavaloPrincipal.proprietario || "Não informado"}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Inspetor Técnico:</span>
                        <span>{dadosExtraidos.cavaloPrincipal.inspetor || "Não informado"}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                {/* Estatísticas da Importação */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Estatísticas</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">Total de animais:</span>
                        <span>{dadosExtraidos.detalhes.totalAnimais}</span>
                      </div>
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">Novos cadastros:</span>
                        <span className="text-green-600 font-semibold">{dadosExtraidos.detalhes.novosAnimais}</span>
                      </div>
                      <div className="flex justify-between items-center border-b pb-1">
                        <span className="font-medium">Atualizados:</span>
                        <span className="text-blue-600 font-semibold">{dadosExtraidos.detalhes.atualizados}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Data:</span>
                        <span>{new Date(dadosExtraidos.detalhes.dataImportacao).toLocaleString()}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Lista de Familiares */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <ListTree className="h-4 w-4" />
                    Genealogia Importada
                  </CardTitle>
                  <CardDescription>
                    Lista de todos os familiares processados a partir do documento.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Tipo</TableHead>
                        <TableHead>Nome</TableHead>
                        <TableHead>Registro</TableHead>
                        <TableHead>Detalhes</TableHead>
                        <TableHead className="text-right">Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {dadosExtraidos.familiares.map((familiar, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{familiar.tipo}</TableCell>
                          <TableCell>{familiar.nome}</TableCell>
                          <TableCell>{familiar.registro || "—"}</TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-2 text-xs">
                              <Badge variant="outline" className="bg-gray-50">
                                {familiar.sexo || "-"}
                              </Badge>
                              <Badge variant="outline" className="bg-gray-50">
                                {familiar.pelagem || "-"}
                              </Badge>
                              <Badge variant="outline" className="bg-gray-50">
                                Nasc: {familiar.nascimento || "-"}
                              </Badge>
                              <Badge variant="outline" className="bg-blue-50">
                                Criador: {familiar.criador ? (familiar.criador.length > 15 ? familiar.criador.substring(0, 15) + '...' : familiar.criador) : "-"}
                              </Badge>
                              <Badge variant="outline" className="bg-blue-50">
                                Prop: {familiar.proprietario ? (familiar.proprietario.length > 15 ? familiar.proprietario.substring(0, 15) + '...' : familiar.proprietario) : "-"}
                              </Badge>
                              <Badge variant="outline" className="bg-blue-50">
                                Insp: {familiar.inspetor ? (familiar.inspetor.length > 15 ? familiar.inspetor.substring(0, 15) + '...' : familiar.inspetor) : "-"}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            {familiar.novo ? (
                              <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">
                                Novo
                              </span>
                            ) : (
                              <span className="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full">
                                Existente
                              </span>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
              
              {/* Erros de Processamento (se houver) */}
              {dadosExtraidos.detalhes.erros.length > 0 && (
                <Card className="border-red-200">
                  <CardHeader className="pb-2 text-red-700">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      Alertas e Erros
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-1 text-sm">
                      {dadosExtraidos.detalhes.erros.map((erro, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <ChevronRight className="h-4 w-4 mt-0.5 text-red-500 flex-shrink-0" />
                          <span>{erro}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
              
              {/* Botões de ação */}
              <div className="flex gap-2 justify-end mt-4">
                <Button 
                  variant="outline" 
                  onClick={() => setTabAtiva('upload')}
                >
                  Importar Outro Arquivo
                </Button>
                
                <Button 
                  onClick={() => {
                    // Redirecionar para a página do cavalo, se tiver ID
                    if (dadosExtraidos.cavaloPrincipal.id) {
                      window.location.href = `/cavalo/${dadosExtraidos.cavaloPrincipal.id}`;
                    }
                  }}
                  className="gap-2"
                  disabled={!dadosExtraidos.cavaloPrincipal.id}
                >
                  <ArrowRight className="h-4 w-4" />
                  Ver Cavalo no Sistema
                </Button>
              </div>
            </div>
          )}
        </TabsContent>
        
        {/* Aba de Logs */}
        <TabsContent value="logs" className="border rounded-md p-4 mt-2">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-gray-500" />
              <h2 className="text-xl font-semibold">Log de Processamento</h2>
            </div>
            
            <div className="bg-gray-900 text-gray-100 p-4 rounded-md font-mono text-sm max-h-80 overflow-y-auto">
              {logMensagens.map((mensagem, index) => (
                <div key={index} className="mb-1">
                  {mensagem}
                </div>
              ))}
              {logMensagens.length === 0 && (
                <div className="text-gray-400 italic">Nenhuma mensagem de log disponível.</div>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}