{"timestamp": "2025-05-15T23:11:32.609Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 296411136, "heapTotal": 118546432, "heapUsed": 89775464, "external": 8350553, "arrayBuffers": 243725}, "uptime": 1.80312424, "cpuUsage": {"user": 2696314, "system": 367184}, "resourceUsage": {"userCPUTime": 2696382, "systemCPUTime": 367194, "maxRSS": 289464, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105490, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8919, "involuntaryContextSwitches": 3698}}