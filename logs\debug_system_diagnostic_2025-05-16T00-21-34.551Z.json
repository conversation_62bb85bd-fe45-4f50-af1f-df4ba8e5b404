{"timestamp": "2025-05-16T00:21:34.550Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 417579008, "heapTotal": 129048576, "heapUsed": 104668944, "external": 8557020, "arrayBuffers": 583586}, "uptime": 4.7203938, "cpuUsage": {"user": 3848020, "system": 512736}, "resourceUsage": {"userCPUTime": 3848031, "systemCPUTime": 512738, "maxRSS": 407792, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 114574, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 192, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 10268, "involuntaryContextSwitches": 13592}}