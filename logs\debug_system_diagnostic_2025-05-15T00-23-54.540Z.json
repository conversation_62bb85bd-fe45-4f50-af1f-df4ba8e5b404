{"timestamp": "2025-05-15T00:23:54.539Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 291278848, "heapTotal": 101322752, "heapUsed": 62226064, "external": 6815481, "arrayBuffers": 60485}, "uptime": 2.973773147, "cpuUsage": {"user": 2192371, "system": 372032}, "resourceUsage": {"userCPUTime": 2192434, "systemCPUTime": 372032, "maxRSS": 284452, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100957, "majorPageFault": 6, "swappedOut": 0, "fsRead": 25800, "fsWrite": 408, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6442, "involuntaryContextSwitches": 2559}}