{"timestamp": "2025-05-16T23:10:19.348Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 293388288, "heapTotal": 153235456, "heapUsed": 148321552, "external": 17351382, "arrayBuffers": 4378139}, "uptime": 634.769984881, "cpuUsage": {"user": 21504184, "system": 1481915}, "resourceUsage": {"userCPUTime": 21504187, "systemCPUTime": 1481915, "maxRSS": 606556, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 298936, "majorPageFault": 3, "swappedOut": 0, "fsRead": 8, "fsWrite": 1976, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 30432, "involuntaryContextSwitches": 26625}}