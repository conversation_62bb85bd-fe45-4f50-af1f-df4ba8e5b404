import { useState } from 'react';
import { BotMessageSquare, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SmartAssistantAI } from './SmartAssistantAI';

interface AssistantButtonProps {
  initialOpen?: boolean;
}

export function AssistantButton({ initialOpen = false }: AssistantButtonProps) {
  const [isOpen, setIsOpen] = useState(initialOpen);

  return (
    <>
      {/* Botão flutuante para abrir/fechar o assistente */}
      <div className="fixed bottom-20 right-6 z-50">
        <Button
          onClick={() => setIsOpen(!isOpen)}
          size="lg"
          className={`rounded-full h-14 w-14 shadow-lg flex items-center justify-center ${
            isOpen ? 'bg-red-600 hover:bg-red-700' : 'bg-primary hover:bg-primary/90'
          }`}
          aria-label={isOpen ? "Fechar assistente" : "Abrir assistente"}
        >
          {isOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <BotMessageSquare className="h-6 w-6" />
          )}
        </Button>
      </div>

      {/* Modal do assistente */}
      {isOpen && (
        <div className="fixed bottom-40 right-6 z-40 max-w-md w-full md:w-1/3">
          <SmartAssistantAI isExpanded={true} onToggleExpand={() => setIsOpen(false)} />
        </div>
      )}
    </>
  );
}