{"timestamp": "2025-05-15T17:56:30.235Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 396410880, "heapTotal": 114073600, "heapUsed": 72443280, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.833877666, "cpuUsage": {"user": 2824055, "system": 385595}, "resourceUsage": {"userCPUTime": 2824101, "systemCPUTime": 385602, "maxRSS": 387120, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104501, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 48, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8665, "involuntaryContextSwitches": 1936}}