{"timestamp": "2025-05-15T00:32:31.945Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 378232832, "heapTotal": 91099136, "heapUsed": 68878432, "external": 6853977, "arrayBuffers": 98802}, "uptime": 3.087485653, "cpuUsage": {"user": 2289019, "system": 320201}, "resourceUsage": {"userCPUTime": 2289078, "systemCPUTime": 320209, "maxRSS": 369368, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 98654, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 96, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6903, "involuntaryContextSwitches": 10406}}