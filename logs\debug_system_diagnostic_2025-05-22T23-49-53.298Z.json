{"timestamp": "2025-05-22T23:49:53.298Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405106688, "heapTotal": 117481472, "heapUsed": 74165976, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.148058042, "cpuUsage": {"user": 3103911, "system": 405389}, "resourceUsage": {"userCPUTime": 3103988, "systemCPUTime": 405399, "maxRSS": 395612, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104407, "majorPageFault": 0, "swappedOut": 0, "fsRead": 32, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8719, "involuntaryContextSwitches": 4249}}