{"dados": "preview_1748036233097", "logs": "{\n  \"dados\": {\n    \"cavalo\": {\n      \"nome\": \"QUEBRACH<PERSON> DO ARROIO DO MEIO\",\n      \"registro\": \"B216636\",\n      \"rp\": \"374\",\n      \"sexo\": \"<PERSON>HO\",\n      \"nascimento\": \"27/12/2015\",\n      \"pelagem\": \"BAIA RUANA\",\n      \"criador\": \"TELMO GOMES BRAGA\",\n      \"proprietario\": \"CAMILA WEBER\",\n      \"inspetor\": \"RICARDO GUAZZELLI MARTINS\"\n    },\n    \"pai\": {\n      \"nome\": \"ESTRIBO DO ARROIO DO MEIO\",\n      \"registro\": \"B496067\"\n    },\n    \"mae\": {\n      \"nome\": \"ÍNDIA DO ARROIO DO MEIO\",\n      \"registro\": \"B142486\"\n    },\n    \"avoPai\": {\n      \"nome\": \"BT MACANUDO\"\n    },\n    \"avaMae\": {\n      \"nome\": \"TEGEBE CUMPARSITA\"\n    },\n    \"avoPai2\": {\n      \"nome\": \"GUAMPA TARRUDO\"\n    },\n    \"avaMae2\": {\n      \"nome\": \"TEGEBÊ PRINCESA\"\n    }\n  },\n  \"log\": \"[2025-05-23T21:37:08.174Z] [INFO] Iniciando processamento com OpenAI\\n[2025-05-23T21:37:13.092Z] [DEBUG] Conteúdo bruto da resposta: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"QUEBRACHO DO ARROIO DO MEIO\\\",\\n    \\\"registro\\\": \\\"B216636\\\",\\n    \\\"rp\\\": \\\"374\\\",\\n    \\\"sexo\\\": \\\"MACHO\\\",\\n    \\\"nascimento\\\": \\\"27/12/2015\\\",\\n    \\\"pelagem\\\": \\\"BAIA RUANA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"TELMO GOMES BRAGA\\\",\\n    \\\"inspetor_tecnico\\\": \\\"RICARDO GUAZZELLI MARTINS\\\",\\n    \\\"status\\\": null,\\n    \\\"id_sistema\\\": null\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"ESTRIBO DO ARROIO DO MEIO\\\",\\n    \\\"registro\\\": \\\"B496067\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"ÍNDIA DO ARROIO DO MEIO\\\",\\n    \\\"registro\\\": \\\"B142486\\\"\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"BT MACANUDO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"TEGEBE CUMPARSITA\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"GUAMPA TARRUDO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"TEGEBÊ PRINCESA\\\",\\n    \\\"registro\\\": null\\n  }\\n}\\n[2025-05-23T21:37:13.092Z] [INFO] Recebido resposta da OpenAI e parseado com sucesso\\n[2025-05-23T21:37:13.092Z] [DEBUG] Dados extraídos: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"QUEBRACHO DO ARROIO DO MEIO\\\",\\n    \\\"registro\\\": \\\"B216636\\\",\\n    \\\"rp\\\": \\\"374\\\",\\n    \\\"sexo\\\": \\\"MACHO\\\",\\n    \\\"nascimento\\\": \\\"27/12/2015\\\",\\n    \\\"pelagem\\\": \\\"BAIA RUANA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"TELMO GOMES BRAGA\\\",\\n    \\\"inspetor_tecnico\\\": \\\"RICARDO GUAZZELLI MARTINS\\\",\\n    \\\"status\\\": null,\\n    \\\"id_sistema\\\": null\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"ESTRIBO DO ARROIO DO MEIO\\\",\\n    \\\"registro\\\": \\\"B496067\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"ÍNDIA DO ARROIO DO MEIO\\\",\\n    \\\"registro\\\": \\\"B142486\\\"\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"BT MACANUDO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"TEGEBE CUMPARSITA\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"GUAMPA TARRUDO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"TEGEBÊ PRINCESA\\\",\\n    \\\"registro\\\": null\\n  }\\n}\\n[2025-05-23T21:37:13.092Z] [INFO] Dados do cavalo principal extraídos: {\\n  \\\"nome\\\": \\\"QUEBRACHO DO ARROIO DO MEIO\\\",\\n  \\\"registro\\\": \\\"B216636\\\",\\n  \\\"rp\\\": \\\"374\\\",\\n  \\\"sexo\\\": \\\"MACHO\\\",\\n  \\\"nascimento\\\": \\\"27/12/2015\\\",\\n  \\\"pelagem\\\": \\\"BAIA RUANA\\\",\\n  \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n  \\\"criador\\\": \\\"TELMO GOMES BRAGA\\\",\\n  \\\"inspetor_tecnico\\\": \\\"RICARDO GUAZZELLI MARTINS\\\",\\n  \\\"status\\\": null,\\n  \\\"id_sistema\\\": null\\n}\\n[2025-05-23T21:37:13.096Z] [INFO] Dados do cavalo principal extraídos: QUEBRACHO DO ARROIO DO MEIO (B216636)\\n[2025-05-23T21:37:13.096Z] [INFO] Pai: ESTRIBO DO ARROIO DO MEIO (B496067)\\n[2025-05-23T21:37:13.096Z] [INFO] Mãe: ÍNDIA DO ARROIO DO MEIO (B142486)\\n[2025-05-23T21:37:13.096Z] [INFO] Dados convertidos com sucesso\"\n}", "timestamp": "2025-05-23T21:37:13.097Z"}