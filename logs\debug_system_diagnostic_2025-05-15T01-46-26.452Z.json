{"timestamp": "2025-05-15T01:46:26.452Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 383582208, "heapTotal": 104730624, "heapUsed": 62051216, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.399921878, "cpuUsage": {"user": 2217998, "system": 285700}, "resourceUsage": {"userCPUTime": 2218047, "systemCPUTime": 285707, "maxRSS": 374592, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100090, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6212, "involuntaryContextSwitches": 1841}}