{"timestamp": "2025-05-16T17:06:14.511Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405897216, "heapTotal": 115122176, "heapUsed": 72594424, "external": 8219482, "arrayBuffers": 243725}, "uptime": 3.495760696, "cpuUsage": {"user": 3239322, "system": 470578}, "resourceUsage": {"userCPUTime": 3239322, "systemCPUTime": 470642, "maxRSS": 396384, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103998, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8692, "involuntaryContextSwitches": 12917}}