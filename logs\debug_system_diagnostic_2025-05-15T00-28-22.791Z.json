{"timestamp": "2025-05-15T00:28:22.790Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 373518336, "heapTotal": 92147712, "heapUsed": 72044072, "external": 6898936, "arrayBuffers": 115186}, "uptime": 1.373472261, "cpuUsage": {"user": 2204728, "system": 285828}, "resourceUsage": {"userCPUTime": 2204775, "systemCPUTime": 285828, "maxRSS": 364764, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 97650, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5999, "involuntaryContextSwitches": 1582}}