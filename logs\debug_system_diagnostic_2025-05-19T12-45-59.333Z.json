{"timestamp": "2025-05-19T12:45:59.332Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 282705920, "heapTotal": 110403584, "heapUsed": 74066032, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.326558957, "cpuUsage": {"user": 2674678, "system": 386425}, "resourceUsage": {"userCPUTime": 2674744, "systemCPUTime": 386434, "maxRSS": 276080, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101533, "majorPageFault": 0, "swappedOut": 0, "fsRead": 40944, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8126, "involuntaryContextSwitches": 3754}}