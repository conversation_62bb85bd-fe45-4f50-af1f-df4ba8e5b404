{"dados": {"cavalo": {"nome": "POTRO CRIOULO DO SUL REGISTRO", "registro": "123456", "sexo": "<PERSON><PERSON>", "nascimento": "15/03/2019", "pelagem": "Tordilho NASCIMENTO", "titulos": []}, "avaMae": {"nome": "FRONTEIRA BELA", "tipo": "<PERSON><PERSON><PERSON>"}, "bisavos": []}, "logs": [{"nivel": "info", "mensagem": "Iniciando processamento do PDF /home/<USER>/workspace/uploads/abccc_pdfs/abccc-1747330774055-261553778.pdf", "timestamp": "2025-05-15T17:39:35.900Z"}, {"nivel": "info", "mensagem": "Arquivo lido com sucesso (1263910 bytes)", "timestamp": "2025-05-15T17:39:35.900Z"}, {"nivel": "info", "mensagem": "PDF processado: 1 páginas, 566 caracteres de texto", "timestamp": "2025-05-15T17:39:35.901Z"}, {"nivel": "debug", "mensagem": "Texto normalizado para processamento: CERTIFICADO DE REGISTRO GENEALÓGICO ASSOCIAÇÃO BRASILEIRA DE CRIADORES DE CAVALOS CRIOULOS NOME: POTRO CRIOULO DO SUL REGISTRO: 123456 SEXO: Macho PEL...", "timestamp": "2025-05-15T17:39:35.901Z"}, {"nivel": "debug", "mensagem": "Nome encontrado: POTRO CRIOULO DO SUL REGISTRO", "timestamp": "2025-05-15T17:39:35.901Z"}, {"nivel": "debug", "mensagem": "Registro encontrado: 123456", "timestamp": "2025-05-15T17:39:35.901Z"}, {"nivel": "debug", "mensagem": "RP não encontrado no documento", "timestamp": "2025-05-15T17:39:35.901Z"}, {"nivel": "debug", "mensagem": "Sexo encontrado: <PERSON><PERSON>", "timestamp": "2025-05-15T17:39:35.902Z"}, {"nivel": "debug", "mensagem": "Data de nascimento encontrada: 15/03/2019", "timestamp": "2025-05-15T17:39:35.902Z"}, {"nivel": "debug", "mensagem": "Pelagem encontrada: Tordilho NASCIMENTO", "timestamp": "2025-05-15T17:39:35.902Z"}, {"nivel": "alerta", "mensagem": "Criador não encontrado no documento", "timestamp": "2025-05-15T17:39:35.902Z"}, {"nivel": "alerta", "mensagem": "Proprietário não encontrado no documento", "timestamp": "2025-05-15T17:39:35.902Z"}, {"nivel": "debug", "mensagem": "Origem não encontrada no documento", "timestamp": "2025-05-15T17:39:35.903Z"}, {"nivel": "debug", "mensagem": "0 títulos encontrados", "timestamp": "2025-05-15T17:39:35.903Z"}, {"nivel": "alerta", "mensagem": "Informações do pai não encontradas no documento", "timestamp": "2025-05-15T17:39:35.903Z"}, {"nivel": "alerta", "mensagem": "Informações da mãe não encontradas no documento", "timestamp": "2025-05-15T17:39:35.904Z"}, {"nivel": "debug", "mensagem": "Avó materna encontrada: FRONTEIRA BELA", "timestamp": "2025-05-15T17:39:35.904Z"}, {"nivel": "info", "mensagem": "Extração concluída: POTRO CRIOULO DO SUL REGISTRO", "timestamp": "2025-05-15T17:39:35.904Z"}], "timestamp": "2025-05-15T17:39:35.904Z"}