{"timestamp": "2025-05-16T23:10:56.044Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 421888000, "heapTotal": 129998848, "heapUsed": 110846696, "external": 8386462, "arrayBuffers": 298426}, "uptime": 2.906088533, "cpuUsage": {"user": 3207953, "system": 406357}, "resourceUsage": {"userCPUTime": 3208004, "systemCPUTime": 406364, "maxRSS": 412000, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107109, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 112, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8415, "involuntaryContextSwitches": 10731}}