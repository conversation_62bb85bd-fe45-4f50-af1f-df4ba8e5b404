{"timestamp": "2025-05-15T22:02:04.331Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399343616, "heapTotal": 111452160, "heapUsed": 72445032, "external": 8211290, "arrayBuffers": 235533}, "uptime": 3.551038911, "cpuUsage": {"user": 2572799, "system": 369188}, "resourceUsage": {"userCPUTime": 2572869, "systemCPUTime": 369188, "maxRSS": 389984, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102326, "majorPageFault": 1, "swappedOut": 0, "fsRead": 41872, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7404, "involuntaryContextSwitches": 3132}}