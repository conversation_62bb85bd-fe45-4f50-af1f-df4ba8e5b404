{"timestamp": "2025-05-26T21:26:19.566Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 273485824, "heapTotal": 121040896, "heapUsed": 94732904, "external": 8343186, "arrayBuffers": 261044}, "uptime": 2.005747643, "cpuUsage": {"user": 2867047, "system": 369442}, "resourceUsage": {"userCPUTime": 2867105, "systemCPUTime": 369450, "maxRSS": 330192, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104767, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8783, "involuntaryContextSwitches": 3660}}