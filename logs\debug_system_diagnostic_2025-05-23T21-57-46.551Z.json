{"timestamp": "2025-05-23T21:57:46.551Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 265707520, "heapTotal": 118530048, "heapUsed": 74534280, "external": 8227674, "arrayBuffers": 251917}, "uptime": 2.09450342, "cpuUsage": {"user": 2969487, "system": 388714}, "resourceUsage": {"userCPUTime": 2969537, "systemCPUTime": 388720, "maxRSS": 325992, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103467, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8679, "involuntaryContextSwitches": 4103}}