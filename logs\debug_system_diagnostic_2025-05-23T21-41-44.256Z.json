{"timestamp": "2025-05-23T21:41:44.256Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 258863104, "heapTotal": 112238592, "heapUsed": 74503160, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.994003397, "cpuUsage": {"user": 2860176, "system": 385978}, "resourceUsage": {"userCPUTime": 2860231, "systemCPUTime": 385985, "maxRSS": 329532, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101980, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8866, "involuntaryContextSwitches": 4614}}