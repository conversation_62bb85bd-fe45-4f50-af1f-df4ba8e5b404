{"timestamp": "2025-05-15T17:54:19.768Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405012480, "heapTotal": 116170752, "heapUsed": 72128104, "external": 7873428, "arrayBuffers": 225351}, "uptime": 4.680199895, "cpuUsage": {"user": 3355153, "system": 449073}, "resourceUsage": {"userCPUTime": 3355224, "systemCPUTime": 449082, "maxRSS": 395520, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105447, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 24, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8411, "involuntaryContextSwitches": 17515}}