{"timestamp": "2025-05-16T16:50:32.791Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 400510976, "heapTotal": 111190016, "heapUsed": 72636776, "external": 8219482, "arrayBuffers": 243725}, "uptime": 3.12155764, "cpuUsage": {"user": 2829749, "system": 391932}, "resourceUsage": {"userCPUTime": 2829811, "systemCPUTime": 391932, "maxRSS": 391124, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104931, "majorPageFault": 1, "swappedOut": 0, "fsRead": 43976, "fsWrite": 152, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7634, "involuntaryContextSwitches": 6460}}