// Script para executar migração SQL manual para criar a tabela abccc_tokens
import { pool } from './db';
import { getModuleLogger } from './logger';

const logger = getModuleLogger('migration-abccc-tokens');

/**
 * Executa migração SQL para criar a tabela abccc_tokens se não existir
 */
export async function executeAbcccTokensMigration() {
  logger.info('Iniciando migração SQL para criar tabela abccc_tokens');
  
  const client = await pool.connect();
  
  try {
    // Verificar se a tabela já existe
    const checkTableQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'abccc_tokens'
    `;
    
    const checkResult = await client.query(checkTableQuery);
    
    if (checkResult.rows.length === 0) {
      logger.info('Tabela abccc_tokens não existe. Criando tabela...');
      
      // Criar a tabela abccc_tokens
      const createTableQuery = `
        CREATE TABLE abccc_tokens (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          token TEXT NOT NULL,
          origem TEXT DEFAULT 'sistema',
          data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          ultimo_uso TIMESTAMP,
          valido BOOLEAN DEFAULT TRUE,
          sucessos INTEGER DEFAULT 0,
          falhas INTEGER DEFAULT 0,
          observacoes TEXT
        )
      `;
      
      await client.query(createTableQuery);
      logger.info('Tabela abccc_tokens criada com sucesso!');
      
      // Inserir tokens iniciais
      const initialTokens = [
        'a8e28cacca43b61d5431cbdb7b55af96dd5b311e',
        'a77d90ccd3acaed9e1ab296a3ba69e9b78b3a25b'
      ];
      
      for (const token of initialTokens) {
        await client.query(`
          INSERT INTO abccc_tokens (token, origem, observacoes)
          VALUES ($1, 'inicial', 'Token inicial para bootstrap do sistema')
        `, [token]);
      }
      
      logger.info(`Inseridos ${initialTokens.length} tokens iniciais`);
      
      return true;
    } else {
      logger.info('Tabela abccc_tokens já existe. Verificando estrutura...');
      
      // Verificar se a tabela tem a estrutura correta
      const checkColumnsQuery = `
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'abccc_tokens'
      `;
      
      const columnsResult = await client.query(checkColumnsQuery);
      const columns = columnsResult.rows.map(row => row.column_name);
      
      const requiredColumns = ['id', 'token', 'origem', 'data_criacao', 'ultimo_uso', 'valido', 'sucessos', 'falhas', 'observacoes'];
      const missingColumns = requiredColumns.filter(col => !columns.includes(col));
      
      if (missingColumns.length > 0) {
        logger.info(`Faltam colunas na tabela abccc_tokens: ${missingColumns.join(', ')}. Adicionando...`);
        
        for (const column of missingColumns) {
          let addColumnQuery = `ALTER TABLE abccc_tokens ADD COLUMN ${column}`;
          
          switch (column) {
            case 'id':
              addColumnQuery += ` TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text`;
              break;
            case 'token':
              addColumnQuery += ` TEXT NOT NULL`;
              break;
            case 'origem':
              addColumnQuery += ` TEXT DEFAULT 'sistema'`;
              break;
            case 'data_criacao':
              addColumnQuery += ` TIMESTAMP DEFAULT CURRENT_TIMESTAMP`;
              break;
            case 'ultimo_uso':
              addColumnQuery += ` TIMESTAMP`;
              break;
            case 'valido':
              addColumnQuery += ` BOOLEAN DEFAULT TRUE`;
              break;
            case 'sucessos':
              addColumnQuery += ` INTEGER DEFAULT 0`;
              break;
            case 'falhas':
              addColumnQuery += ` INTEGER DEFAULT 0`;
              break;
            case 'observacoes':
              addColumnQuery += ` TEXT`;
              break;
          }
          
          await client.query(addColumnQuery);
          logger.info(`Coluna ${column} adicionada com sucesso!`);
        }
      }
      
      return true;
    }
  } catch (error: any) {
    logger.error(`Erro na migração de abccc_tokens: ${error.message}`);
    return false;
  } finally {
    client.release();
  }
}

export default executeAbcccTokensMigration;