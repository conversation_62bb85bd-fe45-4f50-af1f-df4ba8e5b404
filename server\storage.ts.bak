import { 
  users, type User, type InsertUser,
  cavalos, type Cavalo, type InsertCavalo,
  manejos, type Manejo, type InsertManejo,
  arquivos, type Arquivo, type InsertArquivo,
  eventos, type Evento, type InsertEvento,
  procedimentosVet, type ProcedimentoVet, type InsertProcedimentoVet,
  reproducao, type Reproducao, type InsertReproducao,
  medidasFisicas, type MedidaFisica, type InsertMedidaFisica,
  nutricao, type Nutricao, type InsertNutricao
} from "@shared/schema";
import { db } from "./db";
import { eq, and, asc, desc } from "drizzle-orm";

// modify the interface with any CRUD methods
// you might need

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Cavalo (Horse) methods
  getCavalos(userId: number): Promise<Cavalo[]>;
  getCavalo(id: number, userId: number): Promise<Cavalo | undefined>;
  createCavalo(cavalo: InsertCavalo): Promise<Cavalo>;
  updateCavalo(id: number, userId: number, cavalo: Partial<InsertCavalo>): Promise<Cavalo | undefined>;
  deleteCavalo(id: number, userId: number): Promise<boolean>;
  
  // Manejo (Horse care) methods
  getManejos(userId: number): Promise<Manejo[]>;
  getManejosByHorse(horseId: number, userId: number): Promise<Manejo[]>;
  getManejo(id: number, userId: number): Promise<Manejo | undefined>;
  createManejo(manejo: InsertManejo): Promise<Manejo>;
  updateManejo(id: number, userId: number, manejo: Partial<InsertManejo>): Promise<Manejo | undefined>;
  deleteManejo(id: number, userId: number): Promise<boolean>;
  
  // Arquivo (File) methods
  getArquivos(userId: number): Promise<Arquivo[]>;
  getArquivosByHorse(horseId: number, userId: number): Promise<Arquivo[]>;
  getArquivo(id: number, userId: number): Promise<Arquivo | undefined>;
  createArquivo(arquivo: InsertArquivo): Promise<Arquivo>;
  deleteArquivo(id: number, userId: number): Promise<boolean>;
  
  // Evento (Schedule) methods
  getEventos(userId: number): Promise<Evento[]>;
  getEventosByDate(data: string, userId: number): Promise<Evento[]>;
  getEventosByHorse(horseId: number, userId: number): Promise<Evento[]>;
  getEventosByManejo(manejoId: number, userId: number): Promise<Evento[]>;
  getEvento(id: number, userId: number): Promise<Evento | undefined>;
  createEvento(evento: InsertEvento): Promise<Evento>;
  updateEvento(id: number, userId: number, evento: Partial<InsertEvento>): Promise<Evento | undefined>;
  deleteEvento(id: number, userId: number): Promise<boolean>;
  
  // Procedimentos Veterinários methods
  getProcedimentosVet(userId: number): Promise<ProcedimentoVet[]>;
  getProcedimentosVetByHorse(horseId: number, userId: number): Promise<ProcedimentoVet[]>;
  getProcedimentosVetByManejo(manejoId: number, userId: number): Promise<ProcedimentoVet[]>;
  getProcedimentosVetByEvento(eventoId: number, userId: number): Promise<ProcedimentoVet[]>;
  getProcedimentoVet(id: number, userId: number): Promise<ProcedimentoVet | undefined>;
  createProcedimentoVet(procedimento: InsertProcedimentoVet): Promise<ProcedimentoVet>;
  updateProcedimentoVet(id: number, userId: number, procedimento: Partial<InsertProcedimentoVet>): Promise<ProcedimentoVet | undefined>;
  deleteProcedimentoVet(id: number, userId: number): Promise<boolean>;
  
  // Reprodução methods
  getReproducoes(userId: number): Promise<Reproducao[]>;
  getReproducoesByHorse(horseId: number, userId: number): Promise<Reproducao[]>;
  getReproducoesByProcedimento(procedimentoId: number, userId: number): Promise<Reproducao[]>;
  getReproducoesByEvento(eventoId: number, userId: number): Promise<Reproducao[]>;
  getReproducao(id: number, userId: number): Promise<Reproducao | undefined>;
  createReproducao(reproducaoData: InsertReproducao): Promise<Reproducao>;
  updateReproducao(id: number, userId: number, reproducaoData: Partial<InsertReproducao>): Promise<Reproducao | undefined>;
  deleteReproducao(id: number, userId: number): Promise<boolean>;
  
  // Medidas Físicas methods
  getMedidasFisicas(userId: number): Promise<MedidaFisica[]>;
  getMedidasFisicasByHorse(horseId: number, userId: number): Promise<MedidaFisica[]>;
  getMedidaFisica(id: number, userId: number): Promise<MedidaFisica | undefined>;
  createMedidaFisica(medidaData: InsertMedidaFisica): Promise<MedidaFisica>;
  updateMedidaFisica(id: number, userId: number, medidaData: Partial<InsertMedidaFisica>): Promise<MedidaFisica | undefined>;
  deleteMedidaFisica(id: number, userId: number): Promise<boolean>;
  
  // Nutrição methods
  getNutricoes(userId: number): Promise<Nutricao[]>;
  getNutricoesByHorse(horseId: number, userId: number): Promise<Nutricao[]>;
  getNutricao(id: number, userId: number): Promise<Nutricao | undefined>;
  createNutricao(nutricaoData: InsertNutricao): Promise<Nutricao>;
  updateNutricao(id: number, userId: number, nutricaoData: Partial<InsertNutricao>): Promise<Nutricao | undefined>;
  deleteNutricao(id: number, userId: number): Promise<boolean>;
}

export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }
  
  // Cavalo (Horse) methods
  async getCavalos(userId: number): Promise<Cavalo[]> {
    return db.select().from(cavalos).where(eq(cavalos.userId, userId));
  }
  
  async getCavalo(id: number, userId: number): Promise<Cavalo | undefined> {
    const [cavalo] = await db.select()
      .from(cavalos)
      .where(and(eq(cavalos.id, id), eq(cavalos.userId, userId)));
    return cavalo || undefined;
  }
  
  async createCavalo(insertCavalo: InsertCavalo): Promise<Cavalo> {
    const [cavalo] = await db
      .insert(cavalos)
      .values(insertCavalo)
      .returning();
    return cavalo;
  }
  
  async updateCavalo(id: number, userId: number, cavaloData: Partial<InsertCavalo>): Promise<Cavalo | undefined> {
    const [cavalo] = await db
      .update(cavalos)
      .set(cavaloData)
      .where(and(eq(cavalos.id, id), eq(cavalos.userId, userId)))
      .returning();
    return cavalo || undefined;
  }
  
  async deleteCavalo(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(cavalos)
      .where(and(eq(cavalos.id, id), eq(cavalos.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Manejo (Horse care) methods
  async getManejos(userId: number): Promise<Manejo[]> {
    return db.select()
      .from(manejos)
      .where(eq(manejos.userId, userId))
      .orderBy(asc(manejos.data)); // Ordenação crescente por data (mais antigo primeiro)
  }
  
  async getManejosByHorse(horseId: number, userId: number): Promise<Manejo[]> {
    return db.select()
      .from(manejos)
      .where(and(eq(manejos.horseId, horseId), eq(manejos.userId, userId)))
      .orderBy(asc(manejos.data)); // Ordenação cronológica por data
  }
  
  async getManejo(id: number, userId: number): Promise<Manejo | undefined> {
    const [manejo] = await db.select()
      .from(manejos)
      .where(and(eq(manejos.id, id), eq(manejos.userId, userId)));
    return manejo || undefined;
  }
  
  async createManejo(insertManejo: InsertManejo): Promise<Manejo> {
    const [manejo] = await db
      .insert(manejos)
      .values(insertManejo)
      .returning();
    return manejo;
  }
  
  async updateManejo(id: number, userId: number, manejoData: Partial<InsertManejo>): Promise<Manejo | undefined> {
    const [manejo] = await db
      .update(manejos)
      .set(manejoData)
      .where(and(eq(manejos.id, id), eq(manejos.userId, userId)))
      .returning();
    return manejo || undefined;
  }
  
  async deleteManejo(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(manejos)
      .where(and(eq(manejos.id, id), eq(manejos.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Arquivo (File) methods
  async getArquivos(userId: number): Promise<Arquivo[]> {
    return db.select().from(arquivos).where(eq(arquivos.userId, userId));
  }
  
  async getArquivosByHorse(horseId: number, userId: number): Promise<Arquivo[]> {
    return db.select()
      .from(arquivos)
      .where(and(eq(arquivos.horseId, horseId), eq(arquivos.userId, userId)));
  }
  
  async getArquivo(id: number, userId: number): Promise<Arquivo | undefined> {
    const [arquivo] = await db.select()
      .from(arquivos)
      .where(and(eq(arquivos.id, id), eq(arquivos.userId, userId)));
    return arquivo || undefined;
  }
  
  async createArquivo(insertArquivo: InsertArquivo): Promise<Arquivo> {
    const [arquivo] = await db
      .insert(arquivos)
      .values(insertArquivo)
      .returning();
    return arquivo;
  }
  
  async deleteArquivo(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(arquivos)
      .where(and(eq(arquivos.id, id), eq(arquivos.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Evento (Schedule) methods
  async getEventos(userId: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(eq(eventos.userId, userId))
      .orderBy(asc(eventos.data)); // Ordenação cronológica por data (asc = ascendente)
  }
  
  async getEventosByDate(data: string, userId: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(and(eq(eventos.data, data), eq(eventos.userId, userId)))
      .orderBy(asc(eventos.horaInicio)); // Ordenação pelos horários
  }
  
  async getEventosByHorse(horseId: number, userId: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(and(eq(eventos.horseId, horseId), eq(eventos.userId, userId)))
      .orderBy(asc(eventos.data)); // Ordenação por data ascendente
  }
  
  async getEventosByManejo(manejoId: number, userId: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(and(eq(eventos.manejoId, manejoId), eq(eventos.userId, userId)))
      .orderBy(asc(eventos.data)); // Ordenação por data ascendente
  }
  
  async getEvento(id: number, userId: number): Promise<Evento | undefined> {
    const [evento] = await db.select()
      .from(eventos)
      .where(and(eq(eventos.id, id), eq(eventos.userId, userId)));
    return evento || undefined;
  }
  
  async createEvento(insertEvento: InsertEvento): Promise<Evento> {
    const [evento] = await db
      .insert(eventos)
      .values(insertEvento)
      .returning();
    return evento;
  }
  
  async updateEvento(id: number, userId: number, eventoData: Partial<InsertEvento>): Promise<Evento | undefined> {
    const [evento] = await db
      .update(eventos)
      .set(eventoData)
      .where(and(eq(eventos.id, id), eq(eventos.userId, userId)))
      .returning();
    return evento || undefined;
  }
  
  async deleteEvento(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(eventos)
      .where(and(eq(eventos.id, id), eq(eventos.userId, userId)))
      .returning();
    return result.length > 0;
  }

  // Procedimentos Veterinários methods
  async getProcedimentosVet(userId: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(eq(procedimentosVet.userId, userId))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentosVetByHorse(horseId: number, userId: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.horseId, horseId), eq(procedimentosVet.userId, userId)))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentosVetByManejo(manejoId: number, userId: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.manejoId, manejoId), eq(procedimentosVet.userId, userId)))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentosVetByEvento(eventoId: number, userId: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.eventoId, eventoId), eq(procedimentosVet.userId, userId)))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentoVet(id: number, userId: number): Promise<ProcedimentoVet | undefined> {
    const [procedimento] = await db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.id, id), eq(procedimentosVet.userId, userId)));
    return procedimento || undefined;
  }
  
  async createProcedimentoVet(insertProcedimento: InsertProcedimentoVet): Promise<ProcedimentoVet> {
    const [procedimento] = await db
      .insert(procedimentosVet)
      .values(insertProcedimento)
      .returning();
    return procedimento;
  }
  
  async updateProcedimentoVet(id: number, userId: number, procedimentoData: Partial<InsertProcedimentoVet>): Promise<ProcedimentoVet | undefined> {
    const [procedimento] = await db
      .update(procedimentosVet)
      .set(procedimentoData)
      .where(and(eq(procedimentosVet.id, id), eq(procedimentosVet.userId, userId)))
      .returning();
    return procedimento || undefined;
  }
  
  async deleteProcedimentoVet(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(procedimentosVet)
      .where(and(eq(procedimentosVet.id, id), eq(procedimentosVet.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Reprodução methods
  async getReproducoes(userId: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(eq(reproducao.userId, userId))
      .orderBy(asc(reproducao.createdAt)); // Ordenação por data de criação
  }
  
  async getReproducoesByHorse(horseId: number, userId: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(and(eq(reproducao.horseId, horseId), eq(reproducao.userId, userId)))
      .orderBy(asc(reproducao.createdAt)); // Ordenação por data de criação
  }
  
  async getReproducoesByProcedimento(procedimentoId: number, userId: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(and(eq(reproducao.procedimentoVetId, procedimentoId), eq(reproducao.userId, userId)))
      .orderBy(asc(reproducao.createdAt)); // Ordenação por data de criação
  }
  
  async getReproducoesByEvento(eventoId: number, userId: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(and(eq(reproducao.eventoId, eventoId), eq(reproducao.userId, userId)))
      .orderBy(asc(reproducao.createdAt)); // Ordenação por data de criação
  }
  
  async getReproducao(id: number, userId: number): Promise<Reproducao | undefined> {
    const [item] = await db.select()
      .from(reproducao)
      .where(and(eq(reproducao.id, id), eq(reproducao.userId, userId)));
    return item || undefined;
  }
  
  async createReproducao(insertReproducao: InsertReproducao): Promise<Reproducao> {
    const [item] = await db
      .insert(reproducao)
      .values(insertReproducao)
      .returning();
    return item;
  }
  
  async updateReproducao(id: number, userId: number, reproducaoData: Partial<InsertReproducao>): Promise<Reproducao | undefined> {
    const [item] = await db
      .update(reproducao)
      .set(reproducaoData)
      .where(and(eq(reproducao.id, id), eq(reproducao.userId, userId)))
      .returning();
    return item || undefined;
  }
  
  async deleteReproducao(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(reproducao)
      .where(and(eq(reproducao.id, id), eq(reproducao.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Medidas Físicas methods
  async getMedidasFisicas(userId: number): Promise<MedidaFisica[]> {
    return db.select()
      .from(medidasFisicas)
      .where(eq(medidasFisicas.userId, userId))
      .orderBy(asc(medidasFisicas.data)); // Ordenação cronológica por data
  }
  
  async getMedidasFisicasByHorse(horseId: number, userId: number): Promise<MedidaFisica[]> {
    return db.select()
      .from(medidasFisicas)
      .where(and(eq(medidasFisicas.horseId, horseId), eq(medidasFisicas.userId, userId)))
      .orderBy(asc(medidasFisicas.data)); // Ordenação cronológica por data
  }
  
  async getMedidaFisica(id: number, userId: number): Promise<MedidaFisica | undefined> {
    const [medida] = await db.select()
      .from(medidasFisicas)
      .where(and(eq(medidasFisicas.id, id), eq(medidasFisicas.userId, userId)));
    return medida || undefined;
  }
  
  async createMedidaFisica(insertMedida: InsertMedidaFisica): Promise<MedidaFisica> {
    const [medida] = await db
      .insert(medidasFisicas)
      .values(insertMedida)
      .returning();
    return medida;
  }
  
  async updateMedidaFisica(id: number, userId: number, medidaData: Partial<InsertMedidaFisica>): Promise<MedidaFisica | undefined> {
    const [medida] = await db
      .update(medidasFisicas)
      .set(medidaData)
      .where(and(eq(medidasFisicas.id, id), eq(medidasFisicas.userId, userId)))
      .returning();
    return medida || undefined;
  }
  
  async deleteMedidaFisica(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(medidasFisicas)
      .where(and(eq(medidasFisicas.id, id), eq(medidasFisicas.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Nutrição methods
  async getNutricoes(userId: number): Promise<Nutricao[]> {
    return db.select()
      .from(nutricao)
      .where(eq(nutricao.userId, userId))
      .orderBy(asc(nutricao.data)); // Ordenação cronológica por data
  }
  
  async getNutricoesByHorse(horseId: number, userId: number): Promise<Nutricao[]> {
    // Validação para evitar problemas de tipos
    if (isNaN(horseId) || isNaN(userId)) {
      console.error(`Valores inválidos para consulta: horseId=${horseId}, userId=${userId}`);
      return [];
    }
    
    return db.select()
      .from(nutricao)
      .where(and(eq(nutricao.horseId, horseId), eq(nutricao.userId, userId)))
      .orderBy(asc(nutricao.data)); // Ordenação cronológica por data
  }
  
  async getNutricao(id: number, userId: number): Promise<Nutricao | undefined> {
    const [item] = await db.select()
      .from(nutricao)
      .where(and(eq(nutricao.id, id), eq(nutricao.userId, userId)));
    return item || undefined;
  }
  
  async createNutricao(insertNutricao: InsertNutricao): Promise<Nutricao> {
    const [item] = await db
      .insert(nutricao)
      .values(insertNutricao)
      .returning();
    return item;
  }
  
  async updateNutricao(id: number, userId: number, nutricaoData: Partial<InsertNutricao>): Promise<Nutricao | undefined> {
    const [item] = await db
      .update(nutricao)
      .set(nutricaoData)
      .where(and(eq(nutricao.id, id), eq(nutricao.userId, userId)))
      .returning();
    return item || undefined;
  }
  
  async deleteNutricao(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(nutricao)
      .where(and(eq(nutricao.id, id), eq(nutricao.userId, userId)))
      .returning();
    return result.length > 0;
  }
}

export const storage = new DatabaseStorage();
