import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { format, addMonths, isBefore } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { LayoutWrapper } from '@/components/Layout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/debug-select';
import { logger, logError, logWarn } from '@/lib/logger';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Eye, Edit, AlertCircle } from 'lucide-react';
import { ProcedimentoVet, Cavalo } from '@shared/schema';
import { AuthenticationErrorHandler, isAuthenticationError } from '@/components/auth/AuthenticationErrorHandler';

/**
 * Página de Vacinações
 * Exibe e gerencia os registros de vacinações dos cavalos
 */
export default function VacinacoesPage() {
  // Registrar inicialização do componente
  React.useEffect(() => {
    logger.info('veterinario.vacinacoes', 'Página de vacinações carregada');
    
    return () => {
      logger.info('veterinario.vacinacoes', 'Página de vacinações desmontada');
    };
  }, []);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCavalo, setFilterCavalo] = useState('todos');
  const [filterStatus, setFilterStatus] = useState('todos');
  const [filterVacina, setFilterVacina] = useState('todas');

  // Buscar dados de procedimentos veterinários
  const { data: procedimentos = [], isLoading: loadingProcedimentos, error: procedimentosError } = useQuery<ProcedimentoVet[]>({
    queryKey: ['/api/procedimentos-vet'],
    // Usar apenas callbacks suportados pela API do TanStack Query v5
    retry: 1,
    // Logging dentro do useEffect abaixo
  });

  // Buscar dados de cavalos para o filtro
  const { data: cavalos = [], isLoading: loadingCavalos, error: cavalosError } = useQuery<Cavalo[]>({
    queryKey: ['/api/cavalos'],
    // Usar apenas callbacks suportados pela API do TanStack Query v5
    retry: 1,
    // Logging dentro do useEffect abaixo
  });
  
  // Log de erros nas queries
  React.useEffect(() => {
    if (procedimentosError) {
      logError('veterinario.vacinacoes', 'Erro na consulta de procedimentos', {}, procedimentosError as Error);
    }
    if (cavalosError) {
      logError('veterinario.vacinacoes', 'Erro na consulta de cavalos', {}, cavalosError as Error);
    }
  }, [procedimentosError, cavalosError]);

  // Filtrar apenas vacinações
  const vacinacoes = Array.isArray(procedimentos) ? procedimentos
    .filter((proc: ProcedimentoVet) => 
      proc.tipo.toLowerCase().includes('vacina') || 
      proc.descricao.toLowerCase().includes('vacina') ||
      proc.medicamentos?.toLowerCase().includes('vacina')
    )
    .filter((proc: ProcedimentoVet) => 
      (searchTerm === '' || 
        proc.descricao.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proc.medicamentos?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        proc.veterinario?.toLowerCase().includes(searchTerm.toLowerCase())
      ) &&
      (filterCavalo === 'todos' || proc.horseId.toString() === filterCavalo) &&
      (filterStatus === 'todos' || getStatusVacinacao(proc) === filterStatus) &&
      (filterVacina === 'todas' || getTipoVacina(proc).toLowerCase().includes(filterVacina.toLowerCase()))
    ) : [];

  // Obter o nome do cavalo pelo ID
  const getCavaloName = (id: number) => {
    if (!Array.isArray(cavalos)) return 'N/A';
    const cavalo = cavalos.find((c: Cavalo) => c.id === id);
    return cavalo ? cavalo.name : 'N/A';
  };

  // Extrair o tipo de vacina da descrição ou medicamentos
  function getTipoVacina(procedimento: ProcedimentoVet): string {
    // Primeiro tenta extrair do campo medicamentos
    if (procedimento.medicamentos) {
      const match = procedimento.medicamentos.match(/vacina\s+de\s+([^\s,;.]+)/i) ||
                    procedimento.medicamentos.match(/contra\s+([^\s,;.]+)/i) ||
                    procedimento.medicamentos.match(/([^\s,;.]+)\s+vaccine/i);
      if (match) return match[1];
    }
    
    // Depois tenta da descrição
    if (procedimento.descricao) {
      const match = procedimento.descricao.match(/vacina\s+de\s+([^\s,;.]+)/i) ||
                    procedimento.descricao.match(/contra\s+([^\s,;.]+)/i) ||
                    procedimento.descricao.match(/([^\s,;.]+)\s+vaccine/i);
      if (match) return match[1];
    }
    
    // Se não conseguir extrair, retorna o tipo genérico
    return 'Não especificada';
  }

  // Função para determinar o status da vacinação
  function getStatusVacinacao(procedimento: ProcedimentoVet): string {
    if (!procedimento.dataProximoProcedimento) {
      try {
        // Se não há data próxima explícita mas há data da vacinação
        // Assume validade de 12 meses para a maioria das vacinas
        const dataVacinacao = new Date(procedimento.data);
        const dataEstimadaProxima = addMonths(dataVacinacao, 12);
        
        if (isBefore(dataEstimadaProxima, new Date())) {
          return 'vencida';
        }
        
        return 'válida';
      } catch {
        return 'não especificado';
      }
    }
    
    try {
      const dataProximo = new Date(procedimento.dataProximoProcedimento);
      const hoje = new Date();
      
      // Se a data de próxima vacinação já passou
      if (isBefore(dataProximo, hoje)) return 'vencida';
      
      // Se a data de próxima vacinação está a menos de 30 dias
      const diasDiferenca = Math.ceil((dataProximo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
      if (diasDiferenca <= 30) return 'próxima';
      
      return 'válida';
    } catch {
      return 'não especificado';
    }
  }

  // Extrair tipos únicos de vacinas para o filtro
  const tiposVacinas = Array.from(new Set(
    vacinacoes.map((v: ProcedimentoVet) => getTipoVacina(v))
  )).filter((tipo: string) => typeof tipo === 'string' && tipo.trim() !== '');

  // Verificar se há erros de autenticação
  if (isAuthenticationError(procedimentosError) || isAuthenticationError(cavalosError)) {
    return (
      <LayoutWrapper pageTitle="Vacinações" showBackButton backUrl="/veterinario">
        <div className="container mx-auto py-10">
          <AuthenticationErrorHandler 
            error={procedimentosError || cavalosError} 
            message="Sua sessão expirou ou você não está autenticado. Para visualizar as vacinações dos cavalos, faça login novamente."
          />
        </div>
      </LayoutWrapper>
    );
  }

  return (
    <LayoutWrapper pageTitle="Vacinações" showBackButton backUrl="/veterinario">
      <div className="container mx-auto py-6">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filtros</CardTitle>
            <CardDescription>Filtre as vacinações por cavalo, vacina ou status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Buscar</label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Buscar em vacinações..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Cavalo</label>
                <Select 
                  value={filterCavalo} 
                  onValueChange={(value) => {
                    logger.debug('veterinario.vacinacoes', `Filtro de cavalo alterado para: ${value}`, {
                      previousValue: filterCavalo,
                      newValue: value
                    });
                    setFilterCavalo(value);
                  }}
                  debugId="filtro-cavalo"
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um cavalo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos" debugId="cavalo-todos">Todos</SelectItem>
                    {Array.isArray(cavalos) && cavalos.map((cavalo: Cavalo) => (
                      <SelectItem 
                        key={cavalo.id} 
                        value={cavalo.id.toString()}
                        debugId={`cavalo-${cavalo.id}`}
                      >
                        {cavalo.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Tipo de Vacina</label>
                <Select 
                  value={filterVacina} 
                  onValueChange={(value) => {
                    logger.debug('veterinario.vacinacoes', `Filtro de vacina alterado para: ${value}`, {
                      previousValue: filterVacina,
                      newValue: value
                    });
                    setFilterVacina(value);
                  }}
                  debugId="filtro-vacina"
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma vacina" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todas" debugId="vacina-todas">Todas</SelectItem>
                    {Array.isArray(tiposVacinas) && tiposVacinas.map((tipo: string) => (
                      <SelectItem 
                        key={tipo} 
                        value={tipo || "vacina_nao_especificada"}
                        debugId={`vacina-${tipo.replace(/\s+/g, '_').toLowerCase() || "nao_especificada"}`}
                      >
                        {tipo || "Não especificada"}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select 
                  value={filterStatus} 
                  onValueChange={(value) => {
                    logger.debug('veterinario.vacinacoes', `Filtro de status alterado para: ${value}`, {
                      previousValue: filterStatus,
                      newValue: value
                    });
                    setFilterStatus(value);
                  }}
                  debugId="filtro-status"
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos" debugId="status-todos">Todos</SelectItem>
                    <SelectItem value="vencida" debugId="status-vencida">Vencidas</SelectItem>
                    <SelectItem value="próxima" debugId="status-proxima">Próximas</SelectItem>
                    <SelectItem value="válida" debugId="status-valida">Válidas</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">Vacinações</h2>
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Nova Vacinação
          </Button>
        </div>

        {loadingProcedimentos ? (
          <div className="text-center py-10">Carregando vacinações...</div>
        ) : vacinacoes.length > 0 ? (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Data</TableHead>
                  <TableHead>Cavalo</TableHead>
                  <TableHead>Vacina</TableHead>
                  <TableHead>Lote</TableHead>
                  <TableHead>Veterinário</TableHead>
                  <TableHead>Próxima Dose</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Array.isArray(vacinacoes) && vacinacoes.map((vacinacao: ProcedimentoVet) => {
                  const status = getStatusVacinacao(vacinacao);
                  const tipoVacina = getTipoVacina(vacinacao);
                  return (
                    <TableRow key={vacinacao.id} className={status === 'vencida' ? 'bg-red-50' : ''}>
                      <TableCell>{formato.data(vacinacao.data)}</TableCell>
                      <TableCell>{getCavaloName(vacinacao.horseId)}</TableCell>
                      <TableCell>{tipoVacina}</TableCell>
                      <TableCell>{vacinacao.observacoes?.match(/lote:?\s*([^,;.]+)/i)?.[1] || 'N/A'}</TableCell>
                      <TableCell>{vacinacao.veterinario || 'N/A'}</TableCell>
                      <TableCell>
                        {vacinacao.dataProximoProcedimento 
                          ? formato.data(vacinacao.dataProximoProcedimento) 
                          : 'Não especificada'}
                      </TableCell>
                      <TableCell>
                        <StatusBadge status={status} />
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Edit className="h-4 w-4" />
                        </Button>
                        {status === 'vencida' && (
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-red-500">
                            <AlertCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-10">
            <p className="text-muted-foreground">Nenhuma vacinação encontrada.</p>
            <Button variant="outline" className="mt-4">
              <Plus className="mr-2 h-4 w-4" /> Registrar Primeira Vacinação
            </Button>
          </div>
        )}
      </div>
    </LayoutWrapper>
  );
}

// Componente para mostrar o status com cor apropriada
function StatusBadge({ status }: { status: string }) {
  let variant: "default" | "secondary" | "destructive" | "outline" = "default";
  let label = status.charAt(0).toUpperCase() + status.slice(1);
  
  switch (status) {
    case 'vencida':
      variant = "destructive";
      break;
    case 'próxima':
      variant = "secondary";
      break;
    case 'válida':
      variant = "default";
      break;
    default:
      variant = "outline";
      break;
  }
  
  return <Badge variant={variant}>{label}</Badge>;
}

// Funções auxiliares
const formato = {
  data: (dataString: string) => {
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy', { locale: ptBR });
    } catch (e) {
      return dataString;
    }
  }
};