{"timestamp": "2025-05-23T20:19:05.971Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 297119744, "heapTotal": 119992320, "heapUsed": 92964976, "external": 8772322, "arrayBuffers": 261044}, "uptime": 1.718274054, "cpuUsage": {"user": 2627318, "system": 348515}, "resourceUsage": {"userCPUTime": 2627366, "systemCPUTime": 348521, "maxRSS": 290156, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105044, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8834, "involuntaryContextSwitches": 1834}}