{"timestamp": "2025-05-15T19:16:53.226Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 259776512, "heapTotal": 110927872, "heapUsed": 72479848, "external": 8211290, "arrayBuffers": 235533}, "uptime": 3.286596898, "cpuUsage": {"user": 2964766, "system": 447527}, "resourceUsage": {"userCPUTime": 2964827, "systemCPUTime": 447536, "maxRSS": 282576, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104165, "majorPageFault": 0, "swappedOut": 0, "fsRead": 36968, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7104, "involuntaryContextSwitches": 6578}}