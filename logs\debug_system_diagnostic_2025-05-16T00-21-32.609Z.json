{"timestamp": "2025-05-16T00:21:32.609Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 394018816, "heapTotal": 112762880, "heapUsed": 93934320, "external": 8333261, "arrayBuffers": 265658}, "uptime": 2.7788289, "cpuUsage": {"user": 2900819, "system": 427583}, "resourceUsage": {"userCPUTime": 2900876, "systemCPUTime": 427591, "maxRSS": 384784, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 109164, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 88, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8536, "involuntaryContextSwitches": 11120}}