{"timestamp": "2025-05-15T21:25:58.916Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 393269248, "heapTotal": 110927872, "heapUsed": 72497976, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.889252366, "cpuUsage": {"user": 2724214, "system": 347273}, "resourceUsage": {"userCPUTime": 2724251, "systemCPUTime": 347278, "maxRSS": 384052, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103466, "majorPageFault": 1, "swappedOut": 0, "fsRead": 0, "fsWrite": 64, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8371, "involuntaryContextSwitches": 4085}}