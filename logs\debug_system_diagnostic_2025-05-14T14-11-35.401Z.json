{"timestamp": "2025-05-14T14:11:35.400Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 407478272, "heapTotal": 109916160, "heapUsed": 88337984, "external": 7462474, "arrayBuffers": 102101}, "uptime": 2.066974917, "cpuUsage": {"user": 3472340, "system": 415107}, "resourceUsage": {"userCPUTime": 3472381, "systemCPUTime": 415112, "maxRSS": 397928, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105201, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 12437, "involuntaryContextSwitches": 4417}}