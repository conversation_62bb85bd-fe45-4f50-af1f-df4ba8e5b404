{"timestamp": "2025-05-16T17:30:39.449Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 396779520, "heapTotal": 113549312, "heapUsed": 72592872, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.78080944, "cpuUsage": {"user": 2918738, "system": 334553}, "resourceUsage": {"userCPUTime": 2918803, "systemCPUTime": 334561, "maxRSS": 387480, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105202, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 184, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8225, "involuntaryContextSwitches": 2477}}