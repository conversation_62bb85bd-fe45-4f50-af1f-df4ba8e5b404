{"timestamp": "2025-05-23T21:41:35.715Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 400887808, "heapTotal": 112238592, "heapUsed": 74485000, "external": 8211290, "arrayBuffers": 235533}, "uptime": 3.466621599, "cpuUsage": {"user": 3231088, "system": 409545}, "resourceUsage": {"userCPUTime": 3231139, "systemCPUTime": 409551, "maxRSS": 391492, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104712, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 64, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9347, "involuntaryContextSwitches": 13387}}