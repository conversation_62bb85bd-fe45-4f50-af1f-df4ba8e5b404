{"timestamp": "2025-05-26T21:30:37.906Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404770816, "heapTotal": 121044992, "heapUsed": 97343536, "external": 8455824, "arrayBuffers": 249274}, "uptime": 1.957277526, "cpuUsage": {"user": 2991583, "system": 331613}, "resourceUsage": {"userCPUTime": 2991643, "systemCPUTime": 331619, "maxRSS": 395284, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105670, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8360, "involuntaryContextSwitches": 2798}}