{"timestamp": "2025-05-16T21:21:06.905Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403644416, "heapTotal": 116432896, "heapUsed": 72581032, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.753874992, "cpuUsage": {"user": 2795549, "system": 345242}, "resourceUsage": {"userCPUTime": 2795595, "systemCPUTime": 345248, "maxRSS": 394184, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105080, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 48, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8013, "involuntaryContextSwitches": 1530}}