{"timestamp": "2025-05-23T05:52:27.129Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 262234112, "heapTotal": 115646464, "heapUsed": 74179664, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.358438419, "cpuUsage": {"user": 3115113, "system": 418259}, "resourceUsage": {"userCPUTime": 3115150, "systemCPUTime": 418264, "maxRSS": 296568, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102744, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9346, "involuntaryContextSwitches": 8773}}