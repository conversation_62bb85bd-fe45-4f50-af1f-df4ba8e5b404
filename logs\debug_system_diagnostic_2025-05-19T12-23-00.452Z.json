{"timestamp": "2025-05-19T12:23:00.452Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397803520, "heapTotal": 117112832, "heapUsed": 97418704, "external": 8771184, "arrayBuffers": 282042}, "uptime": 1.918170402, "cpuUsage": {"user": 2849656, "system": 391282}, "resourceUsage": {"userCPUTime": 2849703, "systemCPUTime": 391288, "maxRSS": 388480, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 113187, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 48, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8623, "involuntaryContextSwitches": 3612}}