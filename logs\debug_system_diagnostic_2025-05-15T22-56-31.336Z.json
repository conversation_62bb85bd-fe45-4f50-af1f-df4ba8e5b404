{"timestamp": "2025-05-15T22:56:31.336Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 386719744, "heapTotal": 102277120, "heapUsed": 80832064, "external": 8314546, "arrayBuffers": 265658}, "uptime": 2.157599618, "cpuUsage": {"user": 3006998, "system": 358146}, "resourceUsage": {"userCPUTime": 3007062, "systemCPUTime": 358146, "maxRSS": 377656, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100037, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8298, "involuntaryContextSwitches": 4570}}