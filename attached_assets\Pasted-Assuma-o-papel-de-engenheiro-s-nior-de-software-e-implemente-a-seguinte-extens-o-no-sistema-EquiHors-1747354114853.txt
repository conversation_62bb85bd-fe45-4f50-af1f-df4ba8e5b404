Assuma o papel de engenheiro sênior de software e implemente a seguinte extensão no sistema EquiHorse, sem alterar a lógica central já existente:

Incorpore ao backend Node.js/TypeScript o scraper de dados do site da ABCCC (https://www.cavalocrioulo.org.br/studbook/pesquisa_animais) que, ao receber um número de registro de cavalo (por exemplo, extraído do PDF de registro), acesse o site e extraia:

Dados completos do animal: nome, registro, sexo, nascimento, pelagem, criador, proprietário, pai, mãe.

Genealogia ampliada (todos os ancestrais disponíveis na ficha).

Fotos oficiais, exposições, títulos, se disponíveis.

Integre esse scraper à rotina de importação/cadastro de cavalo existente:

Após extrair os dados iniciais do PDF, use o número de registro para buscar e complementar automaticamente os dados do animal e de seus ancestrais via scraping.

Preencha todos os campos extras possíveis no objeto de cadastro (InsertCavalo/Cavalo).

Garanta que não serão sobrescritos dados já cadastrados manualmente pelo usuário, apenas enriquecidos os campos faltantes ou novos.

Ajuste o endpoint de importação para:

Fazer upload do PDF, extrair número de registro, acionar o scraper e retornar um objeto com todos os dados completos para revisão do usuário antes do cadastro ou atualização.

Permitir ao usuário visualizar, editar e confirmar os dados extraídos/enriquecidos antes de salvar.

Não altere a lógica central do sistema, apenas acople esta rotina.

Implemente como middleware/extensão modular.

Use logs e debug conforme padrão do sistema.

Adicione exemplos de uso e instruções rápidas de integração no README.

Reforce:

Não altere fluxos já existentes — acople a funcionalidade ao fluxo de importação.

Siga tipagem forte (TypeScript) e padrões de validação/segurança do sistema.

Comente e explique cada etapa do código novo.


"Inclua testes unitários para o scraper e para a integração com o fluxo de importação.


quando inserir uma pelagem via pdf da abccc, adicione a pelagem a o banco de dados , onde também possa ser usado no cadastro manual, confira se já existe esse dado antes de inserir, essa pelagem é para cavalo da raça crioulo



Assuma o papel de engenheiro sênior de softwarere faça toda a página de detalhes e edição dos cavalos cadastrados, pois não esta trazendo vários dados cadastrados.sem alterar a lógica central já existente