{"timestamp": "2025-05-15T18:16:08.059Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 410988544, "heapTotal": 118550528, "heapUsed": 90381792, "external": 8232827, "arrayBuffers": 235533}, "uptime": 2.140304676, "cpuUsage": {"user": 2843071, "system": 392895}, "resourceUsage": {"userCPUTime": 2843128, "systemCPUTime": 392903, "maxRSS": 401356, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104451, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8489, "involuntaryContextSwitches": 4182}}