-- Adiciona os novos campos de genealogia dual (ID + Nome)
ALTER TABLE cavalos
ADD COLUMN IF NOT EXISTS pai_id INTEGER REFERENCES cavalos(id),
ADD COLUMN IF NOT EXISTS pai_nome TEXT,
ADD COLUMN IF NOT EXISTS mae_id INTEGER REFERENCES cavalos(id),
ADD COLUMN IF NOT EXISTS mae_nome TEXT;

-- Transfere dados existentes de pai/mae para os novos campos
-- Para pai: tenta converter para ID primeiro, caso contrário considera como nome
UPDATE cavalos
SET 
  pai_id = CASE 
    WHEN pai ~ E'^\\d+$' -- Se pai é numérico
    THEN pai::integer
    ELSE NULL
  END,
  pai_nome = CASE 
    WHEN pai IS NOT NULL AND NOT (pai ~ E'^\\d+$') -- Se pai não é numérico
    THEN pai
    ELSE NULL
  END
WHERE pai IS NOT NULL;

-- Para mãe: tenta converter para ID primeiro, caso contrário considera como nome
UPDATE cavalos
SET 
  mae_id = CASE 
    WHEN mae ~ E'^\\d+$' -- Se mae é numérico
    THEN mae::integer
    ELSE NULL
  END,
  mae_nome = CASE 
    WHEN mae IS NOT NULL AND NOT (mae ~ E'^\\d+$') -- Se mae não é numérico
    THEN mae
    ELSE NULL
  END
WHERE mae IS NOT NULL;