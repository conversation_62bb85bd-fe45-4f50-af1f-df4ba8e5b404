import React from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Loader2, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format, isValid, parse } from "date-fns";
import { ptBR } from "date-fns/locale";
import { toast } from "@/hooks/use-toast";
import {
  GenealogiaSelector,
  TipoEntradaGenealogica,
  EntradaGenealogica,
} from "./GenealogiaSelector";
import { usePelagens } from "@/hooks/use-pelagens";
import { useABCCCImport } from "@/hooks/use-abccc-import";
import { useAuth } from "@/context/AuthContext";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { cavaloSchema as baseCavaloSchema } from "@shared/schema";
import { logger } from "@/lib/logger";

// Adaptando o schema do esquema unificado para o formulário
const cavaloSchema = z.object({
  nome: baseCavaloSchema.shape.nome,
  breed: baseCavaloSchema.shape.breed,
  birthDate: z
    .string()
    .optional()
    .transform((val) => (val ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  sexo: baseCavaloSchema.shape.sexo,
  pelagem: baseCavaloSchema.shape.pelagem,
  peso: baseCavaloSchema.shape.peso,
  altura: baseCavaloSchema.shape.altura,
  status: z.enum([
      "ativo",
      "vendido",
      "falecido",
      "doado",
      "emprestado",
      "reservado",
      "reprodução",
      "tratamento",
      "nao_informado",
    ]),
  origem: baseCavaloSchema.shape.origem,
  numeroRegistro: baseCavaloSchema.shape.numeroRegistro,
  criador: baseCavaloSchema.shape.criador,
  proprietario: baseCavaloSchema.shape.proprietario,
  inspetor: baseCavaloSchema.shape.inspetor,
  valorCompra: baseCavaloSchema.shape.valorCompra,
  dataCompra: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val && val.trim() ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  dataEntrada: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val && val.trim() ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  dataSaida: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val && val.trim() ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  pai: z
    .object({
      tipo: z.enum(["sistema", "externo", "nao_informado"]),
      cavaloSistemaId: z.string().nullable(),
      cavaloNome: z.string().nullable(),
    })
    .optional(),
  mae: z
    .object({
      tipo: z.enum(["sistema", "externo", "nao_informado"]),
      cavaloSistemaId: z.string().nullable(),
      cavaloNome: z.string().nullable(),
    })
    .optional(),
  observacoes: baseCavaloSchema.shape.observacoes,
  userId: baseCavaloSchema.shape.userId,
  // Campos para compatibilidade com o modelo de dados
  motivoSaida: baseCavaloSchema.shape.motivoSaida,
  paiId: baseCavaloSchema.shape.paiId,
  paiNome: baseCavaloSchema.shape.paiNome,
  maeId: baseCavaloSchema.shape.maeId,
  maeNome: baseCavaloSchema.shape.maeNome,
});

// Interface para os valores do formulário
export type CavaloFormValues = z.infer<typeof cavaloSchema>;

interface CavaloFormProps {
  onSubmit: (values: CavaloFormValues) => Promise<void>;
  defaultValues?: Partial<CavaloFormValues>;
  isSubmitting?: boolean;
  cavalos?: any[];
}

/**
 * Formulário de cavalos melhorado com organização por seções
 */
export function CavaloFormNew({
  onSubmit,
  defaultValues,
  isSubmitting = false,
  cavalos = [],
}: CavaloFormProps) {
  const { userId } = useAuth();
  const { pelagens, isLoading: isPelagensLoading } = usePelagens();
  const { importData, isLoading: isABCCCLoading } = useABCCCImport();
  const [isConfirmDialogOpen, setConfirmDialogOpen] = React.useState(false);
  const [formValues, setFormValues] = React.useState<CavaloFormValues | null>(
    null,
  );

  const form = useForm<CavaloFormValues>({
    resolver: zodResolver(cavaloSchema),
    defaultValues: {
      nome: defaultValues?.nome || "",
      breed: defaultValues?.breed || "",
      birthDate: defaultValues?.birthDate || null,
      sexo: defaultValues?.sexo || "nao_informado",
      pelagem: defaultValues?.pelagem || "nao_informado",
      peso: defaultValues?.peso || null,
      altura: defaultValues?.altura || null,
      status: defaultValues?.status || "nao_informado",
      origem: defaultValues?.origem || "",
      numeroRegistro: defaultValues?.numeroRegistro || "",
      criador: defaultValues?.criador || "",
      proprietario: defaultValues?.proprietario || "",
      inspetor: defaultValues?.inspetor || "",
      valorCompra: defaultValues?.valorCompra || null,
      dataCompra: defaultValues?.dataCompra || null,
      dataEntrada: defaultValues?.dataEntrada || null,
      dataSaida: defaultValues?.dataSaida || null,
      pai: {
        tipo: defaultValues?.paiId
          ? "sistema"
          : defaultValues?.paiNome
            ? "externo"
            : "nao_informado",
        cavaloSistemaId: defaultValues?.paiId?.toString() || null,
        cavaloNome: defaultValues?.paiNome || null,
      },
      mae: {
        tipo: defaultValues?.maeId
          ? "sistema"
          : defaultValues?.maeNome
            ? "externo"
            : "nao_informado",
        cavaloSistemaId: defaultValues?.maeId?.toString() || null,
        cavaloNome: defaultValues?.maeNome || null,
      },
      observacoes: defaultValues?.observacoes || "",
      userId,
    },
  });

  // Lidar com busca ABCCC
  const handleBuscarABCCC = async (registro: string) => {
    try {
      const data = await importData(registro);
      logger.info('cavalo-form', `Dados ABCCC recebidos para registro ${registro}`);

      form.setValue("nome", data.nome);
      form.setValue("sexo", data.sexo === "M" ? "Macho" : "Fêmea");
      form.setValue("birthDate", data.nascimento);
      form.setValue("pelagem", data.pelagem);
      form.setValue("criador", data.criador);
      form.setValue("proprietario", data.proprietario);
      form.setValue("numeroRegistro", data.registro);
      form.setValue("pai", {
        tipo: "externo",
        cavaloSistemaId: null,
        cavaloNome: data.pai,
      });
      form.setValue("mae", {
        tipo: "externo",
        cavaloSistemaId: null,
        cavaloNome: data.mae,
      });
    } catch (error) {
      logger.error('cavalo-form', `Erro ao buscar ABCCC: ${error.message}`, error);
      // Erros são tratados pelo useABCCCImport via toast
    }
  };

  // Processar submissão
  const handleSubmit = async (values: CavaloFormValues) => {
    try {
      logger.info('cavalo-form', "Processando dados do formulário", { 
        nome: values.nome, 
        breed: values.breed,
        sexo: values.sexo
      });

      const valuesAjustados = {
        ...values,
        birthDate: values.birthDate
          ? format(values.birthDate, "yyyy-MM-dd")
          : null,
        dataCompra: values.dataCompra
          ? format(values.dataCompra, "yyyy-MM-dd")
          : null,
        dataEntrada: values.dataEntrada
          ? format(values.dataEntrada, "yyyy-MM-dd")
          : null,
        dataSaida: values.dataSaida
          ? format(values.dataSaida, "yyyy-MM-dd")
          : null,
        paiId:
          values.pai?.tipo === "sistema" && values.pai.cavaloSistemaId
            ? parseInt(values.pai.cavaloSistemaId)
            : values.pai?.tipo === "externo" && values.pai.cavaloSistemaId
              ? parseInt(values.pai.cavaloSistemaId)
              : null,
        paiNome:
          values.pai?.tipo === "externo" && !values.pai.cavaloSistemaId
            ? values.pai.cavaloNome
            : null,
        maeId:
          values.mae?.tipo === "sistema" && values.mae.cavaloSistemaId
            ? parseInt(values.mae.cavaloSistemaId)
            : values.mae?.tipo === "externo" && values.mae.cavaloSistemaId
              ? parseInt(values.mae.cavaloSistemaId)
              : null,
        maeNome:
          values.mae?.tipo === "externo" && !values.mae.cavaloSistemaId
            ? values.mae.cavaloNome
            : null,
      };

      delete valuesAjustados.pai;
      delete valuesAjustados.mae;

      setFormValues(valuesAjustados);
      setConfirmDialogOpen(true);
    } catch (error) {
      logger.error('cavalo-form', 'Erro ao processar formulário', error);
      toast({
        title: "Erro ao processar dados",
        description:
          "Não foi possível processar os dados do formulário. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  // Confirmar submissão
  const confirmSubmit = async () => {
    if (!formValues) return;

    try {
      await onSubmit(formValues);
      
      // Registrar sucesso no log
      logger.info('cavalo-form', 'Cavalo salvo com sucesso', { 
        id: formValues.id, 
        nome: formValues.nome,
        breed: formValues.breed
      });
      
      form.reset();
      setConfirmDialogOpen(false);
      toast({
        title: "Cavalo salvo",
        description: "Os dados do cavalo foram salvos com sucesso.",
      });
    } catch (error) {
      logger.error('cavalo-form', 'Erro ao salvar cavalo', { 
        mensagem: error.message, 
        erro: error
      });
      toast({
        title: "Erro ao salvar",
        description: error.message || "Não foi possível salvar o cavalo.",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <Form {...form}>
        <form
          id="cavalo-form"
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-6"
        >
          {/* Seção 1: Informações Básicas */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-4">Informações Básicas</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Nome */}
              <FormField
                control={form.control}
                name="nome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome *</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome do cavalo" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Raça */}
              <FormField
                control={form.control}
                name="breed"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Raça *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione a raça" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Crioulo">Crioulo</SelectItem>
                        <SelectItem value="Quarto de Milha">
                          Quarto de Milha
                        </SelectItem>
                        <SelectItem value="Mangalarga">Mangalarga</SelectItem>
                        <SelectItem value="Mangalarga Marchador">
                          Mangalarga Marchador
                        </SelectItem>
                        <SelectItem value="Árabe">Árabe</SelectItem>
                        <SelectItem value="Paint Horse">Paint Horse</SelectItem>
                        <SelectItem value="Appaloosa">Appaloosa</SelectItem>
                        <SelectItem value="Campolina">Campolina</SelectItem>
                        <SelectItem value="Lusitano">Lusitano</SelectItem>
                        <SelectItem value="Pônei">Pônei</SelectItem>
                        <SelectItem value="Puro Sangue Inglês">
                          Puro Sangue Inglês
                        </SelectItem>
                        <SelectItem value="Outros">Outros</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Data Nascimento */}
              <FormField
                control={form.control}
                name="birthDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Data de Nascimento</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground",
                            )}
                            aria-label="Selecionar data de nascimento"
                          >
                            {field.value ? (
                              format(field.value, "dd/MM/yyyy", {
                                locale: ptBR,
                              })
                            ) : (
                              <span>Selecione uma data</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1970-01-01")
                          }
                          initialFocus
                          locale={ptBR}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Sexo */}
              <FormField
                control={form.control}
                name="sexo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sexo</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o sexo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="nao_informado">
                          Não informado
                        </SelectItem>
                        <SelectItem value="Macho">Macho</SelectItem>
                        <SelectItem value="Fêmea">Fêmea</SelectItem>
                        <SelectItem value="Garanhão">Garanhão</SelectItem>
                        <SelectItem value="Égua">Égua</SelectItem>
                        <SelectItem value="Castrado">Castrado</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Pelagem */}
              <FormField
                control={form.control}
                name="pelagem"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pelagem</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isPelagensLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={
                              isPelagensLoading
                                ? "Carregando pelagens..."
                                : "Selecione a pelagem"
                            }
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="nao_informado">
                          Não informado
                        </SelectItem>
                        {pelagens.map((pelagem) => (
                          <SelectItem key={pelagem.id} value={pelagem.nome}>
                            {pelagem.nome}
                          </SelectItem>
                        ))}
                        {!isPelagensLoading && pelagens.length === 0 && (
                          <SelectItem value="outros" disabled>
                            Nenhuma pelagem cadastrada
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="nao_informado">
                          Não informado
                        </SelectItem>
                        <SelectItem value="Ativo">Ativo</SelectItem>
                        <SelectItem value="Vendido">Vendido</SelectItem>
                        <SelectItem value="Falecido">Falecido</SelectItem>
                        <SelectItem value="Doado">Doado</SelectItem>
                        <SelectItem value="Reprodução">Reprodução</SelectItem>
                        <SelectItem value="Competição">Competição</SelectItem>
                        <SelectItem value="Tratamento">Tratamento</SelectItem>
                        <SelectItem value="Aposentado">Aposentado</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Seção 2: Características Físicas */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-4">
              Características Físicas
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Peso */}
              <FormField
                control={form.control}
                name="peso"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Peso (kg)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.1"
                        placeholder="Peso em kg"
                        value={field.value ?? ""}
                        onChange={(e) =>
                          field.onChange(
                            e.target.value ? parseFloat(e.target.value) : null,
                          )
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Altura */}
              <FormField
                control={form.control}
                name="altura"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Altura (m)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Altura em metros"
                        value={field.value ?? ""}
                        onChange={(e) =>
                          field.onChange(
                            e.target.value ? parseFloat(e.target.value) : null,
                          )
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Seção 3: Genealogia */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-4">Genealogia</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Pai */}
              <FormField
                control={form.control}
                name="pai"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pai</FormLabel>
                    <FormControl>
                      <GenealogiaSelector
                        tipoEntrada={TipoEntradaGenealogica.Pai}
                        valorInicial={field.value}
                        cavalos={cavalos.filter((c) =>
                          ["Macho", "Garanhão"].includes(c.sexo),
                        )}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Mãe */}
              <FormField
                control={form.control}
                name="mae"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mãe</FormLabel>
                    <FormControl>
                      <GenealogiaSelector
                        tipoEntrada={TipoEntradaGenealogica.Mae}
                        valorInicial={field.value}
                        cavalos={cavalos.filter((c) =>
                          ["Fêmea", "Égua"].includes(c.sexo),
                        )}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Seção 4: Registro e Identificação */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-4">
              Registro e Identificação
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Número de Registro */}
              <FormField
                control={form.control}
                name="numeroRegistro"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Número de Registro</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Registro (ex: B123456)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Origem */}
              <FormField
                control={form.control}
                name="origem"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Haras/Fazenda de Origem</FormLabel>
                    <FormControl>
                      <Input placeholder="Origem do cavalo" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Criador */}
              <FormField
                control={form.control}
                name="criador"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Criador</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome do criador" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Proprietário */}
              <FormField
                control={form.control}
                name="proprietario"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Proprietário</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome do proprietário" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Inspetor */}
              <FormField
                control={form.control}
                name="inspetor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Inspetor Técnico</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome do inspetor" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Seção 5: Dados Financeiros e Datas */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-4">Financeiro e Datas</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Valor de Compra */}
              <FormField
                control={form.control}
                name="valorCompra"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Valor de Compra (R$)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Valor em reais"
                        value={field.value ?? ""}
                        onChange={(e) =>
                          field.onChange(
                            e.target.value ? parseFloat(e.target.value) : null,
                          )
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Data de Compra */}
              <FormField
                control={form.control}
                name="dataCompra"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Data de Compra</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground",
                            )}
                            aria-label="Selecionar data de compra"
                          >
                            {field.value ? (
                              format(field.value, "dd/MM/yyyy", {
                                locale: ptBR,
                              })
                            ) : (
                              <span>Selecione uma data</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1970-01-01")
                          }
                          initialFocus
                          locale={ptBR}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Data de Entrada */}
              <FormField
                control={form.control}
                name="dataEntrada"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Data de Entrada</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground",
                            )}
                            aria-label="Selecionar data de entrada"
                          >
                            {field.value ? (
                              format(field.value, "dd/MM/yyyy", {
                                locale: ptBR,
                              })
                            ) : (
                              <span>Selecione uma data</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1970-01-01")
                          }
                          initialFocus
                          locale={ptBR}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Data de Saída */}
              <FormField
                control={form.control}
                name="dataSaida"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Data de Saída</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground",
                            )}
                            aria-label="Selecionar data de saída"
                          >
                            {field.value ? (
                              format(field.value, "dd/MM/yyyy", {
                                locale: ptBR,
                              })
                            ) : (
                              <span>Selecione uma data</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1970-01-01")
                          }
                          initialFocus
                          locale={ptBR}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Seção 6: Observações */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-4">Observações</h3>
            <FormField
              control={form.control}
              name="observacoes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Observações</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Observações gerais sobre o cavalo"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Botões */}
          <div className="flex justify-end gap-4">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Salvando...
                </>
              ) : (
                "Salvar"
              )}
            </Button>
          </div>
        </form>
      </Form>

      {/* Diálogo de Confirmação */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Cadastro</DialogTitle>
            <DialogDescription>
              Por favor, confirme os dados do cavalo antes de salvar:
              <ul className="mt-2 space-y-1">
                <li>
                  <strong>Nome:</strong> {formValues?.nome}
                </li>
                <li>
                  <strong>Raça:</strong> {formValues?.breed}
                </li>
                <li>
                  <strong>Registro:</strong>{" "}
                  {formValues?.numeroRegistro || "Não informado"}
                </li>
                <li>
                  <strong>Pai:</strong>{" "}
                  {formValues?.paiNome || formValues?.paiId || "Não informado"}
                </li>
                <li>
                  <strong>Mãe:</strong>{" "}
                  {formValues?.maeNome || formValues?.maeId || "Não informado"}
                </li>
              </ul>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button onClick={confirmSubmit}>Confirmar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
