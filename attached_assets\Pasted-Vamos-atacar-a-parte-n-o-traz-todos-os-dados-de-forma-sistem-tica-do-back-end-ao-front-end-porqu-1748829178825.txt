Vamos atacar a parte “não traz todos os dados” de forma sistemática, do back-end ao front-end, porque normalmente o gargalo está em um destes três pontos:

Etapa	O que costuma falhar	Como testar / corrigir
1. API GET /api/cavalos/:id	• Query não faz join com a tabela genealogia ou não seleciona as colunas avoPaterno, avoMaterno, etc.
• Falta cabeçalho user-id, filtrando demais	Teste: no Insomnia/Postman (ou DevTools → Network) chame o endpoint e veja o JSON.
— Se os campos estão ausentes → corrija a query
— Se eles chegam aninhados → prossiga p/ etapa 2
2. Normalização (backend)	• Converte JSON → string múltiplas vezes (efeito “matrioska”).
• Não converte null em "", gerando undefined no front	Correção: antes de enviar ao cliente, aplique uma função “descasca-JSON” parecida com extractCleanName (ou mude o tipo da coluna p/ jsonb).
3. Estado no React (EditarGenealogia.tsx)	• useEffect carrega o cavalo mas não limpa os avós.
• Inputs controlados recebem valor undefined ⇒ ficam vazios.
• Falta await/enabled no useQuery, renderização corre antes.	Correção:
```ts
useEffect(() => {		
if (!data) return; // ← data = cavalo		
setFormData({		

kotlin
Copiar
Editar
paiId: data.paiId ?? null,
maeId: data.maeId ?? null,
avoPaterno: extractCleanName(data.avoPaterno),
avoMaterno: extractCleanName(data.avoMaterno),
});
}, [data]);
```<br>Nos <Input>:

tsx
Copiar
Editar
value={formData.avoPaterno ?? ""}
``` |

---

## 1 · Verifique primeiro a resposta da API

Abra DevTools → aba **Network**, recarregue a página de edição e observe o payload do `GET /api/cavalos/:id`.

- **Os campos dos avós NÃO aparecem**?  
  ➜ Ajuste o controlador.  
  **Exemplo (Express + Drizzle):**
  ```ts
  // controllers/cavalos.ts
  export const getCavalo = async (req, res) => {
    const id = Number(req.params.id);
    const cavalo = await db.query.cavalos.findFirst({
      where: eq(cavalos.id, id),
      with: {
        genealogia: true,           // <-- garante join
        pai: { columns: { id: true, nome: true } },
        mae: { columns: { id: true, nome: true } },
      },
    });
    if (!cavalo) return res.status(404).json({ error: 'Not found' });
    return res.json(cavalo);
  };
Os campos aparecem mas ainda vêm com { "tipo":"externo", ... } dentro de string
➜ Pode desaninhar no back-end:

ts
Copiar
Editar
const cleanName = (raw) => {
  try {
    let cur = raw;
    let depth = 0;
    while (typeof cur === 'string' && cur.trim().startsWith('{') && depth < 5) {
      cur = JSON.parse(cur).cavaloNome;
      depth++;
    }
    return cur;
  } catch { return raw; }
};

cavalo.avoPaterno = cleanName(cavalo.avoPaterno);
cavalo.avoMaterno = cleanName(cavalo.avoMaterno);
2 · Garanta que o estado React receba o dado limpo
Mesmo que prefira limpar somente no front-end, o setFormData precisa fazer isso logo que o cavalo chega:

ts
Copiar
Editar
const { data: cavalo, isLoading } = useQuery(['cavalo', id], fetchCavalo, {
  enabled: !!id,
});

useEffect(() => {
  if (!cavalo) return;
  setFormData({
    paiId: cavalo.paiId ?? null,
    maeId: cavalo.maeId ?? null,
    avoPaterno: extractCleanName(cavalo.avoPaterno),
    avoMaterno: extractCleanName(cavalo.avoMaterno),
  });
}, [cavalo]);
Inputs controlados
tsx
Copiar
Editar
<Input
  label="Avô paterno"
  value={formData.avoPaterno ?? ""}
  onChange={(e) =>
    setFormData((p) => ({ ...p, avoPaterno: e.target.value }))
  }
/>
O mesmo para avoMaterno.

3 · Salvar sem criar nova “cebola” de JSON
ts
Copiar
Editar
const normalizarAvos = (nome: string | null) =>
  nome
    ? JSON.stringify({
        tipo: 'externo',
        cavaloSistemaId: null,
        cavaloNome: nome.trim(),
      })
    : null;
No mutationFn, monte o corpo com normalizarAvos(formData.avoPaterno) e idem para materno.

4 · Checklist rápido
API devolve avoPaterno e avoMaterno já limpos?
☐ Sim → vá p/ 2
☐ Não → corrija a query / parse

React DevTools → Components: o estado formData mostra os nomes dos avós como strings simples?
☐ Sim → inputs devem exibir.
☐ Não → ajuste o useEffect.

Editar → Salvar → Recarregar: nada de camadas extras no banco?
☐ Sim → problema resolvido.

Se ainda faltar algum campo
Verifique se há mais colunas na tabela genealogia que não estão no SELECT (bisavôs, trisavôs…).

Adicione‐as no join ou crie um endpoint dedicado /api/genealogia/:cavaloId.

Precisa de ajuda com uma query ou erro específico?
Copie/cole aqui:

pgsql
Copiar
Editar
• stack trace completo do back-end,  
• payload real da API (redigindo dados sensíveis),  
• ou código do componente onde o campo “some”.
Com essa info consigo apontar a linha exata a consertar.