Problema a resolver
A função extractCleanName foi criada, mas os inputs ainda exibem o JSON bruto porque os valores chegavam ao estado (formData) já aninhados. Precisamos garantir que o estado carregue nomes limpos e que os inputs continuem controlados.

🎯 Objetivos
Limpar imediatamente avoPaterno e avoMaterno quando o cavalo é carregado (useEffect).

Manter os inputs <Input> controlados, exibindo o nome limpo.

Continuar salvando com apenas uma camada de JSON.stringify (função normalizarAvos).

Manter compatibilidade com o componente ExpandedGenealogy.tsx (já usa o util).

🗂️ Arquivos a editar
Caminho	Ação
client/src/pages/EditarGenealogia.tsx	Ajustar useEffect, inputs e onChange
client/src/components/genetics/ExpandedGenealogy.tsx	Nada a mudar (já importa util)

🔧 Implementações solicitadas
1. useEffect – limpar assim que carregar
ts
Copiar
Editar
useEffect(() => {
  if (!cavalo) return;

  setFormData({
    paiId: cavalo.paiId ?? null,
    maeId: cavalo.maeId ?? null,
    avoPaterno: extractCleanName(cavalo.avoPaterno), // LIMPO!
    avoMaterno: extractCleanName(cavalo.avoMaterno), // LIMPO!
  });
}, [cavalo]);
2. Inputs de avós – controlados com valor limpo
tsx
Copiar
Editar
<Input
  label="Avô paterno"
  value={formData.avoPaterno}           // já está limpo
  onChange={(e) =>
    setFormData(prev => ({ ...prev, avoPaterno: e.target.value }))
  }
/>

<Input
  label="Avó materna"
  value={formData.avoMaterno}           // já está limpo
  onChange={(e) =>
    setFormData(prev => ({ ...prev, avoMaterno: e.target.value }))
  }
/>
3. Salvar – continuar usando normalizarAvos
ts
Copiar
Editar
const normalizarAvos = (nome: string | null) =>
  nome
    ? JSON.stringify({
        tipo: "externo",
        cavaloSistemaId: null,
        cavaloNome: nome.trim(),
      })
    : null;
Isso garante uma camada de JSON, independentemente do que havia antes.

✅ Critérios de aceitação
Abrir /cavalo/:id/genealogia/editar agora mostra nomes dos avós (sem {…}) nos inputs.

Alterar, salvar e recarregar não gera novas camadas de JSON.

ExpandedGenealogy continua exibindo nomes corretamente.

Testes/lint continuam passando e nenhuma rota de cavalo é quebrada.

Atenção, Replit AI: edite apenas o necessário para atingir os objetivos e mantenha o restante da lógica inalterado.