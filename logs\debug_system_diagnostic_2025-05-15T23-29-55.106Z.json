{"timestamp": "2025-05-15T23:29:55.106Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 395993088, "heapTotal": 109092864, "heapUsed": 72409712, "external": 8219482, "arrayBuffers": 243725}, "uptime": 3.173172681, "cpuUsage": {"user": 3010728, "system": 377869}, "resourceUsage": {"userCPUTime": 3010766, "systemCPUTime": 377873, "maxRSS": 386712, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102396, "majorPageFault": 1, "swappedOut": 0, "fsRead": 27832, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7997, "involuntaryContextSwitches": 6915}}