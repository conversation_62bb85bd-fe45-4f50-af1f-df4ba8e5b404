{"timestamp": "2025-05-16T18:16:32.493Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405807104, "heapTotal": 116695040, "heapUsed": 72642832, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.911781253, "cpuUsage": {"user": 3018378, "system": 350194}, "resourceUsage": {"userCPUTime": 3018425, "systemCPUTime": 350199, "maxRSS": 396296, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105572, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8912, "involuntaryContextSwitches": 5874}}