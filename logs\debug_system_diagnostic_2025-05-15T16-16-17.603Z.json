{"timestamp": "2025-05-15T16:16:17.603Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 398725120, "heapTotal": 102109184, "heapUsed": 66004528, "external": 7016254, "arrayBuffers": 60485}, "uptime": 1.551673217, "cpuUsage": {"user": 2367402, "system": 273258}, "resourceUsage": {"userCPUTime": 2367453, "systemCPUTime": 273263, "maxRSS": 389380, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105642, "majorPageFault": 0, "swappedOut": 0, "fsRead": 2520, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6928, "involuntaryContextSwitches": 1608}}