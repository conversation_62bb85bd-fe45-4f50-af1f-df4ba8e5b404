{"timestamp": "2025-05-15T01:54:32.994Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 388476928, "heapTotal": 101863424, "heapUsed": 73101872, "external": 6997039, "arrayBuffers": 60485}, "uptime": 2.898045599, "cpuUsage": {"user": 2445349, "system": 366393}, "resourceUsage": {"userCPUTime": 2445428, "systemCPUTime": 366393, "maxRSS": 379372, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99068, "majorPageFault": 0, "swappedOut": 0, "fsRead": 22160, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6074, "involuntaryContextSwitches": 7706}}