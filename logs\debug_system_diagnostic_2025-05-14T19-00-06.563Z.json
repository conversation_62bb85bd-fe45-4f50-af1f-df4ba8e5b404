{"timestamp": "2025-05-14T19:00:06.563Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 379850752, "heapTotal": 102633472, "heapUsed": 62023928, "external": 6815481, "arrayBuffers": 60485}, "uptime": 2.096602084, "cpuUsage": {"user": 2323905, "system": 296704}, "resourceUsage": {"userCPUTime": 2323964, "systemCPUTime": 296704, "maxRSS": 370948, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 98239, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7009, "involuntaryContextSwitches": 7294}}