{"timestamp": "2025-05-16T20:58:20.252Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403927040, "heapTotal": 116170752, "heapUsed": 72627496, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.26136132, "cpuUsage": {"user": 3174210, "system": 430927}, "resourceUsage": {"userCPUTime": 3174284, "systemCPUTime": 430927, "maxRSS": 394460, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104679, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 48, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8376, "involuntaryContextSwitches": 6820}}