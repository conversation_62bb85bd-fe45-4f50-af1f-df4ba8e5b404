{"timestamp": "2025-05-23T21:45:37.008Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 270278656, "heapTotal": 119332864, "heapUsed": 91831912, "external": 8318387, "arrayBuffers": 235533}, "uptime": 3.027955029, "cpuUsage": {"user": 3058167, "system": 413099}, "resourceUsage": {"userCPUTime": 3058216, "systemCPUTime": 413105, "maxRSS": 295900, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104968, "majorPageFault": 0, "swappedOut": 0, "fsRead": 2136, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8758, "involuntaryContextSwitches": 11040}}