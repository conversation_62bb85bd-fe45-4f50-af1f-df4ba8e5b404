{"timestamp": "2025-05-15T14:43:25.145Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 389726208, "heapTotal": 101863424, "heapUsed": 74192072, "external": 6960996, "arrayBuffers": 60485}, "uptime": 1.701994997, "cpuUsage": {"user": 2201172, "system": 332104}, "resourceUsage": {"userCPUTime": 2201233, "systemCPUTime": 332104, "maxRSS": 380592, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99793, "majorPageFault": 0, "swappedOut": 0, "fsRead": 7128, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6989, "involuntaryContextSwitches": 4479}}