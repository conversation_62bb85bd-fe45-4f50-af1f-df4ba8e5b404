{"timestamp": "2025-05-14T23:53:37.142Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 246329344, "heapTotal": 101322752, "heapUsed": 62260472, "external": 6815481, "arrayBuffers": 60485}, "uptime": 7.338566659, "cpuUsage": {"user": 2571898, "system": 378764}, "resourceUsage": {"userCPUTime": 2572005, "systemCPUTime": 378764, "maxRSS": 293652, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101464, "majorPageFault": 6, "swappedOut": 0, "fsRead": 42072, "fsWrite": 368, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6888, "involuntaryContextSwitches": 3583}}