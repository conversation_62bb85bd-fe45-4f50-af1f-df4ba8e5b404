{"timestamp": "2025-05-16T18:02:00.061Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 261357568, "heapTotal": 113287168, "heapUsed": 72604440, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.764152141, "cpuUsage": {"user": 2787202, "system": 370389}, "resourceUsage": {"userCPUTime": 2787252, "systemCPUTime": 370389, "maxRSS": 334552, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103333, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 40, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8289, "involuntaryContextSwitches": 2203}}