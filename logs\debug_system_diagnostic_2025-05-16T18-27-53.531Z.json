{"timestamp": "2025-05-16T18:27:53.531Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 303734784, "heapTotal": 113287168, "heapUsed": 72627688, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.847609793, "cpuUsage": {"user": 2772863, "system": 357443}, "resourceUsage": {"userCPUTime": 2772903, "systemCPUTime": 357448, "maxRSS": 296616, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102179, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8382, "involuntaryContextSwitches": 2906}}