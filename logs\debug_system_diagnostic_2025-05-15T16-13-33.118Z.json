{"timestamp": "2025-05-15T16:13:33.118Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 242335744, "heapTotal": 97914880, "heapUsed": 62412096, "external": 6815481, "arrayBuffers": 60485}, "uptime": 2.410080698, "cpuUsage": {"user": 2168851, "system": 337168}, "resourceUsage": {"userCPUTime": 2168892, "systemCPUTime": 337174, "maxRSS": 296044, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 97127, "majorPageFault": 0, "swappedOut": 0, "fsRead": 27376, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6422, "involuntaryContextSwitches": 2965}}