{"timestamp": "2025-05-15T14:27:19.233Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 376860672, "heapTotal": 91099136, "heapUsed": 70024352, "external": 6890744, "arrayBuffers": 106994}, "uptime": 3.330467242, "cpuUsage": {"user": 2582207, "system": 301958}, "resourceUsage": {"userCPUTime": 2582290, "systemCPUTime": 301958, "maxRSS": 368028, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 97282, "majorPageFault": 0, "swappedOut": 0, "fsRead": 10088, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6889, "involuntaryContextSwitches": 10782}}