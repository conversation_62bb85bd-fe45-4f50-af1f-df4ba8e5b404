{"timestamp": "2025-05-15T22:58:52.751Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397701120, "heapTotal": 113811456, "heapUsed": 72513304, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.75087508, "cpuUsage": {"user": 2745033, "system": 373709}, "resourceUsage": {"userCPUTime": 2745103, "systemCPUTime": 373709, "maxRSS": 388380, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104880, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 152, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8417, "involuntaryContextSwitches": 3433}}