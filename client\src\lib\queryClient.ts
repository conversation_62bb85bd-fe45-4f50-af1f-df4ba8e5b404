import { QueryClient, QueryFunction, QueryClientConfig, MutationCache, QueryCache } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";

/**
 * Utilitário para verificar se a resposta da API é válida
 * Lança uma exceção com mensagem formatada em caso de erro
 */
async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

/**
 * Recupera informações do usuário autenticado do localStorage
 * com tratamento de erros adequado
 * 
 * @returns Objeto do usuário ou null se não estiver autenticado
 */
function getLoggedInUser() {
  try {
    const userJson = localStorage.getItem('user');
    if (userJson) {
      return JSON.parse(userJson);
    }
  } catch (e) {
    console.error('Erro ao obter usuário do localStorage', e);
  }
  return null;
}

/**
 * Gerencia autenticação por token incluindo verificação de expiração
 * 
 * @returns Token de autenticação válido ou string vazia
 */
function getAuthToken() {
  try {
    const token = localStorage.getItem('auth_token') || '';
    const expiration = localStorage.getItem('token_expiration');
    
    // Se não houver token ou expiração, retornar vazio
    if (!token || !expiration) return '';
    
    // Verificar se o token expirou
    const expirationTime = parseInt(expiration);
    const currentTime = new Date().getTime();
    
    if (currentTime > expirationTime) {
      // Token expirado, limpar dados de autenticação e redirecionar
      console.warn('Token de autenticação expirado. Redirecionando para login...');
      clearAuthData();
      
      // Redirecionar para página de autenticação
      setTimeout(() => {
        window.location.href = '/login';
      }, 100);
      
      return '';
    }
    
    return token;
  } catch (e) {
    console.error('Erro ao obter token do localStorage', e);
    return '';
  }
}

/**
 * Limpa todos os dados de autenticação do localStorage
 */
function clearAuthData() {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('token_expiration');
  localStorage.removeItem('user');
}

/**
 * Função utilitária para realizar requisições à API com tratamento 
 * adequado de erros e autenticação
 * 
 * @param method Método HTTP (GET, POST, PATCH, DELETE)
 * @param path Caminho da API
 * @param data Dados a serem enviados (para POST/PATCH)
 * @returns Resposta da API tipada
 */
export async function apiRequest<T = any>(
  method: string,
  path: string,
  data?: any,
  options: RequestInit = {}
): Promise<T> {
  // Obter credenciais do usuário
  const user = getLoggedInUser();
  const token = getAuthToken();
  
  // Configurar cabeçalhos com informações de autenticação
  // Preservar cabeçalhos existentes se forem enviados nas opções
  const headers = options.headers ? new Headers(options.headers) : new Headers();
  
  // Adicionar Content-Type se não estiver presente e não for FormData
  if (!headers.has('Content-Type') && !(data instanceof FormData)) {
    headers.set('Content-Type', 'application/json');
  }
  
  // Garantir que o ID do usuário existe e é um número válido
  if (user?.id && typeof user.id === 'number') {
    // Não sobrescrever se o header já estiver definido nas opções
    if (!headers.has('user-id')) {
      headers.set('user-id', user.id.toString());
    }
    console.log('User ID para autenticação:', user.id);
    console.log('Headers enviados:', Object.fromEntries(headers.entries()));
  }
  
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }

  // Configuração da requisição
  const config: RequestInit = {
    method,
    headers,
    credentials: "include",
    ...options // Mesclar opções adicionais
  };

  // Adicionar corpo da requisição para métodos que o suportam
  if (data && (method === 'POST' || method === 'PATCH' || method === 'PUT')) {
    // Se for FormData, usar diretamente; caso contrário, converter para JSON
    config.body = data instanceof FormData ? data : JSON.stringify(data);
  }

  try {
    // Realizar a requisição
    const res = await fetch(path, config);
    
    // Tratamento específico para erro de autenticação
    if (res.status === 401) {
      clearAuthData();
      toast({
        title: "Sessão expirada",
        description: "Você será redirecionado para a página de login.",
        variant: "destructive"
      });
      
      setTimeout(() => {
        window.location.href = '/login';
      }, 1500);
      
      throw new Error('Sua sessão expirou. Por favor, faça login novamente.');
    }
    
    // Tratar outros erros
    if (!res.ok) {
      const text = await res.text();
      try {
        // Tentar interpretar resposta de erro como JSON
        const errorData = JSON.parse(text);
        throw new Error(errorData.message || `Erro ${res.status}: ${res.statusText}`);
      } catch (e) {
        // Caso não seja JSON, usar texto bruto
        throw new Error(`${res.status}: ${text || res.statusText}`);
      }
    }
    
    // Para métodos como DELETE que podem não retornar conteúdo
    if (res.status === 204) {
      return {} as T;
    }
    
    // Tentar fazer parse do JSON da resposta
    try {
      return await res.json() as T;
    } catch (e) {
      // Se não for possível fazer parse (resposta vazia), retornar objeto vazio
      return {} as T;
    }
  } catch (error) {
    console.error(`Erro na requisição ${method} ${path}:`, error);
    throw error;
  }
}

/**
 * Comportamentos possíveis ao receber erro 401 (não autorizado)
 */
type UnauthorizedBehavior = "returnNull" | "throw";

/**
 * Factory para criar funções de query compatíveis com React Query
 * com suporte a autenticação e tratamento de erros
 */
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey, signal }) => {
    // Obter dados de autenticação
    const user = getLoggedInUser();
    const token = getAuthToken();
    
    // Construir URL completa se o queryKey contiver parâmetros
    let url = queryKey[0] as string;
    const params = queryKey[1] as Record<string, string> | undefined;
    
    // Adicionar parâmetros de consulta se fornecidos
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value);
        }
      });
      
      const queryString = searchParams.toString();
      if (queryString) {
        url = `${url}?${queryString}`;
      }
    }
    
    // Configurar cabeçalhos com dados de autenticação
    const headers = new Headers();
    headers.set('Content-Type', 'application/json');
    
    if (user?.id && typeof user.id === 'number') {
      headers.set('user-id', user.id.toString());
    }
    
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }
    
    try {
      const res = await fetch(url, {
        headers,
        credentials: "include",
        signal, // Suporte a cancelamento de requests
      });

      // Tratamento específico para erro de autenticação
      if (res.status === 401) {
        clearAuthData();
        
        if (unauthorizedBehavior === "returnNull") {
          toast({
            title: "Sessão expirada",
            description: "Você será redirecionado para a página de login.",
            variant: "destructive"
          });
          
          setTimeout(() => {
            window.location.href = '/login';
          }, 1500);
          
          return null;
        } else {
          throw new Error('Sua sessão expirou. Por favor, faça login novamente.');
        }
      }

      // Tratar outros erros da API
      if (!res.ok) {
        const text = await res.text();
        try {
          const errorData = JSON.parse(text);
          throw new Error(errorData.message || `Erro ${res.status}: ${res.statusText}`);
        } catch (e) {
          throw new Error(`${res.status}: ${text || res.statusText}`);
        }
      }
      
      // Para respostas que não contêm conteúdo
      if (res.status === 204) {
        return null;
      }
      
      // Tentar fazer parse do JSON da resposta
      try {
        return await res.json();
      } catch (e) {
        // Se não for possível fazer parse, retornar null
        return null;
      }
    } catch (error) {
      // Ignorar erros de abortamento de requisição (causados por cancelamento)
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.info('Request cancelada pelo React Query');
        throw error;
      }
      
      console.error(`Erro na consulta ${url}:`, error);
      throw error;
    }
  };

// Configuração avançada do queryClient
const queryClientConfig: QueryClientConfig = {
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: true,
      staleTime: 30000, // 30 segundos - tempo em que os dados são considerados atualizados
      retry: (failureCount, error) => {
        // Não tentar novamente para erros de autenticação ou permissão
        if (error instanceof Error && error.message.includes('401')) {
          return false;
        }
        // Para outros erros, tentar no máximo 2 vezes
        return failureCount < 2;
      },
      gcTime: 300000, // 5 minutos (tempo que os dados permanecem em cache)
      refetchOnMount: true, // Recarregar dados quando o componente montar
      refetchOnReconnect: true, // Recarregar dados quando reconectar
    },
    mutations: {
      retry: (failureCount, error) => {
        // Não tentar novamente para erros de autenticação ou permissão
        if (error instanceof Error && error.message.includes('401')) {
          return false;
        }
        // Para outros erros, tentar no máximo 1 vez
        return failureCount < 1;
      },
    },
  },
  // Cache global para queries com notificações de erro
  queryCache: new QueryCache({
    onError: (error, query) => {
      // Evitar mostrar toast para erros de autenticação (já tratados separadamente)
      if (error instanceof Error && !error.message.includes('401')) {
        toast({
          title: "Erro ao carregar dados",
          description: error.message,
          variant: "destructive"
        });
      }
    }
  }),
  // Cache global para mutations com notificações de erro
  mutationCache: new MutationCache({
    onError: (error, variables, context, mutation) => {
      // Evitar mostrar toast para erros de autenticação (já tratados separadamente)
      if (error instanceof Error && !error.message.includes('401')) {
        toast({
          title: "Erro ao salvar dados",
          description: error.message,
          variant: "destructive"
        });
      }
    }
  })
};

// Exportar o queryClient configurado
export const queryClient = new QueryClient(queryClientConfig);
