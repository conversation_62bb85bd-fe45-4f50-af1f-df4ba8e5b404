{"timestamp": "2025-05-16T17:22:19.675Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 389128192, "heapTotal": 109355008, "heapUsed": 72582520, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.160673405, "cpuUsage": {"user": 3037439, "system": 386545}, "resourceUsage": {"userCPUTime": 3037479, "systemCPUTime": 386545, "maxRSS": 380008, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101720, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8280, "involuntaryContextSwitches": 6727}}