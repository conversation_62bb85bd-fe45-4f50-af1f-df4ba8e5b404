{"timestamp": "2025-05-16T23:10:19.346Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 293257216, "heapTotal": 153235456, "heapUsed": 147962720, "external": 17342950, "arrayBuffers": 4369707}, "uptime": 634.767958171, "cpuUsage": {"user": 21503305, "system": 1480924}, "resourceUsage": {"userCPUTime": 21503358, "systemCPUTime": 1480928, "maxRSS": 606556, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 298903, "majorPageFault": 3, "swappedOut": 0, "fsRead": 8, "fsWrite": 1776, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 30432, "involuntaryContextSwitches": 26625}}