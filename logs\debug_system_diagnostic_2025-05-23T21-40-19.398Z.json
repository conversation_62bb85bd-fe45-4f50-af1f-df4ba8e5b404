{"timestamp": "2025-05-23T21:40:19.397Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 299876352, "heapTotal": 121303040, "heapUsed": 96274584, "external": 8338663, "arrayBuffers": 274785}, "uptime": 1.921360718, "cpuUsage": {"user": 2705522, "system": 389414}, "resourceUsage": {"userCPUTime": 2705567, "systemCPUTime": 389414, "maxRSS": 292848, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104980, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9316, "involuntaryContextSwitches": 3352}}