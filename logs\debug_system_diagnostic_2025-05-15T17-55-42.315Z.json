{"timestamp": "2025-05-15T17:55:42.315Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403791872, "heapTotal": 115908608, "heapUsed": 72541824, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.233504597, "cpuUsage": {"user": 3176939, "system": 404685}, "resourceUsage": {"userCPUTime": 3176996, "systemCPUTime": 404685, "maxRSS": 394328, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104790, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 128, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8572, "involuntaryContextSwitches": 5506}}