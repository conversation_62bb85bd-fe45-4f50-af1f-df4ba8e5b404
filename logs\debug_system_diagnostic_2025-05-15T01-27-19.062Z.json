{"timestamp": "2025-05-15T01:27:19.062Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 386002944, "heapTotal": 101601280, "heapUsed": 74201440, "external": 6976932, "arrayBuffers": 60485}, "uptime": 1.792816623, "cpuUsage": {"user": 2313561, "system": 332145}, "resourceUsage": {"userCPUTime": 2313606, "systemCPUTime": 332152, "maxRSS": 376956, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100257, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5918, "involuntaryContextSwitches": 3816}}