{"timestamp": "2025-05-16T13:14:31.504Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402366464, "heapTotal": 113811456, "heapUsed": 72589208, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.948247248, "cpuUsage": {"user": 2885539, "system": 353218}, "resourceUsage": {"userCPUTime": 2885581, "systemCPUTime": 353223, "maxRSS": 392936, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103399, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8289, "involuntaryContextSwitches": 3302}}