{"timestamp": "2025-05-16T18:10:39.671Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 410509312, "heapTotal": 108568576, "heapUsed": 72472360, "external": 8219482, "arrayBuffers": 243725}, "uptime": 6.20352423, "cpuUsage": {"user": 2886355, "system": 411038}, "resourceUsage": {"userCPUTime": 2886424, "systemCPUTime": 411038, "maxRSS": 400888, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107614, "majorPageFault": 6, "swappedOut": 0, "fsRead": 36456, "fsWrite": 712, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7404, "involuntaryContextSwitches": 3989}}