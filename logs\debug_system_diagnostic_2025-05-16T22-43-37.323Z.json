{"timestamp": "2025-05-16T22:43:37.322Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404348928, "heapTotal": 115908608, "heapUsed": 72598984, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.767114021, "cpuUsage": {"user": 2801913, "system": 338687}, "resourceUsage": {"userCPUTime": 2802156, "systemCPUTime": 338687, "maxRSS": 394872, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103545, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7351, "involuntaryContextSwitches": 2646}}