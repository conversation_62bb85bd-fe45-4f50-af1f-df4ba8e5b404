{"timestamp": "2025-05-14T18:13:58.385Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 378040320, "heapTotal": 100798464, "heapUsed": 62175752, "external": 6815481, "arrayBuffers": 60485}, "uptime": 5.849376301, "cpuUsage": {"user": 1957090, "system": 263441}, "resourceUsage": {"userCPUTime": 1957139, "systemCPUTime": 263441, "maxRSS": 369180, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102436, "majorPageFault": 6, "swappedOut": 0, "fsRead": 42264, "fsWrite": 368, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6564, "involuntaryContextSwitches": 277}}