{"timestamp": "2025-05-15T21:59:04.028Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397774848, "heapTotal": 113610752, "heapUsed": 72216608, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.686847407, "cpuUsage": {"user": 2709474, "system": 347670}, "resourceUsage": {"userCPUTime": 2709515, "systemCPUTime": 347670, "maxRSS": 388452, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103188, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8171, "involuntaryContextSwitches": 2412}}