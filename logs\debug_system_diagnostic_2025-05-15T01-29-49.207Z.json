{"timestamp": "2025-05-15T01:29:49.207Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 385380352, "heapTotal": 96620544, "heapUsed": 69056056, "external": 6916228, "arrayBuffers": 60485}, "uptime": 3.160431738, "cpuUsage": {"user": 2469035, "system": 378769}, "resourceUsage": {"userCPUTime": 2469090, "systemCPUTime": 378777, "maxRSS": 376348, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100640, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 96, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5624, "involuntaryContextSwitches": 10165}}