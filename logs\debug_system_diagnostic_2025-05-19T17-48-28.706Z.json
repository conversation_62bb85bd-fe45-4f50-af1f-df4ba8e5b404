{"timestamp": "2025-05-19T17:48:28.705Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 270135296, "heapTotal": 119595008, "heapUsed": 92277808, "external": 8461178, "arrayBuffers": 235533}, "uptime": 1.73484491, "cpuUsage": {"user": 2670685, "system": 357200}, "resourceUsage": {"userCPUTime": 2670745, "systemCPUTime": 357200, "maxRSS": 327120, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104799, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8849, "involuntaryContextSwitches": 1875}}