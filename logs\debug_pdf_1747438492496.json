{"dados": "preview_1747438492496", "logs": "{\n  \"dados\": {\n    \"cavalo\": {\n      \"nome\": \"RZ CAMPONESA DA CARAPUÇA\",\n      \"registro\": \"B644631\",\n      \"rp\": \"2195\",\n      \"sexo\": \"FÊMEA\",\n      \"nascimento\": \"15/10/2021\",\n      \"pelagem\": \"BAIA\",\n      \"criador\": \"RUBENS ELIAS ZOGBI\",\n      \"proprietario\": \"CAMILA WEBER\",\n      \"inspetor\": \"RODRIGO RODRIGUES TEIXEIRA\"\n    },\n    \"pai\": {\n      \"nome\": \"BASCA CONFIANÇA\",\n      \"registro\": \"B481211\"\n    },\n    \"mae\": {\n      \"nome\": \"BT DELANTERO\"\n    },\n    \"avoPai\": {\n      \"nome\": \"LA INVERNADA HORNERO\"\n    },\n    \"avaMae\": {\n      \"nome\": \"EL IDEAL ASI GUARDA\"\n    },\n    \"avoPai2\": {\n      \"nome\": \"BT MANDALA\"\n    },\n    \"avaMae2\": {\n      \"nome\": \"LA INVERNADA ANIVERSÁRIO\"\n    }\n  },\n  \"log\": \"[2025-05-16T23:34:44.397Z] [INFO] Iniciando processamento com OpenAI\\n[2025-05-16T23:34:52.495Z] [DEBUG] Conteúdo bruto da resposta: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"RZ CAMPONESA DA CARAPUÇA\\\",\\n    \\\"registro\\\": \\\"B644631\\\",\\n    \\\"rp\\\": \\\"2195\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"15/10/2021\\\",\\n    \\\"pelagem\\\": \\\"BAIA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"RUBENS ELIAS ZOGBI\\\",\\n    \\\"inspetor_tecnico\\\": \\\"RODRIGO RODRIGUES TEIXEIRA\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"BASCA CONFIANÇA\\\",\\n    \\\"registro\\\": \\\"B481211\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"BT DELANTERO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"LA INVERNADA HORNERO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"EL IDEAL ASI GUARDA\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"BT MANDALA\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"LA INVERNADA ANIVERSÁRIO\\\",\\n    \\\"registro\\\": null\\n  }\\n}\\n[2025-05-16T23:34:52.495Z] [INFO] Recebido resposta da OpenAI e parseado com sucesso\\n[2025-05-16T23:34:52.495Z] [DEBUG] Dados extraídos: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"RZ CAMPONESA DA CARAPUÇA\\\",\\n    \\\"registro\\\": \\\"B644631\\\",\\n    \\\"rp\\\": \\\"2195\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"15/10/2021\\\",\\n    \\\"pelagem\\\": \\\"BAIA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"RUBENS ELIAS ZOGBI\\\",\\n    \\\"inspetor_tecnico\\\": \\\"RODRIGO RODRIGUES TEIXEIRA\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"BASCA CONFIANÇA\\\",\\n    \\\"registro\\\": \\\"B481211\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"BT DELANTERO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"LA INVERNADA HORNERO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"EL IDEAL ASI GUARDA\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"BT MANDALA\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"LA INVERNADA ANIVERSÁRIO\\\",\\n    \\\"registro\\\": null\\n  }\\n}\\n[2025-05-16T23:34:52.495Z] [INFO] Dados do cavalo principal extraídos: {\\n  \\\"nome\\\": \\\"RZ CAMPONESA DA CARAPUÇA\\\",\\n  \\\"registro\\\": \\\"B644631\\\",\\n  \\\"rp\\\": \\\"2195\\\",\\n  \\\"sexo\\\": \\\"FEMEA\\\",\\n  \\\"nascimento\\\": \\\"15/10/2021\\\",\\n  \\\"pelagem\\\": \\\"BAIA\\\",\\n  \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n  \\\"criador\\\": \\\"RUBENS ELIAS ZOGBI\\\",\\n  \\\"inspetor_tecnico\\\": \\\"RODRIGO RODRIGUES TEIXEIRA\\\"\\n}\\n[2025-05-16T23:34:52.496Z] [INFO] Dados do cavalo principal extraídos: RZ CAMPONESA DA CARAPUÇA (B644631)\\n[2025-05-16T23:34:52.496Z] [INFO] Pai: BASCA CONFIANÇA (B481211)\\n[2025-05-16T23:34:52.496Z] [INFO] Mãe: BT DELANTERO (sem registro)\\n[2025-05-16T23:34:52.496Z] [INFO] Dados convertidos com sucesso\"\n}", "timestamp": "2025-05-16T23:34:52.496Z"}