{"timestamp": "2025-05-26T21:27:03.822Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404602880, "heapTotal": 123334656, "heapUsed": 99123120, "external": 8937698, "arrayBuffers": 257466}, "uptime": 2.076392282, "cpuUsage": {"user": 3003859, "system": 376682}, "resourceUsage": {"userCPUTime": 3003923, "systemCPUTime": 376682, "maxRSS": 395120, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 113830, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8244, "involuntaryContextSwitches": 3308}}