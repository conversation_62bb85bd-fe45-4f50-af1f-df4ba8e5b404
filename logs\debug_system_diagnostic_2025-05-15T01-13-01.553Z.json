{"timestamp": "2025-05-15T01:13:01.553Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 242802688, "heapTotal": 98701312, "heapUsed": 62183880, "external": 6815481, "arrayBuffers": 60485}, "uptime": 3.095496118, "cpuUsage": {"user": 2298470, "system": 367419}, "resourceUsage": {"userCPUTime": 2298536, "systemCPUTime": 367429, "maxRSS": 285708, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 98098, "majorPageFault": 1, "swappedOut": 0, "fsRead": 31944, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5742, "involuntaryContextSwitches": 5646}}