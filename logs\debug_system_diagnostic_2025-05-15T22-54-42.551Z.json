{"timestamp": "2025-05-15T22:54:42.551Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402714624, "heapTotal": 114073600, "heapUsed": 72521424, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.474518937, "cpuUsage": {"user": 3006626, "system": 420050}, "resourceUsage": {"userCPUTime": 3006686, "systemCPUTime": 420050, "maxRSS": 393276, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103938, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 16, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9020, "involuntaryContextSwitches": 8485}}