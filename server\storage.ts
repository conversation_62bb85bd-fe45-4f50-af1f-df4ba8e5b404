import { 
  users, type User, type InsertUser,
  cavalos, type Cavalo, type InsertCavalo,
  manejos, type Manejo, type InsertManejo,
  arquivos, type Arquivo, type InsertArquivo,
  eventos, type Evento, type InsertEvento,
  procedimentosVet, type ProcedimentoVet, type InsertProcedimentoVet,
  reproducao, type Reproducao, type InsertReproducao,
  medidasFisicas, type MedidaFisica, type InsertMedidaFisica,
  nutricao, type Nutricao, type InsertNutricao,
  
  // Novas tabelas de genética
  morfologia, type Morfologia, type InsertMorfologia,
  desempenhoHistorico, type DesempenhoHistorico, type InsertDesempenhoHistorico,
  genealogia, type Genealogia, type InsertGenealogia,
  sugestoesCruzamento, type SugestoesCruzamento, type InsertSugestoesCruzamento
} from "@shared/schema";
import { db } from "./db";
import { eq, and, asc, desc, like, sql, ne, or, isNull, notInArray, gte, lte, lt, inArray } from "drizzle-orm";
import { getModuleLogger, logDbError } from './logger';

// modify the interface with any CRUD methods
// you might need

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Cavalo (Horse) methods
  getCavalos(userId: number): Promise<Cavalo[]>;
  getCavalo(id: number, userId: number): Promise<Cavalo | undefined>;
  createCavalo(cavalo: InsertCavalo): Promise<Cavalo>;
  updateCavalo(id: number, userId: number, cavalo: Partial<InsertCavalo>): Promise<Cavalo | undefined>;
  deleteCavalo(id: number, userId: number): Promise<boolean>;
  
  // Manejo (Horse care) methods
  getManejos(userId: number): Promise<Manejo[]>;
  getManejosByHorse(horseId: number, userId: number): Promise<Manejo[]>;
  getManejo(id: number, userId: number): Promise<Manejo | undefined>;
  createManejo(manejo: InsertManejo): Promise<Manejo>;
  updateManejo(id: number, userId: number, manejo: Partial<InsertManejo>): Promise<Manejo | undefined>;
  deleteManejo(id: number, userId: number): Promise<boolean>;
  
  // Arquivo (File) methods
  getArquivos(userId: number): Promise<Arquivo[]>;
  getArquivosByHorse(horseId: number, userId: number): Promise<Arquivo[]>;
  getArquivo(id: number, userId: number): Promise<Arquivo | undefined>;
  createArquivo(arquivo: InsertArquivo): Promise<Arquivo>;
  deleteArquivo(id: number, userId: number): Promise<boolean>;
  
  // Evento (Schedule) methods
  getEventos(userId: number): Promise<Evento[]>;
  getEventosByDate(data: string, userId: number): Promise<Evento[]>;
  getEventosByHorse(horseId: number, userId: number): Promise<Evento[]>;
  getEventosByManejo(manejoId: number, userId: number): Promise<Evento[]>;
  getEvento(id: number, userId: number): Promise<Evento | undefined>;
  createEvento(evento: InsertEvento): Promise<Evento>;
  updateEvento(id: number, userId: number, evento: Partial<InsertEvento>): Promise<Evento | undefined>;
  deleteEvento(id: number, userId: number): Promise<boolean>;
  
  // Procedimentos Veterinários methods
  getProcedimentosVet(userId: number): Promise<ProcedimentoVet[]>;
  getProcedimentosVetByHorse(horseId: number, userId: number): Promise<ProcedimentoVet[]>;
  getProcedimentosVetByManejo(manejoId: number, userId: number): Promise<ProcedimentoVet[]>;
  getProcedimentosVetByEvento(eventoId: number, userId: number): Promise<ProcedimentoVet[]>;
  getProcedimentoVet(id: number, userId: number): Promise<ProcedimentoVet | undefined>;
  createProcedimentoVet(procedimento: InsertProcedimentoVet): Promise<ProcedimentoVet>;
  updateProcedimentoVet(id: number, userId: number, procedimento: Partial<InsertProcedimentoVet>): Promise<ProcedimentoVet | undefined>;
  deleteProcedimentoVet(id: number, userId: number): Promise<boolean>;
  
  // Reprodução methods
  getReproducoes(userId: number): Promise<Reproducao[]>;
  getReproducoesByHorse(horseId: number, userId: number): Promise<Reproducao[]>;
  getReproducoesByProcedimento(procedimentoId: number, userId: number): Promise<Reproducao[]>;
  getReproducoesByEvento(eventoId: number, userId: number): Promise<Reproducao[]>;
  getReproducao(id: number, userId: number): Promise<Reproducao | undefined>;
  createReproducao(reproducaoData: InsertReproducao): Promise<Reproducao>;
  updateReproducao(id: number, userId: number, reproducaoData: Partial<InsertReproducao>): Promise<Reproducao | undefined>;
  deleteReproducao(id: number, userId: number): Promise<boolean>;
  
  // Medidas Físicas methods
  getMedidasFisicas(userId: number): Promise<MedidaFisica[]>;
  getMedidasFisicasByHorse(horseId: number, userId: number): Promise<MedidaFisica[]>;
  getMedidaFisica(id: number, userId: number): Promise<MedidaFisica | undefined>;
  createMedidaFisica(medidaData: InsertMedidaFisica): Promise<MedidaFisica>;
  updateMedidaFisica(id: number, userId: number, medidaData: Partial<InsertMedidaFisica>): Promise<MedidaFisica | undefined>;
  deleteMedidaFisica(id: number, userId: number): Promise<boolean>;
  
  // Nutrição methods
  getNutricoes(userId: number): Promise<Nutricao[]>;
  getNutricoesByHorse(horseId: number, userId: number): Promise<Nutricao[]>;
  getNutricao(id: number, userId: number): Promise<Nutricao | undefined>;
  createNutricao(nutricaoData: InsertNutricao): Promise<Nutricao>;
  updateNutricao(id: number, userId: number, nutricaoData: Partial<InsertNutricao>): Promise<Nutricao | undefined>;
  deleteNutricao(id: number, userId: number): Promise<boolean>;
  
  // Morfologia methods (medidas morfológicas detalhadas)
  getMorfologias(userId: number): Promise<Morfologia[]>;
  getMorfologiasByHorse(horseId: number, userId: number): Promise<Morfologia[]>;
  getMorfologia(id: number, userId: number): Promise<Morfologia | undefined>;
  createMorfologia(morfologiaData: InsertMorfologia): Promise<Morfologia>;
  updateMorfologia(id: number, userId: number, morfologiaData: Partial<InsertMorfologia>): Promise<Morfologia | undefined>;
  deleteMorfologia(id: number, userId: number): Promise<boolean>;
  
  // Histórico de Desempenho methods (competições, eventos, etc.)
  getDesempenhos(userId: number): Promise<DesempenhoHistorico[]>;
  getDesempenhosByHorse(horseId: number, userId: number): Promise<DesempenhoHistorico[]>;
  getDesempenho(id: number, userId: number): Promise<DesempenhoHistorico | undefined>;
  createDesempenho(desempenhoData: InsertDesempenhoHistorico): Promise<DesempenhoHistorico>;
  updateDesempenho(id: number, userId: number, desempenhoData: Partial<InsertDesempenhoHistorico>): Promise<DesempenhoHistorico | undefined>;
  deleteDesempenho(id: number, userId: number): Promise<boolean>;
  
  // Genealogia methods (registro detalhado de ancestrais)
  getGenealogias(userId: number): Promise<Genealogia[]>;
  getGenealogiaByHorse(horseId: number, userId: number): Promise<Genealogia | undefined>;
  createGenealogia(genealogiaData: InsertGenealogia): Promise<Genealogia>;
  updateGenealogia(id: number, userId: number, genealogiaData: Partial<InsertGenealogia>): Promise<Genealogia | undefined>;
  deleteGenealogia(id: number, userId: number): Promise<boolean>;
  calcularConsanguinidade(horseId: number, userId: number): Promise<number>;
  
  // Sugestões de Cruzamento methods (IA para sugerir cruzamentos)
  getSugestoesCruzamento(userId: number): Promise<SugestoesCruzamento[]>;
  getSugestoesCruzamentoByHorse(horseIdBase: number, userId: number): Promise<SugestoesCruzamento[]>;
  getSugestoesCruzamentoById(id: number, userId: number): Promise<SugestoesCruzamento | undefined>;
  createSugestaoCruzamento(sugestaoData: InsertSugestoesCruzamento): Promise<SugestoesCruzamento>;
  deleteSugestaoCruzamento(id: number, userId: number): Promise<boolean>;
  gerarSugestoesCruzamento(horseIdBase: number, objetivo: string, userId: number): Promise<SugestoesCruzamento[]>;
}

export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }
  
  // Cavalo (Horse) methods
  async getCavalos(userId: number): Promise<Cavalo[]> {
    const logger = getModuleLogger('storage');
    
    try {
      // Buscar apenas cavalos que não são externos (excluir os da genealogia)
      // Utilizando o where composto para filtrar por userId e isExternal = false
      logger.debug(`Buscando cavalos para usuário ${userId} (excluindo externos)`);
      
      return db.select()
        .from(cavalos)
        .where(
          and(
            eq(cavalos.userId, userId),
            or(
              eq(cavalos.isExternal, false),
              isNull(cavalos.isExternal)
            )
          )
        );
    } catch (error: any) {
      // Se ocorrer algum erro relacionado à coluna is_external, fazer fallback para consulta simples
      if (error.message && error.message.includes('is_external')) {
        logger.warn(`Erro ao usar isExternal na consulta: ${error.message}. Usando consulta de fallback.`);
        return db.select()
          .from(cavalos)
          .where(eq(cavalos.userId, userId));
      }
      // Se for outro erro, propagar
      logger.error(`Erro ao buscar cavalos: ${error.message}`);
      throw error;
    }
  }
  
  async getCavalo(id: number, userId: number): Promise<Cavalo | undefined> {
    try {
      // Buscar cavalo com dados de genealogia (avós estão na própria tabela cavalos)
      const result = await db
        .select()
        .from(cavalos)
        .where(eq(cavalos.id, id));

      if (result.length === 0) {
        return undefined;
      }

      const cavalo = result[0];
      
      // Limpar campos de genealogia que podem ter JSON aninhado
      if (cavalo.avoPaterno) {
        cavalo.avoPaterno = this.extractCleanName(cavalo.avoPaterno);
      }
      if (cavalo.avoMaterno) {
        cavalo.avoMaterno = this.extractCleanName(cavalo.avoMaterno);
      }
      
      return cavalo;
    } catch (error) {
      console.error(`Erro ao buscar cavalo ${id}:`, error);
      return undefined;
    }
  }

  // Função auxiliar para extrair nomes limpos de campos JSON aninhados
  private extractCleanName(rawValue: any): string | null {
    if (!rawValue) return null;
    
    try {
      let current = rawValue;
      let depth = 0;
      
      // Desaninhar JSON até 5 níveis de profundidade
      while (typeof current === 'string' && current.trim().startsWith('{') && depth < 5) {
        const parsed = JSON.parse(current);
        current = parsed.cavaloNome || parsed.nome || parsed.name || current;
        depth++;
      }
      
      return typeof current === 'string' ? current : null;
    } catch {
      return typeof rawValue === 'string' ? rawValue : null;
    }
  }
  
  async createCavalo(insertCavalo: InsertCavalo): Promise<Cavalo> {
    const logger = getModuleLogger('storage');
    
    try {
      // Processar dados de genealogia no formato novo antes de inserir
      const processedData = { ...insertCavalo } as any;
      
      // Processar os objetos 'pai' e 'mae' se existirem
      if (processedData.pai) {
        const valorPai = processedData.pai;
        
        if (typeof valorPai === 'object' && valorPai !== null) {
          // Se for objeto EntradaGenealogica
          const entradaPai = valorPai as any;
          
          if (entradaPai.tipo === 'sistema' && entradaPai.cavaloId) {
            processedData.paiId = entradaPai.cavaloId;
            processedData.paiNome = null;
          } 
          else if (entradaPai.tipo === 'externo' && entradaPai.cavaloNome) {
            processedData.paiId = null;
            processedData.paiNome = entradaPai.cavaloNome;
          }
          else {
            processedData.paiId = null;
            processedData.paiNome = null;
          }
        }
        
        // Remover o campo 'pai'
        delete processedData.pai;
      }
      
      if (processedData.mae) {
        const valorMae = processedData.mae;
        
        if (typeof valorMae === 'object' && valorMae !== null) {
          // Se for objeto EntradaGenealogica
          const entradaMae = valorMae as any;
          
          if (entradaMae.tipo === 'sistema' && entradaMae.cavaloId) {
            processedData.maeId = entradaMae.cavaloId;
            processedData.maeNome = null;
          } 
          else if (entradaMae.tipo === 'externo' && entradaMae.cavaloNome) {
            processedData.maeId = null;
            processedData.maeNome = entradaMae.cavaloNome;
          }
          else {
            processedData.maeId = null;
            processedData.maeNome = null;
          }
        }
        
        // Remover o campo 'mae'
        delete processedData.mae;
      }
      
      logger.debug('Criando cavalo com dados processados:', {
        dados_originais: JSON.stringify(insertCavalo),
        dados_processados: JSON.stringify(processedData)
      });
      
      const [cavalo] = await db
        .insert(cavalos)
        .values(processedData)
        .returning();
        
      return cavalo;
    }
    catch (error) {
      logger.error('Erro ao criar cavalo:', error);
      throw error;
    }
  }
  
  async updateCavalo(id: number, userId: number, cavaloData: Partial<InsertCavalo>): Promise<Cavalo | undefined> {
    // Usando o logger importado no topo do arquivo
    const storageLogger = getModuleLogger('storage');

    try {
      storageLogger.info(`⚙️ INICIANDO ATUALIZAÇÃO DO CAVALO ${id} PARA USUÁRIO ${userId}`, { 
        cavaloId: id,
        userId: userId,
        operation: 'updateCavalo'
      });
      
      // Log detalhado dos dados recebidos para depuração
      storageLogger.debug(`📋 Dados completos recebidos para atualização:`, { 
        dados_recebidos: JSON.stringify(cavaloData),
        campos_presentes: Object.keys(cavaloData),
        operation: 'updateCavalo'
      });
      
      // Criar uma cópia dos dados para processamento
      const processedData = { ...cavaloData } as any;
      
      // Lista de campos que NUNCA devem ser removidos, mesmo se vazios
      // Isso garante que valores como breed não sejam removidos do banco
      const requiredFields = ['breed', 'name', 'birthDate'];
      
      // SISTEMA DE GENEALOGIA DUAL (ID + NOME)
      // Esta implementação suporta tanto cavalos do sistema (via ID) quanto cavalos externos (via nome)
      // Cada tipo de entrada é armazenado em campos diferentes: paiId/paiNome e maeId/maeNome

      // Processar campo pai (objeto tipo EntradaGenealogica)
      if ('pai' in processedData) {
        const valorPai = processedData.pai;
        
        // Log debug
        storageLogger.debug(`Processando campo pai:`, { 
          valor_pai: valorPai,
          tipo_pai: typeof valorPai
        });
        
        // Se o valor for null ou undefined, limpar ambos os campos
        if (valorPai === null || valorPai === undefined) {
          processedData.paiId = null;
          processedData.paiNome = null;
        }
        // Se for um objeto EntradaGenealogica
        else if (typeof valorPai === 'object' && valorPai !== null) {
          const entradaPai = valorPai as any;
          
          // Log detalhado para diagnóstico
          storageLogger.debug('Objeto EntradaGenealogica para pai:', {
            objeto_completo: entradaPai,
            tipo: entradaPai.tipo,
            cavaloSistemaId: entradaPai.cavaloSistemaId,
            cavaloNome: entradaPai.cavaloNome
          });
          
          if (entradaPai.tipo === 'sistema' && entradaPai.cavaloSistemaId) {
            // Para cavalos do sistema, usar ID e limpar nome
            processedData.paiId = parseInt(entradaPai.cavaloSistemaId);
            processedData.paiNome = null;
            
            storageLogger.debug('Pai do sistema selecionado:', { 
              paiId: processedData.paiId, 
              tipo: 'sistema' 
            });
          } 
          else if (entradaPai.tipo === 'externo' && entradaPai.cavaloNome) {
            // Para cavalos externos, usar nome e limpar ID
            processedData.paiId = null;
            processedData.paiNome = entradaPai.cavaloNome;
            
            storageLogger.debug('Pai externo definido:', { 
              paiNome: processedData.paiNome, 
              tipo: 'externo' 
            });
          }
          else if (entradaPai.tipo === 'nenhum') {
            // Para "nenhum", limpar ambos
            processedData.paiId = null;
            processedData.paiNome = null;
            
            storageLogger.debug('Pai definido como nenhum');
          }
        }
        
        // Remover o objeto pai que foi processado
        delete processedData.pai;
      }
      
      // Processar campo mae (mesmo padrão do pai)
      if ('mae' in processedData) {
        const valorMae = processedData.mae;
        
        // Log debug
        storageLogger.debug(`Processando campo mae:`, { 
          valor_mae: valorMae,
          tipo_mae: typeof valorMae
        });
        
        // Se o valor for null ou undefined, limpar ambos os campos
        if (valorMae === null || valorMae === undefined) {
          processedData.maeId = null;
          processedData.maeNome = null;
        }
        // Se for um objeto EntradaGenealogica
        else if (typeof valorMae === 'object' && valorMae !== null) {
          const entradaMae = valorMae as any;
          
          // Log detalhado para diagnóstico
          storageLogger.debug('Objeto EntradaGenealogica para mãe:', {
            objeto_completo: entradaMae,
            tipo: entradaMae.tipo,
            cavaloSistemaId: entradaMae.cavaloSistemaId,
            cavaloNome: entradaMae.cavaloNome
          });
          
          if (entradaMae.tipo === 'sistema' && entradaMae.cavaloSistemaId) {
            // Para éguas do sistema, usar ID e limpar nome
            processedData.maeId = parseInt(entradaMae.cavaloSistemaId);
            processedData.maeNome = null;
            
            storageLogger.debug('Mãe do sistema selecionada:', { 
              maeId: processedData.maeId, 
              tipo: 'sistema' 
            });
          } 
          else if (entradaMae.tipo === 'externo' && entradaMae.cavaloNome) {
            // Para éguas externas, usar nome e limpar ID
            processedData.maeId = null;
            processedData.maeNome = entradaMae.cavaloNome;
            
            storageLogger.debug('Mãe externa definida:', { 
              maeNome: processedData.maeNome, 
              tipo: 'externo' 
            });
          }
          else if (entradaMae.tipo === 'nenhum') {
            // Para "nenhum", limpar ambos
            processedData.maeId = null;
            processedData.maeNome = null;
            
            storageLogger.debug('Mãe definida como nenhuma');
          }
        }
        
        // Remover o objeto mae que foi processado
        delete processedData.mae;
      }
      
      // Campos legados para compatibilidade com código antigo
      // Limpar dados antigos para evitar conflitos
      if ('pai' in processedData) delete processedData.pai;
      if ('mae' in processedData) delete processedData.mae;
      
      // Processar os campos numéricos (real) para evitar erro com strings vazias
      ['peso', 'altura', 'valorCompra'].forEach(field => {
        // Verificar se o campo está presente no objeto antes de processá-lo
        if (field in processedData) {
          // Se o campo for uma string vazia, converter para null (permitir limpar o campo)
          if (processedData[field] === '') {
            processedData[field] = null;
            storageLogger.debug(`Campo numérico vazio convertido para null: ${field}`, { field });
          } 
          // Se for uma string contendo número, converta para número
          else if (typeof processedData[field] === 'string') {
            const numberValue = parseFloat(processedData[field]);
            if (!isNaN(numberValue)) {
              storageLogger.debug(`Campo numérico convertido: ${field}`, { 
                field, 
                originalValue: processedData[field],
                convertedValue: numberValue 
              });
              processedData[field] = numberValue;
            } else {
              // Se for string mas não puder ser convertida para número, definir como null
              processedData[field] = null;
              storageLogger.debug(`Campo numérico inválido convertido para null: ${field}`, { field, value: processedData[field] });
            }
          }
        }
      });
      
      // Processar campos de genealogia (texto) para permitir limpeza
      ['paiNome', 'maeNome', 'avoPaterno', 'avoMaterno'].forEach(field => {
        if (field in processedData) {
          // Se for string vazia, converter para null (permitir limpar o campo)
          if (processedData[field] === '') {
            processedData[field] = null;
            storageLogger.debug(`Campo de genealogia vazio convertido para null: ${field}`, { field });
          }
        }
      });
      
      // Remover campos undefined para evitar erros na atualização
      // Mas preservar campos obrigatórios mesmo que sejam vazios
      Object.keys(processedData).forEach(key => {
        // Não remover campos obrigatórios mesmo que sejam vazios
        if (processedData[key] === undefined && !requiredFields.includes(key)) {
          storageLogger.debug(`Campo removido por ser undefined: ${key}`);
          delete processedData[key];
        }
      });
      
      // Log detalhado após processamento
      storageLogger.info(`🔄 PROCESSAMENTO CONCLUÍDO - Enviando atualização para o banco:`, { 
        cavaloId: id,
        userId: userId, 
        processedData: JSON.stringify(processedData),
        campos_processados: Object.keys(processedData),
        campos_genealogia: {
          pai: processedData.pai || null,
          mae: processedData.mae || null,
          paiId: processedData.paiId || null,
          maeId: processedData.maeId || null
        }
      });
      
      // Executar a atualização no banco de dados
      const [cavalo] = await db
        .update(cavalos)
        .set(processedData)
        .where(and(eq(cavalos.id, id), eq(cavalos.userId, userId)))
        .returning();
      
      if (cavalo) {
        storageLogger.info(`✅ ATUALIZAÇÃO CONCLUÍDA COM SUCESSO`, { 
          cavaloId: cavalo.id,
          nome: cavalo.name,
          // Inclui informações específicas sobre genealogia para depuração
          genealogia: {
            pai: cavalo.pai,
            mae: cavalo.mae,
            // Mostra quais campos de fato foram salvos no banco
            pai_salvo: typeof cavalo.pai !== 'undefined' && cavalo.pai !== null,
            mae_salvo: typeof cavalo.mae !== 'undefined' && cavalo.mae !== null,
          },
          // Inclui informações sobre todos os campos atualizados
          campos_atualizados: Object.keys(processedData)
        });
      } else {
        storageLogger.warn(`⚠️ FALHA NA ATUALIZAÇÃO - Cavalo não encontrado`, { 
          cavaloId: id, 
          userId 
        });
      }
      
      return cavalo || undefined;
    } catch (error) {
      // Log detalhado do erro usando nosso sistema de logging
      logDbError(
        error instanceof Error ? error : new Error(String(error)), 
        `UPDATE cavalos SET ... WHERE id=${id} AND userId=${userId}`,
        { cavaloId: id, userId, error: String(error) }
      );
      
      // Re-throw para tratamento posterior
      throw error;
    }
  }
  
  async deleteCavalo(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(cavalos)
      .where(and(eq(cavalos.id, id), eq(cavalos.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Manejo (Horse care) methods
  async getManejos(userId: number): Promise<Manejo[]> {
    return db.select()
      .from(manejos)
      .where(eq(manejos.userId, userId))
      .orderBy(asc(manejos.data)); // Ordenação crescente por data (mais antigo primeiro)
  }
  
  async getManejosByHorse(horseId: number, userId: number): Promise<Manejo[]> {
    return db.select()
      .from(manejos)
      .where(and(eq(manejos.horseId, horseId), eq(manejos.userId, userId)))
      .orderBy(asc(manejos.data)); // Ordenação cronológica por data
  }
  
  async getManejo(id: number, userId: number): Promise<Manejo | undefined> {
    const [manejo] = await db.select()
      .from(manejos)
      .where(and(eq(manejos.id, id), eq(manejos.userId, userId)));
    return manejo || undefined;
  }
  
  async createManejo(insertManejo: InsertManejo): Promise<Manejo> {
    const [manejo] = await db
      .insert(manejos)
      .values(insertManejo)
      .returning();
    return manejo;
  }
  
  async updateManejo(id: number, userId: number, manejoData: Partial<InsertManejo>): Promise<Manejo | undefined> {
    const [manejo] = await db
      .update(manejos)
      .set(manejoData)
      .where(and(eq(manejos.id, id), eq(manejos.userId, userId)))
      .returning();
    return manejo || undefined;
  }
  
  async deleteManejo(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(manejos)
      .where(and(eq(manejos.id, id), eq(manejos.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Arquivo (File) methods
  async getArquivos(userId: number): Promise<Arquivo[]> {
    return db.select().from(arquivos).where(eq(arquivos.userId, userId));
  }
  
  async getArquivosByHorse(horseId: number, userId: number): Promise<Arquivo[]> {
    return db.select()
      .from(arquivos)
      .where(and(eq(arquivos.horseId, horseId), eq(arquivos.userId, userId)));
  }
  
  async getArquivo(id: number, userId: number): Promise<Arquivo | undefined> {
    const [arquivo] = await db.select()
      .from(arquivos)
      .where(and(eq(arquivos.id, id), eq(arquivos.userId, userId)));
    return arquivo || undefined;
  }
  
  async createArquivo(insertArquivo: InsertArquivo): Promise<Arquivo> {
    const [arquivo] = await db
      .insert(arquivos)
      .values(insertArquivo)
      .returning();
    return arquivo;
  }
  
  async deleteArquivo(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(arquivos)
      .where(and(eq(arquivos.id, id), eq(arquivos.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Evento (Schedule) methods
  async getEventos(userId: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(eq(eventos.userId, userId))
      .orderBy(asc(eventos.data)); // Ordenação cronológica por data (asc = ascendente)
  }
  
  async getEventosByDate(data: string, userId: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(and(eq(eventos.data, data), eq(eventos.userId, userId)))
      .orderBy(asc(eventos.horaInicio)); // Ordenação pelos horários
  }
  
  async getEventosByHorse(horseId: number, userId: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(and(eq(eventos.horseId, horseId), eq(eventos.userId, userId)))
      .orderBy(asc(eventos.data)); // Ordenação por data ascendente
  }
  
  async getEventosByManejo(manejoId: number, userId: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(and(eq(eventos.manejoId, manejoId), eq(eventos.userId, userId)))
      .orderBy(asc(eventos.data)); // Ordenação por data ascendente
  }
  
  async getEvento(id: number, userId: number): Promise<Evento | undefined> {
    const [evento] = await db.select()
      .from(eventos)
      .where(and(eq(eventos.id, id), eq(eventos.userId, userId)));
    return evento || undefined;
  }
  
  async createEvento(insertEvento: InsertEvento): Promise<Evento> {
    // Verificar se os campos obrigatórios estão presentes
    if (!insertEvento.horaInicio) {
      insertEvento.horaInicio = "09:00"; // Valor padrão para hora de início
    }
    if (!insertEvento.horaFim) {
      insertEvento.horaFim = "10:00"; // Valor padrão para hora de fim
    }
    if (!insertEvento.tipo) {
      insertEvento.tipo = "Outro"; // Valor padrão para tipo
    }
    
    console.log("Criando evento com dados:", JSON.stringify(insertEvento, null, 2));
    
    const [evento] = await db
      .insert(eventos)
      .values(insertEvento)
      .returning();
    return evento;
  }
  
  async updateEvento(id: number, userId: number, eventoData: Partial<InsertEvento>): Promise<Evento | undefined> {
    const [evento] = await db
      .update(eventos)
      .set(eventoData)
      .where(and(eq(eventos.id, id), eq(eventos.userId, userId)))
      .returning();
    return evento || undefined;
  }
  
  async deleteEvento(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(eventos)
      .where(and(eq(eventos.id, id), eq(eventos.userId, userId)))
      .returning();
    return result.length > 0;
  }

  // Procedimentos Veterinários methods
  async getProcedimentosVet(userId: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(eq(procedimentosVet.userId, userId))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentosVetByHorse(horseId: number, userId: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.horseId, horseId), eq(procedimentosVet.userId, userId)))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentosVetByManejo(manejoId: number, userId: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.manejoId, manejoId), eq(procedimentosVet.userId, userId)))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentosVetByEvento(eventoId: number, userId: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.eventoId, eventoId), eq(procedimentosVet.userId, userId)))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentoVet(id: number, userId: number): Promise<ProcedimentoVet | undefined> {
    const [procedimento] = await db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.id, id), eq(procedimentosVet.userId, userId)));
    return procedimento || undefined;
  }
  
  async createProcedimentoVet(insertProcedimento: InsertProcedimentoVet): Promise<ProcedimentoVet> {
    const [procedimento] = await db
      .insert(procedimentosVet)
      .values(insertProcedimento)
      .returning();
    return procedimento;
  }
  
  async updateProcedimentoVet(id: number, userId: number, procedimentoData: Partial<InsertProcedimentoVet>): Promise<ProcedimentoVet | undefined> {
    const [procedimento] = await db
      .update(procedimentosVet)
      .set(procedimentoData)
      .where(and(eq(procedimentosVet.id, id), eq(procedimentosVet.userId, userId)))
      .returning();
    return procedimento || undefined;
  }
  
  async deleteProcedimentoVet(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(procedimentosVet)
      .where(and(eq(procedimentosVet.id, id), eq(procedimentosVet.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Reprodução methods
  async getReproducoes(userId: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(eq(reproducao.userId, userId))
      .orderBy(asc(reproducao.createdAt)); // Ordenação por data de criação
  }
  
  async getReproducoesByHorse(horseId: number, userId: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(and(eq(reproducao.horseId, horseId), eq(reproducao.userId, userId)))
      .orderBy(asc(reproducao.createdAt)); // Ordenação por data de criação
  }
  
  async getReproducoesByProcedimento(procedimentoId: number, userId: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(and(eq(reproducao.procedimentoVetId, procedimentoId), eq(reproducao.userId, userId)))
      .orderBy(asc(reproducao.createdAt)); // Ordenação por data de criação
  }
  
  async getReproducoesByEvento(eventoId: number, userId: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(and(eq(reproducao.eventoId, eventoId), eq(reproducao.userId, userId)))
      .orderBy(asc(reproducao.createdAt)); // Ordenação por data de criação
  }
  
  async getReproducao(id: number, userId: number): Promise<Reproducao | undefined> {
    const [item] = await db.select()
      .from(reproducao)
      .where(and(eq(reproducao.id, id), eq(reproducao.userId, userId)));
    return item || undefined;
  }
  
  async createReproducao(insertReproducao: InsertReproducao): Promise<Reproducao> {
    const [item] = await db
      .insert(reproducao)
      .values(insertReproducao)
      .returning();
    return item;
  }
  
  async updateReproducao(id: number, userId: number, reproducaoData: Partial<InsertReproducao>): Promise<Reproducao | undefined> {
    const [item] = await db
      .update(reproducao)
      .set(reproducaoData)
      .where(and(eq(reproducao.id, id), eq(reproducao.userId, userId)))
      .returning();
    return item || undefined;
  }
  
  async deleteReproducao(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(reproducao)
      .where(and(eq(reproducao.id, id), eq(reproducao.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Medidas Físicas methods
  async getMedidasFisicas(userId: number): Promise<MedidaFisica[]> {
    return db.select()
      .from(medidasFisicas)
      .where(eq(medidasFisicas.userId, userId))
      .orderBy(asc(medidasFisicas.data)); // Ordenação cronológica por data
  }
  
  async getMedidasFisicasByHorse(horseId: number, userId: number): Promise<MedidaFisica[]> {
    return db.select()
      .from(medidasFisicas)
      .where(and(eq(medidasFisicas.horseId, horseId), eq(medidasFisicas.userId, userId)))
      .orderBy(asc(medidasFisicas.data)); // Ordenação cronológica por data
  }
  
  async getMedidaFisica(id: number, userId: number): Promise<MedidaFisica | undefined> {
    const [medida] = await db.select()
      .from(medidasFisicas)
      .where(and(eq(medidasFisicas.id, id), eq(medidasFisicas.userId, userId)));
    return medida || undefined;
  }
  
  async createMedidaFisica(insertMedida: InsertMedidaFisica): Promise<MedidaFisica> {
    const [medida] = await db
      .insert(medidasFisicas)
      .values(insertMedida)
      .returning();
    return medida;
  }
  
  async updateMedidaFisica(id: number, userId: number, medidaData: Partial<InsertMedidaFisica>): Promise<MedidaFisica | undefined> {
    const [medida] = await db
      .update(medidasFisicas)
      .set(medidaData)
      .where(and(eq(medidasFisicas.id, id), eq(medidasFisicas.userId, userId)))
      .returning();
    return medida || undefined;
  }
  
  async deleteMedidaFisica(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(medidasFisicas)
      .where(and(eq(medidasFisicas.id, id), eq(medidasFisicas.userId, userId)))
      .returning();
    return result.length > 0;
  }
  
  // Nutrição methods
  async getNutricoes(userId: number): Promise<Nutricao[]> {
    return db.select()
      .from(nutricao)
      .where(eq(nutricao.userId, userId))
      .orderBy(asc(nutricao.data)); // Ordenação cronológica por data
  }
  
  async getNutricoesByHorse(horseId: number, userId: number): Promise<Nutricao[]> {
    // Validação para evitar problemas de tipos
    if (isNaN(horseId) || isNaN(userId)) {
      console.error(`Valores inválidos para consulta: horseId=${horseId}, userId=${userId}`);
      return [];
    }
    
    return db.select()
      .from(nutricao)
      .where(and(eq(nutricao.horseId, horseId), eq(nutricao.userId, userId)))
      .orderBy(asc(nutricao.data)); // Ordenação cronológica por data
  }
  
  async getNutricao(id: number, userId: number): Promise<Nutricao | undefined> {
    const [item] = await db.select()
      .from(nutricao)
      .where(and(eq(nutricao.id, id), eq(nutricao.userId, userId)));
    return item || undefined;
  }
  
  async createNutricao(insertNutricao: InsertNutricao): Promise<Nutricao> {
    const [item] = await db
      .insert(nutricao)
      .values(insertNutricao)
      .returning();
    return item;
  }
  
  async updateNutricao(id: number, userId: number, nutricaoData: Partial<InsertNutricao>): Promise<Nutricao | undefined> {
    const [item] = await db
      .update(nutricao)
      .set(nutricaoData)
      .where(and(eq(nutricao.id, id), eq(nutricao.userId, userId)))
      .returning();
    return item || undefined;
  }
  
  async deleteNutricao(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(nutricao)
      .where(and(eq(nutricao.id, id), eq(nutricao.userId, userId)))
      .returning();
    return result.length > 0;
  }

  // Métodos Morfologia
  async getMorfologias(userId: number): Promise<Morfologia[]> {
    return db.select()
      .from(morfologia)
      .where(eq(morfologia.userId, userId))
      .orderBy(asc(morfologia.dataMedicao));
  }

  async getMorfologiasByHorse(horseId: number, userId: number): Promise<Morfologia[]> {
    return db.select()
      .from(morfologia)
      .where(and(eq(morfologia.horseId, horseId), eq(morfologia.userId, userId)))
      .orderBy(asc(morfologia.dataMedicao));
  }

  async getMorfologia(id: number, userId: number): Promise<Morfologia | undefined> {
    const [item] = await db.select()
      .from(morfologia)
      .where(and(eq(morfologia.id, id), eq(morfologia.userId, userId)));
    return item || undefined;
  }

  async createMorfologia(morfologiaData: InsertMorfologia): Promise<Morfologia> {
    // Calcular pontuação total caso não esteja definida
    if (!morfologiaData.pontuacaoTotal) {
      const pontuacoes = [
        morfologiaData.pontuacaoCabeca,
        morfologiaData.pontuacaoPescoco,
        morfologiaData.pontuacaoEspalda,
        morfologiaData.pontuacaoDorso,
        morfologiaData.pontuacaoGarupa,
        morfologiaData.pontuacaoMembros,
        morfologiaData.pontuacaoAprumos,
        morfologiaData.pontuacaoAndamento,
        morfologiaData.pontuacaoHarmonia
      ];

      // Filtrar pontuações definidas e calcular média
      const pontuacoesFiltradas = pontuacoes.filter(p => p !== undefined && p !== null) as number[];
      if (pontuacoesFiltradas.length > 0) {
        const media = pontuacoesFiltradas.reduce((a, b) => a + b, 0) / pontuacoesFiltradas.length;
        morfologiaData.pontuacaoTotal = parseFloat(media.toFixed(2));
      }
    }

    const [item] = await db
      .insert(morfologia)
      .values(morfologiaData)
      .returning();
    return item;
  }

  async updateMorfologia(id: number, userId: number, morfologiaData: Partial<InsertMorfologia>): Promise<Morfologia | undefined> {
    // Se houver atualizações de pontuações, recalcular pontuação total
    if (
      morfologiaData.pontuacaoCabeca !== undefined ||
      morfologiaData.pontuacaoPescoco !== undefined ||
      morfologiaData.pontuacaoEspalda !== undefined ||
      morfologiaData.pontuacaoDorso !== undefined ||
      morfologiaData.pontuacaoGarupa !== undefined ||
      morfologiaData.pontuacaoMembros !== undefined ||
      morfologiaData.pontuacaoAprumos !== undefined ||
      morfologiaData.pontuacaoAndamento !== undefined ||
      morfologiaData.pontuacaoHarmonia !== undefined
    ) {
      // Buscar item atual para ter todos os dados
      const itemAtual = await this.getMorfologia(id, userId);
      if (itemAtual) {
        // Combinar dados atuais com atualizações
        const pontuacoes = [
          morfologiaData.pontuacaoCabeca ?? itemAtual.pontuacaoCabeca,
          morfologiaData.pontuacaoPescoco ?? itemAtual.pontuacaoPescoco,
          morfologiaData.pontuacaoEspalda ?? itemAtual.pontuacaoEspalda,
          morfologiaData.pontuacaoDorso ?? itemAtual.pontuacaoDorso,
          morfologiaData.pontuacaoGarupa ?? itemAtual.pontuacaoGarupa,
          morfologiaData.pontuacaoMembros ?? itemAtual.pontuacaoMembros,
          morfologiaData.pontuacaoAprumos ?? itemAtual.pontuacaoAprumos,
          morfologiaData.pontuacaoAndamento ?? itemAtual.pontuacaoAndamento,
          morfologiaData.pontuacaoHarmonia ?? itemAtual.pontuacaoHarmonia
        ];

        // Filtrar pontuações definidas e calcular média
        const pontuacoesFiltradas = pontuacoes.filter(p => p !== undefined && p !== null) as number[];
        if (pontuacoesFiltradas.length > 0) {
          const media = pontuacoesFiltradas.reduce((a, b) => a + b, 0) / pontuacoesFiltradas.length;
          morfologiaData.pontuacaoTotal = parseFloat(media.toFixed(2));
        }
      }
    }

    const [item] = await db
      .update(morfologia)
      .set(morfologiaData)
      .where(and(eq(morfologia.id, id), eq(morfologia.userId, userId)))
      .returning();
    return item || undefined;
  }

  async deleteMorfologia(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(morfologia)
      .where(and(eq(morfologia.id, id), eq(morfologia.userId, userId)))
      .returning();
    return result.length > 0;
  }

  // Métodos Histórico de Desempenho
  async getDesempenhos(userId: number): Promise<DesempenhoHistorico[]> {
    return db.select()
      .from(desempenhoHistorico)
      .where(eq(desempenhoHistorico.userId, userId))
      .orderBy(desc(desempenhoHistorico.data)); // Mais recentes primeiro
  }

  async getDesempenhosByHorse(horseId: number, userId: number): Promise<DesempenhoHistorico[]> {
    return db.select()
      .from(desempenhoHistorico)
      .where(and(eq(desempenhoHistorico.horseId, horseId), eq(desempenhoHistorico.userId, userId)))
      .orderBy(desc(desempenhoHistorico.data)); // Mais recentes primeiro
  }

  async getDesempenho(id: number, userId: number): Promise<DesempenhoHistorico | undefined> {
    const [item] = await db.select()
      .from(desempenhoHistorico)
      .where(and(eq(desempenhoHistorico.id, id), eq(desempenhoHistorico.userId, userId)));
    return item || undefined;
  }

  async createDesempenho(desempenhoData: InsertDesempenhoHistorico): Promise<DesempenhoHistorico> {
    const [item] = await db
      .insert(desempenhoHistorico)
      .values(desempenhoData)
      .returning();
    return item;
  }

  async updateDesempenho(id: number, userId: number, desempenhoData: Partial<InsertDesempenhoHistorico>): Promise<DesempenhoHistorico | undefined> {
    const [item] = await db
      .update(desempenhoHistorico)
      .set(desempenhoData)
      .where(and(eq(desempenhoHistorico.id, id), eq(desempenhoHistorico.userId, userId)))
      .returning();
    return item || undefined;
  }

  async deleteDesempenho(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(desempenhoHistorico)
      .where(and(eq(desempenhoHistorico.id, id), eq(desempenhoHistorico.userId, userId)))
      .returning();
    return result.length > 0;
  }

  // Métodos Genealogia
  async getGenealogias(userId: number): Promise<Genealogia[]> {
    return db.select()
      .from(genealogia)
      .where(eq(genealogia.userId, userId));
  }

  async getGenealogiaByHorse(horseId: number, userId: number): Promise<Genealogia | undefined> {
    const [item] = await db.select()
      .from(genealogia)
      .where(and(eq(genealogia.horseId, horseId), eq(genealogia.userId, userId)));
    return item || undefined;
  }

  async createGenealogia(genealogiaData: InsertGenealogia): Promise<Genealogia> {
    // Calcular coeficiente de consanguinidade se não estiver definido
    if (genealogiaData.coeficienteConsanguinidade === undefined) {
      genealogiaData.coeficienteConsanguinidade = 0; // Valor padrão, será calculado depois
    }

    const [item] = await db
      .insert(genealogia)
      .values(genealogiaData)
      .returning();
    
    // Após criar, recalcular o coeficiente de consanguinidade
    await this.calcularConsanguinidade(genealogiaData.horseId, genealogiaData.userId);
    
    return item;
  }

  async updateGenealogia(id: number, userId: number, genealogiaData: Partial<InsertGenealogia>): Promise<Genealogia | undefined> {
    const [item] = await db
      .update(genealogia)
      .set(genealogiaData)
      .where(and(eq(genealogia.id, id), eq(genealogia.userId, userId)))
      .returning();
    
    // Se houver atualização de ancestrais, recalcular consanguinidade
    if (item && (
      genealogiaData.pai !== undefined || 
      genealogiaData.mae !== undefined ||
      genealogiaData.avoPaternoId !== undefined ||
      genealogiaData.avoMaternoId !== undefined
    )) {
      await this.calcularConsanguinidade(item.horseId, userId);
    }
    
    return item || undefined;
  }

  async deleteGenealogia(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(genealogia)
      .where(and(eq(genealogia.id, id), eq(genealogia.userId, userId)))
      .returning();
    return result.length > 0;
  }

  async calcularConsanguinidade(horseId: number, userId: number): Promise<number> {
    // Obter a genealogia do cavalo
    const gen = await this.getGenealogiaByHorse(horseId, userId);
    if (!gen) return 0;

    // Algoritmo simplificado para cálculo de consanguinidade até 3 gerações
    let coeficiente = 0;
    
    // Verificar ancestrais comuns e calcular coeficiente
    // Este é um algoritmo simplificado que verifica apenas coincidências diretas
    
    // Exemplo: verificar se há algum avô que também seja pai
    if (gen.pai && (gen.pai === gen.avoMaterno || gen.pai === gen.avoPaterno)) {
      coeficiente += 0.125; // Adiciona 12.5% por cada ancestral comum
    }
    
    if (gen.mae && (gen.mae === gen.avoPaterno || gen.mae === gen.avoMaterno)) {
      coeficiente += 0.125;
    }
    
    // Verificar bisavós comuns com avós
    if (gen.avoPaterno && (
      gen.avoPaterno === gen.bisavoPaternoPaterno || 
      gen.avoPaterno === gen.bisavoMaternoPaterno || 
      gen.avoPaterno === gen.bisavoPaternoMaterno || 
      gen.avoPaterno === gen.bisavoMaternoMaterno
    )) {
      coeficiente += 0.0625; // 6.25% para cada bisavô comum
    }
    
    // E assim por diante para outros casos...
    
    // Limitar o coeficiente a 1 (100%)
    coeficiente = Math.min(coeficiente, 1);
    
    // Atualizar o coeficiente na tabela
    await db
      .update(genealogia)
      .set({ coeficienteConsanguinidade: coeficiente })
      .where(and(eq(genealogia.horseId, horseId), eq(genealogia.userId, userId)));
    
    return coeficiente;
  }

  // Métodos Sugestões de Cruzamento
  async getSugestoesCruzamento(userId: number): Promise<SugestoesCruzamento[]> {
    return db.select()
      .from(sugestoesCruzamento)
      .where(eq(sugestoesCruzamento.userId, userId))
      .orderBy(desc(sugestoesCruzamento.dataSugestao)); // Mais recentes primeiro
  }

  async getSugestoesCruzamentoByHorse(horseIdBase: number, userId: number): Promise<SugestoesCruzamento[]> {
    return db.select()
      .from(sugestoesCruzamento)
      .where(and(eq(sugestoesCruzamento.horseIdBase, horseIdBase), eq(sugestoesCruzamento.userId, userId)))
      .orderBy(desc(sugestoesCruzamento.pontuacaoCompatibilidade)); // Melhores pontuações primeiro
  }

  async getSugestoesCruzamentoById(id: number, userId: number): Promise<SugestoesCruzamento | undefined> {
    const [item] = await db.select()
      .from(sugestoesCruzamento)
      .where(and(eq(sugestoesCruzamento.id, id), eq(sugestoesCruzamento.userId, userId)));
    return item || undefined;
  }

  async createSugestaoCruzamento(sugestaoData: InsertSugestoesCruzamento): Promise<SugestoesCruzamento> {
    const [item] = await db
      .insert(sugestoesCruzamento)
      .values(sugestaoData)
      .returning();
    return item;
  }

  async deleteSugestaoCruzamento(id: number, userId: number): Promise<boolean> {
    const result = await db
      .delete(sugestoesCruzamento)
      .where(and(eq(sugestoesCruzamento.id, id), eq(sugestoesCruzamento.userId, userId)))
      .returning();
    return result.length > 0;
  }

  async gerarSugestoesCruzamento(horseIdBase: number, objetivo: string, userId: number): Promise<SugestoesCruzamento[]> {
    // Obter informações do cavalo base
    const cavaloBase = await this.getCavalo(horseIdBase, userId);
    if (!cavaloBase) {
      throw new Error("Cavalo base não encontrado");
    }
    
    // Obter todos os cavalos possíveis para cruzamento (sexo oposto)
    const sexoOposto = cavaloBase.sexo === 'Macho' ? 'Fêmea' : 'Macho';
    const cavalosCompativeis = await db.select()
      .from(cavalos)
      .where(and(
        eq(cavalos.userId, userId),
        eq(cavalos.sexo, sexoOposto),
        // Cavalo não pode estar inativo
        eq(cavalos.status, 'ativo')
      ));
    
    if (cavalosCompativeis.length === 0) {
      return []; // Não há cavalos compatíveis para cruzamento
    }
    
    // Obter informações de morfologia e desempenho do cavalo base
    const morfologiaBase = await this.getMorfologiasByHorse(horseIdBase, userId);
    const desempenhoBase = await this.getDesempenhosByHorse(horseIdBase, userId);
    const genealogiaBase = await this.getGenealogiaByHorse(horseIdBase, userId);
    
    // Lista para armazenar as sugestões
    const sugestoes: SugestoesCruzamento[] = [];
    
    // Avaliar cada cavalo compatível
    for (const candidato of cavalosCompativeis) {
      if (candidato.id === horseIdBase) continue; // Pular o próprio cavalo
      
      // Obter informações de morfologia e desempenho do candidato
      const morfologiaCandidato = await this.getMorfologiasByHorse(candidato.id, userId);
      const desempenhoCandidato = await this.getDesempenhosByHorse(candidato.id, userId);
      const genealogiaCandidato = await this.getGenealogiaByHorse(candidato.id, userId);
      
      // Calcular pontuação de compatibilidade (algoritmo simplificado)
      let pontuacaoCompatibilidade = 70; // Pontuação base
      
      // Análises para diferentes objetivos
      let analiseMorfologia = "Compatibilidade morfológica padrão.";
      let analiseDesempenho = "Compatibilidade de desempenho padrão.";
      let analiseConsanguinidade = "Sem informações de consanguinidade detalhadas.";
      
      // Pontuação morfológica
      let prevPotencialMorfologico = 7; // Valor padrão
      if (morfologiaBase.length > 0 && morfologiaCandidato.length > 0) {
        const ultimaMorfologiaBase = morfologiaBase[0];
        const ultimaMorfologiaCandidato = morfologiaCandidato[0];
        
        // Análise morfológica baseada no objetivo
        if (objetivo === 'morfologia') {
          // Para objetivo morfológico, valorizar candidatos com boa pontuação total
          if (ultimaMorfologiaCandidato.pontuacaoTotal && ultimaMorfologiaCandidato.pontuacaoTotal > 8) {
            pontuacaoCompatibilidade += 15;
            analiseMorfologia = "Excelente morfologia do candidato complementa o cavalo base.";
            prevPotencialMorfologico = 9;
          } else if (ultimaMorfologiaCandidato.pontuacaoTotal && ultimaMorfologiaCandidato.pontuacaoTotal > 7) {
            pontuacaoCompatibilidade += 10;
            analiseMorfologia = "Boa morfologia do candidato complementa o cavalo base.";
            prevPotencialMorfologico = 8;
          }
        }
      }
      
      // Pontuação de desempenho
      let prevPotencialDesempenho = 7; // Valor padrão
      if (desempenhoBase.length > 0 && desempenhoCandidato.length > 0) {
        // Análise de desempenho baseada no objetivo
        if (objetivo === 'desempenho') {
          // Para objetivo de desempenho, valorizar candidatos com bons resultados
          if (desempenhoCandidato.some(d => d.resultado?.toLowerCase().includes('primeiro') || d.resultado?.toLowerCase().includes('1º'))) {
            pontuacaoCompatibilidade += 15;
            analiseDesempenho = "Excelente histórico de desempenho em competições.";
            prevPotencialDesempenho = 9;
          } else if (desempenhoCandidato.some(d => d.resultado?.toLowerCase().includes('segundo') || d.resultado?.toLowerCase().includes('2º'))) {
            pontuacaoCompatibilidade += 10;
            analiseDesempenho = "Bom histórico de desempenho em competições.";
            prevPotencialDesempenho = 8;
          }
        }
      }
      
      // Análise de consanguinidade
      let prevPotencialTemperamento = 7; // Valor padrão
      if (genealogiaBase && genealogiaCandidato) {
        // Verificar ancestrais comuns (simplificado)
        const ancestraisComuns = [];
        if (genealogiaBase.pai && (genealogiaBase.pai === genealogiaCandidato.pai || 
                                    genealogiaBase.pai === genealogiaCandidato.mae)) {
          ancestraisComuns.push(genealogiaBase.pai);
        }
        if (genealogiaBase.mae && (genealogiaBase.mae === genealogiaCandidato.pai || 
                                    genealogiaBase.mae === genealogiaCandidato.mae)) {
          ancestraisComuns.push(genealogiaBase.mae);
        }
        
        if (ancestraisComuns.length > 0) {
          // Penalizar consanguinidade próxima
          pontuacaoCompatibilidade -= 20;
          analiseConsanguinidade = `Atenção: Consanguinidade detectada com ${ancestraisComuns.length} ancestrais comuns.`;
          prevPotencialTemperamento = 5;
        } else {
          analiseConsanguinidade = "Não foram detectados ancestrais comuns próximos.";
          prevPotencialTemperamento = 7;
        }
      }
      
      // Análise de docilidade (objetivo temperamento)
      if (objetivo === 'docilidade') {
        if (candidato.notes?.toLowerCase().includes('dócil') || candidato.notes?.toLowerCase().includes('calmo')) {
          pontuacaoCompatibilidade += 15;
          prevPotencialTemperamento = 9;
        }
      }
      
      // Análise de força (objetivo força)
      if (objetivo === 'força') {
        // Verificar se o candidato tem bom porte físico
        const ultimaMedidaCandidato = await this.getMedidasFisicasByHorse(candidato.id, userId);
        if (ultimaMedidaCandidato.length > 0 && ultimaMedidaCandidato[0].peso && ultimaMedidaCandidato[0].peso > 500) {
          pontuacaoCompatibilidade += 15;
          prevPotencialMorfologico = 9;
        }
      }
      
      // Limitar pontuação entre 0 e 100
      pontuacaoCompatibilidade = Math.max(0, Math.min(100, pontuacaoCompatibilidade));
      
      // Criar recomendação com justificativa
      const justificativa = `Baseado na análise de compatibilidade para o objetivo "${objetivo}", este cruzamento apresenta 
                            pontuação de ${pontuacaoCompatibilidade.toFixed(1)}/100.`;
      
      // Recomendações baseadas na pontuação
      let recomendacoes = "";
      if (pontuacaoCompatibilidade >= 85) {
        recomendacoes = "Cruzamento altamente recomendado. Alta probabilidade de resultados excelentes.";
      } else if (pontuacaoCompatibilidade >= 70) {
        recomendacoes = "Cruzamento recomendado. Boa probabilidade de resultados positivos.";
      } else if (pontuacaoCompatibilidade >= 50) {
        recomendacoes = "Cruzamento aceitável. Resultados medianos esperados.";
      } else {
        recomendacoes = "Cruzamento não recomendado. Considere outras opções.";
      }
      
      // Criar entrada de sugestão
      const hoje = new Date().toISOString().split('T')[0];
      const sugestao: InsertSugestoesCruzamento = {
        horseIdBase,
        horseIdSugerido: candidato.id,
        dataSugestao: hoje,
        objetivo,
        pontuacaoCompatibilidade,
        analiseMorfologia,
        analiseDesempenho,
        analiseConsanguinidade,
        prevPotencialMorfologico,
        prevPotencialDesempenho,
        prevPotencialTemperamento,
        justificativa,
        recomendacoes,
        userId
      };
      
      // Salvar no banco de dados
      const novaSugestao = await this.createSugestaoCruzamento(sugestao);
      sugestoes.push(novaSugestao);
    }
    
    // Retornar sugestões ordenadas por compatibilidade (melhor primeiro)
    return sugestoes.sort((a, b) => b.pontuacaoCompatibilidade - a.pontuacaoCompatibilidade);
  }
}

export const storage = new DatabaseStorage();
