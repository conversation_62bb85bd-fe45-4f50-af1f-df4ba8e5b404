{"timestamp": "2025-05-15T21:27:10.596Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 263196672, "heapTotal": 116695040, "heapUsed": 72455528, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.313024945, "cpuUsage": {"user": 2820744, "system": 414591}, "resourceUsage": {"userCPUTime": 2820812, "systemCPUTime": 414601, "maxRSS": 331064, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102756, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8375, "involuntaryContextSwitches": 6990}}