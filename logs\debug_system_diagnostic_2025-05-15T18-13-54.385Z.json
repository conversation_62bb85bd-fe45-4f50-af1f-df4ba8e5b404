{"timestamp": "2025-05-15T18:13:54.384Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 330399744, "heapTotal": 192077824, "heapUsed": 169829536, "external": 14608185, "arrayBuffers": 1641723}, "uptime": 74.298821017, "cpuUsage": {"user": 16712231, "system": 1036348}, "resourceUsage": {"userCPUTime": 16712236, "systemCPUTime": 1036348, "maxRSS": 898956, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 262858, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 680, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 24357, "involuntaryContextSwitches": 12518}}