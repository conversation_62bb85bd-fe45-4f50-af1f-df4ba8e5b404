{"timestamp": "2025-05-23T00:59:44.056Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402739200, "heapTotal": 117112832, "heapUsed": 92824656, "external": 8499751, "arrayBuffers": 274785}, "uptime": 1.989626699, "cpuUsage": {"user": 2901335, "system": 398129}, "resourceUsage": {"userCPUTime": 2901393, "systemCPUTime": 398137, "maxRSS": 393428, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104857, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 16, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8777, "involuntaryContextSwitches": 4811}}