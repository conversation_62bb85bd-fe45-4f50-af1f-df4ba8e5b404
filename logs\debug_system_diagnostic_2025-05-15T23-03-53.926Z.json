{"timestamp": "2025-05-15T23:03:53.926Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 396779520, "heapTotal": 112500736, "heapUsed": 83323840, "external": 8612190, "arrayBuffers": 243725}, "uptime": 1.778384846, "cpuUsage": {"user": 2811929, "system": 313642}, "resourceUsage": {"userCPUTime": 2811968, "systemCPUTime": 313647, "maxRSS": 387480, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104201, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7955, "involuntaryContextSwitches": 1844}}