{"timestamp": "2025-05-19T17:53:24.463Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 306888704, "heapTotal": 165429248, "heapUsed": 144248680, "external": 13461560, "arrayBuffers": 889050}, "uptime": 297.492054448, "cpuUsage": {"user": 17494048, "system": 1222035}, "resourceUsage": {"userCPUTime": 17494124, "systemCPUTime": 1222040, "maxRSS": 724872, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 276215, "majorPageFault": 0, "swappedOut": 0, "fsRead": 296, "fsWrite": 1112, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 27339, "involuntaryContextSwitches": 13300}}