{"timestamp": "2025-05-19T15:44:50.343Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402055168, "heapTotal": 114860032, "heapUsed": 74191248, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.924383887, "cpuUsage": {"user": 3244576, "system": 429216}, "resourceUsage": {"userCPUTime": 3244645, "systemCPUTime": 429216, "maxRSS": 392632, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103861, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8421, "involuntaryContextSwitches": 9285}}