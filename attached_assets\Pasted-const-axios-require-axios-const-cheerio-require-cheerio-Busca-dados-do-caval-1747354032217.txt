const axios = require('axios');
const cheerio = require('cheerio');

/**
 * Busca dados do cavalo pelo número de registro na ABCCC (studbook).
 * @param {string} registro - Ex: 'B405132'
 * @returns {Promise<Object>} - Objeto com os principais campos extraídos.
 */
async function getCriouloDataByRegistro(registro) {
  // Passo 1: Buscar a página de resultado da pesquisa
  const searchUrl = `https://www.cavalocrioulo.org.br/studbook/pesquisa_animais?registro=${registro}`;
  const { data: searchPage } = await axios.get(searchUrl);
  const $search = cheerio.load(searchPage);

  // Procura link para ficha do animal usando registro
  let detailLink = null;
  $search('a').each((i, el) => {
    const href = $search(el).attr('href');
    if (href && href.includes(`/studbook/ficha/index/`)) {
      detailLink = `https://www.cavalocrioulo.org.br${href}`;
      return false; // break
    }
  });

  if (!detailLink) {
    throw new Error('Ficha do animal não encontrada. Confira o registro.');
  }

  // Passo 2: Buscar a página detalhada do animal
  const { data: detailPage } = await axios.get(detailLink);
  const $ = cheerio.load(detailPage);

  // Extração dos campos principais
  const nome = $('td:contains("Nome")').next().text().trim();
  const registroOut = $('td:contains("Registro")').next().text().trim();
  const sexo = $('td:contains("Sexo")').next().text().trim();
  const nascimento = $('td:contains("Nascimento")').next().text().trim();
  const pelagem = $('td:contains("Pelagem")').next().text().trim();
  const pai = $('td:contains("Pai")').next().text().trim();
  const mae = $('td:contains("Mãe")').next().text().trim();
  const criador = $('td:contains("Criador")').next().text().trim();
  const proprietario = $('td:contains("Proprietário")').next().text().trim();

  // Exemplo de extração de genealogia ampliada (ajuste conforme o HTML real)
  const genealogia = [];
  $('table.genealogia td').each((i, el) => {
    genealogia.push($(el).text().trim());
  });

  // Fotos (se houver)
  const fotoUrl = $('img[src*="animais"]').attr('src');
  const foto = fotoUrl ? `https://www.cavalocrioulo.org.br${fotoUrl}` : null;

  // Outros campos e exposições, títulos, etc., podem ser adicionados conforme o HTML real

  return {
    nome,
    registro: registroOut,
    sexo,
    nascimento,
    pelagem,
    pai,
    mae,
    criador,
    proprietario,
    foto,
    genealogia
    // Adicione mais campos conforme necessidade
  };
}

// Exemplo de uso:
getCriouloDataByRegistro("B405132")
  .then(console.log)
  .catch(console.error);

module.exports = { getCriouloDataByRegistro };
