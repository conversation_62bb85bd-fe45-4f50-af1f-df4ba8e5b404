import { apiRequest } from "@/lib/queryClient";

/**
 * Tipo para as mensagens do chat
 */
export type ChatMessage = {
  role: 'user' | 'assistant' | 'system';
  content: string;
};

// Expressões regulares para perguntas comuns
const RE_QUANTOS_CAVALOS = /^quantos\s+cavalos\s+(eu\s+)?(tenho|possuo|existem|estão cadastrados|há)(\?)?$/i;
const RE_LISTAR_CAVALOS = /^(liste|quais|quais são|mostre|exiba|me mostre)\s+(os|meus|todos os)?\s*cavalos(\s+cadastrados)?(\?)?$/i;
const RE_SOBRE_SISTEMA = /^(o que é|como funciona|me fale sobre|explique)\s+(o|este|esse)?\s*(sistema|equigestor|aplicativo|app|programa)(\?)?$/i;

// Comandos de atalho
const RE_COMANDO_AJUDA = /^\/ajuda(\s.*)?$/i;
const RE_COMANDO_AGENDAR = /^\/agendar(\s.*)?$/i;
const RE_COMANDO_CADASTRAR = /^\/cadastrar(\s.*)?$/i;
const RE_COMANDO_BUSCAR = /^\/buscar(\s.*)?$/i;
const RE_COMANDO_STATS = /^\/stats(\s.*)?$/i;

// Perguntas sobre eventos e manejos
const RE_PROXIMOS_EVENTOS = /^(quais|mostre|liste|exiba|me\s+mostre)\s+(os|são\s+os)?\s*(próximos|futuros)?\s*(eventos|compromissos|agendamentos)(\s+marcados|\s+agendados|\s+do\s+sistema)?(\?)?$/i;
const RE_MANEJOS_PENDENTES = /^(quantos)?\s*(manejos|cuidados|tarefas)(\s+eu\s+tenho)?\s*(pendentes|não\s+realizados|em\s+aberto|a\s+fazer)(\?)?$/i;
const RE_ULTIMOS_PROCEDIMENTOS = /^(quais|mostre|liste|exiba|me\s+mostre)\s+(os|são\s+os|foram\s+os)?\s*(últimos|recentes)?\s*(procedimentos|manejos|cuidados)(\s+realizados)?(\?)?$/i;

// Perguntas sobre estatísticas
const RE_ESTATISTICAS_GERAIS = /^(mostre|exiba|me\s+mostre|qual|quais\s+são)\s+(as|minhas)?\s*(estatísticas|stats|resumo|dashboard|números|dados|informações)(\s+gerais|\s+do\s+sistema)?(\?)?$/i;
const RE_ESTATISTICAS_CAVALO = /^(mostre|exiba|me\s+mostre|qual|quais\s+são)\s+(as)?\s*(estatísticas|stats|resumo|numbers|dados|informações)\s+(do|sobre|para|de|da)\s+(.+?)(\?)?$/i;

/**
 * Extrai e formata dados estatísticos para apresentação ao usuário
 * @returns Texto formatado com as principais estatísticas
 */
async function getStatisticsData(cavaloId?: number): Promise<string> {
  try {
    // Buscar dados
    const cavalos = await apiRequest<any[]>("GET", "/api/cavalos");
    const manejos = await apiRequest<any[]>("GET", "/api/manejos");
    const eventos = await apiRequest<any[]>("GET", "/api/eventos");
    
    // Estatísticas gerais
    if (!cavaloId) {
      const totalCavalos = cavalos.length;
      const totalManejos = manejos.length;
      const manejosPendentes = manejos.filter(m => m.status === 'pendente').length;
      const manejosRealizados = manejos.filter(m => m.status === 'realizado').length;
      const proximosEventos = eventos.filter(e => new Date(e.data) > new Date()).length;
      
      return `📊 **Estatísticas do Sistema**

📋 Resumo Geral:
• Total de Cavalos: ${totalCavalos}
• Total de Manejos: ${totalManejos}
• Manejos Pendentes: ${manejosPendentes}
• Manejos Realizados: ${manejosRealizados}
• Próximos Eventos: ${proximosEventos}

Os manejos pendentes representam ${totalManejos > 0 ? Math.round((manejosPendentes / totalManejos) * 100) : 0}% do total de manejos cadastrados.`;
    } 
    // Estatísticas de um cavalo específico
    else {
      const cavalo = cavalos.find(c => c.id === cavaloId);
      if (!cavalo) return "Cavalo não encontrado.";
      
      const manejosCavalo = manejos.filter(m => m.cavaloId === cavaloId);
      const manejosPendentes = manejosCavalo.filter(m => m.status === 'pendente').length;
      const eventosCavalo = eventos.filter(e => e.cavaloId === cavaloId);
      const proximosEventos = eventosCavalo.filter(e => new Date(e.data) > new Date()).length;
      
      return `📊 **Estatísticas de ${cavalo.name}**

📋 Resumo:
• Total de Manejos: ${manejosCavalo.length}
• Manejos Pendentes: ${manejosPendentes}
• Manejos Realizados: ${manejosCavalo.length - manejosPendentes}
• Próximos Eventos: ${proximosEventos}

${cavalo.name} tem ${manejosPendentes} manejos pendentes que precisam de atenção.`;
    }
  } catch (error) {
    console.error("Erro ao gerar estatísticas:", error);
    return "Não foi possível gerar estatísticas no momento.";
  }
}

/**
 * Obtém uma resposta local rápida para perguntas comuns, sem chamar a OpenAI
 * @param message Mensagem do usuário
 * @param selectedHorseId ID do cavalo selecionado (se houver)
 * @returns Resposta local ou null se precisa usar a OpenAI
 */
async function getLocalFastResponse(message: string, selectedHorseId?: number): Promise<string | null> {
  // Normalizar mensagem
  const normalizedMessage = message.trim().toLowerCase();
  
  try {
    // Perguntas sobre quantidade de cavalos
    if (RE_QUANTOS_CAVALOS.test(normalizedMessage)) {
      // Busca direta na API local para economizar tokens
      const response = await apiRequest<any[]>("GET", "/api/cavalos");
      const totalCavalos = response.length;
      return `Atualmente você possui ${totalCavalos} cavalos cadastrados no sistema.`;
    }
    
    // Listar cavalos
    if (RE_LISTAR_CAVALOS.test(normalizedMessage)) {
      const response = await apiRequest<any[]>("GET", "/api/cavalos");
      
      if (response.length === 0) {
        return "Você ainda não possui nenhum cavalo cadastrado no sistema.";
      }
      
      let nomesRacas = response.map(cavalo => 
        `${cavalo.name}${cavalo.breed ? ` (${cavalo.breed})` : ''}`
      );
      
      return `Seus cavalos cadastrados são: ${nomesRacas.join(", ")}.`;
    }
    
    // Perguntas sobre o sistema
    if (RE_SOBRE_SISTEMA.test(normalizedMessage)) {
      return `O EquiGestor AI é um sistema completo para gerenciamento de cavalos. Ele permite o cadastro e acompanhamento de informações como dados básicos dos animais, manejos (vacinação, vermifugação, etc.), agenda de compromissos, controle reprodutivo, nutrição, e muito mais. O sistema foi desenvolvido para facilitar a vida de proprietários e tratadores de cavalos, oferecendo uma visão organizada de todas as informações relevantes.`;
    }
    
    // Comando de ajuda
    if (RE_COMANDO_AJUDA.test(normalizedMessage)) {
      return `🔍 **Comandos disponíveis no EquiGestor AI**

*Comandos rápidos:*
• /ajuda - Mostra esta lista de comandos
• /agendar - Inicia o processo de agendamento de um manejo
• /cadastrar - Inicia o processo de cadastro de um cavalo
• /buscar [termo] - Busca por informações específicas
• /stats - Mostra estatísticas gerais do sistema

*Exemplos de perguntas que você pode fazer:*
• "Quantos cavalos tenho?"
• "Liste meus cavalos"
• "Quais são os próximos eventos?"
• "Quantos manejos tenho pendentes?"
• "Mostre as estatísticas do Trovão"

Você também pode selecionar um cavalo específico no menu acima para obter informações contextualizadas.`;
    }
    
    // Comando de estatísticas
    if (RE_COMANDO_STATS.test(normalizedMessage) || RE_ESTATISTICAS_GERAIS.test(normalizedMessage)) {
      return await getStatisticsData();
    }
    
    // Estatísticas de cavalo específico
    if (RE_ESTATISTICAS_CAVALO.test(normalizedMessage)) {
      // Extrair nome do cavalo da mensagem
      const match = normalizedMessage.match(RE_ESTATISTICAS_CAVALO);
      if (match && match[5]) {
        const cavaloNome = match[5].trim();
        
        // Buscar cavalo pelo nome
        const cavalos = await apiRequest<any[]>("GET", "/api/cavalos");
        const cavalo = cavalos.find(c => c.name.toLowerCase() === cavaloNome);
        
        if (cavalo) {
          return await getStatisticsData(cavalo.id);
        }
      }
      
      // Se tiver um cavalo selecionado, usar suas estatísticas
      if (selectedHorseId) {
        return await getStatisticsData(selectedHorseId);
      }
    }
    
    // Manejos pendentes
    if (RE_MANEJOS_PENDENTES.test(normalizedMessage)) {
      const manejos = await apiRequest<any[]>("GET", "/api/manejos");
      const manejosPendentes = manejos.filter(m => m.status === 'pendente');
      
      if (manejosPendentes.length === 0) {
        return "Não há manejos pendentes no momento. Tudo está em dia!";
      }
      
      return `Existem ${manejosPendentes.length} manejos pendentes. Estes incluem diversos tipos de cuidados como vacinações, vermifugações e outros tratamentos que ainda precisam ser realizados.`;
    }
    
    // Próximos eventos
    if (RE_PROXIMOS_EVENTOS.test(normalizedMessage)) {
      const eventos = await apiRequest<any[]>("GET", "/api/eventos");
      const hoje = new Date();
      const proximosEventos = eventos.filter(e => new Date(e.data) > hoje);
      
      if (proximosEventos.length === 0) {
        return "Não há eventos futuros agendados no momento.";
      }
      
      // Ordena por data (mais próximos primeiro)
      proximosEventos.sort((a, b) => new Date(a.data).getTime() - new Date(b.data).getTime());
      
      // Pega apenas os 5 mais próximos
      const proximosImediatos = proximosEventos.slice(0, 5);
      
      // Formata datas para exibição dd/mm/yyyy
      const eventosFormatados = proximosImediatos.map(evento => {
        const data = new Date(evento.data);
        const dataFormatada = `${data.getDate().toString().padStart(2, '0')}/${(data.getMonth() + 1).toString().padStart(2, '0')}/${data.getFullYear()}`;
        return `• ${dataFormatada}: ${evento.titulo}`;
      });
      
      return `Próximos eventos agendados:\n\n${eventosFormatados.join('\n')}\n\nTotal: ${proximosEventos.length} eventos futuros.`;
    }
    
    // Se não for nenhuma das perguntas padrão, retorna null para usar a IA
    return null;
  } catch (error) {
    console.error("Erro ao processar resposta local:", error);
    // Em caso de erro, recorrer à IA como fallback
    return null;
  }
}

/**
 * Envia uma mensagem para o assistente virtual e retorna a resposta
 * @param message Mensagem a ser enviada
 * @param history Histórico de mensagens anteriores
 * @returns Resposta do assistente
 */
/**
 * Verifica se a mensagem contém um comando de atalho
 * @param message Mensagem a ser analisada
 * @returns Tipo de comando ou null se não for um comando
 */
function detectShortcutCommand(message: string): string | null {
  const normalizedMessage = message.trim().toLowerCase();
  
  if (RE_COMANDO_AJUDA.test(normalizedMessage)) return 'ajuda';
  if (RE_COMANDO_AGENDAR.test(normalizedMessage)) return 'agendar';
  if (RE_COMANDO_CADASTRAR.test(normalizedMessage)) return 'cadastrar';
  if (RE_COMANDO_BUSCAR.test(normalizedMessage)) return 'buscar';
  if (RE_COMANDO_STATS.test(normalizedMessage)) return 'stats';
  
  return null;
}

/**
 * Envia uma mensagem para o assistente virtual e retorna a resposta
 * @param message Mensagem a ser enviada
 * @param history Histórico de mensagens anteriores
 * @param selectedHorseId ID do cavalo selecionado (se houver)
 * @returns Resposta do assistente
 */
export async function sendMessageToAssistant(
  message: string, 
  history: ChatMessage[] = [],
  selectedHorseId?: number
): Promise<string> {
  try {
    // Garantir que temos uma mensagem válida
    if (!message?.trim()) {
      return "Por favor, digite uma mensagem.";
    }
    
    // Verificar se é um comando de atalho
    const commandType = detectShortcutCommand(message);
    if (commandType) {
      console.log(`[Assistente] Comando de atalho detectado: ${commandType}`);
      
      // Processamento específico para cada tipo de comando
      switch (commandType) {
        case 'ajuda':
          return `🔍 **Comandos disponíveis no EquiGestor AI**

*Comandos rápidos:*
• /ajuda - Mostra esta lista de comandos
• /agendar - Inicia o processo de agendamento de um manejo
• /cadastrar - Inicia o processo de cadastro de um cavalo
• /buscar [termo] - Busca por informações específicas
• /stats - Mostra estatísticas gerais do sistema

*Exemplos de perguntas que você pode fazer:*
• "Quantos cavalos tenho?"
• "Liste meus cavalos"
• "Quais são os próximos eventos?"
• "Quantos manejos tenho pendentes?"
• "Mostre as estatísticas do Trovão"

Você também pode selecionar um cavalo específico no menu acima para obter informações contextualizadas.`;

        case 'agendar':
          // Essa ação será processada pelo backend via OpenAI, pois precisa de contexto
          break;
          
        case 'cadastrar':
          // Essa ação será processada pelo backend via OpenAI, pois precisa de contexto
          break;
          
        case 'stats':
          return await getStatisticsData(selectedHorseId);
          
        case 'buscar':
          // Extrai o termo de busca após o comando
          const searchTerm = message.trim().substring('/buscar'.length).trim();
          if (!searchTerm) {
            return "Por favor, informe um termo para busca após o comando /buscar. Exemplo: /buscar vacinação";
          }
          // Essa busca específica será processada pelo backend via OpenAI
          break;
      }
    }
    
    // Primeiro tenta obter uma resposta local rápida para economizar tokens
    const localResponse = await getLocalFastResponse(message, selectedHorseId);
    if (localResponse) {
      console.log("[Assistente] Resposta local rápida:", localResponse);
      return localResponse;
    }
    
    console.log("[Assistente] Enviando mensagem para o servidor:", message);
    
    // Se não tiver resposta local, solicita resposta ao backend que usará o OpenAI
    const data = await apiRequest<{content: string}>("POST", "/api/chat", {
      message,
      messages: history,
      horseId: selectedHorseId // Envia o ID do cavalo selecionado para contexto
    });
    
    console.log("[Assistente] Resposta recebida:", data);
    
    // Retornar o conteúdo da resposta
    return data.content || "Desculpe, não consegui processar sua solicitação.";
  } catch (error) {
    console.error("Erro ao enviar mensagem ao assistente:", error);
    return "Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente mais tarde.";
  }
}

/**
 * Detecta a intenção do usuário a partir de uma mensagem, usando a IA
 * @param message Mensagem do usuário
 * @returns Objeto com a intenção e o tipo (consulta ou ação)
 */
export async function detectIntent(message: string): Promise<{intent: string, type: 'action' | 'query'}> {
  try {
    // Verificar se a mensagem está vazia
    if (!message?.trim()) {
      throw new Error("Mensagem vazia");
    }
    
    console.log("Intenção detectada: Consulta de informação (query)");
    
    // Solicitar detecção de intenção ao backend
    const response = await apiRequest("POST", "/api/assistente/intent", {
      message
    });
    
    if (!response.ok) {
      throw new Error(`Erro na API: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data || !data.intent) {
      throw new Error("Resposta da API inválida");
    }
    
    return {
      intent: data.intent,
      type: data.type as 'action' | 'query'
    };
  } catch (error) {
    console.error("Erro ao detectar intenção:", error);
    // Fallback seguro para consulta informativa
    return {
      intent: "Consulta de informação",
      type: "query"
    };
  }
}

/**
 * Estrutura para representar entidades extraídas com sua origem
 */
export interface ExtractedEntity {
  value: string | number | Date;
  source: 'regex' | 'openai';
  confidence?: number; // 0-1, apenas para extrações via IA
}

/**
 * Extrai entidades relevantes de uma mensagem do usuário
 * @param message Mensagem do usuário
 * @param useLocalOnly Se true, só usa extração por regex sem chamar APIs externas
 * @returns Objeto com as entidades extraídas
 */
export async function extractEntities(
  message: string, 
  useLocalOnly = false
): Promise<Record<string, ExtractedEntity>> {
  try {
    // Verificar se a mensagem está vazia
    if (!message?.trim()) {
      throw new Error("Mensagem vazia");
    }
    
    // Inicializa um objeto para guardar as entidades extraídas localmente
    const entities: Record<string, ExtractedEntity> = {};
    
    // Normaliza a mensagem para as extracões com regex
    const normalizedMessage = message.trim().toLowerCase();
    
    // Extração de datas (dd/mm/yyyy ou dd-mm-yyyy ou dd/mm ou dd-mm)
    const dateRegex = /(\d{1,2})[\/\-](\d{1,2})(?:[\/\-](\d{4}))?/g;
    let dateMatch;
    while ((dateMatch = dateRegex.exec(normalizedMessage)) !== null) {
      const day = parseInt(dateMatch[1]);
      const month = parseInt(dateMatch[2]);
      const year = dateMatch[3] ? parseInt(dateMatch[3]) : new Date().getFullYear();
      
      if (day >= 1 && day <= 31 && month >= 1 && month <= 12) {
        const date = new Date(year, month - 1, day);
        if (!isNaN(date.getTime())) {
          entities.data = {
            value: date,
            source: 'regex'
          };
        }
      }
    }
    
    // Extração de cavalo por nome
    const horseNameRegex = /(cavalo|égua|potro|potra|animal)\s+([a-zà-úA-ZÀ-Ú]+)/i;
    const horseMatch = normalizedMessage.match(horseNameRegex);
    if (horseMatch && horseMatch[2]) {
      const horseName = horseMatch[2].trim();
      entities.cavaloNome = {
        value: horseName.charAt(0).toUpperCase() + horseName.slice(1).toLowerCase(),
        source: 'regex'
      };
    }
    
    // Extração de tipos de manejo comuns
    const manejoTypes = [
      { regex: /vacina[çr]/i, value: 'Vacinação' },
      { regex: /vermifuga[çr]/i, value: 'Vermifugação' },
      { regex: /ferrage(m|ar)/i, value: 'Ferrageamento' },
      { regex: /casque(ar|amento)/i, value: 'Casqueamento' },
      { regex: /banho/i, value: 'Banho' },
      { regex: /doma/i, value: 'Doma' },
      { regex: /treino/i, value: 'Treinamento' },
      { regex: /nutrição|alimenta[çr]/i, value: 'Nutrição' },
      { regex: /consult[ar]\s+veterin[aá]ri[ao]/i, value: 'Consulta Veterinária' }
    ];
    
    for (const tipo of manejoTypes) {
      if (tipo.regex.test(normalizedMessage)) {
        entities.tipoManejo = {
          value: tipo.value,
          source: 'regex'
        };
        break;
      }
    }
    
    // Se a flag useLocalOnly for false, solicita extração de entidades ao backend via OpenAI
    if (!useLocalOnly) {
      try {
        const response = await apiRequest("POST", "/api/assistente/entities", {
          message
        });
        
        if (response.ok) {
          const openaiEntities = await response.json();
          
          // Mescla as entidades da OpenAI com as extraídas via regex
          for (const [key, value] of Object.entries(openaiEntities)) {
            // Se a entidade já foi extraída via regex, só substitui se a confiança da IA for alta
            if (!entities[key] || (openaiEntities[key].confidence && openaiEntities[key].confidence > 0.7)) {
              entities[key] = {
                value: value as string | number | Date,
                source: 'openai',
                confidence: openaiEntities[key].confidence || 0.5
              };
            }
          }
        }
      } catch (error) {
        console.warn("Erro ao extrair entidades via OpenAI:", error);
        // Continua com as entidades extraídas via regex
      }
    }
    
    return entities;
  } catch (error) {
    console.error("Erro ao extrair entidades:", error);
    return {};
  }
}

/**
 * Executa uma ação baseada na intenção do usuário
 * @param functionName Nome da função a ser executada
 * @param parameters Parâmetros para a função
 * @returns Resultado da execução
 */
export async function executeAction(
  functionName: string,
  parameters: Record<string, any>
): Promise<{success: boolean, message?: string, data?: any}> {
  try {
    // Validação básica
    if (!functionName?.trim()) {
      throw new Error("Nome da função não informado");
    }
    
    // Solicitar execução de ação ao backend
    const response = await apiRequest("POST", "/api/assistente/action", {
      function_call: {
        name: functionName,
        arguments: parameters || {}
      }
    });
    
    if (!response.ok) {
      throw new Error(`Erro na API: ${response.status}`);
    }
    
    const data = await response.json();
    return data || { success: false, message: "Resposta vazia do servidor" };
  } catch (error) {
    console.error("Erro ao executar ação:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Não foi possível executar a ação solicitada."
    };
  }
}