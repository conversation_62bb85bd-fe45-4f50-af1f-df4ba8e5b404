import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { useLockBody } from "@/hooks/use-lock-body";
import { apiRequest } from "@/lib/queryClient";
import { <PERSON><PERSON><PERSON> } from "@shared/schema";

// Lucide ícones - importar conforme necessário
import {
  Menu,
  X,
  Home,
  Stethoscope,
  Calendar,
  ClipboardList,
  LineChart,
  FileText,
  Settings,
  ChevronRight,
  ChevronDown,
  User,
  Ruler,
  Heart,
  Database,
  PencilRuler,
  ArrowRightLeft,
  Beaker,
  PenTool,
  DollarSign,
  Microscope,
  FilePen,
  ShoppingBag,
  CircleDollarSign,
  BarChart,
  BookCopy,
  HelpCircle,
  UserCog,
} from "lucide-react";

// Interface para itens do menu
interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
  submenu?: MenuItem[];
}

// Interface para seções do menu
interface MenuSection {
  title: string;
  items: MenuItem[];
}

// Criando a estrutura do menu conforme referência do EquinoGestor
const menuSections: MenuSection[] = [
  {
    title: "Principal",
    items: [
      {
        id: "dashboard",
        label: "Dashboard",
        icon: <Home className="h-4 w-4" />,
        href: "/",
      },
    ],
  },
  {
    title: "Animais",
    items: [
      {
        id: "cavalos",
        label: "Animais",
        icon: <Database className="h-4 w-4" />,
        href: "/cavalos",
        submenu: [
          {
            id: "cadastro-cavalo",
            label: "Cadastro de Animais",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/cavalo/cadastro",
          },
          {
            id: "manejos-animais",
            label: "Manejos",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/manejos",
          },
          {
            id: "saida-retorno",
            label: "Saída e Retorno de Animais",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/cavalos/saida-retorno",
          },
          {
            id: "dados-biometricos",
            label: "Dados Biométricos",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/cavalos/biometria",
          },
          {
            id: "nutricao",
            label: "Nutrição",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/cavalos/nutricao",
          },
        ],
      },
    ],
  },
  /* Menu Financeiro movido para seção 'Agenda & Relatórios' */
  {
    title: "Reprodução",
    items: [
      {
        id: "reproducao",
        label: "Reproduções",
        icon: <Heart className="h-4 w-4" />,
        href: "/reproducao",
        submenu: [
          {
            id: "controle-reproducao",
            label: "Controle de Reprodução",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/reproducao",
          },
          {
            id: "partos-pendencias",
            label: "Partos com Pendências",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/reproducao/partos",
          },
          {
            id: "cobricoes-pendencias",
            label: "Cobrições com Pendências",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/reproducao/cobricoes",
          },
          {
            id: "nascimentos",
            label: "Nascimentos",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/reproducao/nascimentos",
          },
          {
            id: "coleta-semen",
            label: "Controle de Coleta de Sêmen",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/reproducao/coleta-semen",
          },
        ],
      },
    ],
  },
  {
    title: "Veterinário",
    items: [
      {
        id: "veterinario",
        label: "Veterinário",
        icon: <Stethoscope className="h-4 w-4" />,
        href: "/veterinario",
        submenu: [
          {
            id: "procedimentos-vet",
            label: "Procedimentos Veterinários",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/procedimentos-vet",
          },
          {
            id: "registros-clinicos",
            label: "Registros Clínicos",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/veterinario/registros",
          },
          {
            id: "vacinacoes",
            label: "Vacinações",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/veterinario/vacinacoes",
          },
          {
            id: "vermifugacoes",
            label: "Vermifugações",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/veterinario/vermifugacoes",
          },
          {
            id: "ferrageamentos",
            label: "Ferrageamentos",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/manejos/farrier",
          },
          {
            id: "exames-laboratoriais",
            label: "Exames Laboratoriais",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/veterinario/exames",
          },
          {
            id: "galeria-imagens",
            label: "Galeria de Imagens",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/veterinario/imagens",
          },
        ],
      },
    ],
  },
  {
    title: "Estoque",
    items: [
      {
        id: "estoque",
        label: "Estoque",
        icon: <ShoppingBag className="h-4 w-4" />,
        href: "/estoque",
        submenu: [
          {
            id: "categorias",
            label: "Categoria de Itens",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/estoque/categorias",
          },
          {
            id: "entradas",
            label: "Entradas",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/estoque/entradas",
          },
          {
            id: "saidas",
            label: "Saídas",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/estoque/saidas",
          },
        ],
      },
    ],
  },
  {
    title: "Agenda & Relatórios",
    items: [
      {
        id: "agenda",
        label: "Agenda",
        icon: <Calendar className="h-4 w-4" />,
        href: "/agenda",
      },
      {
        id: "financeiro",
        label: "Financeiro",
        icon: <DollarSign className="h-4 w-4" />,
        href: "/financeiro",
        submenu: [
          {
            id: "lancamentos",
            label: "Lançamentos Financeiros",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/financeiro/lancamentos",
          },
          {
            id: "centros-custos",
            label: "Centros de Custos",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/financeiro/centros-custos",
          },
          {
            id: "contas",
            label: "Contas Financeiras",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/financeiro/contas",
          },
          {
            id: "demonstrativos",
            label: "Demonstrativos Financeiros",
            icon: <ChevronRight className="h-4 w-4" />,
            href: "/financeiro/demonstrativos",
          },
        ],
      },
      {
        id: "configuracoes",
        label: "Configurações",
        icon: <Settings className="h-4 w-4" />,
        href: "/configuracoes",
      },
      {
        id: "administracao",
        label: "Administrativo",
        icon: <UserCog className="h-4 w-4" />,
        href: "/admin",
      },

      {
        id: "ajuda",
        label: "Ajuda",
        icon: <HelpCircle className="h-4 w-4" />,
        href: "/ajuda",
      },
    ],
  },
];
