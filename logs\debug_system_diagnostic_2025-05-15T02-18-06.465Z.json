{"timestamp": "2025-05-15T02:18:06.465Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 381353984, "heapTotal": 104992768, "heapUsed": 62334680, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.347546267, "cpuUsage": {"user": 2175492, "system": 277846}, "resourceUsage": {"userCPUTime": 2175534, "systemCPUTime": 277852, "maxRSS": 372416, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99509, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5881, "involuntaryContextSwitches": 1610}}