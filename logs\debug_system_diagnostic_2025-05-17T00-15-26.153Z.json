{"timestamp": "2025-05-17T00:15:26.152Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402345984, "heapTotal": 118005760, "heapUsed": 73690512, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.745202238, "cpuUsage": {"user": 2928770, "system": 347074}, "resourceUsage": {"userCPUTime": 2928829, "systemCPUTime": 347074, "maxRSS": 392916, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105759, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 144, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8293, "involuntaryContextSwitches": 1986}}