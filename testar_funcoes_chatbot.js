/**
 * Script de teste específico para o assistente virtual (chatbot) do EquiGestor AI
 * Este script testa todas as funcionalidades do assistente virtual
 */

const axios = require('axios');

// Configurações
const API_URL = 'http://localhost:3000/api';
const USER_ID = 1;
const AUTH_HEADER = {'user-id': USER_ID.toString()};

// Função utilitária para imprimir resultados
const printResult = (funcName, success, data = null, error = null) => {
  const status = success ? '✅ SUCESSO' : '❌ FALHA';
  console.log(`[${status}] ${funcName}`);
  if (data) console.log('  Resposta:', typeof data === 'object' ? JSON.stringify(data, null, 2) : data);
  if (error) console.log('  Erro:', error);
  console.log('-------------------------------------------');
};

// Função principal que executa todos os testes do assistente
async function testarAssistenteVirtual() {
  console.log('🔍 INICIANDO TESTES DO ASSISTENTE VIRTUAL DO EQUIGESTOR AI 🔍');
  console.log('====================================================================');
  
  try {
    // 1. TESTES DE INFORMAÇÕES GERAIS
    await testarInformacoesGerais();
    
    // 2. TESTES DE AGENDAMENTO
    await testarAgendamentos();
    
    // 3. TESTES DE CONSULTAS
    await testarConsultas();
    
    // 4. TESTES DE MANEJOS
    await testarManejos();
    
    // 5. TESTES DE REPRODUÇÃO
    await testarReproducao();
    
    // 6. TESTES DE GENÉTICA
    await testarGenetica();
    
    // 7. TESTES DE ENTIDADES E INTENÇÕES
    await testarEntidadesIntencoes();
    
    console.log('✨ TESTES DO ASSISTENTE CONCLUÍDOS COM SUCESSO ✨');
  } catch (error) {
    console.error('❌ ERRO DURANTE OS TESTES:', error.message);
  }
}

// 1. TESTES DE INFORMAÇÕES GERAIS
async function testarInformacoesGerais() {
  console.log('\n📋 TESTANDO CAPACIDADE DE FORNECER INFORMAÇÕES GERAIS');
  console.log('----------------------------------------------------------');
  
  // 1.1 Pergunta sobre o sistema
  await enviarMensagem('O que é o EquiGestor?', 'Pergunta sobre o sistema');
  
  // 1.2 Pergunta sobre funcionalidades
  await enviarMensagem('Quais são as principais funcionalidades do sistema?', 'Pergunta sobre funcionalidades');
  
  // 1.3 Saudação simples
  await enviarMensagem('Olá, como vai?', 'Saudação simples');
  
  // 1.4 Pergunta sobre ajuda
  await enviarMensagem('Como você pode me ajudar?', 'Pergunta sobre ajuda');
}

// 2. TESTES DE AGENDAMENTO
async function testarAgendamentos() {
  console.log('\n📋 TESTANDO FUNCIONALIDADE DE AGENDAMENTO');
  console.log('----------------------------------------------------------');
  
  // 2.1 Agendar evento simples
  await enviarMensagem('Agende um treinamento para o cavalo Trovão amanhã às 9h', 'Agendar evento simples');
  
  // 2.2 Agendar com mais detalhes
  await enviarMensagem('Preciso agendar uma consulta veterinária para o cavalo Ventania na próxima segunda-feira às 14h com o Dr. Carlos', 'Agendar com mais detalhes');
  
  // 2.3 Agendar vacinação
  await enviarMensagem('Agende vacinação para o cavalo Diamante Negro no dia 20/05/2025 às 10h da manhã', 'Agendar vacinação');
  
  // 2.4 Consultar próximos eventos
  await enviarMensagem('Quais são os próximos eventos agendados?', 'Consultar próximos eventos');
  
  // 2.5 Consultar agenda específica
  await enviarMensagem('Mostre a agenda do Trovão para a próxima semana', 'Consultar agenda específica');
}

// 3. TESTES DE CONSULTAS
async function testarConsultas() {
  console.log('\n📋 TESTANDO FUNCIONALIDADE DE CONSULTAS');
  console.log('----------------------------------------------------------');
  
  // 3.1 Consulta de cavalos
  await enviarMensagem('Quantos cavalos temos no sistema?', 'Consulta de cavalos');
  
  // 3.2 Consulta detalhada de cavalo
  await enviarMensagem('Me dê informações sobre o cavalo Trovão', 'Consulta detalhada de cavalo');
  
  // 3.3 Consulta de raças
  await enviarMensagem('Quais cavalos da raça Mangalarga temos no sistema?', 'Consulta de raças');
  
  // 3.4 Consulta de manejos recentes
  await enviarMensagem('Quais foram os últimos manejos realizados?', 'Consulta de manejos recentes');
  
  // 3.5 Consulta de medidas físicas
  await enviarMensagem('Qual é o peso atual do cavalo Trovão?', 'Consulta de medidas físicas');
}

// 4. TESTES DE MANEJOS
async function testarManejos() {
  console.log('\n📋 TESTANDO FUNCIONALIDADE DE MANEJOS');
  console.log('----------------------------------------------------------');
  
  // 4.1 Registrar vacinação
  await enviarMensagem('Registre uma vacinação para o cavalo Trovão hoje', 'Registrar vacinação');
  
  // 4.2 Registrar vermifugação
  await enviarMensagem('Preciso registrar uma vermifugação para o cavalo Ventania realizada ontem', 'Registrar vermifugação');
  
  // 4.3 Registrar casqueamento
  await enviarMensagem('Registre casqueamento para o cavalo Diamante Negro com o José Ferrador', 'Registrar casqueamento');
  
  // 4.4 Consultar próximos manejos
  await enviarMensagem('Quais são os próximos manejos programados?', 'Consultar próximos manejos');
  
  // 4.5 Consultar histórico de manejos
  await enviarMensagem('Mostre o histórico de manejos do cavalo Trovão', 'Consultar histórico de manejos');
}

// 5. TESTES DE REPRODUÇÃO
async function testarReproducao() {
  console.log('\n📋 TESTANDO FUNCIONALIDADE DE REPRODUÇÃO');
  console.log('----------------------------------------------------------');
  
  // 5.1 Registrar cobertura
  await enviarMensagem('Registre uma cobertura da égua Lua Prateada com o garanhão Trovão Negro para o dia 25 deste mês', 'Registrar cobertura');
  
  // 5.2 Consultar éguas disponíveis
  await enviarMensagem('Quais éguas estão disponíveis para reprodução?', 'Consultar éguas disponíveis');
  
  // 5.3 Consultar histórico reprodutivo
  await enviarMensagem('Mostre o histórico reprodutivo da égua Pérola Negra', 'Consultar histórico reprodutivo');
  
  // 5.4 Consultar status gestacional
  await enviarMensagem('Qual o status gestacional da égua Tormenta?', 'Consultar status gestacional');
  
  // 5.5 Consultar previsão de parto
  await enviarMensagem('Quando está previsto o parto da égua Aurora Dourada?', 'Consultar previsão de parto');
}

// 6. TESTES DE GENÉTICA
async function testarGenetica() {
  console.log('\n📋 TESTANDO FUNCIONALIDADE DE GENÉTICA');
  console.log('----------------------------------------------------------');
  
  // 6.1 Consultar morfologia
  await enviarMensagem('Quais são os dados morfológicos do cavalo Trovão?', 'Consultar morfologia');
  
  // 6.2 Consultar desempenho
  await enviarMensagem('Mostre o histórico de desempenho do cavalo Vento Forte', 'Consultar desempenho');
  
  // 6.3 Consultar genealogia
  await enviarMensagem('Qual é a genealogia do cavalo Diamante Negro?', 'Consultar genealogia');
  
  // 6.4 Consultar consanguinidade
  await enviarMensagem('Qual é o coeficiente de consanguinidade do cavalo Thor?', 'Consultar consanguinidade');
  
  // 6.5 Solicitar sugestões de cruzamento
  await enviarMensagem('Quais são as sugestões de cruzamento para a égua Lua Prateada?', 'Solicitar sugestões de cruzamento');
}

// 7. TESTES DE ENTIDADES E INTENÇÕES
async function testarEntidadesIntencoes() {
  console.log('\n📋 TESTANDO DETECÇÃO DE ENTIDADES E INTENÇÕES');
  console.log('----------------------------------------------------------');
  
  // 7.1 Testar detecção de intenção de agendamento
  await detectarIntencao('Quero marcar uma consulta veterinária para o Trovão amanhã', 'Detecção de intenção de agendamento');
  
  // 7.2 Testar detecção de intenção de consulta
  await detectarIntencao('Preciso saber o peso atual do cavalo Ventania', 'Detecção de intenção de consulta');
  
  // 7.3 Testar extração de entidades
  await extrairEntidades('Agende uma vacinação para o cavalo Trovão na próxima sexta-feira às 10h com o Dr. Carlos', 'Extração de entidades');
  
  // 7.4 Testar extração de entidades com datas
  await extrairEntidades('Registre uma cobertura da égua Lua Prateada com o garanhão Trovão Negro no dia 25/05/2025', 'Extração de entidades com datas');
}

// Função para enviar mensagem ao assistente
async function enviarMensagem(mensagem, descricaoTeste) {
  const data = {
    message: mensagem,
    user_id: USER_ID
  };
  
  try {
    const response = await axios.post(`${API_URL}/assistente/mensagem`, data, { headers: AUTH_HEADER });
    printResult(descricaoTeste, true, response.data);
    return response.data;
  } catch (error) {
    printResult(descricaoTeste, false, null, error.message);
    return null;
  }
}

// Função para testar detecção de intenção
async function detectarIntencao(mensagem, descricaoTeste) {
  const data = {
    message: mensagem,
    user_id: USER_ID
  };
  
  try {
    const response = await axios.post(`${API_URL}/assistente/detectar-intencao`, data, { headers: AUTH_HEADER });
    printResult(descricaoTeste, true, response.data);
    return response.data;
  } catch (error) {
    printResult(descricaoTeste, false, null, error.message);
    return null;
  }
}

// Função para testar extração de entidades
async function extrairEntidades(mensagem, descricaoTeste) {
  const data = {
    message: mensagem,
    user_id: USER_ID
  };
  
  try {
    const response = await axios.post(`${API_URL}/assistente/extrair-entidades`, data, { headers: AUTH_HEADER });
    printResult(descricaoTeste, true, response.data);
    return response.data;
  } catch (error) {
    printResult(descricaoTeste, false, null, error.message);
    return null;
  }
}

// Executar todos os testes
testarAssistenteVirtual();