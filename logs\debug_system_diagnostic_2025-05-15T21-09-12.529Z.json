{"timestamp": "2025-05-15T21:09:12.529Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399097856, "heapTotal": 113811456, "heapUsed": 72440936, "external": 8211290, "arrayBuffers": 235533}, "uptime": 4.128694293, "cpuUsage": {"user": 3261189, "system": 501699}, "resourceUsage": {"userCPUTime": 3261252, "systemCPUTime": 501709, "maxRSS": 389744, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106814, "majorPageFault": 6, "swappedOut": 0, "fsRead": 53752, "fsWrite": 576, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9343, "involuntaryContextSwitches": 11584}}