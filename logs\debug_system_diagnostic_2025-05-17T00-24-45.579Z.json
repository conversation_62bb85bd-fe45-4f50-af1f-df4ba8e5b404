{"timestamp": "2025-05-17T00:24:45.578Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 398573568, "heapTotal": 114073600, "heapUsed": 85787880, "external": 8323215, "arrayBuffers": 243725}, "uptime": 2.291515372, "cpuUsage": {"user": 3046553, "system": 394413}, "resourceUsage": {"userCPUTime": 3046595, "systemCPUTime": 394418, "maxRSS": 389232, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103305, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8328, "involuntaryContextSwitches": 7574}}