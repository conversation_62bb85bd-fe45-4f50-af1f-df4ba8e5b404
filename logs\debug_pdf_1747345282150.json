{"dados": "preview_1747345282150", "logs": "{\n  \"dados\": {\n    \"cavalo\": {\n      \"nome\": \"RZ CAMPONESA DA CARAPUÇA\",\n      \"registro\": \"B644631\",\n      \"rp\": \"2195\",\n      \"sexo\": \"FEMEA\",\n      \"nascimento\": \"15/10/2021\",\n      \"pelagem\": \"BAIA\",\n      \"criador\": \"RUBENS ELIAS ZOGBI\",\n      \"proprietario\": \"CAMILA WEBER\",\n      \"inspetor\": \"RODRIGO RODRIGUES TEIXEIRA\"\n    },\n    \"pai\": {\n      \"nome\": \"BASCA CONFIANÇA\",\n      \"registro\": \"B481211\"\n    },\n    \"mae\": {\n      \"nome\": \"BT DELANTERO\",\n      \"registro\": \"REG.MÉRITO\"\n    },\n    \"avoPai\": {\n      \"nome\": \"BT MANDALA\",\n      \"registro\": \"REG.MÉRITO\"\n    },\n    \"avaMae\": {\n      \"nome\": \"BT DELANTERO\",\n      \"registro\": \"REG.MÉRITO\"\n    },\n    \"avoPai2\": {\n      \"nome\": \"ABA DO IGIQUIQUÁ\",\n      \"registro\": \"REG.MÉRITO\"\n    },\n    \"avaMae2\": {\n      \"nome\": \"TREN TREN ARREBOL\",\n      \"registro\": \"REG.MÉRITO\"\n    }\n  },\n  \"log\": \"[2025-05-15T21:41:18.103Z] [INFO] Iniciando processamento com OpenAI\\n[2025-05-15T21:41:22.150Z] [DEBUG] Conteúdo bruto da resposta: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"RZ CAMPONESA DA CARAPUÇA\\\",\\n    \\\"registro\\\": \\\"B644631\\\",\\n    \\\"rp\\\": \\\"2195\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"15/10/2021\\\",\\n    \\\"pelagem\\\": \\\"BAIA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"RUBENS ELIAS ZOGBI\\\",\\n    \\\"inspetor_tecnico\\\": \\\"RODRIGO RODRIGUES TEIXEIRA\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"BASCA CONFIANÇA\\\",\\n    \\\"registro\\\": \\\"B481211\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"BT DELANTERO\\\",\\n    \\\"registro\\\": \\\"REG.MÉRITO\\\"\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"BT MANDALA\\\",\\n    \\\"registro\\\": \\\"REG.MÉRITO\\\"\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"BT DELANTERO\\\",\\n    \\\"registro\\\": \\\"REG.MÉRITO\\\"\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"ABA DO IGIQUIQUÁ\\\",\\n    \\\"registro\\\": \\\"REG.MÉRITO\\\"\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"TREN TREN ARREBOL\\\",\\n    \\\"registro\\\": \\\"REG.MÉRITO\\\"\\n  }\\n}\\n[2025-05-15T21:41:22.150Z] [INFO] Recebido resposta da OpenAI e parseado com sucesso\\n[2025-05-15T21:41:22.150Z] [DEBUG] Dados extraídos: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"RZ CAMPONESA DA CARAPUÇA\\\",\\n    \\\"registro\\\": \\\"B644631\\\",\\n    \\\"rp\\\": \\\"2195\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"15/10/2021\\\",\\n    \\\"pelagem\\\": \\\"BAIA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"RUBENS ELIAS ZOGBI\\\",\\n    \\\"inspetor_tecnico\\\": \\\"RODRIGO RODRIGUES TEIXEIRA\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"BASCA CONFIANÇA\\\",\\n    \\\"registro\\\": \\\"B481211\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"BT DELANTERO\\\",\\n    \\\"registro\\\": \\\"REG.MÉRITO\\\"\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"BT MANDALA\\\",\\n    \\\"registro\\\": \\\"REG.MÉRITO\\\"\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"BT DELANTERO\\\",\\n    \\\"registro\\\": \\\"REG.MÉRITO\\\"\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"ABA DO IGIQUIQUÁ\\\",\\n    \\\"registro\\\": \\\"REG.MÉRITO\\\"\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"TREN TREN ARREBOL\\\",\\n    \\\"registro\\\": \\\"REG.MÉRITO\\\"\\n  }\\n}\\n[2025-05-15T21:41:22.150Z] [INFO] Dados do cavalo principal extraídos: RZ CAMPONESA DA CARAPUÇA (B644631)\\n[2025-05-15T21:41:22.150Z] [INFO] Pai: BASCA CONFIANÇA (B481211)\\n[2025-05-15T21:41:22.150Z] [INFO] Mãe: BT DELANTERO (REG.MÉRITO)\\n[2025-05-15T21:41:22.150Z] [INFO] Dados convertidos com sucesso\"\n}", "timestamp": "2025-05-15T21:41:22.150Z"}