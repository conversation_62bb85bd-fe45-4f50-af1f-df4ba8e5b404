{"timestamp": "2025-05-15T02:17:26.958Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 246460416, "heapTotal": 101584896, "heapUsed": 62232160, "external": 6815481, "arrayBuffers": 60485}, "uptime": 2.210816299, "cpuUsage": {"user": 2234430, "system": 355885}, "resourceUsage": {"userCPUTime": 2234501, "systemCPUTime": 355885, "maxRSS": 290048, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99819, "majorPageFault": 0, "swappedOut": 0, "fsRead": 22432, "fsWrite": 112, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6849, "involuntaryContextSwitches": 3660}}