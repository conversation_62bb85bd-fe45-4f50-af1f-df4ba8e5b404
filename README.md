# RS Horse - Sistema de Gestão Equina

O RS Horse é um sistema completo de gestão de cavalos e haras, integrando funcionalidades de manejo, genética, nutrição, saúde e reprodução equina com assistência inteligente baseada em IA.

## Funcionalidades Principais

### Gerenciamento de Cavalos
- Cadastro completo com histórico, genealogia e características
- Acompanhamento de medidas físicas
- Documentação e arquivos associados
- Gestão de status (ativo, vendido, óbito, etc.)

### Manejo Equino
- Agendamento e controle de manejos (vacinações, vermifugações, casqueamentos)
- Histórico completo de procedimentos
- Alertas de manejos programados
- Controle de responsáveis

### Agenda e Eventos
- Calendário completo de atividades
- Agendamento de treinamentos, competições e procedimentos
- Alertas e notificações
- Visualização por cavalo, tipo ou período

### Genética e Reprodução
- Análise morfológica detalhada
- Controle genealógico até 3 gerações
- Cálculo de consanguinidade
- Sugestões inteligentes de cruzamentos
- Acompanhamento completo do ciclo reprodutivo

### Nutrição e Saúde
- Controle alimentar individualizado
- Histórico de procedimentos veterinários
- Acompanhamento de desenvolvimento físico
- Controle de custos com alimentação e medicamentos

### Assistente Virtual Inteligente
- Interface conversacional em linguagem natural
- Agendamento de eventos e manejos por comandos de voz/texto
- Respostas contextuais baseadas no histórico do plantel
- Extração inteligente de entidades e intenções

## Tecnologias Utilizadas

- **Frontend**: React com TypeScript, Tailwind CSS, Shadcn UI
- **Backend**: Node.js, Express
- **Banco de Dados**: PostgreSQL com Drizzle ORM
- **Autenticação**: Firebase Authentication
- **IA**: OpenAI GPT-4o para o assistente virtual
- **Armazenamento**: Upload de arquivos para o servidor

## Scripts Inclusos

Este repositório inclui vários scripts úteis para trabalhar com o EquiGestor AI:

1. **teste_completo_equigestor.js**: Script para testar todas as funcionalidades do sistema
2. **popular_banco_equigestor.sql**: Script SQL para popular o banco de dados com dados completos
3. **otimizar_banco_equigestor.sql**: Script SQL para otimizar o banco de dados com índices
4. **testar_funcoes_chatbot.js**: Script para testar o assistente virtual

## Começando

### Requisitos

- Node.js 18.x ou superior
- PostgreSQL 14.x ou superior
- Conta Firebase para autenticação
- Chave de API OpenAI

### Instalação

1. Clone o repositório:
   ```
   git clone https://github.com/seu-usuario/equigestor-ai.git
   cd equigestor-ai
   ```

2. Instale as dependências:
   ```
   npm install
   ```

3. Configure as variáveis de ambiente:
   ```
   cp .env.example .env
   ```
   Preencha as variáveis em `.env` com suas credenciais.

4. Crie o banco de dados:
   ```
   npm run db:push
   ```

5. Popule o banco de dados (opcional):
   ```
   psql -U seu_usuario -d equigestor -f popular_banco_equigestor.sql
   ```

6. Inicie o servidor de desenvolvimento:
   ```
   npm run dev
   ```

## Recursos Adicionais

Para testar o sistema com dados completos, você pode usar os scripts inclusos:

1. Para testar todas as funcionalidades:
   ```
   node teste_completo_equigestor.js
   ```

2. Para testar especificamente o assistente virtual:
   ```
   node testar_funcoes_chatbot.js
   ```

3. Para otimizar o banco de dados:
   ```
   psql -U seu_usuario -d equigestor -f otimizar_banco_equigestor.sql
   ```

## Estrutura do Projeto

- `/client` - Código frontend React
- `/server` - Código backend Express
- `/shared` - Código compartilhado entre frontend e backend
- `/scripts` - Scripts utilitários

## Contribuindo

Contribuições são bem-vindas! Por favor, leia as diretrizes de contribuição antes de enviar pull requests.

## Licença

Este projeto está licenciado sob a MIT License.# EquiHorsenovoBCCC
