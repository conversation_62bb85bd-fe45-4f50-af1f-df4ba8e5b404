{"timestamp": "2025-05-14T14:41:36.061Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 384208896, "heapTotal": 99749888, "heapUsed": 62188464, "external": 6815481, "arrayBuffers": 60485}, "uptime": 2.144469314, "cpuUsage": {"user": 2218265, "system": 304604}, "resourceUsage": {"userCPUTime": 2218311, "systemCPUTime": 304604, "maxRSS": 375204, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99694, "majorPageFault": 1, "swappedOut": 0, "fsRead": 30288, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6700, "involuntaryContextSwitches": 1771}}