{"timestamp": "2025-05-15T21:52:24.963Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402784256, "heapTotal": 117219328, "heapUsed": 72448000, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.026669587, "cpuUsage": {"user": 2997948, "system": 336255}, "resourceUsage": {"userCPUTime": 2998002, "systemCPUTime": 336255, "maxRSS": 393344, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103369, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8713, "involuntaryContextSwitches": 3597}}