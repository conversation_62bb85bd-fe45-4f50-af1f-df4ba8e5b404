{"timestamp": "2025-05-15T20:52:13.758Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 406593536, "heapTotal": 116105216, "heapUsed": 78889800, "external": 8451428, "arrayBuffers": 477994}, "uptime": 5.007873999, "cpuUsage": {"user": 3088005, "system": 455722}, "resourceUsage": {"userCPUTime": 3088015, "systemCPUTime": 455723, "maxRSS": 397064, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104968, "majorPageFault": 2, "swappedOut": 0, "fsRead": 60224, "fsWrite": 168, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8560, "involuntaryContextSwitches": 4002}}