{"timestamp": "2025-05-17T00:27:28.901Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403943424, "heapTotal": 116957184, "heapUsed": 73571136, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.262594322, "cpuUsage": {"user": 3004413, "system": 361985}, "resourceUsage": {"userCPUTime": 3004460, "systemCPUTime": 361991, "maxRSS": 394476, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103677, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8955, "involuntaryContextSwitches": 6640}}