{"timestamp": "2025-05-15T00:30:12.833Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 384790528, "heapTotal": 102109184, "heapUsed": 62209256, "external": 6815481, "arrayBuffers": 60485}, "uptime": 2.15471771, "cpuUsage": {"user": 2373984, "system": 340535}, "resourceUsage": {"userCPUTime": 2374022, "systemCPUTime": 340540, "maxRSS": 375772, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100662, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 96, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6149, "involuntaryContextSwitches": 5833}}