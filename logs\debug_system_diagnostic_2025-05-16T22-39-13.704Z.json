{"timestamp": "2025-05-16T22:39:13.704Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 389120000, "heapTotal": 106364928, "heapUsed": 83530816, "external": 8416544, "arrayBuffers": 265658}, "uptime": 3.747496355, "cpuUsage": {"user": 2875215, "system": 453970}, "resourceUsage": {"userCPUTime": 2875287, "systemCPUTime": 453970, "maxRSS": 380000, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108234, "majorPageFault": 1, "swappedOut": 0, "fsRead": 44584, "fsWrite": 40, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7142, "involuntaryContextSwitches": 7197}}