{"timestamp": "2025-05-22T23:53:01.783Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397602816, "heapTotal": 118267904, "heapUsed": 74137872, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.361711398, "cpuUsage": {"user": 3253814, "system": 406798}, "resourceUsage": {"userCPUTime": 3253875, "systemCPUTime": 406805, "maxRSS": 388284, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104021, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7928, "involuntaryContextSwitches": 5213}}