{"timestamp": "2025-05-16T23:01:17.014Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 295792640, "heapTotal": 156803072, "heapUsed": 148861608, "external": 17530858, "arrayBuffers": 4557615}, "uptime": 92.436305667, "cpuUsage": {"user": 16026535, "system": 1126263}, "resourceUsage": {"userCPUTime": 16026593, "systemCPUTime": 1126267, "maxRSS": 606556, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 247010, "majorPageFault": 3, "swappedOut": 0, "fsRead": 8, "fsWrite": 1008, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 22616, "involuntaryContextSwitches": 17967}}