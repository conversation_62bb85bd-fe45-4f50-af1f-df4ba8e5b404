{"timestamp": "2025-05-23T20:21:46.254Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 328335360, "heapTotal": 131047424, "heapUsed": 111372224, "external": 8307307, "arrayBuffers": 282042}, "uptime": 1.954369947, "cpuUsage": {"user": 2742228, "system": 383355}, "resourceUsage": {"userCPUTime": 2742288, "systemCPUTime": 383363, "maxRSS": 320640, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108871, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8811, "involuntaryContextSwitches": 3410}}