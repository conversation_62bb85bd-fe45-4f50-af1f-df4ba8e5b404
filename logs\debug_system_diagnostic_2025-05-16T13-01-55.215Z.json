{"timestamp": "2025-05-16T13:01:55.215Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405590016, "heapTotal": 115908608, "heapUsed": 72539552, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.832523639, "cpuUsage": {"user": 2874929, "system": 312740}, "resourceUsage": {"userCPUTime": 2874987, "systemCPUTime": 312746, "maxRSS": 396084, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103835, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7773, "involuntaryContextSwitches": 2687}}