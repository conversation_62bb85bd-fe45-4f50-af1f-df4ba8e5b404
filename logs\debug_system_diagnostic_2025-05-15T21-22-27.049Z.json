{"timestamp": "2025-05-15T21:22:27.049Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 413892608, "heapTotal": 115122176, "heapUsed": 72430592, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.510830279, "cpuUsage": {"user": 3069302, "system": 398871}, "resourceUsage": {"userCPUTime": 3069369, "systemCPUTime": 398871, "maxRSS": 404192, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108175, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8620, "involuntaryContextSwitches": 8789}}