{"timestamp": "2025-05-16T17:18:31.331Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 415178752, "heapTotal": 123969536, "heapUsed": 96162912, "external": 8349713, "arrayBuffers": 243725}, "uptime": 2.969745869, "cpuUsage": {"user": 2995272, "system": 393560}, "resourceUsage": {"userCPUTime": 2995324, "systemCPUTime": 393567, "maxRSS": 405448, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103704, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7768, "involuntaryContextSwitches": 10543}}