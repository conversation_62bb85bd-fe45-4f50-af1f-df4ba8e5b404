{"timestamp": "2025-05-15T18:52:03.097Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399597568, "heapTotal": 111714304, "heapUsed": 72459192, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.728587734, "cpuUsage": {"user": 2613699, "system": 352646}, "resourceUsage": {"userCPUTime": 2613776, "systemCPUTime": 352646, "maxRSS": 390232, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103465, "majorPageFault": 0, "swappedOut": 0, "fsRead": 36320, "fsWrite": 64, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7735, "involuntaryContextSwitches": 2306}}