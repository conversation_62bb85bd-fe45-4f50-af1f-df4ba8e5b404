{"timestamp": "2025-05-16T13:04:47.259Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 281047040, "heapTotal": 113565696, "heapUsed": 88545904, "external": 8282731, "arrayBuffers": 257466}, "uptime": 2.389671417, "cpuUsage": {"user": 2852071, "system": 394758}, "resourceUsage": {"userCPUTime": 2852155, "systemCPUTime": 394758, "maxRSS": 284888, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107453, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8644, "involuntaryContextSwitches": 8022}}