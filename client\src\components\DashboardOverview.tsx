import React from 'react';
import { <PERSON> } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Cavalo, Manejo, ProcedimentoVet, Reproducao, Evento } from '@shared/schema';
import { format, isAfter, isBefore, parseISO, differenceInDays } from 'date-fns';
import { pt } from 'date-fns/locale';

import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  CircleAlert, 
  Clock, 
  PawPrint, 
  Heart, 
  Stethoscope, 
  CalendarDays,
  ArrowRight,
  ChevronRight
} from 'lucide-react';

interface DashboardOverviewProps {
  userId: number;
}

/**
 * Componente de visão geral do dashboard
 * 
 * Exibe resumo de cavalos, manejos, eventos próximos e estatísticas
 */
export function DashboardOverview({ userId }: DashboardOverviewProps) {
  // Consultar cavalos
  const { 
    data: cavalos = [], 
    isLoading: isLoadingCavalos 
  } = useQuery({
    queryKey: ['/api/cavalos'],
    enabled: !!userId,
    queryFn: async () => {
      try {
        return await apiRequest<Cavalo[]>('GET', '/api/cavalos');
      } catch (error) {
        console.error('Erro ao buscar cavalos:', error);
        return [];
      }
    }
  });

  // Consultar manejos
  const { 
    data: manejos = [], 
    isLoading: isLoadingManejos 
  } = useQuery({
    queryKey: ['/api/manejos'],
    enabled: !!userId,
    queryFn: async () => {
      try {
        return await apiRequest<Manejo[]>('GET', '/api/manejos');
      } catch (error) {
        console.error('Erro ao buscar manejos:', error);
        return [];
      }
    }
  });

  // Consultar eventos
  const { 
    data: eventos = [], 
    isLoading: isLoadingEventos 
  } = useQuery({
    queryKey: ['/api/eventos'],
    enabled: !!userId,
    queryFn: async () => {
      try {
        return await apiRequest<Evento[]>('GET', '/api/eventos');
      } catch (error) {
        console.error('Erro ao buscar eventos:', error);
        return [];
      }
    }
  });

  // Calcular métricas
  const calcularMetricas = () => {
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);
    
    // Manejos vencidos
    const manejosVencidos = manejos.filter(manejo => {
      if (manejo.status !== 'pendente') return false;
      
      const dataVencimento = manejo.dataVencimento 
        ? parseISO(manejo.dataVencimento)
        : null;
        
      if (!dataVencimento) return false;
      
      return isBefore(dataVencimento, hoje);
    });
    
    // Manejos próximos do vencimento (próximos 7 dias)
    const manejosProximos = manejos.filter(manejo => {
      if (manejo.status !== 'pendente') return false;
      
      const dataVencimento = manejo.dataVencimento 
        ? parseISO(manejo.dataVencimento)
        : null;
        
      if (!dataVencimento) return false;
      
      if (isBefore(dataVencimento, hoje)) return false; // já está nos vencidos
      
      const diasAteVencimento = differenceInDays(dataVencimento, hoje);
      return diasAteVencimento <= 7; // até 7 dias para vencer
    });
    
    // Eventos próximos (próximos 7 dias)
    const eventosProximos = eventos.filter(evento => {
      const dataEvento = evento.data 
        ? parseISO(evento.data)
        : null;
        
      if (!dataEvento) return false;
      
      if (isBefore(dataEvento, hoje)) return false; // eventos passados
      
      const diasAteEvento = differenceInDays(dataEvento, hoje);
      return diasAteEvento <= 7; // próximos 7 dias
    });
    
    // Total de cavalos
    const totalCavalos = cavalos.length;
    
    // Total de manejos pendentes
    const totalManejosPendentes = manejos.filter(m => m.status === 'pendente').length;
    
    return {
      totalCavalos,
      totalManejosPendentes,
      manejosVencidos: manejosVencidos.length,
      manejosProximos: manejosProximos.length,
      eventosProximos: eventosProximos.length,
      eventoProximo: eventosProximos.length > 0 ? eventosProximos[0] : null,
    };
  };

  const metricas = calcularMetricas();

  // Obter nome do cavalo pelo ID
  const getNomeCavaloPeloId = (horseId: number | null) => {
    if (!horseId) return 'Sem cavalo';
    const cavalo = cavalos.find(c => c.id === horseId);
    return cavalo ? cavalo.name : `Cavalo #${horseId}`;
  };

  // Obter dados formatados para exibição
  const formatarData = (data: string) => {
    try {
      return format(parseISO(data), 'dd/MM/yyyy', { locale: pt });
    } catch (e) {
      return 'Data inválida';
    }
  };
  
  const loading = isLoadingCavalos || isLoadingManejos || isLoadingEventos;

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-5 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-20 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Estatísticas rápidas - Formato mais organizado */}
      <div className="grid gap-4 grid-cols-2 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total de Cavalos
            </CardTitle>
            <PawPrint className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{metricas.totalCavalos}</div>
            <p className="text-xs text-muted-foreground">
              {metricas.totalCavalos === 1 ? 'Cavalo cadastrado' : 'Cavalos cadastrados'}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Manejos Pendentes
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{metricas.totalManejosPendentes}</div>
            <p className="text-xs text-muted-foreground">
              {metricas.manejosVencidos > 0 ? (
                <span className="text-red-500">
                  {metricas.manejosVencidos} vencidos
                </span>
              ) : 'Nenhum manejo vencido'}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Próximos Manejos
            </CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{metricas.manejosProximos}</div>
            <p className="text-xs text-muted-foreground">
              Nos próximos 7 dias
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Próximos Eventos
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{metricas.eventosProximos}</div>
            <p className="text-xs text-muted-foreground">
              Nos próximos 7 dias
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Alertas e próximos eventos - layout melhorado para mobile */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
        {/* Manejos vencidos */}
        <Card className={metricas.manejosVencidos > 0 ? "border-red-200 bg-red-50" : ""}>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Manejos Vencidos</CardTitle>
              {metricas.manejosVencidos > 0 && (
                <Badge variant="destructive">
                  {metricas.manejosVencidos}
                </Badge>
              )}
            </div>
            <CardDescription>
              {metricas.manejosVencidos > 0 
                ? 'Manejos que precisam de atenção imediata'
                : 'Não há manejos vencidos'}
            </CardDescription>
          </CardHeader>
          <CardContent className="pb-2">
            {metricas.manejosVencidos > 0 ? (
              <div className="space-y-4">
                {manejos
                  .filter(m => {
                    if (m.status !== 'pendente') return false;
                    const dataVencimento = m.dataVencimento 
                      ? parseISO(m.dataVencimento)
                      : null;
                    if (!dataVencimento) return false;
                    return isBefore(dataVencimento, new Date());
                  })
                  .slice(0, 3)
                  .map(manejo => (
                    <div key={manejo.id} className="flex items-start space-x-3">
                      <CircleAlert className="h-5 w-5 text-red-500 mt-0.5" />
                      <div>
                        <div className="font-medium">{manejo.observacoes || 'Manejo sem descrição'}</div>
                        <div className="text-sm text-gray-600">
                          {getNomeCavaloPeloId(manejo.horseId)}
                        </div>
                        <div className="text-xs text-red-600">
                          Vencido em {manejo.dataVencimento ? formatarData(manejo.dataVencimento) : 'data desconhecida'}
                        </div>
                      </div>
                    </div>
                  ))
                }
              </div>
            ) : (
              <div className="flex justify-center items-center h-24 text-gray-500 text-sm">
                Todos os manejos estão em dia!
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/manejos">
              <Button variant="outline" size="sm" className="w-full">
                <span>{metricas.manejosVencidos > 0 ? 'Ver todos os manejos vencidos' : 'Ver todos os manejos'}</span>
                <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
        
        {/* Próximo evento */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Próximo Evento</CardTitle>
            <CardDescription>
              {metricas.eventoProximo ? 'Evento agendado em breve' : 'Não há eventos agendados para os próximos dias'}
            </CardDescription>
          </CardHeader>
          <CardContent className="pb-2">
            {metricas.eventoProximo ? (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-blue-500" />
                  <span className="text-sm font-medium">{metricas.eventoProximo.data ? formatarData(metricas.eventoProximo.data) : 'Data não definida'}</span>
                </div>
                <h3 className="text-lg font-semibold">{metricas.eventoProximo.titulo || 'Evento sem título'}</h3>
                <p className="text-sm text-gray-600">{metricas.eventoProximo.descricao || 'Sem descrição'}</p>
                {metricas.eventoProximo.horseId && (
                  <div className="flex items-center text-sm text-gray-600">
                    <PawPrint className="h-4 w-4 mr-1" />
                    {getNomeCavaloPeloId(metricas.eventoProximo.horseId)}
                  </div>
                )}
              </div>
            ) : (
              <div className="flex justify-center items-center h-24 text-gray-500 text-sm">
                Nenhum evento agendado para os próximos dias.
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/agenda">
              <Button variant="outline" size="sm" className="w-full">
                <span>Ver todos os eventos</span>
                <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
      
      {/* Links rápidos - melhor layout para mobile */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
        <Link href="/cavalo/cadastro">
          <Card className="hover:bg-gray-50 cursor-pointer transition-colors">
            <CardHeader className="pb-2">
              <CardTitle className="text-md">Cadastrar Cavalo</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm text-gray-600">Adicione um novo cavalo ao seu plantel</p>
            </CardContent>
            <CardFooter className="pt-0 flex justify-end">
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </CardFooter>
          </Card>
        </Link>
        
        <Link href="/manejos">
          <Card className="hover:bg-gray-50 cursor-pointer transition-colors">
            <CardHeader className="pb-2">
              <CardTitle className="text-md">Registrar Manejo</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm text-gray-600">Agende um novo manejo ou atendimento</p>
            </CardContent>
            <CardFooter className="pt-0 flex justify-end">
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </CardFooter>
          </Card>
        </Link>
        
        <Link href="/dashboard/estatisticas">
          <Card className="hover:bg-gray-50 cursor-pointer transition-colors">
            <CardHeader className="pb-2">
              <CardTitle className="text-md">Ver Estatísticas</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm text-gray-600">Análise detalhada do seu plantel</p>
            </CardContent>
            <CardFooter className="pt-0 flex justify-end">
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </CardFooter>
          </Card>
        </Link>
      </div>
    </div>
  );
}