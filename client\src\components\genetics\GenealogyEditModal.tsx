import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Save, X, Search } from 'lucide-react';

interface GenealogyEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  cavaloId: number;
  cavalo: any;
}

interface GenealogyData {
  paiId: number | null;
  maeId: number | null;
  avoPaterno: string;
  avoPaterna: string;
  avoMaterno: string;
  avoMaterna: string;
  bisavoPaternoPai: string;
  bisavoPaternaMae: string;
  bisavoMaternoPai: string;
  bisavoMaternaMae: string;
}

export function GenealogyEditModal({ isOpen, onClose, cavaloId, cavalo }: GenealogyEditModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Função para extrair nome limpo dos dados de avô
  const extractCleanName = (avoData: string) => {
    if (!avoData) return '';
    
    // Se já é um nome simples, retornar diretamente
    if (!avoData.includes('{') && !avoData.includes('"')) {
      return avoData;
    }
    
    try {
      // Primeiro nível de parsing
      const firstParse = JSON.parse(avoData);
      
      // Se tem cavaloNome no primeiro nível
      if (firstParse.cavaloNome) {
        const cavaloNome = firstParse.cavaloNome;
        
        // Se cavaloNome é outro JSON string, parsear novamente
        if (typeof cavaloNome === 'string' && cavaloNome.includes('{')) {
          try {
            const secondParse = JSON.parse(cavaloNome);
            if (secondParse.cavaloNome) {
              return secondParse.cavaloNome;
            }
            return cavaloNome;
          } catch {
            return cavaloNome;
          }
        }
        
        return cavaloNome;
      }
      
      return '';
    } catch {
      // Fallback: extrair usando regex
      const deepMatch = avoData.match(/"cavaloNome":\s*"([^"]+)"/g);
      if (deepMatch && deepMatch.length > 0) {
        const lastMatch = deepMatch[deepMatch.length - 1];
        const nameMatch = lastMatch.match(/"cavaloNome":\s*"([^"]+)"/);
        return nameMatch ? nameMatch[1] : '';
      }
      return '';
    }
  };

  // Debug dos dados do cavalo
  console.log('Dados do cavalo no modal:', {
    avoPaterno: cavalo.avoPaterno,
    avoMaterno: cavalo.avoMaterno,
    avoPaterna: cavalo.avoPaterna,
    avoMaterna: cavalo.avoMaterna
  });

  const [genealogyData, setGenealogyData] = useState<GenealogyData>({
    paiId: null,
    maeId: null,
    avoPaterno: '',
    avoPaterna: '',
    avoMaterno: '',
    avoMaterna: '',
    bisavoPaternoPai: '',
    bisavoPaternaMae: '',
    bisavoMaternoPai: '',
    bisavoMaternaMae: '',
  });

  // Atualizar dados quando o modal abrir ou cavalo mudar
  useEffect(() => {
    if (isOpen && cavalo) {
      setGenealogyData({
        paiId: cavalo.paiId || null,
        maeId: cavalo.maeId || null,
        avoPaterno: extractCleanName(cavalo.avoPaterno),
        avoPaterna: extractCleanName(cavalo.avoPaterna),
        avoMaterno: extractCleanName(cavalo.avoMaterno),
        avoMaterna: extractCleanName(cavalo.avoMaterna),
        bisavoPaternoPai: extractCleanName(cavalo.bisavoPaternoPai),
        bisavoPaternaMae: extractCleanName(cavalo.bisavoPaternaMae),
        bisavoMaternoPai: extractCleanName(cavalo.bisavoMaternoPai),
        bisavoMaternaMae: extractCleanName(cavalo.bisavoMaternaMae),
      });
    }
  }, [isOpen, cavalo]);

  // Debug do que foi extraído
  console.log('Dados extraídos para o modal:', {
    avoPaterno: extractCleanName(cavalo.avoPaterno),
    avoMaterno: extractCleanName(cavalo.avoMaterno)
  });

  // Buscar lista de cavalos para seleção
  const { data: cavalos = [] } = useQuery({
    queryKey: ['/api/cavalos'],
    enabled: isOpen,
  });

  // Filtrar cavalos por sexo
  const machos = (cavalos as any[]).filter((c: any) => c.sexo === 'Macho' && c.id !== cavaloId);
  const femeas = (cavalos as any[]).filter((c: any) => c.sexo === 'Fêmea' && c.id !== cavaloId);

  // Mutation para atualizar genealogia
  const updateMutation = useMutation({
    mutationFn: async (data: GenealogyData) => {
      const response = await fetch(`/api/cavalos/${cavaloId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error('Erro ao atualizar genealogia');
      }
      
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Genealogia atualizada!',
        description: 'Os dados genealógicos foram salvos com sucesso.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${cavaloId}`] });
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: 'Erro ao salvar',
        description: error.message || 'Ocorreu um erro ao salvar a genealogia.',
        variant: 'destructive',
      });
    },
  });

  const handleSave = () => {
    updateMutation.mutate(genealogyData);
  };

  const handleReset = () => {
    setGenealogyData({
      paiId: cavalo.paiId || null,
      maeId: cavalo.maeId || null,
      avoPaterno: extractCleanName(cavalo.avoPaterno),
      avoPaterna: extractCleanName(cavalo.avoPaterna),
      avoMaterno: extractCleanName(cavalo.avoMaterno),
      avoMaterna: extractCleanName(cavalo.avoMaterna),
      bisavoPaternoPai: extractCleanName(cavalo.bisavoPaternoPai),
      bisavoPaternaMae: extractCleanName(cavalo.bisavoPaternaMae),
      bisavoMaternoPai: extractCleanName(cavalo.bisavoMaternoPai),
      bisavoMaternaMae: extractCleanName(cavalo.bisavoMaternaMae),
    });
  };

  const updateField = (field: keyof GenealogyData, value: number | null | string) => {
    setGenealogyData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getCavaloNome = (id: number | null) => {
    if (!id) return 'Não informado';
    const cavalo = (cavalos as any[]).find((c: any) => c.id === id);
    return cavalo?.name || 'Cavalo não encontrado';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Editar Genealogia - {cavalo.name}
          </DialogTitle>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Pais */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="pai">Pai</Label>
              <Select
                value={genealogyData.paiId?.toString() || "none"}
                onValueChange={(value) => updateField('paiId', value === "none" ? null : parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o pai" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Nenhum</SelectItem>
                  {machos.map((cavalo: any) => (
                    <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                      {cavalo.name} (ID: {cavalo.id})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Atual: {getCavaloNome(genealogyData.paiId)}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="mae">Mãe</Label>
              <Select
                value={genealogyData.maeId?.toString() || "none"}
                onValueChange={(value) => updateField('maeId', value === "none" ? null : parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a mãe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Nenhuma</SelectItem>
                  {femeas.map((cavalo: any) => (
                    <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                      {cavalo.name} (ID: {cavalo.id})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Atual: {getCavaloNome(genealogyData.maeId)}
              </p>
            </div>
          </div>

          {/* Avós */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold mb-3">Avós</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="avo-paterno">Avô Paterno</Label>
                <Input
                  value={genealogyData.avoPaterno}
                  onChange={(e) => updateField('avoPaterno', e.target.value)}
                  placeholder="Nome do avô paterno"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="avo-paterna">Avó Paterna</Label>
                <Input
                  value={genealogyData.avoPaterna}
                  onChange={(e) => updateField('avoPaterna', e.target.value)}
                  placeholder="Nome da avó paterna"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="avo-materno">Avô Materno</Label>
                <Input
                  value={genealogyData.avoMaterno}
                  onChange={(e) => updateField('avoMaterno', e.target.value)}
                  placeholder="Nome do avô materno"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="avo-materna">Avó Materna</Label>
                <Input
                  value={genealogyData.avoMaterna}
                  onChange={(e) => updateField('avoMaterna', e.target.value)}
                  placeholder="Nome da avó materna"
                />
              </div>
            </div>
          </div>

          {/* Bisavós */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-semibold mb-3">Bisavós</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bisavo-paterno-pai">Bisavô Paterno (Pai)</Label>
                <Input
                  value={genealogyData.bisavoPaternoPai}
                  onChange={(e) => updateField('bisavoPaternoPai', e.target.value)}
                  placeholder="Pai do avô paterno"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bisavo-paterna-mae">Bisavó Paterna (Mãe)</Label>
                <Input
                  value={genealogyData.bisavoPaternaMae}
                  onChange={(e) => updateField('bisavoPaternaMae', e.target.value)}
                  placeholder="Mãe do avô paterno"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bisavo-materno-pai">Bisavô Materno (Pai)</Label>
                <Input
                  value={genealogyData.bisavoMaternoPai}
                  onChange={(e) => updateField('bisavoMaternoPai', e.target.value)}
                  placeholder="Pai do avô materno"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bisavo-materna-mae">Bisavó Materna (Mãe)</Label>
                <Input
                  value={genealogyData.bisavoMaternaMae}
                  onChange={(e) => updateField('bisavoMaternaMae', e.target.value)}
                  placeholder="Mãe do avô materno"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Botões de Ação */}
        <div className="flex justify-between pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={handleReset}
          >
            Resetar
          </Button>
          
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button
              type="button"
              onClick={handleSave}
              disabled={updateMutation.isPending}
            >
              {updateMutation.isPending ? (
                <>Salvando...</>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar Genealogia
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}