{"dados": "preview_1747403005002", "logs": "{\n  \"dados\": {\n    \"cavalo\": {\n      \"nome\": \"QUEBRACHO DO ARROIO DO MEIO\",\n      \"registro\": \"B216636\",\n      \"sexo\": \"MACHO\",\n      \"nascimento\": \"27/12/2015\",\n      \"pelagem\": \"BAIA RUANA\",\n      \"criador\": \"TELMO GOMES BRAGA\",\n      \"proprietario\": \"CAMILA WEBER\",\n      \"inspetor\": \"RICARDO GUAZZELLI MARTINS\"\n    },\n    \"pai\": {\n      \"nome\": \"ESTRIBO DO ARROIO DO MEIO\",\n      \"registro\": \"B496067\"\n    },\n    \"mae\": {\n      \"nome\": \"ÍNDIA DO ARROIO DO MEIO\",\n      \"registro\": \"B142486\"\n    },\n    \"avoPai\": {\n      \"nome\": \"\"\n    },\n    \"avaMae\": {\n      \"nome\": \"\"\n    },\n    \"avoPai2\": {\n      \"nome\": \"\"\n    },\n    \"avaMae2\": {\n      \"nome\": \"\"\n    }\n  },\n  \"log\": \"[2025-05-16T13:43:22.503Z] [INFO] Iniciando processamento com OpenAI\\n[2025-05-16T13:43:25.001Z] [DEBUG] Conteúdo bruto da resposta: {\\\"cavalo_principal\\\": {\\\"nome\\\": \\\"QUEBRACHO DO ARROIO DO MEIO\\\", \\\"registro\\\": \\\"B216636\\\", \\\"rp\\\": null, \\\"sexo\\\": \\\"MACHO\\\", \\\"nascimento\\\": \\\"27/12/2015\\\", \\\"pelagem\\\": \\\"BAIA RUANA\\\", \\\"proprietario\\\": \\\"CAMILA WEBER\\\", \\\"criador\\\": \\\"TELMO GOMES BRAGA\\\", \\\"inspetor_tecnico\\\": \\\"RICARDO GUAZZELLI MARTINS\\\"}, \\\"pai\\\": {\\\"nome\\\": \\\"ESTRIBO DO ARROIO DO MEIO\\\", \\\"registro\\\": \\\"B496067\\\"}, \\\"mae\\\": {\\\"nome\\\": \\\"ÍNDIA DO ARROIO DO MEIO\\\", \\\"registro\\\": \\\"B142486\\\"}}\\n[2025-05-16T13:43:25.001Z] [INFO] Recebido resposta da OpenAI e parseado com sucesso\\n[2025-05-16T13:43:25.001Z] [DEBUG] Dados extraídos: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"QUEBRACHO DO ARROIO DO MEIO\\\",\\n    \\\"registro\\\": \\\"B216636\\\",\\n    \\\"rp\\\": null,\\n    \\\"sexo\\\": \\\"MACHO\\\",\\n    \\\"nascimento\\\": \\\"27/12/2015\\\",\\n    \\\"pelagem\\\": \\\"BAIA RUANA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"TELMO GOMES BRAGA\\\",\\n    \\\"inspetor_tecnico\\\": \\\"RICARDO GUAZZELLI MARTINS\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"ESTRIBO DO ARROIO DO MEIO\\\",\\n    \\\"registro\\\": \\\"B496067\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"ÍNDIA DO ARROIO DO MEIO\\\",\\n    \\\"registro\\\": \\\"B142486\\\"\\n  }\\n}\\n[2025-05-16T13:43:25.001Z] [INFO] Dados do cavalo principal extraídos: {\\n  \\\"nome\\\": \\\"QUEBRACHO DO ARROIO DO MEIO\\\",\\n  \\\"registro\\\": \\\"B216636\\\",\\n  \\\"rp\\\": null,\\n  \\\"sexo\\\": \\\"MACHO\\\",\\n  \\\"nascimento\\\": \\\"27/12/2015\\\",\\n  \\\"pelagem\\\": \\\"BAIA RUANA\\\",\\n  \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n  \\\"criador\\\": \\\"TELMO GOMES BRAGA\\\",\\n  \\\"inspetor_tecnico\\\": \\\"RICARDO GUAZZELLI MARTINS\\\"\\n}\\n[2025-05-16T13:43:25.002Z] [INFO] Dados do cavalo principal extraídos: QUEBRACHO DO ARROIO DO MEIO (B216636)\\n[2025-05-16T13:43:25.002Z] [INFO] Pai: ESTRIBO DO ARROIO DO MEIO (B496067)\\n[2025-05-16T13:43:25.002Z] [INFO] Mãe: ÍNDIA DO ARROIO DO MEIO (B142486)\\n[2025-05-16T13:43:25.002Z] [INFO] Dados convertidos com sucesso\"\n}", "timestamp": "2025-05-16T13:43:25.002Z"}