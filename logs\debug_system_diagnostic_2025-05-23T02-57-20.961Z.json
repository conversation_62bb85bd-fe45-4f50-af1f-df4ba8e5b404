{"timestamp": "2025-05-23T02:57:20.961Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 256643072, "heapTotal": 109355008, "heapUsed": 74266152, "external": 8227674, "arrayBuffers": 251917}, "uptime": 7.245044406, "cpuUsage": {"user": 3128458, "system": 444175}, "resourceUsage": {"userCPUTime": 3128511, "systemCPUTime": 444182, "maxRSS": 293296, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104787, "majorPageFault": 6, "swappedOut": 0, "fsRead": 55536, "fsWrite": 1128, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8583, "involuntaryContextSwitches": 4100}}