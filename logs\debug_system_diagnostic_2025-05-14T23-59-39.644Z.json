{"timestamp": "2025-05-14T23:59:39.643Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399282176, "heapTotal": 100012032, "heapUsed": 62187592, "external": 6815481, "arrayBuffers": 60485}, "uptime": 2.105686124, "cpuUsage": {"user": 2558301, "system": 348766}, "resourceUsage": {"userCPUTime": 2558375, "systemCPUTime": 348766, "maxRSS": 389924, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102499, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5889, "involuntaryContextSwitches": 7234}}