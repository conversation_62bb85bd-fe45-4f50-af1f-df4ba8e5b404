{"timestamp": "2025-05-15T23:19:40.299Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 260988928, "heapTotal": 112500736, "heapUsed": 72527360, "external": 8219482, "arrayBuffers": 243725}, "uptime": 6.0106125, "cpuUsage": {"user": 3412962, "system": 552868}, "resourceUsage": {"userCPUTime": 3413004, "systemCPUTime": 552875, "maxRSS": 300444, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103610, "majorPageFault": 6, "swappedOut": 0, "fsRead": 53776, "fsWrite": 664, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9820, "involuntaryContextSwitches": 12202}}