import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useGeneticsContext } from "@/contexts/GeneticsContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, TreePine, Zap, Info, LayoutGrid, Eye } from "lucide-react";
import { SimpleGenealogy } from "@/components/SimpleGenealogy";
import { ExpandedGenealogy } from "@/components/genetics/ExpandedGenealogy";

export default function GenealogiaPageSimple() {
  const { selectedHorseId } = useGeneticsContext();
  const [viewMode, setViewMode] = useState<'simple' | 'enhanced'>('simple');

  // Buscar cavalos
  const { data: cavalos, isLoading: isLoadingCavalos } = useQuery<any[]>({
    queryKey: ['/api/cavalos'],
  });

  const selectedHorse = cavalos?.find(cavalo => cavalo.id === selectedHorseId);

  return (
    <div className="space-y-4 sm:space-y-6 max-w-[1400px] mx-auto px-2 sm:px-4">
      {/* Cabeçalho da Genealogia */}
      {!selectedHorseId && (
        <Card>
          <CardHeader>
            <CardTitle>Análise Genealógica</CardTitle>
            <CardDescription>
              Selecione um cavalo no topo da página para visualizar sua árvore genealógica
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* Árvore Genealógica */}
      {selectedHorseId && (
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <TreePine className="h-5 w-5" />
                  Árvore Genealógica
                </CardTitle>
                <CardDescription>
                  Visualize os ancestrais do animal
                </CardDescription>
              </div>
              
              {/* Controles de visualização */}
              <div className="flex gap-2 w-full sm:w-auto">
                <Button
                  variant={viewMode === 'simple' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('simple')}
                  className="flex items-center gap-2 flex-1 sm:flex-none"
                >
                  <LayoutGrid className="h-4 w-4" />
                  <span className="hidden sm:inline">Simples</span>
                </Button>
                <Button
                  variant={viewMode === 'enhanced' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('enhanced')}
                  className="flex items-center gap-2 flex-1 sm:flex-none"
                >
                  <Zap className="h-4 w-4" />
                  <span className="hidden sm:inline">Expandida</span>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {viewMode === 'simple' ? (
              <SimpleGenealogy cavaloId={selectedHorseId} />
            ) : (
              <ExpandedGenealogy cavaloId={selectedHorseId} />
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}