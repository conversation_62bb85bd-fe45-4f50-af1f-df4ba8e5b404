import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  PlusCircle, Search, ArrowRight, ArrowLeft, Calendar, FileText, Edit, 
  ChevronDown, ChevronUp, ArrowRightLeft, Filter, ChevronRight
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LayoutWrapper } from '@/components/Layout';
import { DatePicker } from '@/components/DatePicker';
import { Cavalo } from '@shared/schema';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';

/**
 * Interface para definir a estrutura de uma movimentação de cavalos
 */
interface Movimentacao {
  id: number;
  tipo: 'entrada' | 'saida' | 'retorno';
  cavaloId: number;
  cavaloNome: string;
  propriedadeOrigem?: string;
  propriedadeDestino?: string;
  responsavelNome: string;
  responsavelContato?: string;
  data: string;
  motivoMovimentacao: string;
  previsaoRetorno?: string | null;
  observacoes?: string;
  status: 'pendente' | 'concluida' | 'cancelada';
  documentos?: string[];
}

/**
 * Componente principal da página de Movimentações
 */
export function MovimentacoesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [tipoFilter, setTipoFilter] = useState('todos');
  const [statusFilter, setStatusFilter] = useState('todos');
  const [periodoInicio, setPeriodoInicio] = useState<Date | undefined>(undefined);
  const [periodoFim, setPeriodoFim] = useState<Date | undefined>(undefined);
  const [expandedRow, setExpandedRow] = useState<number | null>(null);

  // Consulta de cavalos
  const { 
    data: cavalos = [], 
    isLoading: isLoadingCavalos 
  } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      try {
        return await apiRequest<Cavalo[]>('GET', '/api/cavalos');
      } catch (error) {
        console.error('Erro ao buscar cavalos:', error);
        return [];
      }
    }
  });

  // Função para encontrar o nome do cavalo pelo ID
  const findCavaloNome = (id: number): string => {
    const cavalo = cavalos.find(c => c.id === id);
    return cavalo ? cavalo.name : `Cavalo ID ${id}`;
  };

  // Filtrar movimentações com base nos filtros aplicados
  const filteredMovimentacoes = movimentacoesData.filter(movimentacao => {
    // Atualizar nome do cavalo com base nos dados reais
    movimentacao.cavaloNome = findCavaloNome(movimentacao.cavaloId);
    
    const matchesSearch = 
      movimentacao.cavaloNome.toLowerCase().includes(searchTerm.toLowerCase()) || 
      (movimentacao.motivoMovimentacao && movimentacao.motivoMovimentacao.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (movimentacao.propriedadeOrigem && movimentacao.propriedadeOrigem.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (movimentacao.propriedadeDestino && movimentacao.propriedadeDestino.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (movimentacao.responsavelNome && movimentacao.responsavelNome.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesTipo = tipoFilter === 'todos' || movimentacao.tipo === tipoFilter;
    
    const matchesStatus = statusFilter === 'todos' || movimentacao.status === statusFilter;
    
    // Filtro de período
    let matchesPeriodo = true;
    const dataMovimentacao = new Date(movimentacao.data);
    
    if (periodoInicio) {
      // Ignorar a parte de horas para comparação justa por data
      const startDate = new Date(periodoInicio);
      startDate.setHours(0, 0, 0, 0);
      dataMovimentacao.setHours(0, 0, 0, 0);
      
      if (dataMovimentacao < startDate) {
        matchesPeriodo = false;
      }
    }
    
    if (periodoFim && matchesPeriodo) {
      // Ignorar a parte de horas para comparação justa por data
      const endDate = new Date(periodoFim);
      endDate.setHours(23, 59, 59, 999);
      dataMovimentacao.setHours(0, 0, 0, 0);
      
      if (dataMovimentacao > endDate) {
        matchesPeriodo = false;
      }
    }
    
    return matchesSearch && matchesTipo && matchesStatus && matchesPeriodo;
  });

  const toggleRowExpand = (id: number) => {
    if (expandedRow === id) {
      setExpandedRow(null);
    } else {
      setExpandedRow(id);
    }
  };

  // Função para renderizar o ícone de tipo de movimentação
  const renderTipoIcon = (tipo: 'entrada' | 'saida' | 'retorno') => {
    if (tipo === 'entrada') {
      return <ArrowRight className="h-4 w-4 text-green-600" />;
    } else if (tipo === 'saida') {
      return <ArrowLeft className="h-4 w-4 text-red-600" />;
    } else {
      return <ArrowRightLeft className="h-4 w-4 text-blue-600" />;
    }
  };

  // Função para renderizar o tipo de movimentação
  const renderTipo = (tipo: 'entrada' | 'saida' | 'retorno') => {
    if (tipo === 'entrada') {
      return <Badge className="bg-green-600">Entrada</Badge>;
    } else if (tipo === 'saida') {
      return <Badge className="bg-red-600">Saída</Badge>;
    } else {
      return <Badge className="bg-blue-600">Retorno</Badge>;
    }
  };

  // Função para renderizar o status da movimentação
  const renderStatus = (status: 'pendente' | 'concluida' | 'cancelada') => {
    if (status === 'pendente') {
      return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pendente</Badge>;
    } else if (status === 'concluida') {
      return <Badge variant="outline" className="text-green-600 border-green-600">Concluída</Badge>;
    } else {
      return <Badge variant="outline" className="text-red-600 border-red-600">Cancelada</Badge>;
    }
  };

  return (
    <LayoutWrapper pageTitle="Movimentações de Animais">
      <div className="flex flex-col gap-6">
        {/* Cabeçalho com ações principais */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Movimentações de Animais</h1>
            <p className="text-muted-foreground">
              Controle de entradas, saídas e retornos de animais
            </p>
          </div>
          
          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Nova Movimentação
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <Link href="/movimentacoes/nova/entrada">
                  <DropdownMenuItem>
                    <ArrowRight className="mr-2 h-4 w-4 text-green-600" />
                    Registrar Entrada
                  </DropdownMenuItem>
                </Link>
                <Link href="/movimentacoes/nova/saida">
                  <DropdownMenuItem>
                    <ArrowLeft className="mr-2 h-4 w-4 text-red-600" />
                    Registrar Saída
                  </DropdownMenuItem>
                </Link>
                <Link href="/movimentacoes/nova/retorno">
                  <DropdownMenuItem>
                    <ArrowRightLeft className="mr-2 h-4 w-4 text-blue-600" />
                    Registrar Retorno
                  </DropdownMenuItem>
                </Link>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <Link href="/movimentacoes/relatorio">
              <Button variant="outline" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span>Relatório</span>
              </Button>
            </Link>
          </div>
        </div>
        
        {/* Resumo de movimentações */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <ArrowRight className="h-5 w-5 text-green-600" />
                Entradas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {movimentacoesData.filter(m => m.tipo === 'entrada').length}
              </div>
              <p className="text-sm text-muted-foreground">
                Total de entradas registradas
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <ArrowLeft className="h-5 w-5 text-red-600" />
                Saídas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {movimentacoesData.filter(m => m.tipo === 'saida').length}
              </div>
              <p className="text-sm text-muted-foreground">
                Total de saídas registradas
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <ArrowRightLeft className="h-5 w-5 text-blue-600" />
                Retornos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {movimentacoesData.filter(m => m.tipo === 'retorno').length}
              </div>
              <p className="text-sm text-muted-foreground">
                Total de retornos registrados
              </p>
            </CardContent>
          </Card>
        </div>
        
        {/* Card de filtros e busca */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Filtros e Busca</CardTitle>
            <CardDescription>
              Encontre movimentações específicas usando os filtros abaixo
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar por cavalo, motivo, propriedade ou responsável..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="grid gap-4 md:grid-cols-3">
                <Select 
                  value={tipoFilter} 
                  onValueChange={setTipoFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tipo de Movimentação" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos os Tipos</SelectItem>
                    <SelectItem value="entrada">Entradas</SelectItem>
                    <SelectItem value="saida">Saídas</SelectItem>
                    <SelectItem value="retorno">Retornos</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select 
                  value={statusFilter} 
                  onValueChange={setStatusFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos os Status</SelectItem>
                    <SelectItem value="pendente">Pendentes</SelectItem>
                    <SelectItem value="concluida">Concluídas</SelectItem>
                    <SelectItem value="cancelada">Canceladas</SelectItem>
                  </SelectContent>
                </Select>
                
                <div className="flex items-center gap-2">
                  <DatePicker
                    date={periodoInicio}
                    setDate={setPeriodoInicio}
                    placeholder="Data inicial"
                  />
                  <span className="text-muted-foreground">até</span>
                  <DatePicker
                    date={periodoFim}
                    setDate={setPeriodoFim}
                    placeholder="Data final"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Tabela de movimentações */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Lista de Movimentações</CardTitle>
            <CardDescription>
              {filteredMovimentacoes.length} movimentações encontradas
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingCavalos ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-700"></div>
              </div>
            ) : filteredMovimentacoes.length === 0 ? (
              <div className="text-center py-8">
                <ArrowRightLeft className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">Nenhuma movimentação encontrada</h3>
                <p className="text-muted-foreground">
                  {searchTerm || tipoFilter !== 'todos' || statusFilter !== 'todos' ? 
                    'Tente ajustar os critérios de busca' : 
                    'Registre movimentações para começar a visualizá-las aqui'}
                </p>
              </div>
            ) : (
              <div className="overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-10"></TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Cavalo</TableHead>
                      <TableHead>Data</TableHead>
                      <TableHead>Motivo</TableHead>
                      <TableHead>Propriedade</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMovimentacoes.map(movimentacao => (
                      <>
                        <TableRow key={movimentacao.id} className="hover:bg-muted/50 cursor-pointer" onClick={() => toggleRowExpand(movimentacao.id)}>
                          <TableCell>
                            {expandedRow === movimentacao.id ? (
                              <ChevronUp className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <ChevronDown className="h-4 w-4 text-muted-foreground" />
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {renderTipoIcon(movimentacao.tipo)}
                              {renderTipo(movimentacao.tipo)}
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            {movimentacao.cavaloNome}
                          </TableCell>
                          <TableCell>
                            {new Date(movimentacao.data).toLocaleDateString('pt-BR')}
                          </TableCell>
                          <TableCell className="max-w-[200px] truncate">
                            {movimentacao.motivoMovimentacao}
                          </TableCell>
                          <TableCell>
                            {movimentacao.tipo === 'entrada' ? movimentacao.propriedadeOrigem : 
                             movimentacao.tipo === 'saida' ? movimentacao.propriedadeDestino :
                             `${movimentacao.propriedadeOrigem} → ${movimentacao.propriedadeDestino}`}
                          </TableCell>
                          <TableCell>
                            {renderStatus(movimentacao.status)}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                  <span className="sr-only">Abrir menu</span>
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                    className="h-5 w-5"
                                  >
                                    <path d="M10 3a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM10 8.5a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM11.5 15.5a1.5 1.5 0 10-3 0 1.5 1.5 0 003 0z"></path>
                                  </svg>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Ações</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <Link href={`/movimentacoes/${movimentacao.id}`}>
                                  <DropdownMenuItem>
                                    <FileText className="mr-2 h-4 w-4" />
                                    Ver Detalhes
                                  </DropdownMenuItem>
                                </Link>
                                <Link href={`/movimentacoes/${movimentacao.id}/editar`}>
                                  <DropdownMenuItem>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Editar
                                  </DropdownMenuItem>
                                </Link>
                                {movimentacao.tipo === 'saida' && movimentacao.status === 'pendente' && (
                                  <Link href={`/movimentacoes/nova/retorno?cavaloId=${movimentacao.cavaloId}`}>
                                    <DropdownMenuItem>
                                      <ArrowRightLeft className="mr-2 h-4 w-4 text-blue-600" />
                                      Registrar Retorno
                                    </DropdownMenuItem>
                                  </Link>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                        
                        {/* Detalhes da movimentação (linha expansível) */}
                        {expandedRow === movimentacao.id && (
                          <TableRow className="bg-muted/50">
                            <TableCell colSpan={8} className="py-4">
                              <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                  <h4 className="text-sm font-semibold mb-2">Informações da Movimentação</h4>
                                  <div className="text-sm space-y-1">
                                    <div className="flex gap-2">
                                      <span className="font-medium">Responsável:</span>
                                      <span>{movimentacao.responsavelNome}</span>
                                    </div>
                                    {movimentacao.responsavelContato && (
                                      <div className="flex gap-2">
                                        <span className="font-medium">Contato:</span>
                                        <span>{movimentacao.responsavelContato}</span>
                                      </div>
                                    )}
                                    <div className="flex gap-2">
                                      <span className="font-medium">Data:</span>
                                      <span>{new Date(movimentacao.data).toLocaleDateString('pt-BR')}</span>
                                    </div>
                                    {movimentacao.previsaoRetorno && (
                                      <div className="flex gap-2">
                                        <span className="font-medium">Previsão de Retorno:</span>
                                        <span>{new Date(movimentacao.previsaoRetorno).toLocaleDateString('pt-BR')}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                
                                <div>
                                  <h4 className="text-sm font-semibold mb-2">Detalhes</h4>
                                  <div className="text-sm space-y-1">
                                    <div className="flex gap-2">
                                      <span className="font-medium">Motivo:</span>
                                      <span>{movimentacao.motivoMovimentacao}</span>
                                    </div>
                                    {movimentacao.observacoes && (
                                      <div className="flex gap-2">
                                        <span className="font-medium">Observações:</span>
                                        <span>{movimentacao.observacoes}</span>
                                      </div>
                                    )}
                                    {movimentacao.tipo === 'entrada' && (
                                      <div className="flex gap-2">
                                        <span className="font-medium">Propriedade de Origem:</span>
                                        <span>{movimentacao.propriedadeOrigem}</span>
                                      </div>
                                    )}
                                    {movimentacao.tipo === 'saida' && (
                                      <div className="flex gap-2">
                                        <span className="font-medium">Propriedade de Destino:</span>
                                        <span>{movimentacao.propriedadeDestino}</span>
                                      </div>
                                    )}
                                    {movimentacao.tipo === 'retorno' && (
                                      <>
                                        <div className="flex gap-2">
                                          <span className="font-medium">Propriedade de Origem:</span>
                                          <span>{movimentacao.propriedadeOrigem}</span>
                                        </div>
                                        <div className="flex gap-2">
                                          <span className="font-medium">Propriedade de Destino:</span>
                                          <span>{movimentacao.propriedadeDestino}</span>
                                        </div>
                                      </>
                                    )}
                                  </div>
                                </div>
                              </div>
                              
                              {/* Documentos relacionados */}
                              {movimentacao.documentos && movimentacao.documentos.length > 0 && (
                                <div className="mt-4">
                                  <h4 className="text-sm font-semibold mb-2">Documentos</h4>
                                  <div className="flex flex-wrap gap-2">
                                    {movimentacao.documentos.map((doc, index) => (
                                      <Link href="#" key={index}>
                                        <Badge variant="outline" className="flex items-center gap-2 cursor-pointer hover:bg-muted">
                                          <FileText className="h-3 w-3" />
                                          {doc}
                                        </Badge>
                                      </Link>
                                    ))}
                                  </div>
                                </div>
                              )}
                              
                              <div className="mt-4 flex justify-end">
                                <Link href={`/movimentacoes/${movimentacao.id}`}>
                                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                                    <ChevronRight className="h-4 w-4" />
                                    Ver Detalhes Completos
                                  </Button>
                                </Link>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </LayoutWrapper>
  );
}

// Data de exemplo para movimentações (apenas para demonstração)
const movimentacoesData: Movimentacao[] = [
  {
    id: 1,
    tipo: 'entrada',
    cavaloId: 1, // Será substituído pelo nome real
    cavaloNome: 'Trovão',
    propriedadeOrigem: 'Haras São Francisco',
    responsavelNome: 'Carlos Mendes',
    responsavelContato: '(11) 98765-4321',
    data: '2025-03-01',
    motivoMovimentacao: 'Aquisição para o plantel',
    observacoes: 'Animal em excelente estado',
    status: 'concluida',
    documentos: ['Contrato_Compra_20250301.pdf', 'Exame_Veterinario_20250228.pdf']
  },
  {
    id: 2,
    tipo: 'saida',
    cavaloId: 2,
    cavaloNome: 'Pegasus',
    propriedadeDestino: 'Centro Equestre Elite',
    responsavelNome: 'Ana Paula Silva',
    responsavelContato: '(21) 99876-5432',
    data: '2025-03-10',
    motivoMovimentacao: 'Participação em competição',
    previsaoRetorno: '2025-03-20',
    observacoes: 'Levar equipamentos de competição',
    status: 'pendente'
  },
  {
    id: 3,
    tipo: 'retorno',
    cavaloId: 2,
    cavaloNome: 'Pegasus',
    propriedadeOrigem: 'Centro Equestre Elite',
    propriedadeDestino: 'EquiGestor Haras',
    responsavelNome: 'Ana Paula Silva',
    responsavelContato: '(21) 99876-5432',
    data: '2025-03-20',
    motivoMovimentacao: 'Retorno após competição',
    observacoes: 'Animal com medalha de 3º lugar',
    status: 'concluida',
    documentos: ['Comprovante_Participacao.pdf', 'Certificado_Premiacao.pdf']
  },
  {
    id: 4,
    tipo: 'saida',
    cavaloId: 3,
    cavaloNome: 'Tempestade',
    propriedadeDestino: 'Haras Bom Retiro',
    responsavelNome: 'Roberto Oliveira',
    responsavelContato: '(31) 98888-7777',
    data: '2025-03-15',
    motivoMovimentacao: 'Venda',
    observacoes: 'Venda aprovada pela gerência',
    status: 'concluida',
    documentos: ['Contrato_Venda_20250315.pdf', 'Recibo_Pagamento.pdf']
  },
  {
    id: 5,
    tipo: 'entrada',
    cavaloId: 4,
    cavaloNome: 'Relâmpago',
    propriedadeOrigem: 'Fazenda Três Marias',
    responsavelNome: 'Márcio Andrade',
    responsavelContato: '(41) 97777-6666',
    data: '2025-03-22',
    motivoMovimentacao: 'Aquisição para reprodução',
    observacoes: 'Animal com excelente genética',
    status: 'concluida',
    documentos: ['Documentos_Registro_Genealogico.pdf', 'Certificado_DNA.pdf']
  },
  {
    id: 6,
    tipo: 'saida',
    cavaloId: 5,
    cavaloNome: 'Ventania',
    propriedadeDestino: 'Clínica Equina Especializada',
    responsavelNome: 'Dr. Felipe Santos',
    responsavelContato: '(11) 95555-4444',
    data: '2025-03-25',
    motivoMovimentacao: 'Tratamento médico especializado',
    previsaoRetorno: '2025-04-10',
    observacoes: 'Problema no casco direito traseiro',
    status: 'pendente'
  }
];

export default MovimentacoesPage;