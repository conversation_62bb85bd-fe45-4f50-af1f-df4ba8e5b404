{"timestamp": "2025-05-15T23:47:24.624Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 410374144, "heapTotal": 118546432, "heapUsed": 89660736, "external": 8268990, "arrayBuffers": 243725}, "uptime": 3.067553409, "cpuUsage": {"user": 3092657, "system": 410811}, "resourceUsage": {"userCPUTime": 3092709, "systemCPUTime": 410811, "maxRSS": 400756, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106433, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 200, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8106, "involuntaryContextSwitches": 10641}}