{"timestamp": "2025-05-15T22:05:26.346Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 264200192, "heapTotal": 115908608, "heapUsed": 72527952, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.862512814, "cpuUsage": {"user": 2713054, "system": 357151}, "resourceUsage": {"userCPUTime": 2713104, "systemCPUTime": 357151, "maxRSS": 284672, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102877, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7636, "involuntaryContextSwitches": 2767}}