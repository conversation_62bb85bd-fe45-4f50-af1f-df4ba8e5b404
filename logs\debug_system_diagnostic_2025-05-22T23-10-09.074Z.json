{"timestamp": "2025-05-22T23:10:09.074Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 409477120, "heapTotal": 116846592, "heapUsed": 89489024, "external": 8708919, "arrayBuffers": 276792}, "uptime": 5.391251652, "cpuUsage": {"user": 2996810, "system": 473192}, "resourceUsage": {"userCPUTime": 2996879, "systemCPUTime": 473203, "maxRSS": 399880, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108602, "majorPageFault": 6, "swappedOut": 0, "fsRead": 54152, "fsWrite": 968, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9284, "involuntaryContextSwitches": 3006}}