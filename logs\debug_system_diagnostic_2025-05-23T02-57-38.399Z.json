{"timestamp": "2025-05-23T02:57:38.398Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 264413184, "heapTotal": 117481472, "heapUsed": 74169008, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.466160488, "cpuUsage": {"user": 3112843, "system": 400793}, "resourceUsage": {"userCPUTime": 3112901, "systemCPUTime": 400800, "maxRSS": 290488, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103258, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8849, "involuntaryContextSwitches": 7209}}