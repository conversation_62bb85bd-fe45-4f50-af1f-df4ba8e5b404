{"timestamp": "2025-05-15T18:11:17.261Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403161088, "heapTotal": 118022144, "heapUsed": 93763688, "external": 8597263, "arrayBuffers": 249274}, "uptime": 2.145219898, "cpuUsage": {"user": 2925741, "system": 412096}, "resourceUsage": {"userCPUTime": 2925801, "systemCPUTime": 412104, "maxRSS": 393712, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106738, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 208, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9216, "involuntaryContextSwitches": 7489}}