{"timestamp": "2025-05-16T13:03:10.919Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 395862016, "heapTotal": 103325696, "heapUsed": 80142704, "external": 8295495, "arrayBuffers": 265658}, "uptime": 1.745858515, "cpuUsage": {"user": 2837721, "system": 323963}, "resourceUsage": {"userCPUTime": 2837773, "systemCPUTime": 323963, "maxRSS": 386584, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103234, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 64, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8374, "involuntaryContextSwitches": 2338}}