{"timestamp": "2025-05-15T01:44:40.606Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 371740672, "heapTotal": 91623424, "heapUsed": 70428000, "external": 6863744, "arrayBuffers": 106994}, "uptime": 1.508670463, "cpuUsage": {"user": 2229243, "system": 285055}, "resourceUsage": {"userCPUTime": 2229304, "systemCPUTime": 285063, "maxRSS": 363028, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 98779, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 64, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6164, "involuntaryContextSwitches": 2386}}