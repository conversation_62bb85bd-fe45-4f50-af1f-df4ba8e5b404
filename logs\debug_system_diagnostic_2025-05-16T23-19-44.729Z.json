{"timestamp": "2025-05-16T23:19:44.729Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 413655040, "heapTotal": 119070720, "heapUsed": 91478136, "external": 8268720, "arrayBuffers": 243725}, "uptime": 2.471203138, "cpuUsage": {"user": 2957927, "system": 412763}, "resourceUsage": {"userCPUTime": 2957998, "systemCPUTime": 412763, "maxRSS": 403960, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105668, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8552, "involuntaryContextSwitches": 7123}}