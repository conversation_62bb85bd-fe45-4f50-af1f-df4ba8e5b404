{"timestamp": "2025-05-23T21:45:39.528Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 287338496, "heapTotal": 135540736, "heapUsed": 101439320, "external": 8555872, "arrayBuffers": 475341}, "uptime": 5.547179869, "cpuUsage": {"user": 3609873, "system": 479292}, "resourceUsage": {"userCPUTime": 3609879, "systemCPUTime": 479293, "maxRSS": 295900, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 109738, "majorPageFault": 0, "swappedOut": 0, "fsRead": 2136, "fsWrite": 88, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 10677, "involuntaryContextSwitches": 12979}}