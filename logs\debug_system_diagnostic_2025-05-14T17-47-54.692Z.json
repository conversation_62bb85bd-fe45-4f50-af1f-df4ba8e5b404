{"timestamp": "2025-05-14T17:47:54.692Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 383225856, "heapTotal": 104206336, "heapUsed": 61999320, "external": 6815481, "arrayBuffers": 60485}, "uptime": 7.540732182, "cpuUsage": {"user": 2568088, "system": 353717}, "resourceUsage": {"userCPUTime": 2568134, "systemCPUTime": 353717, "maxRSS": 374244, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103325, "majorPageFault": 6, "swappedOut": 0, "fsRead": 42296, "fsWrite": 368, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6580, "involuntaryContextSwitches": 3183}}