/**
 * Galeria de fotos do cavalo
 */
import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { 
  Image as ImageIcon, 
  Star, 
  StarOff, 
  Trash2, 
  Edit3,
  ZoomIn,
  X,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { apiRequest, queryClient } from '@/lib/queryClient';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface Photo {
  id: number;
  fileName: string;
  description?: string;
  isProfilePhoto: boolean;
  uploadDate: string;
  fileSize: number;
  mimeType?: string;
}

interface HorsePhotoGalleryProps {
  horseId: number;
  className?: string;
}

export function HorsePhotoGallery({ horseId, className = '' }: HorsePhotoGalleryProps) {
  const [editingPhoto, setEditingPhoto] = useState<Photo | null>(null);
  const [editDescription, setEditDescription] = useState('');
  const { toast } = useToast();

  const { 
    data: photos = [], 
    isLoading, 
    error 
  } = useQuery<Photo[]>({
    queryKey: [`/api/cavalos/${horseId}/photos`],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/cavalos/${horseId}/photos`);
      return response.data || [];
    },
  });

  const updatePhotoMutation = useMutation({
    mutationFn: async ({ photoId, data }: { photoId: number; data: Partial<Photo> }) => {
      return apiRequest('PUT', `/api/cavalos/${horseId}/photos/${photoId}`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${horseId}/photos`] });
      setEditingPhoto(null);
      toast({
        title: "Foto atualizada",
        description: "As informações da foto foram atualizadas com sucesso.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao atualizar",
        description: error.message || "Erro ao atualizar a foto.",
        variant: "destructive",
      });
    },
  });

  const deletePhotoMutation = useMutation({
    mutationFn: async (photoId: number) => {
      return apiRequest('DELETE', `/api/cavalos/${horseId}/photos/${photoId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${horseId}/photos`] });
      toast({
        title: "Foto removida",
        description: "A foto foi removida com sucesso.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao remover",
        description: error.message || "Erro ao remover a foto.",
        variant: "destructive",
      });
    },
  });

  const handleSetProfilePhoto = async (photo: Photo) => {
    updatePhotoMutation.mutate({
      photoId: photo.id,
      data: { isProfilePhoto: true }
    });
  };

  const handleDeletePhoto = async (photoId: number) => {
    if (confirm('Tem certeza que deseja remover esta foto?')) {
      deletePhotoMutation.mutate(photoId);
    }
  };

  const handleEditPhoto = (photo: Photo) => {
    setEditingPhoto(photo);
    setEditDescription(photo.description || '');
  };

  const handleSaveEdit = () => {
    if (!editingPhoto) return;
    
    updatePhotoMutation.mutate({
      photoId: editingPhoto.id,
      data: { description: editDescription }
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Carregando fotos...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center p-8 text-red-600 ${className}`}>
        Erro ao carregar fotos do cavalo.
      </div>
    );
  }

  if (photos.length === 0) {
    return (
      <div className={`text-center p-8 text-gray-500 ${className}`}>
        <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
        <p>Nenhuma foto encontrada para este cavalo.</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {photos.map((photo) => (
          <Card key={photo.id} className="group hover:shadow-lg transition-shadow">
            <CardContent className="p-0">
              <div className="relative">
                <img
                  src={`/api/cavalos/${horseId}/photos/${photo.id}`}
                  alt={photo.description || photo.fileName}
                  className="w-full h-48 object-cover rounded-t-lg"
                />

                {photo.isProfilePhoto && (
                  <Badge className="absolute top-2 left-2 bg-yellow-500">
                    <Star className="h-3 w-3 mr-1" />
                    Principal
                  </Badge>
                )}

                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all rounded-t-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <div className="flex gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button size="sm" variant="secondary">
                          <ZoomIn className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl">
                        <DialogHeader>
                          <DialogTitle>{photo.fileName}</DialogTitle>
                        </DialogHeader>
                        <img
                          src={`/api/cavalos/${horseId}/photos/${photo.id}`}
                          alt={photo.description || photo.fileName}
                          className="w-full max-h-[70vh] object-contain"
                        />
                        {photo.description && (
                          <p className="text-sm text-gray-600 mt-2">
                            {photo.description}
                          </p>
                        )}
                      </DialogContent>
                    </Dialog>

                    <Button 
                      size="sm" 
                      variant="secondary"
                      onClick={() => handleEditPhoto(photo)}
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>

                    {!photo.isProfilePhoto && (
                      <Button 
                        size="sm" 
                        variant="secondary"
                        onClick={() => handleSetProfilePhoto(photo)}
                        disabled={updatePhotoMutation.isPending}
                      >
                        <StarOff className="h-4 w-4" />
                      </Button>
                    )}

                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => handleDeletePhoto(photo.id)}
                      disabled={deletePhotoMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <div className="p-3">
                <p className="text-sm font-medium truncate" title={photo.fileName}>
                  {photo.fileName}
                </p>
                {photo.description && (
                  <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                    {photo.description}
                  </p>
                )}
                <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
                  <span>{formatDate(photo.uploadDate)}</span>
                  <span>{formatFileSize(photo.fileSize)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Dialog open={!!editingPhoto} onOpenChange={() => setEditingPhoto(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Foto</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-description">Descrição</Label>
              <Textarea
                id="edit-description"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                placeholder="Descrição da foto..."
                rows={3}
              />
            </div>
            <div className="flex gap-2">
              <Button 
                onClick={handleSaveEdit}
                disabled={updatePhotoMutation.isPending}
              >
                {updatePhotoMutation.isPending ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                Salvar
              </Button>
              <Button variant="outline" onClick={() => setEditingPhoto(null)}>
                Cancelar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default HorsePhotoGallery;