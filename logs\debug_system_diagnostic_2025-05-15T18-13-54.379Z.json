{"timestamp": "2025-05-15T18:13:54.379Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 330268672, "heapTotal": 192077824, "heapUsed": 169488272, "external": 14591081, "arrayBuffers": 1624619}, "uptime": 74.292988928, "cpuUsage": {"user": 16707687, "system": 1034441}, "resourceUsage": {"userCPUTime": 16707758, "systemCPUTime": 1034441, "maxRSS": 898956, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 262850, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 536, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 24352, "involuntaryContextSwitches": 12516}}