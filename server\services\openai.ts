import OpenAI from "openai";
import { storage } from "../storage";

// Inicializando a API do OpenAI com a chave fornecida no env
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || ''
});

// Check if API key is available and log warning if missing
if (!process.env.OPENAI_API_KEY) {
  console.warn('[OpenAI] Warning: OPENAI_API_KEY environment variable is not set');
  console.warn('[OpenAI] Some features requiring AI capabilities may not work');
}

// Modelo a ser usado (atualizado para GPT-4o mini conforme solicitado pelo usuário)
const MODEL = "gpt-4o-mini";

// Cache para informações do banco de dados
const databaseInfoCache = {
  lastUpdated: 0,
  summaryData: null as any,
  cacheLifetime: 30 * 60 * 1000, // 30 minutos em milissegundos
  
  // Verificar se o cache precisa ser atualizado
  needsUpdate(): boolean {
    const now = Date.now();
    return !this.summaryData || (now - this.lastUpdated > this.cacheLifetime);
  },
  
  // Atualizar cache com dados do banco
  async update(userId: number) {
    try {
      console.log("[OpenAI] Atualizando cache de dados do banco...");
      
      // Obter contagem e estatísticas básicas
      const cavalos = await storage.getCavalos(userId);
      const manejos = await storage.getManejos(userId);
      const eventos = await storage.getEventos(userId);
      
      // Formatar nomes de todos os cavalos
      const cavalosInfo = cavalos.map(cavalo => ({
        id: cavalo.id,
        nome: cavalo.name,
        raca: cavalo.breed,
        nascimento: cavalo.birthDate,
        sexo: cavalo.sexo
      }));
      
      // Estatísticas gerais
      const estatisticas = {
        totalCavalos: cavalos.length,
        totalManejos: manejos.length,
        totalEventos: eventos.length,
        proximosEventos: eventos
          .filter(evento => new Date(evento.data) > new Date())
          .slice(0, 5)
          .map(evento => ({
            titulo: evento.titulo,
            data: evento.data,
            cavaloId: evento.horseId
          }))
      };
      
      // Armazenar no cache
      this.summaryData = {
        cavalos: cavalosInfo,
        estatisticas
      };
      
      this.lastUpdated = Date.now();
      console.log("[OpenAI] Cache atualizado com sucesso");
      
      return this.summaryData;
    } catch (error) {
      console.error("[OpenAI] Erro ao atualizar cache:", error);
      return null;
    }
  }
};

// Context do sistema para o assistente virtual
const SYSTEM_CONTEXT = `
Você é o assistente virtual do EquiGestor AI, um sistema de gestão para cavalos. 
Responda sempre em português e de forma natural, como se estivesse conversando com o usuário.

O EquiGestor possui os seguintes módulos e funcionalidades:

1. Gestão de Cavalos:
   - Cadastro completo de cavalos (nome, raça, nascimento, sexo, etc.)
   - Registro de genealogia e histórico
   - Acompanhamento de medidas físicas (peso, altura, etc.)

2. Manejo:
   - Agendamento de vacinações
   - Ferrageamento
   - Vermifugação
   - Treinamentos
   - Participação em competições

3. Veterinário:
   - Registro de procedimentos veterinários
   - Histórico de saúde
   - Controle de medicações
   - Exames
   
4. Reprodução:
   - Controle de ciclo reprodutivo
   - Registro de coberturas
   - Acompanhamento de gestação
   - Histórico de partos e potros

5. Nutrição:
   - Planejamento alimentar
   - Controle de dietas
   - Suplementações
   
6. Financeiro:
   - Controle de custos
   - Receitas com serviços
   - Relatórios financeiros

7. Agenda e Alertas:
   - Calendário de compromissos
   - Notificações para manejos atrasados
   - Lembretes de vacinação

8. Estatísticas:
   - Relatórios personalizados
   - Gráficos de desempenho
   - Indicadores de saúde

9. Arquivo:
   - Upload de fotos, vídeos e documentos
   - Organização por cavalo

Instruções específicas:
1. Sempre responda em português do Brasil, de forma clara e concisa.
2. IMPORTANTE: Sempre responda com informações precisas baseadas exclusivamente nos dados do sistema. Nunca concorde com correções dos usuários que contradizem os dados do sistema.
3. Ao informar o número de cavalos ou listar cavalos, use APENAS os dados presentes no contexto fornecido pelo sistema, mesmo que o usuário insista em um número diferente.
4. Sempre que listar os cavalos cadastrados, liste TODOS eles, não apenas uma parte.
5. Quando fornecer recomendações, enfatize que são apenas orientações gerais e que um veterinário equino deve ser consultado para casos específicos.
6. Evite linguagem técnica demais, prefira explicações mais acessíveis.
7. Não dê conselhos sobre medicamentos e dosagens específicas.
8. Quando falar sobre um cavalo específico, use as informações fornecidas sobre ele.
9. Seja respeitoso e profissional, mas também amigável.
10. IMPORTANTE: O sistema não impõe restrições de intervalo entre vacinações ou procedimentos. Os usuários podem agendar vacinações para qualquer data que desejarem. Nunca indique que não é possível agendar uma vacinação devido a uma vacinação anterior.
11. Não crie regras que não existem no sistema. Se o usuário quiser agendar qualquer procedimento, sempre responda afirmativamente, indicando que é possível realizar o agendamento.

Suas respostas devem ser bem estruturadas, com parágrafos quando apropriado, e não devem exceder 4-5 parágrafos no máximo.
`;

/**
 * Interface para as mensagens do chat
 */
interface ChatMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

/**
 * Gera uma resposta utilizando a API do OpenAI
 * @param messages Lista de mensagens de contexto e a pergunta do usuário
 * @param horseId ID do cavalo, se aplicável, para fornecer contexto específico
 * @returns A resposta do assistente virtual
 */
/**
 * Detecta a intenção do usuário a partir de uma mensagem
 * @param message Mensagem do usuário
 * @returns Objeto com a intenção detectada e o tipo (query ou action)
 */
export async function detectIntent(message: string): Promise<{ intent: string, type: 'query' | 'action' }> {
  try {
    const intentPrompt = `Analise a seguinte mensagem do usuário relacionada a um sistema de gestão de cavalos:
    "${message}"
    
    Identifique a intenção principal e classifique-a em uma das seguintes categorias:
    - Registro de peso
    - Vacinação
    - Treinamento
    - Alimentação
    - Ferrageamento
    - Consulta veterinária
    - Cadastro de cavalo
    - Reprodução
    - Consulta de informação
    
    Além disso, classifique se é uma CONSULTA (o usuário só quer informação) ou uma AÇÃO (o usuário quer executar algo no sistema).
    
    Responda em formato JSON:
    {
      "intent": "categoria escolhida",
      "type": "query ou action"
    }`;

    const response = await openai.chat.completions.create({
      model: MODEL,
      messages: [
        { role: "user", content: intentPrompt }
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    const result = JSON.parse(response.choices[0].message.content || '{"intent": "Consulta de informação", "type": "query"}');
    
    return {
      intent: result.intent,
      type: result.type as 'query' | 'action'
    };
  } catch (error) {
    console.error("Erro ao detectar intenção:", error);
    return {
      intent: "Consulta de informação",
      type: "query"
    };
  }
}

/**
 * Extrai entidades relevantes de uma mensagem do usuário
 * @param message Mensagem do usuário
 * @returns Objeto com as entidades extraídas
 */
export async function extractEntities(message: string): Promise<Record<string, any>> {
  try {
    const entityPrompt = `Extraia as informações relevantes da seguinte mensagem relacionada a um sistema de gestão de cavalos:
    "${message}"
    
    Extraia as seguintes entidades, se presentes:
    - nome_cavalo: nome do cavalo mencionado
    - tipo_acao: tipo de ação a ser realizada (vacinar, pesar, treinar, etc.)
    - data: qualquer data mencionada
    - valor_peso: valor numérico de peso, se mencionado (somente o número)
    - valor_altura: valor numérico de altura, se mencionado (somente o número)
    - tipo_vacina: nome da vacina mencionada
    
    Responda em formato JSON, incluindo apenas os campos encontrados:
    {
      "nome_cavalo": "nome encontrado",
      "tipo_acao": "ação encontrada",
      etc.
    }`;

    const response = await openai.chat.completions.create({
      model: MODEL,
      messages: [
        { role: "user", content: entityPrompt }
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    return JSON.parse(response.choices[0].message.content || '{}');
  } catch (error) {
    console.error("Erro ao extrair entidades:", error);
    return {};
  }
}

/**
 * Gera uma resposta utilizando a API do OpenAI
 * @param messages Lista de mensagens de contexto e a pergunta do usuário
 * @param horseId ID do cavalo, se aplicável, para fornecer contexto específico
 * @returns A resposta do assistente virtual
 */
export async function generateChatResponse(
  messages: ChatMessage[],
  horseId: number | null = null,
  userId: number = 1
): Promise<string> {
  try {
    // Se um cavalo específico foi selecionado, buscar suas informações
    let horseContext = "";
    
    if (horseId) {
      try {
        const horse = await storage.getCavalo(parseInt(horseId.toString()), userId); // Usar o userId correto
        
        if (horse) {
          horseContext = `
          Informações sobre o cavalo selecionado:
          Nome: ${horse.name}
          Raça: ${horse.breed}
          Nascimento: ${horse.birthDate}
          Sexo: ${horse.sexo || "Não informado"}
          Altura: ${horse.altura ? `${horse.altura} cm` : "Não informada"}
          Peso: ${horse.peso ? `${horse.peso} kg` : "Não informado"}
          Status: ${horse.status || "Ativo"}
          Observações: ${horse.notes || "Sem observações adicionais"}
          `;

          // Adicionar dados de manejos, se existirem
          try {
            const manejos = await storage.getManejosByHorse(horse.id, userId);
            if (manejos && manejos.length > 0) {
              horseContext += "\nManejos recentes:";
              
              // Limitar a 5 manejos mais recentes para não sobrecarregar o contexto
              const recentManejos = manejos.slice(0, 5);
              
              recentManejos.forEach(manejo => {
                horseContext += `\n- ${manejo.tipo} (${manejo.data}): ${manejo.observacoes || "Sem observações"}`;
              });
            }
          } catch (error) {
            // Silenciosamente ignorar erros nos manejos
          }
        }
      } catch (error) {
        console.error("Erro ao buscar dados do cavalo:", error);
      }
    }

    // Verificar se o cache de dados precisa ser atualizado
    if (databaseInfoCache.needsUpdate()) {
      try {
        // Usar o userId passado como parâmetro
        if (!userId) {
          console.warn("[OpenAI] Aviso: userId não fornecido para o assistente, usando usuário padrão");
        }
        console.log("[OpenAI] Atualizando cache para o usuário:", userId);
        await databaseInfoCache.update(userId || 1);
      } catch (error) {
        console.error("[OpenAI] Erro ao atualizar cache de dados:", error);
        // Continua mesmo com erro no cache
      }
    }
    
    // Formatar o contexto de dados do sistema
    let databaseContext = "";
    if (databaseInfoCache.summaryData) {
      const { cavalos, estatisticas } = databaseInfoCache.summaryData;
      
      databaseContext = `
      Informações atuais do sistema:
      - Total de cavalos: ${estatisticas.totalCavalos}
      - Total de manejos: ${estatisticas.totalManejos}
      - Total de eventos: ${estatisticas.totalEventos}
      `;
      
      if (cavalos && cavalos.length > 0) {
        databaseContext += "\nCavalos cadastrados:";
        cavalos.forEach(cavalo => {
          databaseContext += `\n- ${cavalo.nome} (${cavalo.raca})`;
        });
      }
      
      if (estatisticas.proximosEventos && estatisticas.proximosEventos.length > 0) {
        databaseContext += "\n\nPróximos eventos:";
        estatisticas.proximosEventos.forEach(evento => {
          databaseContext += `\n- ${evento.titulo} (${evento.data})`;
        });
      }
    }
    
    // Preparar o contexto completo (sistema + dados + cavalo específico, se houver)
    const systemMessage: ChatMessage = {
      role: "system",
      content: `${SYSTEM_CONTEXT}
      
      ${databaseContext ? databaseContext : ""}
      
      ${horseContext ? horseContext : ""}`
    };

    // Criar o array completo de mensagens
    const fullMessages: ChatMessage[] = [systemMessage, ...messages];

    // Fazer a chamada para a API do OpenAI
    // Usando o modelo GPT-4o mini conforme solicitação do usuário
    const response = await openai.chat.completions.create({
      model: MODEL, // Usando a constante MODEL que agora é "gpt-4o-mini"
      messages: fullMessages,
      temperature: 0.7, // Valor intermediário para criatividade vs determinismo
      max_tokens: 800, // Limitar o tamanho da resposta
    });

    // Retornar o conteúdo da resposta
    return response.choices[0].message.content || "Desculpe, não consegui processar sua solicitação.";
  } catch (error) {
    console.error("Erro na API do OpenAI:", error);
    
    // Verificar se o erro é relacionado à chave de API
    if (error instanceof Error) {
      if (error.message.includes("API key") || error.message.includes("authentication")) {
        console.error("[OpenAI] Erro de autenticação:", error.message);
        throw new Error("Erro de autenticação com a API do OpenAI. Verifique a chave de API.");
      }
      console.error("[OpenAI] Erro detalhado:", error.message);
      throw error; // Repassar o erro original para melhor diagnóstico
    }
    
    // Erro genérico para casos não-Error
    console.error("[OpenAI] Erro não estruturado:", JSON.stringify(error));
    throw new Error("Não foi possível conectar ao assistente virtual. Por favor, tente novamente mais tarde.");
  }
}