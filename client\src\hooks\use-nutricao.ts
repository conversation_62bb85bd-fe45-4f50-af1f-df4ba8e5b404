import { useState, useCallback, useMemo } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Cavalo } from '@shared/schema';
import { apiRequest } from '@/lib/queryClient';

/**
 * Tipo para os registros de nutrição
 */
export interface Nutricao {
  id: number;
  userId: number;
  horseId: number;
  data: string;
  tipoAlimentacao: string;
  nomeAlimento: string;
  quantidade: number;
  unidadeMedida: string;
  frequenciaDiaria: number;
  horarios: string | null;
  custoUnitario: number;
  observacoes: string | null;
  recomendacao: string | null;
  status: string;
  fornecedor: string | null;
  createdAt: Date | null;
}

/**
 * Hook personalizado para gerenciar toda a lógica de nutrição
 * 
 * Este hook encapsula a lógica de busca, filtragem, adição, edição 
 * e remoção de registros de nutrição, seguindo as boas práticas
 * de React e utilizando TanStack Query.
 */
export function useNutricao() {
  const queryClient = useQueryClient();
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string | null>(null);

  // Consulta para buscar todos os cavalos
  const cavalosQuery = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      return await apiRequest<Cavalo[]>('GET', '/api/cavalos');
    },
  });

  // Consulta para buscar nutrições do cavalo selecionado
  const nutricoesQuery = useQuery({
    queryKey: ['/api/nutricao/horse', selectedHorseId],
    queryFn: async () => {
      if (!selectedHorseId) return [];
      return await apiRequest<Nutricao[]>('GET', `/api/nutricao/horse/${selectedHorseId}`);
    },
    enabled: !!selectedHorseId,
  });

  // Mutation para adicionar uma nutrição
  const addNutricaoMutation = useMutation({
    mutationFn: async (formData: any) => {
      return await apiRequest('POST', '/api/nutricao', formData);
    },
    onSuccess: () => {
      // Invalidar query para atualizar a lista
      queryClient.invalidateQueries({ queryKey: ['/api/nutricao/horse', selectedHorseId] });
    },
  });

  // Mutation para atualizar uma nutrição
  const updateNutricaoMutation = useMutation({
    mutationFn: async (formData: any) => {
      const { id, ...data } = formData;
      return await apiRequest('PATCH', `/api/nutricao/${id}`, data);
    },
    onSuccess: () => {
      // Invalidar query para atualizar a lista
      queryClient.invalidateQueries({ queryKey: ['/api/nutricao/horse', selectedHorseId] });
    },
  });

  // Mutation para excluir uma nutrição
  const deleteNutricaoMutation = useMutation({
    mutationFn: async (id: number) => {
      return await apiRequest('DELETE', `/api/nutricao/${id}`);
    },
    onSuccess: () => {
      // Invalidar query para atualizar a lista
      queryClient.invalidateQueries({ queryKey: ['/api/nutricao/horse', selectedHorseId] });
    },
  });

  // Encontrar o cavalo selecionado
  const selectedHorse = useMemo(() => {
    if (!selectedHorseId || !cavalosQuery.data) return null;
    return cavalosQuery.data.find(c => c.id === selectedHorseId) || null;
  }, [selectedHorseId, cavalosQuery.data]);

  // Função auxiliar para extrair os dados da nutrição da resposta da API
  const extractNutricaoData = useCallback((queryData: any): Nutricao[] => {
    if (!queryData) return [];
    
    // Verificar se os dados retornados são um array
    if (Array.isArray(queryData)) {
      return queryData;
    }
    
    // Verificar se é uma resposta da API com propriedade data
    if (queryData && typeof queryData === 'object' && 'data' in queryData && Array.isArray(queryData.data)) {
      return queryData.data;
    }
    
    // Caso não seja possível extrair os dados, retornar array vazio
    return [];
  }, []);
  
  // Filtrar nutrições com base no termo de busca e tipo de alimentação
  const nutricoesFiltradas = useMemo(() => {
    const nutricoes = extractNutricaoData(nutricoesQuery.data);

    return nutricoes.filter((n: Nutricao) => {
      const matchesTerm = !searchTerm || 
        n.nomeAlimento.toLowerCase().includes(searchTerm.toLowerCase()) ||
        n.tipoAlimentacao.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (n.fornecedor && n.fornecedor.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesFilter = !filterType || n.tipoAlimentacao === filterType;
      
      return matchesTerm && matchesFilter;
    });
  }, [nutricoesQuery.data, searchTerm, filterType, extractNutricaoData]);

  // Obter todos os tipos de alimentação disponíveis
  const tiposAlimentacao = useMemo(() => {
    const nutricoes = extractNutricaoData(nutricoesQuery.data);
    
    return Array.from(new Set(nutricoes.map((n: Nutricao) => n.tipoAlimentacao)));
  }, [nutricoesQuery.data, extractNutricaoData]);

  // Função para calcular o custo mensal de um alimento
  const calculateMonthlyCost = useCallback((quantidade: number, frequencia: number, custoUnitario: number): string => {
    const monthlyCost = quantidade * frequencia * 30 * custoUnitario;
    return monthlyCost.toFixed(2);
  }, []);

  return {
    // Estado
    selectedHorseId,
    setSelectedHorseId,
    searchTerm,
    setSearchTerm,
    filterType, 
    setFilterType,
    
    // Queries e resultados
    cavalosQuery,
    nutricoesQuery,
    selectedHorse,
    nutricoesFiltradas,
    tiposAlimentacao,
    
    // Mutations
    addNutricaoMutation,
    updateNutricaoMutation,
    deleteNutricaoMutation,
    
    // Funções utilitárias
    calculateMonthlyCost,
  };
}