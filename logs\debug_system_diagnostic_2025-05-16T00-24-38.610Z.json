{"timestamp": "2025-05-16T00:24:38.609Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 263839744, "heapTotal": 116170752, "heapUsed": 72539408, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.825186592, "cpuUsage": {"user": 2830447, "system": 377595}, "resourceUsage": {"userCPUTime": 2830484, "systemCPUTime": 377600, "maxRSS": 330712, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104963, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 56, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8157, "involuntaryContextSwitches": 2449}}