import React, { useState } from 'react';
import { useVeterinario } from '@/hooks/use-veterinario';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { useAuth } from '@/hooks/use-auth';
import { FilterControls } from '@/components/veterinario/FilterControls';
import { ProcedimentoList } from '@/components/veterinario/ProcedimentoList';
import { ProcedimentoVetForm, ProcedimentoFormValues } from '@/components/veterinario/ProcedimentoVetForm';
import { ProcedimentoVet } from '@shared/schema';
import { AuthenticationErrorHandler, isAuthenticationError } from '@/components/auth/AuthenticationErrorHandler';

/**
 * Página de Vacinações Refatorada
 * 
 * Versão melhorada da página de vacinações usando os novos componentes
 * modulares e o hook personalizado useVeterinario para uma melhor
 * estrutura de código, reutilização e manutenibilidade.
 */
export default function VacinacoesPageRefactored() {
  const veterinario = useVeterinario();
  const { user } = useAuth();
  
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedProcedimentoId, setSelectedProcedimentoId] = useState<number | null>(null);
  
  // Encontrar o procedimento selecionado para edição
  const selectedProcedimento = selectedProcedimentoId 
    ? veterinario.procedimentosQuery.data?.find((p: ProcedimentoVet) => p.id === selectedProcedimentoId) 
    : null;
  
  // Handler para visualizar detalhes do procedimento
  const handleView = (id: number) => {
    // Implementar navegação para página de detalhes
    console.log('View procedimento', id);
  };
  
  // Handler para abrir o diálogo de edição
  const handleEdit = (id: number) => {
    setSelectedProcedimentoId(id);
    setIsEditDialogOpen(true);
  };
  
  // Handler para agendar próxima vacinação
  const handleSchedule = (id: number) => {
    const procedimento = veterinario.procedimentosQuery.data?.find((p: ProcedimentoVet) => p.id === selectedProcedimentoId);
    if (procedimento) {
      // Lógica para agendar próxima vacinação
      console.log('Schedule next vaccination for', id);
    }
  };
  
  // Handler para excluir procedimento
  const handleDelete = (id: number) => {
    veterinario.deleteProcedimentoMutation.mutate(id);
  };
  
  // Handler para o envio do formulário de adição
  const handleAddSubmit = (values: ProcedimentoFormValues) => {
    veterinario.addProcedimentoMutation.mutate({
      ...values,
      userId: user?.id
    });
    setIsAddDialogOpen(false);
  };
  
  // Handler para o envio do formulário de edição
  const handleEditSubmit = (values: ProcedimentoFormValues) => {
    if (selectedProcedimentoId) {
      veterinario.updateProcedimentoMutation.mutate({
        ...values,
        id: selectedProcedimentoId
      });
      setIsEditDialogOpen(false);
      setSelectedProcedimentoId(null);
    }
  };
  
  // Configurar opções de status para o filtro
  const statusOptions = [
    { value: 'vencida', label: 'Vencidas' },
    { value: 'próxima', label: 'Próximas' },
    { value: 'válida', label: 'Válidas' }
  ];
  
  // Valores padrão para o formulário de edição
  const getEditDefaultValues = () => {
    if (!selectedProcedimento) return {};
    
    return {
      horseId: selectedProcedimento.horseId,
      tipo: selectedProcedimento.tipo,
      descricao: selectedProcedimento.descricao,
      data: selectedProcedimento.data,
      veterinario: selectedProcedimento.veterinario || '',
      crmv: selectedProcedimento.crmv || '',
      medicamentos: selectedProcedimento.medicamentos || '',
      dosagem: selectedProcedimento.dosagem || '',
      resultado: selectedProcedimento.resultado || '',
      recomendacoes: selectedProcedimento.recomendacoes || '',
      dataProximoProcedimento: selectedProcedimento.dataProximoProcedimento || '',
      observacoes: selectedProcedimento.observacoes || '',
      custo: selectedProcedimento.custo || 0,
      formaPagamento: selectedProcedimento.formaPagamento || '',
      status: selectedProcedimento.status || 'realizado',
      userId: selectedProcedimento.userId,
    };
  };
  
  // Verificar erros de autenticação
  if (
    isAuthenticationError(veterinario.procedimentosQuery.error) ||
    isAuthenticationError(veterinario.cavalosQuery.error)
  ) {
    return (
      <AuthenticationErrorHandler 
        error={veterinario.procedimentosQuery.error || veterinario.cavalosQuery.error} 
        message="Sua sessão expirou ou você não está autenticado. Por favor, faça login novamente para acessar as vacinações."
      />
    );
  }

  // Mostrar estado de loading enquanto as consultas carregam
  if (veterinario.procedimentosQuery.isLoading || veterinario.cavalosQuery.isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-6">
      <FilterControls
        title="Filtros"
        description="Filtre as vacinações por cavalo, vacina ou status"
        searchTerm={veterinario.searchTerm}
        setSearchTerm={veterinario.setSearchTerm}
        filterCavalo={veterinario.filterCavalo}
        setFilterCavalo={veterinario.setFilterCavalo}
        cavalos={veterinario.cavalosQuery.data || []}
        filterTipo={veterinario.filterTipo}
        setFilterTipo={veterinario.setFilterTipo}
        tiposOptions={veterinario.tiposVacinaUnicos.map((tipo: string) => ({ value: tipo, label: tipo }))}
        filterStatus={veterinario.filterStatus}
        setFilterStatus={veterinario.setFilterStatus}
        statusOptions={statusOptions}
      />
      
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Vacinações</h2>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Nova Vacinação
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[700px]">
            <DialogHeader>
              <DialogTitle>Adicionar Nova Vacinação</DialogTitle>
              <DialogDescription>
                Registre uma nova vacina aplicada a um animal.
              </DialogDescription>
            </DialogHeader>
            
            <ProcedimentoVetForm
              onSubmit={handleAddSubmit}
              isSubmitting={veterinario.addProcedimentoMutation.isPending}
              cavalos={veterinario.cavalosQuery.data || []}
              userId={user?.id || 0}
              procedimentoTipo="vacinacao"
            />
            
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancelar
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      {veterinario.vacinacoesFiltradas.length > 0 ? (
        <ProcedimentoList
          procedimentos={veterinario.vacinacoesFiltradas}
          getCavaloName={veterinario.getCavaloName}
          onView={handleView}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onSchedule={handleSchedule}
          formatarData={veterinario.formatarData}
          getStatus={veterinario.getStatusVacinacao}
          statusType="vacinacao"
          truncarTexto={veterinario.truncarTexto}
          colunas={['data', 'cavalo', 'tipo', 'medicamento', 'dosagem', 'veterinario', 'proximaData', 'status', 'acoes']}
        />
      ) : (
        <div className="text-center py-10">
          <p className="text-muted-foreground">Nenhuma vacinação encontrada.</p>
          <Button variant="outline" className="mt-4" onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Registrar Primeira Vacinação
          </Button>
        </div>
      )}
      
      {/* Dialog para editar vacinação */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Editar Vacinação</DialogTitle>
            <DialogDescription>
              Atualize os dados da vacinação selecionada.
            </DialogDescription>
          </DialogHeader>
          
          <ProcedimentoVetForm
            onSubmit={handleEditSubmit}
            defaultValues={getEditDefaultValues()}
            isSubmitting={veterinario.updateProcedimentoMutation.isPending}
            cavalos={veterinario.cavalosQuery.data || []}
            userId={user?.id || 0}
            procedimentoTipo="vacinacao"
          />
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => {
                setIsEditDialogOpen(false);
                setSelectedProcedimentoId(null);
              }}
            >
              Cancelar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}