import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Loader2, Edit, ChevronDown, Download, PlusCircle, LayoutDashboard, RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useState, useEffect } from "react";

// Componentes personalizados para genealogia
import { GenealogyNode } from "@/components/genetics/GenealogyTree";
import EnhancedGenealogyTree from "@/components/genetics/EnhancedGenealogyTree";
import SimpleGenealogyTree from "@/components/genetics/SimpleGenealogyTree";
import GenealogyEditForm from "@/components/genetics/GenealogyEditForm";
import ConsanguinityIndicator from "@/components/genetics/ConsanguinityIndicator";
import GenealogyViewer from "@/components/genetics/GenealogyViewer";
import { SimpleGenealogy } from "@/components/SimpleGenealogy";

// Hook personalizado para dados de genealogia
import { useGenealogyData } from "@/hooks/use-genealogy-data";

export default function GenealogiaPage() {
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<"card" | "full">("card");

  // Utilizando o hook personalizado para gerenciar os dados e lógica da genealogia
  const {
    selectedHorseId,
    setSelectedHorseId,
    selectedHorse,
    isEditDialogOpen,
    setIsEditDialogOpen,
    cavalos,
    genealogia,
    isLoadingCavalos,
    isLoadingGenealogia,
    handleEditGenealogia,
    handleParentClick,
    convertToGenealogyNode,
  } = useGenealogyData();

  // Manipulador para exportar genealogia
  const handleExportarGenealogia = () => {
    toast({
      title: "Exportação em desenvolvimento",
      description: "A exportação de dados genealógicos será disponibilizada em breve.",
    });
  };

  // Depuração: Verificar dados retornados
  useEffect(() => {
    console.log("GenealogiaPage - Cavalos:", cavalos);
    console.log("GenealogiaPage - SelectedHorse:", selectedHorse);
    console.log("GenealogiaPage - Genealogia:", genealogia);
    console.log("GenealogiaPage - IsLoadingCavalos:", isLoadingCavalos);
  }, [cavalos, selectedHorse, genealogia, isLoadingCavalos]);

  // Renderizar carregamento
  if (isLoadingCavalos) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Carregando cavalos...</span>
      </div>
    );
  }

  // Constrói a árvore genealógica usando os dados do cavalo selecionado
  const rootNode = selectedHorse ? convertToGenealogyNode(selectedHorse) : null;

  return (
    <div className="space-y-6 max-w-[1400px] mx-auto px-4">
      {/* Diálogo de edição de genealogia */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>
              {genealogia ? "Editar Genealogia" : "Cadastrar Genealogia"}
            </DialogTitle>
            <DialogDescription>
              {selectedHorse?.name
                ? `Gerencie informações genealógicas de ${selectedHorse.name}`
                : "Adicione informações sobre os ancestrais deste animal."}
            </DialogDescription>
          </DialogHeader>
          {selectedHorseId && (
            <GenealogyEditForm
              horseId={selectedHorseId}
              cavalos={Array.isArray(cavalos) ? cavalos : []}
              genealogiaExistente={genealogia}
              onClose={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Cabeçalho da página e controles principais */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Genealogia e Pedigree</h1>
          <p className="text-muted-foreground">
            Gerencie e visualize a árvore genealógica dos seus animais
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setViewMode(viewMode === "card" ? "full" : "card")}
          >
            <LayoutDashboard className="h-4 w-4 mr-2" />
            {viewMode === "card" ? "Visualização Completa" : "Visualização Compacta"}
          </Button>
          <Button variant="outline" onClick={handleExportarGenealogia}>
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button onClick={handleEditGenealogia} disabled={!selectedHorseId}>
            <Edit className="h-4 w-4 mr-2" />
            {genealogia ? "Editar" : "Cadastrar"}
          </Button>

        </div>
      </div>

      {/* Seleção de animal */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Selecione um Animal</CardTitle>
          <CardDescription>
            Escolha o animal para visualizar sua árvore genealógica
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row md:items-center gap-4">
            <div className="w-full md:w-1/2">
              <Select
                value={selectedHorseId?.toString() || ""}
                onValueChange={(value) => setSelectedHorseId(Number(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um cavalo" />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(cavalos) &&
                    cavalos.map((cavalo) => (
                      <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                        {cavalo.name} {cavalo.breed ? `(${cavalo.breed})` : ""}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            {selectedHorse && (
              <div className="flex-1">
                <div className="flex flex-wrap gap-2">
                  {selectedHorse.breed && (
                    <Badge variant="outline" className="bg-primary/10">
                      {selectedHorse.breed}
                    </Badge>
                  )}
                  {selectedHorse.sexo && (
                    <Badge
                      variant="outline"
                      className={selectedHorse.sexo === "M" ? "bg-blue-50" : "bg-pink-50"}
                    >
                      {selectedHorse.sexo === "M" ? "Macho" : "Fêmea"}
                    </Badge>
                  )}
                  {selectedHorse.cor && (
                    <Badge variant="outline" className="bg-gray-50">
                      {selectedHorse.cor}
                    </Badge>
                  )}
                  {selectedHorse.dataNascimento && (
                    <Badge variant="outline" className="bg-gray-50">
                      Nascimento: {new Date(selectedHorse.dataNascimento).toLocaleDateString()}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Conteúdo principal */}
      {selectedHorseId && (
        <>
          {viewMode === "card" ? (
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
              {/* Árvore Genealógica */}
              <div className="lg:col-span-8">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle>Árvore Genealógica</CardTitle>
                      {genealogia?.consanguinidade != null && (
                        <ConsanguinityIndicator
                          value={Number(genealogia.consanguinidade) / 100}
                          size="md"
                        />
                      )}
                    </div>
                    <CardDescription>
                      Visualize os ancestrais do animal até a 3ª geração
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isLoadingGenealogia ? (
                      <div className="flex items-center justify-center py-10">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        <span className="ml-2">Carregando genealogia...</span>
                      </div>
                    ) : selectedHorse && selectedHorse.id ? (
                      <SimpleGenealogy cavaloId={selectedHorse.id} />
                    ) : (
                      <div className="border rounded-md p-6 bg-muted/20 flex flex-col items-center justify-center">
                        <Loader2 className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-1">Árvore Genealógica Vazia</h3>
                        <p className="text-sm text-muted-foreground text-center max-w-md mb-4">
                          Nenhum dado genealógico disponível. Cadastre a genealogia para visualizar.
                        </p>
                        <Button onClick={handleEditGenealogia}>
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Cadastrar Genealogia
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Análise de Consanguinidade */}
              <div className="lg:col-span-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Análise de Consanguinidade</CardTitle>
                    <CardDescription>
                      Avaliação do coeficiente de consanguinidade
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {genealogia ? (
                      <div className="space-y-6">
                        <div className="flex justify-center">
                          <ConsanguinityIndicator
                            value={genealogia.consanguinidade ? Number(genealogia.consanguinidade) / 100 : 0}
                            size="lg"
                            showLabel={true}
                          />
                        </div>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-1">
                              Nível de Consanguinidade
                            </h4>
                            <p className="text-sm">
                              {genealogia.consanguinidade === 0
                                ? "Não há consanguinidade detectada."
                                : genealogia.consanguinidade <= 3.125
                                ? "Nível baixo de consanguinidade, dentro dos padrões aceitáveis para cruzamentos."
                                : genealogia.consanguinidade <= 12.5
                                ? "Nível moderado de consanguinidade. Recomenda-se monitorar próximos cruzamentos."
                                : "Nível alto de consanguinidade. Recomenda-se evitar novos cruzamentos endogâmicos."}
                            </p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-1">Pedigree Completo</h4>
                            <p className="text-sm">
                              {genealogia.bisavosConhecidos !== undefined
                                ? `${((genealogia.bisavosConhecidos / 8) * 100).toFixed(0)}% completo (${
                                    genealogia.bisavosConhecidos
                                  }/8 bisavós conhecidos)`
                                : "Informação não disponível"}
                            </p>
                          </div>
                          {genealogia.observacoes && (
                            <Collapsible>
                              <CollapsibleTrigger className="flex items-center w-full text-sm font-medium text-gray-500">
                                <ChevronDown className="h-4 w-4 mr-2" />
                                Observações
                              </CollapsibleTrigger>
                              <CollapsibleContent className="mt-2 text-sm bg-muted/20 p-2 rounded">
                                {genealogia.observacoes}
                              </CollapsibleContent>
                            </Collapsible>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground text-sm">
                        <p className="mb-4">Nenhum dado genealógico cadastrado</p>
                        <Button variant="outline" onClick={handleEditGenealogia}>
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Cadastrar Genealogia
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Árvore Genealógica Completa</CardTitle>
                  {genealogia?.consanguinidade != null && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">Consanguinidade:</span>
                      <ConsanguinityIndicator
                        value={Number(genealogia.consanguinidade) / 100}
                        size="md"
                      />
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {isLoadingGenealogia ? (
                  <div className="flex items-center justify-center py-20">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">Carregando árvore genealógica...</span>
                  </div>
                ) : rootNode ? (
                  <EnhancedGenealogyTree
                    rootNode={rootNode}
                    onNodeClick={handleParentClick}
                    maxGenerations={4}
                    className="h-[700px] w-full"
                  />
                ) : (
                  <div className="text-center py-20 px-4">
                    <p className="text-muted-foreground mb-4">
                      Nenhum dado genealógico cadastrado para este animal
                    </p>
                    <Button onClick={handleEditGenealogia}>
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Cadastrar Genealogia
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}