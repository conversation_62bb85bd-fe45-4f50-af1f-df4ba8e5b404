{"timestamp": "2025-05-16T23:23:45.021Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405229568, "heapTotal": 119070720, "heapUsed": 91482504, "external": 8370083, "arrayBuffers": 243725}, "uptime": 1.892186939, "cpuUsage": {"user": 2812630, "system": 380030}, "resourceUsage": {"userCPUTime": 2812676, "systemCPUTime": 380036, "maxRSS": 395860, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104996, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8836, "involuntaryContextSwitches": 5271}}