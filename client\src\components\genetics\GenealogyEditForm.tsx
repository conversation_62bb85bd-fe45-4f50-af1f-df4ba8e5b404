import React, { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Loader2, AlertTriangle, Save, Upload, Image as ImageIcon, X } from 'lucide-react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

// Utilitário para fazer requisições à API
import { apiRequest } from '@/lib/queryClient';
import { Genealogia, ImagemAncestral } from '@/types/genetica';

interface GenealogyEditFormProps {
  cavaloId: number;
}

// Schema de validação do formulário
const formSchema = z.object({
  // Selects para pais
  paiId: z.string().optional(),
  maeId: z.string().optional(),
  
  // Selects para avós paternos
  avoPaternoId: z.string().optional(),
  avoPaternaId: z.string().optional(),
  
  // Selects para avós maternos
  avoMaternoId: z.string().optional(),
  avoMaternaId: z.string().optional(),
  
  // Selects para bisavós paternos (pai do pai)
  bisavoPaternoPaterno: z.string().optional(),
  bisavoPaternaPaterno: z.string().optional(),
  
  // Selects para bisavós paternos (pai da mãe)
  bisavoMaternoPaterno: z.string().optional(),
  bisavoMaternaPaterno: z.string().optional(),
  
  // Selects para bisavós maternos (mãe do pai)
  bisavoPaternoMaterno: z.string().optional(),
  bisavoPaternaMaterno: z.string().optional(),
  
  // Selects para bisavós maternos (mãe da mãe)
  bisavoMaternoMaterno: z.string().optional(),
  bisavoMaternaMaterno: z.string().optional(),
  
  // Observações
  observacoes: z.string().optional(),
  
  // Campo para exibir na página do cavalo (sim/não)
  exibirNoSite: z.boolean().default(true),
});

type FormValues = z.infer<typeof formSchema>;

const GenealogyEditForm: React.FC<GenealogyEditFormProps> = ({
  horseId,
  cavalos,
  genealogiaExistente,
  onClose,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('pais');
  const [ancestorImages, setAncestorImages] = useState<ImagemAncestral[]>(
    genealogiaExistente?.imagensAncestral || []
  );
  const [selectedCavalo, setSelectedCavalo] = useState<number | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageDescription, setImageDescription] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  
  // Referência para o input de arquivo
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // Filtro para não mostrar o próprio cavalo nas opções de parentes
  const cavalosFiltered = cavalos?.filter(c => c.id !== horseId) || [];

  // Configurar valores default com base nos dados existentes
  const defaultValues = {
    paiId: genealogiaExistente?.pai?.id?.toString() || "",
    maeId: genealogiaExistente?.mae?.id?.toString() || "",
    avoPaternoId: genealogiaExistente?.avoPaterno?.id?.toString() || "",
    avoPaternaId: genealogiaExistente?.avoPaterna?.id?.toString() || "",
    avoMaternoId: genealogiaExistente?.avoMaterno?.id?.toString() || "",
    avoMaternaId: genealogiaExistente?.avoMaterna?.id?.toString() || "",
    bisavoPaternoPaterno: genealogiaExistente?.bisavoPaternoPaternoId?.toString() || "",
    bisavoPaternaPaterno: genealogiaExistente?.bisavoPaternaPaternoId?.toString() || "",
    bisavoMaternoPaterno: genealogiaExistente?.bisavoMaternoPaternoId?.toString() || "",
    bisavoMaternaPaterno: genealogiaExistente?.bisavoMaternaPaternoId?.toString() || "",
    bisavoPaternoMaterno: genealogiaExistente?.bisavoPaternoMaternoId?.toString() || "",
    bisavoPaternaMaterno: genealogiaExistente?.bisavoPaternaMaternoId?.toString() || "",
    bisavoMaternoMaterno: genealogiaExistente?.bisavoMaternoMaternoId?.toString() || "",
    bisavoMaternaMaterno: genealogiaExistente?.bisavoMaternaMaternoId?.toString() || "",
    observacoes: genealogiaExistente?.observacoes || "",
    exibirNoSite: genealogiaExistente?.exibirNoSite !== false, // padrão true
  };

  // Inicializar formulário
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  // Verificar consanguinidade potencial
  const checkConsanguinity = (data: FormValues) => {
    // Verificar se o mesmo animal aparece mais de uma vez
    const ids = [
      data.paiId, 
      data.maeId,
      data.avoPaternoId,
      data.avoPaternaId,
      data.avoMaternoId,
      data.avoMaternaId,
    ].filter(id => id && id !== "");
    
    const uniqueIds = new Set(ids);
    return uniqueIds.size < ids.length;
  };

  // Função auxiliar para converter valores do formulário
  const convertValue = (value: string | null | undefined): number | null => {
    if (!value || value === "_none") return null;
    return parseInt(value);
  };
  
  // Mutação para criar/atualizar a genealogia
  const mutation = useMutation({
    mutationFn: async (data: FormValues) => {
      // Converter IDs de string para número ou null
      const convertedData = {
        horseId,
        paiId: convertValue(data.paiId),
        maeId: convertValue(data.maeId),
        avoPaternoId: convertValue(data.avoPaternoId),
        avoPaternaId: convertValue(data.avoPaternaId),
        avoMaternoId: convertValue(data.avoMaternoId),
        avoMaternaId: convertValue(data.avoMaternaId),
        bisavoPaternoPaterno: convertValue(data.bisavoPaternoPaterno),
        bisavoPaternaPaterno: convertValue(data.bisavoPaternaPaterno),
        bisavoMaternoPaterno: convertValue(data.bisavoMaternoPaterno),
        bisavoMaternaPaterno: convertValue(data.bisavoMaternaPaterno),
        bisavoPaternoMaterno: convertValue(data.bisavoPaternoMaterno),
        bisavoPaternaMaterno: convertValue(data.bisavoPaternaMaterno),
        bisavoMaternoMaterno: convertValue(data.bisavoMaternoMaterno),
        bisavoMaternaMaterno: convertValue(data.bisavoMaternaMaterno),
        observacoes: data.observacoes,
        exibirNoSite: data.exibirNoSite,
        imagensAncestral: ancestorImages, // Adiciona imagens dos ancestrais
      };

      // Se existe genealogia, atualiza; se não, cria
      if (genealogiaExistente?.id) {
        return await apiRequest('PUT', `/api/genealogia/${genealogiaExistente.id}`, convertedData);
      } else {
        return await apiRequest('POST', '/api/genealogia', convertedData);
      }
    },
    onSuccess: () => {
      toast({
        title: "Genealogia salva com sucesso",
        description: "Os dados genealógicos foram atualizados.",
      });
      // Invalidar cache para recarregar os dados
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos', horseId, 'genealogia'] });
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao salvar genealogia",
        description: error.message || "Não foi possível salvar os dados genealógicos.",
        variant: "destructive",
      });
    },
  });

  // Manipulador de envio do formulário
  const onSubmit = (values: FormValues) => {
    if (checkConsanguinity(values)) {
      if (!window.confirm('Detectamos possível consanguinidade nesta árvore genealógica. Deseja continuar mesmo assim?')) {
        return;
      }
    }
    mutation.mutate(values);
  };

  // Renderizar o formulário
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 overflow-y-auto max-h-[70vh] px-1">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="pais">Pais</TabsTrigger>
            <TabsTrigger value="avos">Avós</TabsTrigger>
            <TabsTrigger value="bisavos">Bisavós</TabsTrigger>
            <TabsTrigger value="imagens">Imagens</TabsTrigger>
          </TabsList>
          
          <TabsContent value="pais" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="paiId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pai</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o pai" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="_none">Não informado</SelectItem>
                        {cavalosFiltered
                          .filter(c => c.sexo === 'masculino')
                          .map(cavalo => (
                            <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                              {cavalo.name} ({cavalo.breed || 'Raça não informada'})
                            </SelectItem>
                          ))
                        }
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Selecione o pai deste animal
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="maeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mãe</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione a mãe" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="_none">Não informada</SelectItem>
                        {cavalosFiltered
                          .filter(c => c.sexo === 'feminino')
                          .map(cavalo => (
                            <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                              {cavalo.name} ({cavalo.breed || 'Raça não informada'})
                            </SelectItem>
                          ))
                        }
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Selecione a mãe deste animal
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setActiveTab('avos')}
              className="w-full"
            >
              Avançar para Avós
            </Button>
          </TabsContent>
          
          <TabsContent value="avos" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Lado Paterno</h3>
                <FormField
                  control={form.control}
                  name="avoPaternoId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Avô Paterno</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o avô paterno" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">Não informado</SelectItem>
                          {cavalosFiltered
                            .filter(c => c.sexo === 'masculino')
                            .map(cavalo => (
                              <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                                {cavalo.name} ({cavalo.breed || 'Raça não informada'})
                              </SelectItem>
                            ))
                          }
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="avoPaternaId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Avó Paterna</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a avó paterna" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">Não informada</SelectItem>
                          {cavalosFiltered
                            .filter(c => c.sexo === 'feminino')
                            .map(cavalo => (
                              <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                                {cavalo.name} ({cavalo.breed || 'Raça não informada'})
                              </SelectItem>
                            ))
                          }
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Lado Materno</h3>
                <FormField
                  control={form.control}
                  name="avoMaternoId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Avô Materno</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o avô materno" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">Não informado</SelectItem>
                          {cavalosFiltered
                            .filter(c => c.sexo === 'masculino')
                            .map(cavalo => (
                              <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                                {cavalo.name} ({cavalo.breed || 'Raça não informada'})
                              </SelectItem>
                            ))
                          }
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="avoMaternaId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Avó Materna</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a avó materna" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">Não informada</SelectItem>
                          {cavalosFiltered
                            .filter(c => c.sexo === 'feminino')
                            .map(cavalo => (
                              <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                                {cavalo.name} ({cavalo.breed || 'Raça não informada'})
                              </SelectItem>
                            ))
                          }
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            
            <div className="flex space-x-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setActiveTab('pais')} 
                className="flex-1"
              >
                Voltar para Pais
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setActiveTab('bisavos')} 
                className="flex-1"
              >
                Avançar para Bisavós
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="bisavos" className="space-y-4">
            <FormField
              control={form.control}
              name="observacoes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Observações sobre a Genealogia</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Adicione informações adicionais sobre a linhagem do animal" 
                      {...field} 
                      className="h-24"
                    />
                  </FormControl>
                  <FormDescription>
                    Estas observações podem ser exibidas na ficha do animal
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="flex space-x-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setActiveTab('avos')}
                className="flex-1"
              >
                Voltar para Avós
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setActiveTab('imagens')}
                className="flex-1"
              >
                Ir para Imagens
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="imagens" className="space-y-4">
            <div className="bg-muted/40 p-4 rounded-lg border">
              <h3 className="text-base font-medium mb-4">Imagens dos Ancestrais</h3>
              
              <div className="grid gap-6">
                {/* Seletor de cavalo para associar imagem */}
                <div className="space-y-2">
                  <Label>Selecione um cavalo para adicionar imagem</Label>
                  <Select 
                    value={selectedCavalo?.toString() || "_none"} 
                    onValueChange={(value) => {
                      if (value && value !== "_none") {
                        setSelectedCavalo(parseInt(value));
                      } else {
                        setSelectedCavalo(null);
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Escolha um cavalo da árvore" />
                    </SelectTrigger>
                    <SelectContent>
                      {/* Usamos "_none" como valor não vazio para representar "nenhuma seleção" */}
                      <SelectItem value="_none">Selecione um cavalo</SelectItem>
                      {/* Cavalo atual */}
                      <SelectItem value={horseId.toString()}>
                        {cavalos.find(c => c.id === horseId)?.name || "Cavalo Atual"} (Principal)
                      </SelectItem>
                      
                      {/* Opções para os pais */}
                      {form.watch('paiId') && form.watch('paiId') !== "" && (
                        <SelectItem value={form.watch('paiId') as string}>
                          {cavalosFiltered.find(c => c.id.toString() === form.watch('paiId'))?.name || ""} (Pai)
                        </SelectItem>
                      )}
                      {form.watch('maeId') && form.watch('maeId') !== "" && (
                        <SelectItem value={form.watch('maeId') as string}>
                          {cavalosFiltered.find(c => c.id.toString() === form.watch('maeId'))?.name || ""} (Mãe)
                        </SelectItem>
                      )}
                      
                      {/* Opções para os avós */}
                      {form.watch('avoPaternoId') && form.watch('avoPaternoId') !== "" && (
                        <SelectItem value={form.watch('avoPaternoId') as string}>
                          {cavalosFiltered.find(c => c.id.toString() === form.watch('avoPaternoId'))?.name || ""} (Avô Paterno)
                        </SelectItem>
                      )}
                      {form.watch('avoPaternaId') && form.watch('avoPaternaId') !== "" && (
                        <SelectItem value={form.watch('avoPaternaId') as string}>
                          {cavalosFiltered.find(c => c.id.toString() === form.watch('avoPaternaId'))?.name || ""} (Avó Paterna)
                        </SelectItem>
                      )}
                      {form.watch('avoMaternoId') && form.watch('avoMaternoId') !== "" && (
                        <SelectItem value={form.watch('avoMaternoId') as string}>
                          {cavalosFiltered.find(c => c.id.toString() === form.watch('avoMaternoId'))?.name || ""} (Avô Materno)
                        </SelectItem>
                      )}
                      {form.watch('avoMaternaId') && form.watch('avoMaternaId') !== "" && (
                        <SelectItem value={form.watch('avoMaternaId') as string}>
                          {cavalosFiltered.find(c => c.id.toString() === form.watch('avoMaternaId'))?.name || ""} (Avó Materna)
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                {selectedCavalo && (
                  <div className="space-y-4">
                    {/* Upload de imagem */}
                    <div className="space-y-2">
                      <Label>Imagem do animal</Label>
                      <div className="flex items-center gap-4">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="flex items-center gap-2"
                          disabled={isUploading}
                        >
                          <Upload className="h-4 w-4" />
                          {isUploading ? 'Enviando...' : 'Selecionar Imagem'}
                        </Button>
                        <Input
                          type="file"
                          ref={fileInputRef}
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              // Criar URL temporária para preview
                              const previewUrl = URL.createObjectURL(file);
                              setImagePreview(previewUrl);
                              
                              // Aqui você adicionaria o código para upload da imagem
                              // Por enquanto vamos apenas simular o processo
                              setIsUploading(true);
                              setTimeout(() => {
                                // Simulando um upload bem-sucedido com URL fictícia
                                const imageUrl = previewUrl; // Na implementação real, seria a URL do servidor
                                
                                // Adicionar à lista de imagens de ancestrais
                                const existingIndex = ancestorImages.findIndex(img => img.cavaloId === selectedCavalo);
                                if (existingIndex >= 0) {
                                  // Atualizar imagem existente
                                  const updatedImages = [...ancestorImages];
                                  updatedImages[existingIndex] = {
                                    ...updatedImages[existingIndex],
                                    imageUrl,
                                    descricao: imageDescription
                                  };
                                  setAncestorImages(updatedImages);
                                } else {
                                  // Adicionar nova imagem
                                  setAncestorImages([
                                    ...ancestorImages,
                                    {
                                      cavaloId: selectedCavalo,
                                      imageUrl,
                                      descricao: imageDescription
                                    }
                                  ]);
                                }
                                
                                setIsUploading(false);
                                toast({
                                  title: "Imagem adicionada com sucesso",
                                  description: "A imagem foi associada ao animal na árvore genealógica."
                                });
                              }, 1000);
                            }
                          }}
                        />
                      </div>
                    </div>
                    
                    {/* Descrição da imagem */}
                    <div className="space-y-2">
                      <Label>Descrição da imagem</Label>
                      <Textarea
                        placeholder="Adicione uma descrição para esta imagem"
                        value={imageDescription}
                        onChange={(e) => setImageDescription(e.target.value)}
                        className="h-20"
                      />
                    </div>
                    
                    {/* Preview da imagem */}
                    {imagePreview && (
                      <div className="relative p-2 border rounded-md">
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute top-2 right-2 h-6 w-6 bg-background/80 hover:bg-background"
                          onClick={() => setImagePreview(null)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-full h-auto max-h-60 object-contain rounded-md"
                        />
                      </div>
                    )}
                  </div>
                )}
                
                {/* Lista de imagens já adicionadas */}
                {ancestorImages.length > 0 && (
                  <div className="space-y-2 mt-4">
                    <h4 className="text-sm font-medium">Imagens adicionadas</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {ancestorImages.map((img, index) => {
                        const cavalo = [
                          ...(cavalos || []),
                          ...(cavalosFiltered || [])
                        ].find(c => c.id === img.cavaloId);
                        const cavaloNome = cavalo ? cavalo.name : (img.cavaloId === horseId ? "Cavalo Atual" : "Desconhecido");
                        
                        return (
                          <Card key={index} className="overflow-hidden">
                            <CardContent className="p-3">
                              <div className="flex items-center gap-3">
                                <Avatar className="h-10 w-10 rounded-md">
                                  <AvatarImage src={img.imageUrl} alt={cavaloNome} />
                                  <AvatarFallback><ImageIcon className="h-4 w-4" /></AvatarFallback>
                                </Avatar>
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium truncate">{cavaloNome}</p>
                                  {img.descricao && (
                                    <p className="text-xs text-muted-foreground truncate">{img.descricao}</p>
                                  )}
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="h-7 w-7"
                                  onClick={() => {
                                    setAncestorImages(ancestorImages.filter((_, i) => i !== index));
                                    toast({
                                      title: "Imagem removida",
                                      description: "A imagem foi removida da árvore genealógica."
                                    });
                                  }}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex justify-between">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setActiveTab('bisavos')}
              >
                Voltar para Bisavós
              </Button>
              <FormField
                control={form.control}
                name="exibirNoSite"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                    <FormControl>
                      <div className="hidden">
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </TabsContent>
        </Tabs>
        
        {checkConsanguinity(form.watch()) && (
          <div className="flex items-center text-amber-600 bg-amber-50 p-2 rounded-md text-sm">
            <AlertTriangle className="mr-2 h-4 w-4" />
            <span>
              Detectamos possível consanguinidade nesta árvore genealógica.
              Verifique se os mesmos animais não estão aparecendo em diferentes posições da árvore.
            </span>
          </div>
        )}
        
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onClose}
            disabled={mutation.isPending}
          >
            Cancelar
          </Button>
          <Button 
            type="submit"
            disabled={mutation.isPending}
          >
            {mutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Salvando...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Salvar Genealogia
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default GenealogyEditForm;