{"timestamp": "2025-05-16T00:14:09.516Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 282697728, "heapTotal": 109879296, "heapUsed": 72567712, "external": 8219482, "arrayBuffers": 243725}, "uptime": 3.309215245, "cpuUsage": {"user": 3145449, "system": 450247}, "resourceUsage": {"userCPUTime": 3145491, "systemCPUTime": 450254, "maxRSS": 295112, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102586, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 88, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8197, "involuntaryContextSwitches": 11868}}