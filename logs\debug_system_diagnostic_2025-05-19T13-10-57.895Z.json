{"timestamp": "2025-05-19T13:10:57.894Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 257789952, "heapTotal": 115798016, "heapUsed": 88973064, "external": 8279891, "arrayBuffers": 235533}, "uptime": 4.513826887, "cpuUsage": {"user": 3265065, "system": 482130}, "resourceUsage": {"userCPUTime": 3265109, "systemCPUTime": 482136, "maxRSS": 298016, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 110549, "majorPageFault": 0, "swappedOut": 0, "fsRead": 41184, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8686, "involuntaryContextSwitches": 15495}}