import React, { useEffect, useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { GitBranchPlus, Heart, User, TreePine, Edit, Plus } from "lucide-react";
import { apiRequest } from '@/lib/queryClient';
import { Cavalo } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { extractCleanName } from '@/utils/genealogy';

interface ExpandedGenealogyProps {
  cavaloId: number;
  editable?: boolean;
}

interface HorseInfo {
  id: number | null;
  name: string;
  tipo: 'sistema' | 'externo' | 'nenhum';
  sexo?: string | null;
  breed?: string | null;
  cor?: string | null;
}

export function ExpandedGenealogy({ cavaloId, editable = false }: ExpandedGenealogyProps) {
  const [cavalo, setCavalo] = useState<Cavalo | null>(null);
  const [pai, setPai] = useState<Cavalo | null>(null);
  const [mae, setMae] = useState<Cavalo | null>(null);
  const [avoPaternoData, setAvoPaternoData] = useState<Cavalo | null>(null);
  const [avoMaternoData, setAvoMaternoData] = useState<Cavalo | null>(null);
  const [avoPaternaData, setAvoPaternaData] = useState<Cavalo | null>(null);
  const [avoMaternaData, setAvoMaternaData] = useState<Cavalo | null>(null);
  const [genealogiaData, setGenealogiaData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const [, setLocation] = useLocation();

  const handleEditClick = (tipo: string) => {
    if (tipo === 'genealogia') {
      setLocation(`/cavalo/${cavaloId}/genealogia/editar`);
    } else {
      toast({
        title: "Funcionalidade em desenvolvimento",
        description: `A funcionalidade de ${tipo} será implementada em breve.`,
        duration: 4000,
      });
    }
  };

  // Função para obter informações do pai
  const getPaiInfo = (cavalo: Cavalo | null): HorseInfo => {
    if (!cavalo) return { id: null, name: 'Não informado', tipo: 'nenhum' };
    
    if (cavalo.paiId && pai) {
      return { 
        id: cavalo.paiId,
        name: pai.name, 
        tipo: 'sistema',
        sexo: pai.sexo,
        breed: pai.breed,
        cor: pai.cor
      };
    }
    
    if (cavalo.paiNome) {
      return { 
        id: null,
        name: cavalo.paiNome, 
        tipo: 'externo'
      };
    }
    
    if (cavalo.pai) {
      const isNumeric = /^\d+$/.test(cavalo.pai.toString());
      if (isNumeric && pai) {
        return { 
          id: Number(cavalo.pai),
          name: pai.name, 
          tipo: 'sistema',
          sexo: pai.sexo,
          breed: pai.breed,
          cor: pai.cor
        };
      } else {
        return { 
          id: null,
          name: cavalo.pai.toString(), 
          tipo: 'externo'
        };
      }
    }
    
    return { id: null, name: 'Não informado', tipo: 'nenhum' };
  };

  // Função para obter informações da mãe
  const getMaeInfo = (cavalo: Cavalo | null): HorseInfo => {
    if (!cavalo) return { id: null, name: 'Não informado', tipo: 'nenhum' };
    
    if (cavalo.maeId && mae) {
      return { 
        id: cavalo.maeId,
        name: mae.name, 
        tipo: 'sistema',
        sexo: mae.sexo,
        breed: mae.breed,
        cor: mae.cor
      };
    }
    
    if (cavalo.maeNome) {
      return { 
        id: null,
        name: cavalo.maeNome, 
        tipo: 'externo'
      };
    }
    
    if (cavalo.mae) {
      const isNumeric = /^\d+$/.test(cavalo.mae.toString());
      if (isNumeric && mae) {
        return { 
          id: Number(cavalo.mae),
          name: mae.name, 
          tipo: 'sistema',
          sexo: mae.sexo,
          breed: mae.breed,
          cor: mae.cor
        };
      } else {
        return { 
          id: null,
          name: cavalo.mae.toString(), 
          tipo: 'externo'
        };
      }
    }
    
    return { id: null, name: 'Não informado', tipo: 'nenhum' };
  };

  // Função para obter informações das avós femininas
  const getAvoFemininaInfo = (tipo: 'paterna' | 'materna'): HorseInfo => {
    console.log(`📊 [ExpandedGenealogy] getAvoFemininaInfo(${tipo}) - genealogiaData:`, genealogiaData);
    
    if (genealogiaData) {
      const campoId = tipo === 'paterna' ? 'avoPaternaId' : 'avoMaternaId';
      const campoNome = tipo === 'paterna' ? 'avoPaterna' : 'avoMaterna';
      
      console.log(`📊 [ExpandedGenealogy] ${tipo} - campoId: ${campoId}, valor:`, genealogiaData[campoId]);
      console.log(`📊 [ExpandedGenealogy] ${tipo} - campoNome: ${campoNome}, valor:`, genealogiaData[campoNome]);
      
      if (genealogiaData[campoId]) {
        const avoData = tipo === 'paterna' ? avoPaternaData : avoMaternaData;
        console.log(`📊 [ExpandedGenealogy] ${tipo} - avoData do sistema:`, avoData);
        if (avoData) {
          return {
            id: avoData.id,
            name: avoData.name,
            tipo: 'sistema',
            sexo: avoData.sexo,
            breed: avoData.breed,
            cor: avoData.cor
          };
        }
      }
      
      if (genealogiaData[campoNome]) {
        const cleanName = extractCleanName(genealogiaData[campoNome]);
        console.log(`📊 [ExpandedGenealogy] ${tipo} - nome externo: ${cleanName}`);
        return {
          id: null,
          name: cleanName,
          tipo: 'externo'
        };
      }
    }
    
    console.log(`📊 [ExpandedGenealogy] ${tipo} - retornando 'Não informado'`);
    return { id: null, name: 'Não informado', tipo: 'nenhum' };
  };

  // Função para obter informações dos avós (masculinos e femininos)
  const getAvoInfo = (tipo: 'paterno' | 'materno' | 'paterna' | 'materna'): HorseInfo => {
    if (genealogiaData) {
      let campoId, campoNome, avoData;
      
      switch (tipo) {
        case 'paterno':
          campoId = 'avoPaternoId';
          campoNome = 'avoPaternoName';
          avoData = avoPaternoData;
          break;
        case 'materno':
          campoId = 'avoMaternoId';
          campoNome = 'avoMaternoName';
          avoData = avoMaternoData;
          break;
        case 'paterna':
          campoId = 'avoPaternaId';
          campoNome = 'avoPaternaName';
          avoData = avoPaternaData;
          break;
        case 'materna':
          campoId = 'avoMaternaId';
          campoNome = 'avoMaternaName';
          avoData = avoMaternaData;
          break;
        default:
          return { id: null, name: 'Não informado', tipo: 'nenhum' };
      }
      
      console.log(`📊 [ExpandedGenealogy] getAvoInfo(${tipo}) - campoId: ${campoId}, valor:`, genealogiaData[campoId]);
      console.log(`📊 [ExpandedGenealogy] getAvoInfo(${tipo}) - campoNome: ${campoNome}, valor:`, genealogiaData[campoNome]);
      
      // Se tem ID, busca nos dados do sistema
      if (genealogiaData[campoId]) {
        if (avoData) {
          return {
            id: avoData.id,
            name: avoData.name,
            tipo: 'sistema',
            sexo: avoData.sexo || null,
            breed: avoData.breed || null,
            cor: avoData.cor || null
          };
        }
      }
      
      // Se tem nome, é externo
      if (genealogiaData[campoNome]) {
        const cleanName = extractCleanName(genealogiaData[campoNome]);
        console.log(`📊 [ExpandedGenealogy] ${tipo} - nome externo limpo: ${cleanName}`);
        return {
          id: null,
          name: cleanName,
          tipo: 'externo'
        };
      }
    }
    
    // Fallback: verifica dados no cavalo (campo legado) - apenas para avós masculinos
    if (cavalo && (tipo === 'paterno' || tipo === 'materno')) {
      const campoNome = tipo === 'paterno' ? 'avoPaterno' : 'avoMaterno';
      if (cavalo[campoNome as keyof Cavalo]) {
        return {
          id: null,
          name: extractCleanName(cavalo[campoNome as keyof Cavalo] as string),
          tipo: 'externo'
        };
      }
    }

    console.log(`📊 [ExpandedGenealogy] ${tipo} - retornando 'Não informado'`);
    return { id: null, name: 'Não informado', tipo: 'nenhum' };
  };

  // Carregar dados
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        const userJson = localStorage.getItem('user');
        const userId = userJson ? JSON.parse(userJson).id : null;
        
        if (!userId) {
          console.error('❌ [ExpandedGenealogy] Usuário não encontrado');
          setLoading(false);
          return;
        }
        
        const options = {
          headers: {
            'user-id': userId.toString(),
            'Content-Type': 'application/json'
          }
        };
        
        // Buscar cavalo principal
        const cavaloData = await apiRequest<Cavalo>('GET', `/api/cavalos/${cavaloId}`, undefined, options);
        setCavalo(cavaloData);
        
        // Buscar dados de genealogia
        try {
          const genealogia = await apiRequest<any>('GET', `/api/genealogia/${cavaloId}`, undefined, options);
          setGenealogiaData(genealogia);
          console.log('📊 [ExpandedGenealogy] Genealogia carregada:', genealogia);
          console.log('📊 [ExpandedGenealogy] Campos das avós:', {
            avoPaternaId: genealogia.avoPaternaId,
            avoPaterna: genealogia.avoPaterna,
            avoMaternaId: genealogia.avoMaternaId,
            avoMaterna: genealogia.avoMaterna
          });
          
          // Buscar avós diretos da genealogia se existirem IDs
          if (genealogia.avoPaternoId) {
            try {
              const avoPaterno = await apiRequest<Cavalo>('GET', `/api/cavalos/${genealogia.avoPaternoId}`, undefined, options);
              setAvoPaternoData(avoPaterno);
              console.log('📊 [ExpandedGenealogy] Avô paterno carregado:', avoPaterno);
            } catch (error) {
              console.log('Avô paterno não encontrado no sistema');
            }
          }
          
          if (genealogia.avoMaternoId) {
            try {
              const avoMaterno = await apiRequest<Cavalo>('GET', `/api/cavalos/${genealogia.avoMaternoId}`, undefined, options);
              setAvoMaternoData(avoMaterno);
              console.log('📊 [ExpandedGenealogy] Avô materno carregado:', avoMaterno);
            } catch (error) {
              console.log('Avô materno não encontrado no sistema');
            }
          }
          
          // Buscar avós femininas da genealogia se existirem IDs
          if (genealogia.avoPaternaId) {
            try {
              const avoPaterna = await apiRequest<Cavalo>('GET', `/api/cavalos/${genealogia.avoPaternaId}`, undefined, options);
              setAvoPaternaData(avoPaterna);
              console.log('📊 [ExpandedGenealogy] Avó paterna carregada:', avoPaterna);
            } catch (error) {
              console.log('Avó paterna não encontrada no sistema');
            }
          }
          
          if (genealogia.avoMaternaId) {
            try {
              const avoMaterna = await apiRequest<Cavalo>('GET', `/api/cavalos/${genealogia.avoMaternaId}`, undefined, options);
              setAvoMaternaData(avoMaterna);
              console.log('📊 [ExpandedGenealogy] Avó materna carregada:', avoMaterna);
            } catch (error) {
              console.log('Avó materna não encontrada no sistema');
            }
          }
        } catch (error) {
          console.log('📊 [ExpandedGenealogy] Nenhuma genealogia encontrada para este cavalo');
        }
        
        // Buscar pai se houver
        if (cavaloData.paiId) {
          try {
            const paiData = await apiRequest<Cavalo>('GET', `/api/cavalos/${cavaloData.paiId}`, undefined, options);
            setPai(paiData);

            // Buscar avô paterno se o pai tiver pai
            if (paiData.paiId) {
              try {
                const avoPaterno = await apiRequest<Cavalo>('GET', `/api/cavalos/${paiData.paiId}`, undefined, options);
                setAvoPaternoData(avoPaterno);
              } catch (error) {
                console.log('Avô paterno não encontrado no sistema');
              }
            }
          } catch (error) {
            console.error('❌ Erro ao buscar pai:', error);
          }
        } else if (cavaloData.pai && /^\d+$/.test(cavaloData.pai.toString())) {
          try {
            const paiData = await apiRequest<Cavalo>('GET', `/api/cavalos/${Number(cavaloData.pai)}`, undefined, options);
            setPai(paiData);
          } catch (error) {
            console.error('❌ Erro ao buscar pai (campo legado):', error);
          }
        }
        
        // Buscar mãe se houver
        if (cavaloData.maeId) {
          try {
            const maeData = await apiRequest<Cavalo>('GET', `/api/cavalos/${cavaloData.maeId}`, undefined, options);
            setMae(maeData);

            // Buscar avô materno se a mãe tiver pai
            if (maeData.paiId) {
              try {
                const avoMaterno = await apiRequest<Cavalo>('GET', `/api/cavalos/${maeData.paiId}`, undefined, options);
                setAvoMaternoData(avoMaterno);
              } catch (error) {
                console.log('Avô materno não encontrado no sistema');
              }
            }
          } catch (error) {
            console.error('❌ Erro ao buscar mãe:', error);
          }
        } else if (cavaloData.mae && /^\d+$/.test(cavaloData.mae.toString())) {
          try {
            const maeData = await apiRequest<Cavalo>('GET', `/api/cavalos/${Number(cavaloData.mae)}`, undefined, options);
            setMae(maeData);
          } catch (error) {
            console.error('❌ Erro ao buscar mãe (campo legado):', error);
          }
        }
      } catch (error) {
        console.error('❌ Erro ao buscar dados:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [cavaloId]);

  const paiInfo = getPaiInfo(cavalo);
  const maeInfo = getMaeInfo(cavalo);
  const avoPaterno = getAvoInfo('paterno');
  const avoMaterno = getAvoInfo('materno');

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  const HorseCard = ({ horse, position, isMain = false, generation = 1 }: { 
    horse: HorseInfo, 
    position: string, 
    isMain?: boolean,
    generation?: number
  }) => {
    const cardSize = generation === 1 ? 'w-48' : generation === 2 ? 'w-44' : 'w-40';
    const textSize = generation === 1 ? 'text-base' : generation === 2 ? 'text-sm' : 'text-xs';
    
    return (
      <div className={`${cardSize} transition-all duration-300 transform hover:scale-105`}>
        <Card className={`
          overflow-hidden transition-all duration-300 shadow-md hover:shadow-lg
          ${isMain 
            ? 'border-2 border-primary bg-gradient-to-br from-blue-50 to-indigo-50' 
            : horse.tipo === 'sistema' 
              ? 'border-green-200 bg-gradient-to-br from-green-50 to-emerald-50' 
              : horse.tipo === 'externo'
                ? 'border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50'
                : 'border-gray-200 bg-gradient-to-br from-gray-50 to-slate-50'
          }
        `}>
          <CardContent className="p-3 text-center">
            {/* Ícone */}
            <div className="flex justify-center mb-2">
              {horse.tipo === 'sistema' ? (
                <div className="p-2 bg-green-100 rounded-full">
                  <User className="h-4 w-4 text-green-600" />
                </div>
              ) : horse.tipo === 'externo' ? (
                <div className="p-2 bg-blue-100 rounded-full">
                  <GitBranchPlus className="h-4 w-4 text-blue-600" />
                </div>
              ) : (
                <div className="p-2 bg-gray-100 rounded-full">
                  <Heart className="h-4 w-4 text-gray-400" />
                </div>
              )}
            </div>

            {/* Nome */}
            <div className={`font-bold mb-2 ${textSize}`}>
              {horse.name}
            </div>

            {/* Badge */}
            <div className="mb-2">
              <Badge 
                variant={
                  horse.tipo === 'sistema' ? 'default' : 
                  horse.tipo === 'externo' ? 'secondary' : 
                  'outline'
                }
                className="text-xs"
              >
                {horse.tipo === 'sistema' ? `ID: ${horse.id}` : 
                 horse.tipo === 'externo' ? 'Externo' : 
                 'Não Informado'}
              </Badge>
            </div>

            {/* Informações */}
            {(horse.breed || horse.sexo || horse.cor) && (
              <>
                <Separator className="my-2" />
                <div className="space-y-1 text-xs text-gray-600">
                  {horse.breed && <div><span className="font-medium">Raça:</span> {horse.breed}</div>}
                  {horse.sexo && <div><span className="font-medium">Sexo:</span> {horse.sexo === 'M' ? 'Macho' : 'Fêmea'}</div>}
                  {horse.cor && <div><span className="font-medium">Cor:</span> {horse.cor}</div>}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className="w-full">
      <div className="flex items-center justify-between gap-2 mb-6">
        <div className="flex items-center gap-2">
          <TreePine className="h-6 w-6 text-green-600" />
          <h2 className="text-2xl font-bold text-gray-800">Árvore Genealógica Expandida</h2>
          <Badge variant="secondary" className="ml-2">
            Até 3 Gerações
          </Badge>
        </div>
        
        {editable && (
          <div className="flex gap-2">
            <Button 
              type="button"
              variant="outline" 
              size="sm"
              onClick={() => handleEditClick('genealogia')}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Editar Genealogia
            </Button>
            <Button 
              type="button"
              variant="outline" 
              size="sm"
              onClick={() => handleEditClick('adicionar parente')}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Adicionar
            </Button>
          </div>
        )}
      </div>

      {/* Layout em árvore */}
      <div className="space-y-8">
        {/* 3ª Geração - Avós */}
        <div className="flex justify-center">
          <div className="grid grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-sm font-medium text-gray-500 mb-3">Avô Paterno</div>
              <HorseCard horse={avoPaterno} position="avo-paterno" generation={3} />
            </div>
            <div className="text-center">
              <div className="text-sm font-medium text-gray-500 mb-3">Avó Paterna</div>
              <HorseCard horse={getAvoInfo('paterna')} position="avo-paterna" generation={3} />
            </div>
            <div className="text-center">
              <div className="text-sm font-medium text-gray-500 mb-3">Avô Materno</div>
              <HorseCard horse={avoMaterno} position="avo-materno" generation={3} />
            </div>
            <div className="text-center">
              <div className="text-sm font-medium text-gray-500 mb-3">Avó Materna</div>
              <HorseCard horse={getAvoInfo('materna')} position="avo-materna" generation={3} />
            </div>
          </div>
        </div>

        {/* Linhas conectoras para avós */}
        <div className="flex justify-center">
          <div className="w-80 h-8 relative">
            <div className="absolute top-0 left-20 w-px h-full bg-gray-300"></div>
            <div className="absolute top-0 right-20 w-px h-full bg-gray-300"></div>
            <div className="absolute top-4 left-20 right-20 h-px bg-gray-300"></div>
          </div>
        </div>

        {/* 2ª Geração - Pais */}
        <div className="flex justify-center">
          <div className="flex gap-16">
            <div className="text-center">
              <div className="text-sm font-medium text-gray-500 mb-3">Pai</div>
              <HorseCard horse={paiInfo} position="pai" generation={2} />
            </div>
            <div className="text-center">
              <div className="text-sm font-medium text-gray-500 mb-3">Mãe</div>
              <HorseCard horse={maeInfo} position="mae" generation={2} />
            </div>
          </div>
        </div>

        {/* Linhas conectoras para pais */}
        <div className="flex justify-center">
          <div className="w-64 h-8 relative">
            <div className="absolute top-0 left-24 w-px h-full bg-gray-300"></div>
            <div className="absolute top-0 right-24 w-px h-full bg-gray-300"></div>
            <div className="absolute top-4 left-24 right-24 h-px bg-gray-300"></div>
          </div>
        </div>

        {/* 1ª Geração - Cavalo principal */}
        <div className="flex justify-center">
          <div className="text-center">
            <HorseCard 
              horse={{
                id: cavalo?.id || null,
                name: cavalo?.name || 'Carregando...',
                tipo: 'sistema',
                sexo: cavalo?.sexo,
                breed: cavalo?.breed,
                cor: cavalo?.cor
              }} 
              position="main" 
              isMain={true}
              generation={1}
            />
          </div>
        </div>
      </div>


    </div>
  );
}