{"timestamp": "2025-05-23T21:46:15.578Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 287522816, "heapTotal": 122089472, "heapUsed": 97382928, "external": 8342451, "arrayBuffers": 249274}, "uptime": 2.189802633, "cpuUsage": {"user": 2906930, "system": 423524}, "resourceUsage": {"userCPUTime": 2906994, "systemCPUTime": 423524, "maxRSS": 340884, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 109114, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8829, "involuntaryContextSwitches": 5412}}