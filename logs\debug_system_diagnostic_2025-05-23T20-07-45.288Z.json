{"timestamp": "2025-05-23T20:07:45.288Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 288571392, "heapTotal": 113811456, "heapUsed": 74479560, "external": 8219482, "arrayBuffers": 243725}, "uptime": 9.098646364, "cpuUsage": {"user": 3366503, "system": 444733}, "resourceUsage": {"userCPUTime": 3366554, "systemCPUTime": 444740, "maxRSS": 298108, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104076, "majorPageFault": 6, "swappedOut": 0, "fsRead": 55608, "fsWrite": 1016, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8883, "involuntaryContextSwitches": 8624}}