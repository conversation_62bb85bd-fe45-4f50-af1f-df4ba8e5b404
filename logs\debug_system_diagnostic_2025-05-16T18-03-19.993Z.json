{"timestamp": "2025-05-16T18:03:19.992Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 288837632, "heapTotal": 114597888, "heapUsed": 72603600, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.633098429, "cpuUsage": {"user": 3090300, "system": 434951}, "resourceUsage": {"userCPUTime": 3090346, "systemCPUTime": 434951, "maxRSS": 282068, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103984, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 160, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8827, "involuntaryContextSwitches": 9751}}