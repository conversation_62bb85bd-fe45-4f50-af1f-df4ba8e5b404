/**
 * Serviço de Nutrição - EquiGestor AI
 * Gera planos de alimentação, calcula sobras e gerencia templates de dieta
 */

import { db } from "../db";
import { 
  cavalos, 
  feedTemplates, 
  feedPlanItems, 
  type FeedTemplate, 
  type FeedPlanItem,
  type InsertFeedPlanItem 
} from "@shared/schema";
import { calcForageKg, calcConcentrateKg, calculateFeedPlan } from "@shared/nutrition-utils";
import { eq, and, sql } from "drizzle-orm";

export class NutritionService {
  /**
   * Gera planos de alimentação para todos os cavalos de um usuário
   */
  async generateDailyPlans(userId: number, date: string): Promise<FeedPlanItem[]> {
    try {
      // Buscar todos os cavalos ativos do usuário
      const horses = await db.select()
        .from(cavalos)
        .where(and(
          eq(cavalos.userId, userId),
          eq(cavalos.status, "ativo")
        ));

      // Buscar templates disponíveis
      const templates = await db.select().from(feedTemplates);
      const defaultTemplate = templates.find(t => t.category === "manutenção") || templates[0];

      if (!defaultTemplate) {
        throw new Error("Nenhum template de alimentação encontrado");
      }

      const plans: InsertFeedPlanItem[] = [];

      for (const horse of horses) {
        // Determinar o template baseado nas características do cavalo
        const template = this.selectTemplateForHorse(horse, templates);
        const weight = horse.peso || 450; // Peso padrão se não informado

        // Calcular quantidades
        const { forageKg, concentrateKg } = calculateFeedPlan(weight, template);

        plans.push({
          animalId: horse.id,
          templateId: template.id,
          forageKg,
          concentrateKg,
          date,
          status: "pending",
          userId
        });
      }

      // Inserir planos no banco
      if (plans.length > 0) {
        const result = await db.insert(feedPlanItems)
          .values(plans)
          .returning();
        return result;
      }

      return [];
    } catch (error) {
      console.error("Erro ao gerar planos diários:", error);
      throw error;
    }
  }

  /**
   * Busca planos de alimentação do dia
   */
  async getTodayPlans(userId: number, date?: string): Promise<any[]> {
    const today = date || new Date().toISOString().split('T')[0];
    
    try {
      const plans = await db.select({
        id: feedPlanItems.id,
        animalId: feedPlanItems.animalId,
        templateId: feedPlanItems.templateId,
        forageKg: feedPlanItems.forageKg,
        concentrateKg: feedPlanItems.concentrateKg,
        date: feedPlanItems.date,
        status: feedPlanItems.status,
        leftoverPct: feedPlanItems.leftoverPct,
        horseName: cavalos.name,
        templateCategory: feedTemplates.category
      })
      .from(feedPlanItems)
      .leftJoin(cavalos, eq(feedPlanItems.animalId, cavalos.id))
      .leftJoin(feedTemplates, eq(feedPlanItems.templateId, feedTemplates.id))
      .where(and(
        eq(feedPlanItems.userId, userId),
        eq(feedPlanItems.date, today)
      ));

      return plans;
    } catch (error) {
      console.error("Erro ao buscar planos do dia:", error);
      throw error;
    }
  }

  /**
   * Marca um plano como concluído ou registra sobra
   */
  async markPlanComplete(planId: number, userId: number, leftoverPct?: number): Promise<void> {
    try {
      const status = leftoverPct && leftoverPct > 0 ? "leftover" : "done";
      
      await db.update(feedPlanItems)
        .set({ 
          status,
          leftoverPct: leftoverPct || null
        })
        .where(and(
          eq(feedPlanItems.id, planId),
          eq(feedPlanItems.userId, userId)
        ));

      // Se sobra significativa (>10%), registrar para futura otimização
      if (leftoverPct && leftoverPct > 10) {
        await this.logSignificantLeftover(planId, leftoverPct);
      }
    } catch (error) {
      console.error("Erro ao marcar plano como concluído:", error);
      throw error;
    }
  }

  /**
   * Calcula estatísticas de desperdício
   */
  async getWasteStatistics(userId: number, days: number = 30): Promise<any> {
    try {
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);
      
      const stats = await db.select({
        avgWastePct: sql<number>`AVG(COALESCE(${feedPlanItems.leftoverPct}, 0))`,
        totalPlans: sql<number>`COUNT(*)`,
        plansWithWaste: sql<number>`COUNT(CASE WHEN ${feedPlanItems.leftoverPct} > 0 THEN 1 END)`
      })
      .from(feedPlanItems)
      .where(and(
        eq(feedPlanItems.userId, userId),
        sql`${feedPlanItems.date} >= ${fromDate.toISOString().split('T')[0]}`
      ));

      return stats[0] || { avgWastePct: 0, totalPlans: 0, plansWithWaste: 0 };
    } catch (error) {
      console.error("Erro ao calcular estatísticas de desperdício:", error);
      throw error;
    }
  }

  /**
   * Seleciona o template apropriado para um cavalo
   */
  private selectTemplateForHorse(horse: any, templates: FeedTemplate[]): FeedTemplate {
    // Lógica para determinar template baseado nas características do cavalo
    
    // Se égua em gestação/lactação
    if (horse.sexo?.toLowerCase() === 'fêmea' || horse.sexo?.toLowerCase() === 'égua') {
      // Verificar registros de reprodução para determinar se está gestante/lactante
      const gestationTemplate = templates.find(t => t.category === "gestacao");
      if (gestationTemplate) return gestationTemplate;
    }

    // Se potro (jovem)
    const birthYear = horse.birthDate ? new Date(horse.birthDate).getFullYear() : null;
    const currentYear = new Date().getFullYear();
    const age = birthYear ? currentYear - birthYear : null;
    
    if (age && age < 3) {
      const potroTemplate = templates.find(t => t.category === "potro");
      if (potroTemplate) return potroTemplate;
    }

    // Se cavalo de trabalho (baseado em registros de atividades)
    const athleteTemplate = templates.find(t => t.category === "atleta_leve");
    
    // Default: manutenção
    return templates.find(t => t.category === "manutenção") || templates[0];
  }

  /**
   * Registra sobras significativas para análise futura
   */
  private async logSignificantLeftover(planId: number, leftoverPct: number): Promise<void> {
    // Aqui poderia implementar um sistema de logs ou alertas
    console.log(`Sobra significativa detectada: Plano ${planId} - ${leftoverPct}%`);
    
    // Em uma implementação completa, poderia:
    // 1. Salvar em uma tabela de logs
    // 2. Enviar notificação
    // 3. Sugerir ajuste no próximo plano
  }

  /**
   * Busca todos os templates de alimentação
   */
  async getTemplates(): Promise<FeedTemplate[]> {
    try {
      return await db.select().from(feedTemplates);
    } catch (error) {
      console.error("Erro ao buscar templates:", error);
      throw error;
    }
  }

  /**
   * Cria um novo template de alimentação
   */
  async createTemplate(template: any): Promise<FeedTemplate> {
    try {
      const result = await db.insert(feedTemplates)
        .values(template)
        .returning();
      return result[0];
    } catch (error) {
      console.error("Erro ao criar template:", error);
      throw error;
    }
  }
}

export const nutritionService = new NutritionService();