{"timestamp": "2025-05-26T21:29:57.472Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 268980224, "heapTotal": 117899264, "heapUsed": 94276880, "external": 8748469, "arrayBuffers": 274785}, "uptime": 1.961780005, "cpuUsage": {"user": 2904806, "system": 368435}, "resourceUsage": {"userCPUTime": 2904869, "systemCPUTime": 368443, "maxRSS": 329596, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104276, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8355, "involuntaryContextSwitches": 3705}}