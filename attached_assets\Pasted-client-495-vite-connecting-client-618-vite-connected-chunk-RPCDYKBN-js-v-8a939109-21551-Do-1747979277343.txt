client:495 [vite] connecting...
client:618 [vite] connected.
chunk-RPCDYKBN.js?v=8a939109:21551 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
firebase.ts:11 [Firebase] Initializing Firebase with config: {"apiKey":"*****","authDomain":"cavalo-409dc.firebaseapp.com","projectId":"cavalo-409dc","storageBucket":"cavalo-409dc.firebasestorage.app","messagingSenderId":"672662898514","appId":"1:672662898514:web:f1b7678d4e1baf11a05c8b"}
logger.ts:200 [2025-05-23T05:46:27.706Z] [INFO] [logger]: Sistema de log inicializado Object
AuthContext.tsx:6 [AuthContext] AuthProvider rendered
ProtectedRoute.tsx:29 ProtectedRoute: Verificando autenticação...
ProtectedRoute.tsx:34 ProtectedRoute: Usuário encontrado no localStorage
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
main.tsx:16 Service Worker registrado com sucesso: https://63ed2eba-d4d9-45aa-800f-0ebe7fd3e860-00-122e7dsebuel9.riker.replit.dev/
queryClient.ts:288 Request cancelada pelo React Query
DesempenhoPageModern.tsx:299 Enviando dados: {horse_id: 129, user_id: 1, tipo_evento: 'Competição', nome_evento: 'dasdsad', data: '2025-05-23', …}
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
queryClient.ts:180 Erro na requisição POST /api/desempenho: Error: 400: {"errors":{"_errors":[],"horseId":{"_errors":["Required"]},"tipoEvento":{"_errors":["Required"]},"nomeEvento":{"_errors":["Required"]}}}
    at apiRequest (queryClient.ts:163:15)
    at async handleSubmit (DesempenhoPageModern.tsx:306:9)
apiRequest @ queryClient.ts:180
await in apiRequest
handleSubmit @ DesempenhoPageModern.tsx:306
callCallback2 @ chunk-RPCDYKBN.js?v=8a939109:3674
invokeGuardedCallbackDev @ chunk-RPCDYKBN.js?v=8a939109:3699
invokeGuardedCallback @ chunk-RPCDYKBN.js?v=8a939109:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-RPCDYKBN.js?v=8a939109:3736
executeDispatch @ chunk-RPCDYKBN.js?v=8a939109:7014
processDispatchQueueItemsInOrder @ chunk-RPCDYKBN.js?v=8a939109:7034
processDispatchQueue @ chunk-RPCDYKBN.js?v=8a939109:7043
dispatchEventsForPlugins @ chunk-RPCDYKBN.js?v=8a939109:7051
(anônimo) @ chunk-RPCDYKBN.js?v=8a939109:7174
batchedUpdates$1 @ chunk-RPCDYKBN.js?v=8a939109:18913
batchedUpdates @ chunk-RPCDYKBN.js?v=8a939109:3579
dispatchEventForPluginEventSystem @ chunk-RPCDYKBN.js?v=8a939109:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-RPCDYKBN.js?v=8a939109:5478
dispatchEvent @ chunk-RPCDYKBN.js?v=8a939109:5472
dispatchDiscreteEvent @ chunk-RPCDYKBN.js?v=8a939109:5449
DesempenhoPageModern.tsx:320 Erro completo: Error: 400: {"errors":{"_errors":[],"horseId":{"_errors":["Required"]},"tipoEvento":{"_errors":["Required"]},"nomeEvento":{"_errors":["Required"]}}}
    at apiRequest (queryClient.ts:163:15)
    at async handleSubmit (DesempenhoPageModern.tsx:306:9)
handleSubmit @ DesempenhoPageModern.tsx:320
await in handleSubmit
callCallback2 @ chunk-RPCDYKBN.js?v=8a939109:3674
invokeGuardedCallbackDev @ chunk-RPCDYKBN.js?v=8a939109:3699
invokeGuardedCallback @ chunk-RPCDYKBN.js?v=8a939109:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-RPCDYKBN.js?v=8a939109:3736
executeDispatch @ chunk-RPCDYKBN.js?v=8a939109:7014
processDispatchQueueItemsInOrder @ chunk-RPCDYKBN.js?v=8a939109:7034
processDispatchQueue @ chunk-RPCDYKBN.js?v=8a939109:7043
dispatchEventsForPlugins @ chunk-RPCDYKBN.js?v=8a939109:7051
(anônimo) @ chunk-RPCDYKBN.js?v=8a939109:7174
batchedUpdates$1 @ chunk-RPCDYKBN.js?v=8a939109:18913
batchedUpdates @ chunk-RPCDYKBN.js?v=8a939109:3579
dispatchEventForPluginEventSystem @ chunk-RPCDYKBN.js?v=8a939109:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-RPCDYKBN.js?v=8a939109:5478
dispatchEvent @ chunk-RPCDYKBN.js?v=8a939109:5472
dispatchDiscreteEvent @ chunk-RPCDYKBN.js?v=8a939109:5449
AuthContext.tsx:6 [AuthContext] AuthProvider rendered
