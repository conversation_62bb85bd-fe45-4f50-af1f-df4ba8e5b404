{"timestamp": "2025-05-15T23:54:54.335Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 304091136, "heapTotal": 169840640, "heapUsed": 144110952, "external": 14070250, "arrayBuffers": 1104524}, "uptime": 75.979773178, "cpuUsage": {"user": 16288232, "system": 1172728}, "resourceUsage": {"userCPUTime": 16288288, "systemCPUTime": 1172728, "maxRSS": 843140, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 243292, "majorPageFault": 0, "swappedOut": 0, "fsRead": 8, "fsWrite": 472, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 23068, "involuntaryContextSwitches": 22511}}