{"timestamp": "2025-05-15T01:16:09.605Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 387125248, "heapTotal": 103157760, "heapUsed": 62044392, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.457537031, "cpuUsage": {"user": 2240160, "system": 297187}, "resourceUsage": {"userCPUTime": 2240220, "systemCPUTime": 297192, "maxRSS": 378052, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99605, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6108, "involuntaryContextSwitches": 1501}}