{"timestamp": "2025-05-23T05:48:07.877Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 308293632, "heapTotal": 164024320, "heapUsed": 154814888, "external": 13925272, "arrayBuffers": 1352394}, "uptime": 850.010839891, "cpuUsage": {"user": 30904861, "system": 2307050}, "resourceUsage": {"userCPUTime": 30904870, "systemCPUTime": 2307051, "maxRSS": 613560, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 336191, "majorPageFault": 6, "swappedOut": 0, "fsRead": 104144, "fsWrite": 3472, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 45162, "involuntaryContextSwitches": 35297}}