{"timestamp": "2025-05-15T23:53:40.392Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404615168, "heapTotal": 120119296, "heapUsed": 91492640, "external": 8365609, "arrayBuffers": 243725}, "uptime": 2.037010987, "cpuUsage": {"user": 2832727, "system": 385107}, "resourceUsage": {"userCPUTime": 2832772, "systemCPUTime": 385107, "maxRSS": 395132, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105322, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8613, "involuntaryContextSwitches": 4072}}