{"timestamp": "2025-05-16T23:25:08.494Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 401551360, "heapTotal": 109621248, "heapUsed": 90777384, "external": 8373554, "arrayBuffers": 290234}, "uptime": 1.967673576, "cpuUsage": {"user": 2881318, "system": 371533}, "resourceUsage": {"userCPUTime": 2881360, "systemCPUTime": 371538, "maxRSS": 392268, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103464, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8314, "involuntaryContextSwitches": 3257}}