{"timestamp": "2025-05-19T12:28:45.464Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 313712640, "heapTotal": 118161408, "heapUsed": 95075008, "external": 8734663, "arrayBuffers": 266432}, "uptime": 1.728637656, "cpuUsage": {"user": 2673501, "system": 325957}, "resourceUsage": {"userCPUTime": 2673570, "systemCPUTime": 325965, "maxRSS": 306360, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105993, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 88, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8884, "involuntaryContextSwitches": 1856}}