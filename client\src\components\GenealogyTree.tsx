import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON> } from '@shared/schema';
import { queryClient } from '@/lib/queryClient';

// Tipos para a representação da árvore genealógica
interface TreeNode {
  id: string;
  name: string;
  gender?: string;
  breed?: string;
  isRegistered: boolean;
  children?: TreeNode[];
}

interface GenealogyTreeProps {
  cavalo: Cavalo;
  onAddAncestor?: (relation: string) => void;
}

/**
 * Componente para exibição da árvore genealógica de um cavalo
 * 
 * Exibe uma árvore interativa com links para os ancestrais registrados
 * e diferenciação visual para ancestrais registrados e não-registrados
 */
export function GenealogyTree({ cavalo, onAddAncestor }: GenealogyTreeProps) {
  const [treeData, setTreeData] = useState<TreeNode | null>(null);
  const [loading, setLoading] = useState(true);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']));
  const [error, setError] = useState<string | null>(null);

  // Converter os dados do cavalo em formato adequado para a árvore
  useEffect(() => {
    const buildTree = async () => {
      try {
        setLoading(true);
        
        // Construir o nó raiz (o próprio cavalo)
        const rootNode: TreeNode = {
          id: 'root',
          name: cavalo.name,
          gender: cavalo.sexo || undefined,
          breed: cavalo.breed,
          isRegistered: true, // O cavalo atual está registrado
          children: []
        };
        
        // Adicionar pais - verificar se existem no sistema pelo ID ou criar como não-registrados
        // Obter o nome do pai pelo ID
        let paiNome = 'Pai não informado';
        if (cavalo.pai) {
          const nomePai = await getHorseNameById(cavalo.pai);
          if (nomePai) {
            paiNome = nomePai;
            console.log(`Nome do pai (ID ${cavalo.pai}) resolvido para: ${nomePai}`);
          } else {
            console.log(`Não foi possível resolver o nome para o pai ID: ${cavalo.pai}`);
          }
        }
        
        const paiNode: TreeNode = {
          id: 'pai',
          name: paiNome,
          gender: 'Macho',
          isRegistered: Boolean(cavalo.pai && await checkIfHorseExists(cavalo.pai)),
          children: []
        };
        
        // Obter o nome da mãe pelo ID
        let maeNome = 'Mãe não informada';
        if (cavalo.mae) {
          const nomeMae = await getHorseNameById(cavalo.mae);
          if (nomeMae) {
            maeNome = nomeMae;
            console.log(`Nome da mãe (ID ${cavalo.mae}) resolvido para: ${nomeMae}`);
          } else {
            console.log(`Não foi possível resolver o nome para a mãe ID: ${cavalo.mae}`);
          }
        }
        
        const maeNode: TreeNode = {
          id: 'mae',
          name: maeNome,
          gender: 'Fêmea',
          isRegistered: Boolean(cavalo.mae && await checkIfHorseExists(cavalo.mae)),
          children: []
        };
        
        // Adicionar avós paternos
        if (cavalo.pai) {
          const avoPaterno: TreeNode = {
            id: 'avoPaterno',
            name: cavalo.avoPaterno || 'Avô paterno não informado',
            gender: 'Macho',
            isRegistered: Boolean(cavalo.avoPaterno && await checkIfHorseExists(cavalo.avoPaterno)),
          };
          
          const avoPaterna: TreeNode = {
            id: 'avoPaterna',
            name: 'Avó paterna não informada', // Não temos este campo no schema ainda
            gender: 'Fêmea',
            isRegistered: false,
          };
          
          paiNode.children = [avoPaterno, avoPaterna];
        }
        
        // Adicionar avós maternos
        if (cavalo.mae) {
          const avoMaterno: TreeNode = {
            id: 'avoMaterno',
            name: cavalo.avoMaterno || 'Avô materno não informado',
            gender: 'Macho',
            isRegistered: Boolean(cavalo.avoMaterno && await checkIfHorseExists(cavalo.avoMaterno)),
          };
          
          const avoMaterna: TreeNode = {
            id: 'avoMaterna',
            name: 'Avó materna não informada', // Não temos este campo no schema ainda
            gender: 'Fêmea',
            isRegistered: false,
          };
          
          maeNode.children = [avoMaterno, avoMaterna];
        }
        
        // Adicionar pais ao nó raiz
        rootNode.children = [paiNode, maeNode];
        
        setTreeData(rootNode);
      } catch (err) {
        console.error('Erro ao construir árvore genealógica:', err);
        setError('Não foi possível carregar a árvore genealógica');
      } finally {
        setLoading(false);
      }
    };
    
    buildTree();
  }, [cavalo]);

  // Função para verificar se um cavalo existe no sistema (pelo ID)
  const checkIfHorseExists = async (horseId: string): Promise<boolean> => {
    try {
      // Buscar os cavalos do usuário
      const cavalos = await queryClient.fetchQuery({
        queryKey: ['/api/cavalos'],
        staleTime: 60000, // 1 minuto
      });
      
      // Verificar se existe um cavalo com o ID igual
      return Array.isArray(cavalos) && cavalos.some(
        (c: Cavalo) => c.id.toString() === horseId.toString()
      );
    } catch (err) {
      console.error('Erro ao verificar existência do cavalo:', err);
      return false;
    }
  };
  
  // Função para obter o nome de um cavalo pelo ID
  const getHorseNameById = async (horseId: string): Promise<string | null> => {
    try {
      // Buscar os cavalos do usuário
      const cavalos = await queryClient.fetchQuery({
        queryKey: ['/api/cavalos'],
        staleTime: 60000, // 1 minuto
      });
      
      // Encontrar o cavalo pelo ID e retornar seu nome
      const cavalo = Array.isArray(cavalos) && cavalos.find(
        (c: Cavalo) => c.id.toString() === horseId.toString()
      );
      
      return cavalo ? cavalo.name : null;
    } catch (err) {
      console.error('Erro ao buscar nome do cavalo:', err);
      return null;
    }
  };

  // Expandir/colapsar um nó
  const toggleNode = (nodeId: string) => {
    const newSet = new Set(expandedNodes);
    if (newSet.has(nodeId)) {
      newSet.delete(nodeId);
    } else {
      newSet.add(nodeId);
    }
    setExpandedNodes(newSet);
  };

  // Renderizar um nó da árvore
  const renderNode = (node: TreeNode, level: number = 0, position: 'left' | 'right' | 'center' = 'center') => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children && node.children.length > 0;
    
    return (
      <div 
        key={node.id} 
        className={`genealogy-node level-${level} position-${position} ${node.isRegistered ? 'registered' : 'not-registered'}`}
        style={{
          marginLeft: level * 20,
          borderColor: node.isRegistered ? '#3b82f6' : '#9ca3af',
          backgroundColor: node.isRegistered ? '#eff6ff' : '#f3f4f6',
          padding: '0.5rem',
          marginBottom: '0.5rem',
          borderRadius: '0.375rem',
          borderWidth: '1px',
          position: 'relative'
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span 
              className="gender-indicator" 
              style={{
                display: 'inline-block',
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                marginRight: '0.5rem',
                backgroundColor: node.gender === 'Macho' ? '#3b82f6' : (node.gender === 'Fêmea' ? '#ec4899' : '#9ca3af')
              }}
            />
            <span className="font-semibold">{node.name}</span>
            {node.breed && <span className="text-gray-500 ml-2 text-sm">({node.breed})</span>}
          </div>
          
          {hasChildren && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => toggleNode(node.id)}
              className="p-1 h-6"
            >
              {isExpanded ? '−' : '+'}
            </Button>
          )}
          
          {!node.isRegistered && node.id !== 'root' && onAddAncestor && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => onAddAncestor(node.id)}
              className="ml-2 p-1 h-6 text-xs"
            >
              Registrar
            </Button>
          )}
        </div>
        
        {isExpanded && hasChildren && (
          <div className="children-container mt-2 pl-4 border-l-2 border-dotted border-gray-300">
            {node.children?.map((child, index) => 
              renderNode(
                child, 
                level + 1, 
                index % 2 === 0 ? 'left' : 'right'
              )
            )}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Árvore Genealógica</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Árvore Genealógica</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-red-500">{error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Árvore Genealógica</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="genealogy-container">
          {treeData && renderNode(treeData)}
        </div>
        
        <div className="mt-4 p-2 bg-gray-100 rounded-md">
          <div className="text-sm font-medium mb-2">Legenda:</div>
          <div className="flex items-center mb-1">
            <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
            <span className="text-sm">Macho</span>
          </div>
          <div className="flex items-center mb-1">
            <span className="w-3 h-3 bg-pink-500 rounded-full mr-2"></span>
            <span className="text-sm">Fêmea</span>
          </div>
          <div className="flex items-center mb-1">
            <span className="w-3 h-3 bg-gray-400 rounded-full mr-2"></span>
            <span className="text-sm">Sexo não informado</span>
          </div>
          <div className="flex items-center mb-1">
            <div className="w-4 h-4 border border-blue-500 bg-blue-50 mr-2"></div>
            <span className="text-sm">Ancestral registrado no sistema</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 border border-gray-400 bg-gray-50 mr-2"></div>
            <span className="text-sm">Ancestral não registrado no sistema</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}