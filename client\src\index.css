@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Animações para o gráfico de radar morfológico */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0); opacity: 0; }
  80% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes drawPolygon {
  0% { 
    stroke-dashoffset: 1000; 
    opacity: 0;
  }
  70% { opacity: 0.7; }
  100% { 
    stroke-dashoffset: 0; 
    opacity: 1;
  }
}

@keyframes expandOutward {
  0% { 
    transform: scale(0.5);
    opacity: 0; 
  }
  100% { 
    transform: scale(1);
    opacity: 1; 
  }
}

@keyframes pulsePoint {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

.animate-radar-circles {
  animation: expandOutward 0.8s ease-out forwards;
}

.animate-radar-lines {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-radar-polygon {
  animation: drawPolygon 1.2s ease-out forwards;
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
}

.animate-radar-points {
  animation: scaleIn 0.5s ease-out forwards;
}

.animate-radar-labels {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-pulse {
  animation: pulsePoint 1.5s infinite;
}

/* Estilos aprimorados para dispositivos móveis */
@media (max-width: 768px) {
  /* Prevenir scroll quando o menu estiver aberto */
  .sidebar-mobile-open body {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
  }
  
  /* Melhorar visibilidade e interatividade do botão de menu */
  button[aria-label="Menu principal"] {
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    transition: background-color 0.2s;
  }
  
  button[aria-label="Menu principal"]:active {
    background-color: #134282;
  }
  
  /* Melhorar visibilidade do menu mobile */
  .mobile-sidebar {
    box-shadow: 0 0 20px rgba(0,0,0,0.5);
  }
  
  /* Melhoria na animação e interatividade do overlay */
  .mobile-overlay {
    backdrop-filter: blur(2px);
    transition: opacity 0.3s ease;
  }
}