/**
 * <PERSON><PERSON><PERSON>lo de Logging para o EquiGestor
 * 
 * Este módulo implementa um sistema de logging estruturado e centralizado
 * baseado nas melhores práticas de engenharia de software.
 * 
 * Características:
 * - Formatação consistente
 * - Níveis de log configuráveis
 * - Rotação e armazenamento de logs
 * - Informações contextuais (carimbos de data/hora, metadados)
 * - Categorização por módulos/componentes
 */

import { createLogger, format, transports, Logger } from 'winston';
import path from 'path';

// Define os níveis de log e suas cores
const logLevels = {
  error: 0,    // Erros críticos que requerem atenção imediata
  warn: 1,     // Avisos sobre condições potencialmente problemáticas
  info: 2,     // Informações sobre o funcionamento normal
  http: 3,     // Detalhes sobre requisições HTTP
  debug: 4,    // Informações detalhadas para desenvolvimento/depuração
  trace: 5     // Informações extremamente detalhadas (raramente usado)
};

// Define as cores para cada nível de log no console
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'cyan',
  debug: 'blue',
  trace: 'magenta'
};

// Determina o nível de log baseado no ambiente
// Em produção, usamos 'info' por padrão; em desenvolvimento, usamos 'debug'
const logLevel = process.env.NODE_ENV === 'production' ? 'info' : 'debug';

/**
 * Formatador personalizado que adiciona cores e formatação consistente
 */
const customFormat = format.combine(
  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  format.errors({ stack: true }),
  format.splat(),
  format.metadata({ fillExcept: ['message', 'level', 'timestamp', 'label'] }),
  format.printf(({ timestamp, level, message, metadata, moduleName }) => {
    let moduleInfo = moduleName ? `[${moduleName}] ` : '';
    let meta = '';
    
    // Formata metadados como JSON, excluindo metadados vazios
    if (metadata && Object.keys(metadata).length > 0 && 
        !(Object.keys(metadata).length === 1 && metadata.moduleName)) {
      // Remove o moduleInfo dos metadados para evitar redundância
      const { moduleName, ...rest } = metadata;
      if (Object.keys(rest).length > 0) {
        try {
          meta = ' ' + JSON.stringify(rest);
        } catch (e) {
          meta = ' [Metadata não serializável]';
        }
      }
    }
    
    return `${timestamp} ${level.toUpperCase().padEnd(5)} ${moduleInfo}${message}${meta}`;
  })
);

/**
 * Formatador para console com cores
 */
const consoleFormat = format.combine(
  format.colorize({ all: true }),
  customFormat
);

/**
 * Cria a instância do logger principal
 */
const logger = createLogger({
  levels: logLevels,
  level: logLevel,
  format: customFormat,
  defaultMeta: { service: 'equigestor' },
  transports: [
    // Log de console com cores
    new transports.Console({
      format: consoleFormat
    }),
    // Log de arquivo para todos os níveis
    new transports.File({ 
      filename: path.join('logs', 'equigestor-combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // Log de arquivo separado só para erros
    new transports.File({ 
      filename: path.join('logs', 'equigestor-error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  ],
  exitOnError: false
});

/**
 * Função para criar um logger específico para um módulo
 * @param moduleName Nome do módulo/componente
 */
export function getModuleLogger(moduleName: string): Logger {
  return logger.child({ moduleName });
}

/**
 * Logging contextual para requisições HTTP
 * @param req Objeto de requisição Express
 * @param message Mensagem de log
 * @param meta Metadados adicionais
 */
export function logRequest(req: any, message: string, meta: object = {}) {
  const httpLogger = getModuleLogger('http');
  
  const logData = {
    method: req.method,
    url: req.url,
    ip: req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress,
    userId: req.headers['user-id'] || 'anonymous',
    ...meta
  };
  
  httpLogger.http(message, logData);
}

/**
 * Middleware de logging para Express
 */
export function httpLoggerMiddleware(req: any, res: any, next: Function) {
  const startTime = new Date().getTime();
  
  // Quando a resposta terminar
  res.on('finish', () => {
    const duration = new Date().getTime() - startTime;
    
    logRequest(req, `${req.method} ${req.url}`, {
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length') || 0
    });
  });
  
  next();
}

/**
 * Função para registrar erros de API com contexto
 * @param err Objeto de erro
 * @param req Objeto de requisição Express (opcional)
 * @param moduleName Nome do módulo onde ocorreu o erro
 * @param meta Metadados adicionais
 */
export function logApiError(err: Error, req: any = null, moduleName: string = 'api', meta: object = {}) {
  const errorLogger = getModuleLogger(moduleName);
  
  const logData = {
    ...(req ? {
      method: req.method,
      url: req.url,
      userId: req.headers['user-id'] || 'anonymous'
    } : {}),
    errorName: err.name,
    errorMessage: err.message,
    stackTrace: err.stack,
    ...meta
  };
  
  errorLogger.error(`Erro na API: ${err.message}`, logData);
}

/**
 * Função para registrar erros de banco de dados com contexto
 * @param err Objeto de erro
 * @param query Informações sobre a consulta que falhou (opcional)
 * @param meta Metadados adicionais
 */
export function logDbError(err: Error, query: string = '', meta: object = {}) {
  const dbLogger = getModuleLogger('database');
  
  const logData = {
    errorName: err.name,
    errorMessage: err.message,
    query: query,
    stackTrace: err.stack,
    ...meta
  };
  
  dbLogger.error(`Erro no banco de dados: ${err.message}`, logData);
}

// Exporta o logger principal e as funções auxiliares
export default logger;