{"timestamp": "2025-05-26T20:53:50.906Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 409079808, "heapTotal": 116850688, "heapUsed": 92766320, "external": 8601937, "arrayBuffers": 281567}, "uptime": 7.511453334, "cpuUsage": {"user": 3172217, "system": 451343}, "resourceUsage": {"userCPUTime": 3172272, "systemCPUTime": 451351, "maxRSS": 399492, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108980, "majorPageFault": 6, "swappedOut": 0, "fsRead": 55664, "fsWrite": 992, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9048, "involuntaryContextSwitches": 4073}}