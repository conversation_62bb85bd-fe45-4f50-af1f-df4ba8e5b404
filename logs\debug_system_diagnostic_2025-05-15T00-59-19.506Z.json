{"timestamp": "2025-05-15T00:59:19.506Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 388399104, "heapTotal": 104468480, "heapUsed": 62015192, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.517868653, "cpuUsage": {"user": 2276726, "system": 292568}, "resourceUsage": {"userCPUTime": 2276775, "systemCPUTime": 292574, "maxRSS": 379296, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99956, "majorPageFault": 0, "swappedOut": 0, "fsRead": 80, "fsWrite": 16, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6613, "involuntaryContextSwitches": 2925}}