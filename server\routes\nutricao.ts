import { Router, Request, Response } from "express";
import { storage } from "../storage";
import { insertNutricaoSchema } from "@shared/schema";
import { authenticateUser } from "../auth";

const router = Router();

// Aplicar autenticação em todas as rotas de nutrição
router.use(authenticateUser);

// GET /api/nutricao - Listar todas as entradas de nutrição (filtrado por usuário)
router.get("/", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    const nutricoes = await storage.getNutricoes(userId);
    res.json(nutricoes);
  } catch (error) {
    console.error("Erro ao buscar registros de nutrição:", error);
    res.status(500).send("Erro ao buscar registros de nutrição");
  }
});

// GET /api/nutricao/horse/:horseId - Listar entradas de nutrição por cavalo
router.get("/horse/:horseId", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    const horseId = Number(req.params.horseId);
    
    if (isNaN(horseId)) {
      return res.status(400).json({ error: "ID do cavalo inválido" });
    }
    
    const nutricoes = await storage.getNutricoesByHorse(horseId, userId);
    res.json(nutricoes);
  } catch (error) {
    console.error("Erro ao buscar registros de nutrição por cavalo:", error);
    res.status(500).send("Erro ao buscar registros de nutrição por cavalo");
  }
});

// GET /api/nutricao/:id - Obter um registro de nutrição específico
router.get("/:id", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    const nutricaoId = Number(req.params.id);
    const nutricao = await storage.getNutricao(nutricaoId, userId);
    
    if (!nutricao) {
      return res.status(404).send("Registro de nutrição não encontrado");
    }
    
    res.json(nutricao);
  } catch (error) {
    console.error("Erro ao buscar registro de nutrição:", error);
    res.status(500).send("Erro ao buscar registro de nutrição");
  }
});

// POST /api/nutricao - Criar um novo registro de nutrição
router.post("/", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    
    // Validar dados com Zod
    const validatedData = insertNutricaoSchema.parse({
      ...req.body,
      userId
    });
    
    const nutricao = await storage.createNutricao(validatedData);
    res.status(201).json(nutricao);
  } catch (error: any) {
    console.error("Erro ao criar registro de nutrição:", error);
    
    if (error?.name === 'ZodError') {
      return res.status(400).json({ 
        error: "Dados inválidos", 
        details: error.errors 
      });
    }
    
    res.status(500).send("Erro ao criar registro de nutrição");
  }
});

// PATCH /api/nutricao/:id - Atualizar um registro de nutrição
router.patch("/:id", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    const nutricaoId = Number(req.params.id);
    
    // Verificar se o registro existe e pertence ao usuário
    const existingNutricao = await storage.getNutricao(nutricaoId, userId);
    if (!existingNutricao) {
      return res.status(404).send("Registro de nutrição não encontrado");
    }
    
    // Atualizar o registro
    const updatedNutricao = await storage.updateNutricao(nutricaoId, userId, req.body);
    res.json(updatedNutricao);
  } catch (error) {
    console.error("Erro ao atualizar registro de nutrição:", error);
    res.status(500).send("Erro ao atualizar registro de nutrição");
  }
});

// DELETE /api/nutricao/:id - Excluir um registro de nutrição
router.delete("/:id", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    const nutricaoId = Number(req.params.id);
    
    // Verificar se o registro existe e pertence ao usuário
    const existingNutricao = await storage.getNutricao(nutricaoId, userId);
    if (!existingNutricao) {
      return res.status(404).send("Registro de nutrição não encontrado");
    }
    
    // Excluir o registro
    const deleted = await storage.deleteNutricao(nutricaoId, userId);
    
    if (deleted) {
      res.status(204).send();
    } else {
      res.status(500).send("Erro ao excluir registro de nutrição");
    }
  } catch (error) {
    console.error("Erro ao excluir registro de nutrição:", error);
    res.status(500).send("Erro ao excluir registro de nutrição");
  }
});

export default router;