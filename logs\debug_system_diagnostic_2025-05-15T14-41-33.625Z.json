{"timestamp": "2025-05-15T14:41:33.625Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 385929216, "heapTotal": 101847040, "heapUsed": 62449744, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.946192612, "cpuUsage": {"user": 2089578, "system": 305299}, "resourceUsage": {"userCPUTime": 2089628, "systemCPUTime": 305299, "maxRSS": 376884, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100278, "majorPageFault": 0, "swappedOut": 0, "fsRead": 26320, "fsWrite": 56, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6348, "involuntaryContextSwitches": 1987}}