import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, PlusCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Interfaces e componentes genéticos
import { AvaliacaoMorfologica } from "@/types/genetica";
import { useMorfologiaData } from "@/hooks/use-morfologia-data";
import MorfologiaForm from "@/components/genetics/MorfologiaForm";
import { EnhancedMorfologiaViewer } from "@/components/genetics/EnhancedMorfologiaViewer";

export default function MorfologiaPage() {
  const { toast } = useToast();
  
  // Estados para modais
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isChartModalOpen, setIsChartModalOpen] = useState(false);
  const [selectedAvaliacao, setSelectedAvaliacao] = useState<AvaliacaoMorfologica | null>(null);
  
  // Usar hook personalizado para gerenciar dados de morfologia
  const {
    cavalos,
    isLoadingCavalos,
    selectedHorseId,
    setSelectedHorseId,
    selectedHorse,
    avaliacoes,
    isLoadingAvaliacoes,
    getDestaquesMorfologicos,
    reloadAvaliacoes
  } = useMorfologiaData();
  
  // Manipulador para adicionar nova avaliação
  const handleAdicionarAvaliacao = () => {
    setIsModalOpen(true);
  };
  
  // Fechar modal e recarregar dados após salvar avaliação
  const handleAvaliacaoSuccess = () => {
    setIsModalOpen(false);
    reloadAvaliacoes();
    toast({
      title: "Avaliação salva com sucesso",
      description: "A avaliação morfológica foi adicionada ao histórico.",
    });
  };

  // Manipulador para visualizar gráfico
  const handleVisualizarGrafico = (avaliacao: AvaliacaoMorfologica) => {
    setSelectedAvaliacao(avaliacao);
    setIsChartModalOpen(true);
  };

  // Renderizar carregamento
  if (isLoadingCavalos) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Carregando cavalos...</span>
      </div>
    );
  }

  // Obter nome do cavalo selecionado
  const selectedHorseName = selectedHorse?.name || "Cavalo Selecionado";

  return (
    <div className="space-y-6">
      {/* Modal para adicionar nova avaliação */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Nova Avaliação Morfológica</DialogTitle>
            <DialogDescription>
              Preencha os dados da avaliação morfológica para {selectedHorseName}
            </DialogDescription>
          </DialogHeader>
          
          {selectedHorseId && (
            <MorfologiaForm 
              horseId={selectedHorseId} 
              horseName={selectedHorseName} 
              onSuccess={handleAvaliacaoSuccess}
              onCancel={() => setIsModalOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
      
      {/* Modal para visualizar gráfico da avaliação */}
      <Dialog open={isChartModalOpen} onOpenChange={setIsChartModalOpen}>
        <DialogContent className="max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Análise Morfológica
              {selectedAvaliacao?.dataMedicao && (
                <span className="text-muted-foreground text-sm font-normal ml-2">
                  {format(new Date(selectedAvaliacao.dataMedicao), "dd/MM/yyyy", { locale: ptBR })}
                </span>
              )}
            </DialogTitle>
            <DialogDescription>
              Visualização detalhada das pontuações e análise
            </DialogDescription>
          </DialogHeader>
          
          {selectedAvaliacao && (
            <MorfologiaChart avaliacao={selectedAvaliacao} />
          )}
          
          <div className="flex justify-end mt-6">
            <Button variant="outline" onClick={() => setIsChartModalOpen(false)}>
              Fechar
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-primary">Avaliação Morfológica</h2>
        <Button 
          onClick={handleAdicionarAvaliacao} 
          disabled={!selectedHorseId}
          className="bg-primary text-white hover:bg-primary/90"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Nova Avaliação
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Selecione um Cavalo</CardTitle>
          <CardDescription>
            Visualize o histórico de avaliações morfológicas por cavalo
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select
            value={selectedHorseId?.toString() || ""}
            onValueChange={(value) => setSelectedHorseId(Number(value))}
          >
            <SelectTrigger className="w-full md:w-[300px]">
              <SelectValue placeholder="Selecione um cavalo" />
            </SelectTrigger>
            <SelectContent>
              {cavalos?.map((cavalo) => (
                <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                  {cavalo.name} {cavalo.breed ? `(${cavalo.breed})` : ''}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {selectedHorseId && (
        <Card>
          <CardHeader>
            <CardTitle>
              Histórico de Avaliações
              {selectedHorse && (
                <span className="text-primary">
                  {" "}
                  - {selectedHorseName}
                </span>
              )}
            </CardTitle>
            <CardDescription>
              Pontuações de características morfológicas e avaliações
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Usar o componente viewer refatorado */}
            <MorfologiaViewer
              isLoading={isLoadingAvaliacoes}
              avaliacoes={avaliacoes}
              horseName={selectedHorseName}
              getDestaquesMorfologicos={getDestaquesMorfologicos}
              onAddClick={handleAdicionarAvaliacao}
              onViewChart={handleVisualizarGrafico}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}