{"timestamp": "2025-05-15T18:04:16.702Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 391049216, "heapTotal": 110665728, "heapUsed": 72399608, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.680569189, "cpuUsage": {"user": 2811905, "system": 319805}, "resourceUsage": {"userCPUTime": 2811941, "systemCPUTime": 319809, "maxRSS": 381884, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102076, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8153, "involuntaryContextSwitches": 1320}}