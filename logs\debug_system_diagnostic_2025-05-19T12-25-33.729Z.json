{"timestamp": "2025-05-19T12:25:33.729Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 396615680, "heapTotal": 111607808, "heapUsed": 87606728, "external": 8282327, "arrayBuffers": 249274}, "uptime": 2.453912769, "cpuUsage": {"user": 2680751, "system": 347865}, "resourceUsage": {"userCPUTime": 2680800, "systemCPUTime": 347871, "maxRSS": 387320, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105517, "majorPageFault": 0, "swappedOut": 0, "fsRead": 30344, "fsWrite": 128, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8127, "involuntaryContextSwitches": 2112}}