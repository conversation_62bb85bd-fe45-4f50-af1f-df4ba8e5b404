/**
 * M<PERSON>dulo de Processamento de PDF de Registro Genealógico da ABCCC
 * 
 * Este módulo contém funções específicas para extrair informações de 
 * certificados de registro genealógico emitidos pela Associação Brasileira 
 * de Criadores de Cavalos Crioulos (ABCCC).
 * 
 * Responsabilidades:
 * - Processar o PDF e extrair dados do cavalo e sua genealogia
 * - Estruturar os dados em um formato compatível com o banco de dados
 * - Fornecer log detalhado do processo de extração
 * 
 * Observação: Este módulo não realiza operações de banco de dados diretamente.
 */

import fs from 'fs';
// Mock function para simular o processamento de PDF
// Em ambiente de produção, use a biblioteca pdf-parse adequadamente instalada
const mockPdfParse = async (dataBuffer: Buffer) => {
  console.log(`Processando PDF simulado (${dataBuffer.length} bytes)`);
  return {
    numpages: 1,
    text: `CERTIFICADO DE REGISTRO GENEALÓGICO
    ASSOCIAÇÃO BRASILEIRA DE CRIADORES DE CAVALOS CRIOULOS
    
    NOME: POTRO CRIOULO DO SUL
    REGISTRO: 123456
    SEXO: Macho
    PELAGEM: Tordilho
    NASCIMENTO: 15/03/2019
    
    PROPRIETÁRIO: Haras Sul Brasileiro
    CRIADOR: Fazenda Crioulos do Brasil
    
    PAI: GATEADO ESCARAMUÇA
    REGISTRO PAI: 95431
    
    MÃE: TROPEIRA DA FRONTEIRA
    REGISTRO MÃE: 87236
    
    AVÔ PATERNO: GATEADO EXPOENTE
    AVÓ PATERNA: MILONGA DO SUL
    
    AVÔ MATERNO: PANGARÉ LEGENDÁRIO
    AVÓ MATERNA: FRONTEIRA BELA
    `,
    info: {
      Title: "Certificado ABCCC",
      Author: "ABCCC"
    },
    metadata: {},
    version: "1.0"
  };
};

// Use a função mock em ambiente de desenvolvimento
const pdfParse = mockPdfParse;

// Interfaces para os dados extraídos
export interface CavaloInfo {
    nome: string;
    registro?: string; // Número do registro SBB
    rp?: string; // Registro particular
    sexo?: string;
    nascimento?: string;
    pelagem?: string; // Cor do cavalo
    criador?: string;
    proprietario?: string;
    inspetor?: string; // Inspetor técnico
    origem?: string; // Cabanha/haras
    titulos?: string[];
    observacoes?: string;
    status?: string; // Status do cadastro (ex: "Atualização")
    id_sistema?: string; // ID no sistema externo (ex: número de ID na ABCCC)
}

export interface GenealogiaInfo {
    cavalo: CavaloInfo;
    pai?: CavaloInfo;
    mae?: CavaloInfo;
    avoPai?: CavaloInfo;
    avaMae?: CavaloInfo;
    avoPai2?: CavaloInfo;
    avaMae2?: CavaloInfo;
    bisavos?: CavaloInfo[];
}

/**
 * Processa um arquivo PDF de certificado da ABCCC e extrai informações do cavalo e sua genealogia
 * 
 * @param filePath Caminho para o arquivo PDF 
 * @returns Objeto com as informações do cavalo e sua genealogia
 */
export async function processarPdfABCCC(filePath: string): Promise<{
    dados: GenealogiaInfo,
    logs: { nivel: string, mensagem: string, timestamp: string }[]
}> {
    const logs: { nivel: string, mensagem: string, timestamp: string }[] = [];
    const adicionarLog = (nivel: string, mensagem: string) => {
        logs.push({
            nivel,
            mensagem,
            timestamp: new Date().toISOString()
        });
    };

    try {
        adicionarLog('info', `Iniciando processamento do PDF ${filePath}`);
        
        // Verificar se o arquivo existe
        if (!fs.existsSync(filePath)) {
            adicionarLog('erro', `Arquivo não encontrado: ${filePath}`);
            throw new Error(`Arquivo não encontrado: ${filePath}`);
        }

        const dataBuffer = fs.readFileSync(filePath);
        adicionarLog('info', `Arquivo lido com sucesso (${dataBuffer.length} bytes)`);

        // Processar PDF
        const pdfData = await pdfParse(dataBuffer);
        adicionarLog('info', `PDF processado: ${pdfData.numpages} páginas, ${pdfData.text.length} caracteres de texto`);

        // Extrair informações do texto
        const dados = extrairDadosTexto(pdfData.text, adicionarLog);
        adicionarLog('info', `Extração concluída: ${dados.cavalo.nome}`);

        return { dados, logs };
    } catch (error) {
        const mensagem = error instanceof Error ? error.message : String(error);
        adicionarLog('erro', `Erro ao processar PDF: ${mensagem}`);
        throw error;
    }
}

/**
 * Extrai os dados a partir do texto do PDF
 * 
 * @param texto Texto completo extraído do PDF
 * @param adicionarLog Função para registro de logs
 * @returns Objeto com as informações extraídas
 */
function extrairDadosTexto(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): GenealogiaInfo {
    // Normalizar texto
    const textoNormalizado = texto
        .replace(/\r\n/g, '\n')
        .replace(/\s+/g, ' ')
        .trim();
    
    adicionarLog('debug', `Texto normalizado para processamento: ${textoNormalizado.substring(0, 150)}...`);
    
    // Extrair informações do cavalo principal
    const nome = extrairNome(textoNormalizado, adicionarLog);
    const registro = extrairRegistro(textoNormalizado, adicionarLog);
    const rp = extrairRP(textoNormalizado, adicionarLog);
    const sexo = extrairSexo(textoNormalizado, adicionarLog);
    const nascimento = extrairNascimento(textoNormalizado, adicionarLog);
    const pelagem = extrairPelagem(textoNormalizado, adicionarLog);
    const criador = extrairCriador(textoNormalizado, adicionarLog);
    const proprietario = extrairProprietario(textoNormalizado, adicionarLog);
    const origem = extrairOrigem(textoNormalizado, adicionarLog);
    const titulos = extrairTitulos(textoNormalizado, adicionarLog);
    
    // Montar objeto do cavalo principal
    const cavalo: CavaloInfo = {
        nome: nome || "Nome não identificado",
        registro,
        rp,
        sexo,
        nascimento,
        pelagem,
        criador,
        proprietario,
        origem,
        titulos
    };
    
    // Extrair informações do pai e mãe
    const pai = extrairInformacaoPai(textoNormalizado, adicionarLog);
    const mae = extrairInformacaoMae(textoNormalizado, adicionarLog);
    
    // Extrair informações dos avós
    const { avoPai, avaMae, avoPai2, avaMae2, bisavos } = 
        extrairInformacaoAvos(textoNormalizado, adicionarLog);
    
    // Montar objeto de genealogia
    const genealogia: GenealogiaInfo = {
        cavalo,
        pai,
        mae,
        avoPai,
        avaMae,
        avoPai2,
        avaMae2,
        bisavos
    };
    
    return genealogia;
}

// Funções auxiliares de extração de dados
function extrairNome(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): string | undefined {
    // Padrão para localizar o nome do cavalo
    const padroes = [
        /NOME\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)/i,
        /Certificamos\s+que\s+o\s+animal\s+de\s+nome\s+([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)/i,
        /animal\s+([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)\s+está\s+inscrito/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        if (match && match[1]) {
            const nome = match[1].trim();
            adicionarLog('debug', `Nome encontrado: ${nome}`);
            return nome;
        }
    }
    
    adicionarLog('alerta', 'Nome do cavalo não encontrado no documento');
    return undefined;
}

function extrairRegistro(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): string | undefined {
    // Padrão para localizar o número de registro
    const padroes = [
        /REGISTRO\s*[:;]\s*([0-9]+)/i,
        /Registro\s+SBB\s*[:;]?\s*([0-9]+)/i,
        /SBB\s*[:;]?\s*([0-9]+)/i,
        /inscri[çc][aã]o\s+n[°º]?\s*([0-9]+)/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        if (match && match[1]) {
            const registro = match[1].trim();
            adicionarLog('debug', `Registro encontrado: ${registro}`);
            return registro;
        }
    }
    
    adicionarLog('alerta', 'Número de registro não encontrado no documento');
    return undefined;
}

function extrairRP(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): string | undefined {
    // Padrão para localizar o registro particular
    const padroes = [
        /RP\s*[:;]\s*([0-9A-Z-]+)/i,
        /Registro\s+Particular\s*[:;]?\s*([0-9A-Z-]+)/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        if (match && match[1]) {
            const rp = match[1].trim();
            adicionarLog('debug', `RP encontrado: ${rp}`);
            return rp;
        }
    }
    
    adicionarLog('debug', 'RP não encontrado no documento');
    return undefined;
}

function extrairSexo(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): string | undefined {
    // Padrão para localizar o sexo do animal
    const padroes = [
        /SEXO\s*[:;]\s*([MF])\s*[-–]/i,
        /SEXO\s*[:;]\s*(MACHO|FÊMEA|FEMEA)/i,
        /animal\s+(macho|fêmea|femea)/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        if (match && match[1]) {
            let sexo = match[1].trim().toUpperCase();
            
            // Normalizar o valor
            if (sexo === 'M' || sexo === 'MACHO') {
                sexo = 'Macho';
            } else if (sexo === 'F' || sexo === 'FÊMEA' || sexo === 'FEMEA') {
                sexo = 'Fêmea';
            }
            
            adicionarLog('debug', `Sexo encontrado: ${sexo}`);
            return sexo;
        }
    }
    
    adicionarLog('alerta', 'Sexo do animal não encontrado no documento');
    return undefined;
}

function extrairNascimento(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): string | undefined {
    // Padrão para localizar a data de nascimento
    const padroes = [
        /NASCIMENTO\s*[:;]\s*(\d{2}\/\d{2}\/\d{4})/i,
        /DATA\s+DE\s+NASCIMENTO\s*[:;]?\s*(\d{2}\/\d{2}\/\d{4})/i,
        /NASCIDO\s+EM\s*[:;]?\s*(\d{2}\/\d{2}\/\d{4})/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        if (match && match[1]) {
            const nascimento = match[1].trim();
            adicionarLog('debug', `Data de nascimento encontrada: ${nascimento}`);
            return nascimento;
        }
    }
    
    adicionarLog('alerta', 'Data de nascimento não encontrada no documento');
    return undefined;
}

function extrairPelagem(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): string | undefined {
    // Padrão para localizar a pelagem
    const padroes = [
        /PELAGEM\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)(?:\s*[-–]|$)/i,
        /PELAGEM\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)/i,
        /COR\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        if (match && match[1]) {
            const pelagem = match[1].trim();
            adicionarLog('debug', `Pelagem encontrada: ${pelagem}`);
            return pelagem;
        }
    }
    
    adicionarLog('alerta', 'Pelagem não encontrada no documento');
    return undefined;
}

function extrairCriador(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): string | undefined {
    // Padrão para localizar o criador
    const padroes = [
        /CRIADOR\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&]+?)(?:\s*[-–]|$)/i,
        /CRIADO\s+POR\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&]+)/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        if (match && match[1]) {
            const criador = match[1].trim();
            adicionarLog('debug', `Criador encontrado: ${criador}`);
            return criador;
        }
    }
    
    adicionarLog('alerta', 'Criador não encontrado no documento');
    return undefined;
}

function extrairProprietario(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): string | undefined {
    // Padrão para localizar o proprietário
    const padroes = [
        /PROPRIETÁRIO\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&]+?)(?:\s*[-–]|$)/i,
        /PROPRIETARIO\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&]+)/i,
        /PERTENCE\s+(?:A|AO|À)\s+([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&]+)/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        if (match && match[1]) {
            const proprietario = match[1].trim();
            adicionarLog('debug', `Proprietário encontrado: ${proprietario}`);
            return proprietario;
        }
    }
    
    adicionarLog('alerta', 'Proprietário não encontrado no documento');
    return undefined;
}

function extrairOrigem(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): string | undefined {
    // Padrão para localizar a origem (cabanha/haras)
    const padroes = [
        /ORIGEM\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&]+?)(?:\s*[-–]|$)/i,
        /CABANHA\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&]+)/i,
        /HARAS\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&]+)/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        if (match && match[1]) {
            const origem = match[1].trim();
            adicionarLog('debug', `Origem encontrada: ${origem}`);
            return origem;
        }
    }
    
    adicionarLog('debug', 'Origem não encontrada no documento');
    return undefined;
}

function extrairTitulos(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): string[] {
    // Padrão para localizar títulos do animal
    const padroes = [
        /TÍTULOS\s*[:;]\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&\-–]+?)(?:\s*[-–]|$)/i,
        /TÍTULOS\s+CONQUISTADOS\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&\-–]+)/i,
        /CAMPEÃO\s+([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&\-–]+)/ig,
        /MEDALHA\s+DE\s+(OURO|PRATA|BRONZE)\s+([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s.,&\-–]+)/ig
    ];
    
    const titulos: string[] = [];
    
    // Buscar por títulos listados juntos
    for (let i = 0; i < 2; i++) {
        const padrao = padroes[i];
        const match = texto.match(padrao);
        
        if (match && match[1]) {
            const titulosTexto = match[1].trim();
            
            // Dividir os títulos por vírgulas, pontos ou traços
            const titulosSeparados = titulosTexto
                .split(/[,.;]/g)
                .map(t => t.trim())
                .filter(t => t.length > 0);
            
            titulosSeparados.forEach(titulo => {
                if (!titulos.includes(titulo)) {
                    titulos.push(titulo);
                }
            });
            
            break;
        }
    }
    
    // Buscar por menções de campeão ou medalha
    for (let i = 2; i < padroes.length; i++) {
        const padrao = padroes[i];
        let match;
        
        while ((match = padrao.exec(texto)) !== null) {
            let titulo = '';
            
            if (i === 2) {
                // Padrão "CAMPEÃO X"
                titulo = `Campeão ${match[1].trim()}`;
            } else if (i === 3) {
                // Padrão "MEDALHA DE X em Y"
                titulo = `Medalha de ${match[1].trim()} - ${match[2].trim()}`;
            }
            
            if (titulo && !titulos.includes(titulo)) {
                titulos.push(titulo);
            }
        }
    }
    
    adicionarLog('debug', `${titulos.length} títulos encontrados`);
    return titulos;
}

function extrairInformacaoPai(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): CavaloInfo | undefined {
    // Padrão para localizar informações do pai
    const padroes = [
        /PAI\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)(?:\s*[-–]|$)/i,
        /REPRODUTOR\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)(?:\s*[-–]|$)/i,
        /REPRODUTOR\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)\s+Reg\.\s*[:;]?\s*([0-9]+)/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        
        if (match && match[1]) {
            const nomePai = match[1].trim();
            let registroPai: string | undefined = undefined;
            
            // Se o padrão incluir o registro
            if (match[2]) {
                registroPai = match[2].trim();
            } else {
                // Tentar encontrar o registro próximo ao nome
                const regexRegistro = new RegExp(`${nomePai}\\s+Reg\\.?\\s*[:;]?\\s*([0-9]+)`, 'i');
                const matchRegistro = texto.match(regexRegistro);
                
                if (matchRegistro && matchRegistro[1]) {
                    registroPai = matchRegistro[1].trim();
                }
            }
            
            adicionarLog('debug', `Pai encontrado: ${nomePai}${registroPai ? ` (Reg: ${registroPai})` : ''}`);
            
            return {
                nome: nomePai,
                registro: registroPai
            };
        }
    }
    
    adicionarLog('alerta', 'Informações do pai não encontradas no documento');
    return undefined;
}

function extrairInformacaoMae(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): CavaloInfo | undefined {
    // Padrão para localizar informações da mãe
    const padroes = [
        /MÃE\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)(?:\s*[-–]|$)/i,
        /MATRIZ\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)(?:\s*[-–]|$)/i,
        /MATRIZ\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)\s+Reg\.\s*[:;]?\s*([0-9]+)/i
    ];
    
    for (const padrao of padroes) {
        const match = texto.match(padrao);
        
        if (match && match[1]) {
            const nomeMae = match[1].trim();
            let registroMae: string | undefined = undefined;
            
            // Se o padrão incluir o registro
            if (match[2]) {
                registroMae = match[2].trim();
            } else {
                // Tentar encontrar o registro próximo ao nome
                const regexRegistro = new RegExp(`${nomeMae}\\s+Reg\\.?\\s*[:;]?\\s*([0-9]+)`, 'i');
                const matchRegistro = texto.match(regexRegistro);
                
                if (matchRegistro && matchRegistro[1]) {
                    registroMae = matchRegistro[1].trim();
                }
            }
            
            adicionarLog('debug', `Mãe encontrada: ${nomeMae}${registroMae ? ` (Reg: ${registroMae})` : ''}`);
            
            return {
                nome: nomeMae,
                registro: registroMae
            };
        }
    }
    
    adicionarLog('alerta', 'Informações da mãe não encontradas no documento');
    return undefined;
}

/**
 * Extrai informações de avós e bisavós do texto
 */
function extrairInformacaoAvos(texto: string, adicionarLog: (nivel: string, mensagem: string) => void): {
    avoPai?: CavaloInfo,
    avaMae?: CavaloInfo,
    avoPai2?: CavaloInfo,
    avaMae2?: CavaloInfo,
    bisavos?: CavaloInfo[]
} {
    const resultado: {
        avoPai?: CavaloInfo,
        avaMae?: CavaloInfo,
        avoPai2?: CavaloInfo,
        avaMae2?: CavaloInfo,
        bisavos?: CavaloInfo[]
    } = {
        bisavos: []
    };
    
    // Padrões para localizar avós paternos
    const padroesAvoPai = [
        /AVÔ\s+PATERNO\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)(?:\s*[-–]|$)/i,
        /PAI\s+DO\s+PAI\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)/i
    ];
    
    for (const padrao of padroesAvoPai) {
        const match = texto.match(padrao);
        
        if (match && match[1]) {
            resultado.avoPai = {
                nome: match[1].trim(),
                tipo: 'Avô Paterno'
            };
            
            adicionarLog('debug', `Avô paterno encontrado: ${resultado.avoPai.nome}`);
            break;
        }
    }
    
    // Padrões para localizar avós maternas
    const padroesAvaMae = [
        /AVÓ\s+MATERNA\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)(?:\s*[-–]|$)/i,
        /MÃE\s+DA\s+MÃE\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)/i
    ];
    
    for (const padrao of padroesAvaMae) {
        const match = texto.match(padrao);
        
        if (match && match[1]) {
            resultado.avaMae = {
                nome: match[1].trim(),
                tipo: 'Avó Materna'
            };
            
            adicionarLog('debug', `Avó materna encontrada: ${resultado.avaMae.nome}`);
            break;
        }
    }
    
    // Padrões para localizar avó paterna
    const padroesAvaMaePai = [
        /AVÓ\s+PATERNA\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)(?:\s*[-–]|$)/i,
        /MÃE\s+DO\s+PAI\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)/i
    ];
    
    for (const padrao of padroesAvaMaePai) {
        const match = texto.match(padrao);
        
        if (match && match[1]) {
            resultado.avaMae2 = {
                nome: match[1].trim(),
                tipo: 'Avó Paterna'
            };
            
            adicionarLog('debug', `Avó paterna encontrada: ${resultado.avaMae2.nome}`);
            break;
        }
    }
    
    // Padrões para localizar avô materno
    const padroesAvoPaiMae = [
        /AVÔ\s+MATERNO\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)(?:\s*[-–]|$)/i,
        /PAI\s+DA\s+MÃE\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+)/i
    ];
    
    for (const padrao of padroesAvoPaiMae) {
        const match = texto.match(padrao);
        
        if (match && match[1]) {
            resultado.avoPai2 = {
                nome: match[1].trim(),
                tipo: 'Avô Materno'
            };
            
            adicionarLog('debug', `Avô materno encontrado: ${resultado.avoPai2.nome}`);
            break;
        }
    }
    
    // Padrões para localizar bisavós
    const padroesBisavos = [
        /BISAVÔ\s+([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)(?:\s*[-–]|$)/ig,
        /BISAVÓ\s+([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)\s*[:;]?\s*([A-ZÁÉÍÓÚÃÕÂÊÎÔÛÇ\s]+?)(?:\s*[-–]|$)/ig
    ];
    
    for (const padrao of padroesBisavos) {
        let match;
        
        while ((match = padrao.exec(texto)) !== null) {
            if (match[1] && match[2]) {
                const tipoParente = match[1].trim(); // Ex: "PATERNO", "MATERNO"
                const nome = match[2].trim();
                
                const bisavo: CavaloInfo = {
                    nome: nome,
                    tipo: `Bisavô ${tipoParente}`
                };
                
                resultado.bisavos?.push(bisavo);
                adicionarLog('debug', `Bisavô ${tipoParente} encontrado: ${nome}`);
            }
        }
    }
    
    return resultado;
}

/**
 * Salva um log de debug detalhado do processamento do PDF
 */
export function salvarLogDebug(dados: GenealogiaInfo, logs: { nivel: string, mensagem: string, timestamp: string }[]): string {
    // Formatar JSON para salvar
    const logData = {
        dados,
        logs,
        timestamp: new Date().toISOString()
    };
    
    const logContent = JSON.stringify(logData, null, 2);
    
    // Criar nome de arquivo único
    const timestamp = Date.now();
    const nomeArquivo = `debug_pdf_${timestamp}.json`;
    const caminhoArquivo = `./logs/${nomeArquivo}`;
    
    // Garantir que o diretório de logs existe
    if (!fs.existsSync('./logs')) {
        fs.mkdirSync('./logs', { recursive: true });
    }
    
    // Salvar o arquivo
    fs.writeFileSync(caminhoArquivo, logContent, 'utf8');
    
    return nomeArquivo;
}