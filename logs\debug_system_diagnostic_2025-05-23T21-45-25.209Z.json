{"timestamp": "2025-05-23T21:45:25.209Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 409268224, "heapTotal": 120057856, "heapUsed": 89230216, "external": 8640535, "arrayBuffers": 628879}, "uptime": 6.273111129, "cpuUsage": {"user": 3767394, "system": 500919}, "resourceUsage": {"userCPUTime": 3767399, "systemCPUTime": 500920, "maxRSS": 399676, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106907, "majorPageFault": 0, "swappedOut": 0, "fsRead": 24632, "fsWrite": 88, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 10818, "involuntaryContextSwitches": 14148}}