/**
 * Hook personalizado para autenticação de usuários no EquiGestor
 * 
 * Fornece acesso ao usuário atual, funções de login e logout,
 * e status de carregamento dos dados de autenticação.
 */
export function useAuth() {
  // Verificar se o usuário está autenticado
  const getUserFromStorage = () => {
    try {
      const storedUser = localStorage.getItem('user');
      return storedUser ? JSON.parse(storedUser) : null;
    } catch (e) {
      console.error('Erro ao recuperar usuário:', e);
      return null;
    }
  };
  
  // Recuperar o usuário do armazenamento local
  const user = getUserFromStorage();
  
  // Estado de carregamento (sempre falso porque lê diretamente do localStorage)
  const isLoading = false;
  
  // Função para login
  const login = (userData: any) => {
    localStorage.setItem('user', JSON.stringify(userData));
    window.location.href = '/'; // Redirecionar para a página inicial após login
  };
  
  // Função para logout
  const logout = () => {
    localStorage.removeItem('user');
    window.location.href = '/login'; // Redirecionar para a página de login após logout
  };
  
  return {
    user,
    isLoading,
    login,
    logout
  };
}