/**
 * Página dedicada para gerenciar fotos de cavalos
 */
import React, { useState } from 'react';
import { useParams } from 'wouter';
import { ArrowLeft, Camera, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import HorsePhotoUpload from '@/components/horse/HorsePhotoUpload';
import HorsePhotoGallery from '@/components/horse/HorsePhotoGallery';
import { useLocation } from 'wouter';

interface Horse {
  id: number;
  name: string;
  breed: string;
  birthDate: string;
}

export default function HorsePhotosPage() {
  const params = useParams();
  const horseId = parseInt(params.id || '0');
  const [, setLocation] = useLocation();
  const [showUpload, setShowUpload] = useState(false);
  const { toast } = useToast();

  // Buscar dados do cavalo
  const { data: horse, isLoading: horseLoading } = useQuery<Horse>({
    queryKey: [`/api/cavalos/${horseId}`],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/cavalos/${horseId}`);
      return response;
    },
    enabled: !!horseId && horseId > 0,
  });

  const handleUploadSuccess = () => {
    setShowUpload(false);
    toast({
      title: "Sucesso!",
      description: "Foto adicionada com sucesso à galeria.",
    });
  };

  if (horseLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Carregando informações do cavalo...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!horse) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Cavalo não encontrado</h1>
          <Button onClick={() => setLocation('/cavalos')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar para lista de cavalos
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            onClick={() => setLocation(`/cavalos/${horseId}`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Camera className="h-6 w-6" />
              Fotos - {horse.name}
            </h1>
            <p className="text-gray-600">
              {horse.breed} • Nascido em {new Date(horse.birthDate).toLocaleDateString('pt-BR')}
            </p>
          </div>
        </div>

        <Button onClick={() => setShowUpload(!showUpload)}>
          <Plus className="h-4 w-4 mr-2" />
          Adicionar Foto
        </Button>
      </div>

      {/* Conteúdo */}
      <div className="space-y-6">
        {/* Upload de foto */}
        {showUpload && (
          <Card>
            <CardHeader>
              <CardTitle>Upload de Nova Foto</CardTitle>
            </CardHeader>
            <CardContent>
              <HorsePhotoUpload
                horseId={horseId}
                onUploadSuccess={handleUploadSuccess}
                onClose={() => setShowUpload(false)}
              />
            </CardContent>
          </Card>
        )}

        {/* Galeria de fotos */}
        <Card>
          <CardHeader>
            <CardTitle>Galeria de Fotos</CardTitle>
          </CardHeader>
          <CardContent>
            <HorsePhotoGallery horseId={horseId} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}