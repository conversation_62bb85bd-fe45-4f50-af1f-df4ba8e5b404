{"timestamp": "2025-05-23T01:07:27.500Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 422662144, "heapTotal": 130523136, "heapUsed": 110031288, "external": 8405805, "arrayBuffers": 273850}, "uptime": 2.533722293, "cpuUsage": {"user": 3163470, "system": 399378}, "resourceUsage": {"userCPUTime": 3163524, "systemCPUTime": 399385, "maxRSS": 412756, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105082, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8898, "involuntaryContextSwitches": 10226}}