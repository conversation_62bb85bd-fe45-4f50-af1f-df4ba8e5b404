{"timestamp": "2025-05-17T00:26:34.276Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 269541376, "heapTotal": 118546432, "heapUsed": 90667184, "external": 8391997, "arrayBuffers": 243725}, "uptime": 1.838059515, "cpuUsage": {"user": 2787831, "system": 343879}, "resourceUsage": {"userCPUTime": 2787897, "systemCPUTime": 343887, "maxRSS": 330964, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104250, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8701, "involuntaryContextSwitches": 2161}}