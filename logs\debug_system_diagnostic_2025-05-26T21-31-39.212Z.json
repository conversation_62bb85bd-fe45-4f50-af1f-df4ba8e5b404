{"timestamp": "2025-05-26T21:31:39.212Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397131776, "heapTotal": 115122176, "heapUsed": 75271728, "external": 8235866, "arrayBuffers": 260109}, "uptime": 1.891313967, "cpuUsage": {"user": 2870595, "system": 361356}, "resourceUsage": {"userCPUTime": 2870638, "systemCPUTime": 361361, "maxRSS": 387824, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104992, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 24, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8376, "involuntaryContextSwitches": 4258}}