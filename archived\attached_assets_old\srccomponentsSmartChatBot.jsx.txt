// src/components/SmartChatBot.jsx
import React, { useState, useEffect, useRef } from 'react';
import { useChatCollector } from '../hooks/useChatCollector';

const SmartChatBot = ({ onSendToAI }) => {
  const {
    messages,
    userInput,
    setUserInput,
    isListening,
    startListening,
    stopListening,
    handleUserMessage,
    clearBuffer
  } = useChatCollector();

  const handleSend = () => {
    if (userInput.trim()) {
      handleUserMessage(userInput);
      setUserInput('');
    }
  };

  const handleFinalize = () => {
    const fullContext = messages.map(m => m.content).join('\n');
    onSendToAI(fullContext);
    clearBuffer();
  };

  return (
    <div className="w-full max-w-md mx-auto p-4 border rounded-2xl shadow-xl bg-white">
      <div className="h-80 overflow-y-auto border-b mb-4 pb-2">
        {messages.map((msg, i) => (
          <div
            key={i}
            className={`mb-2 text-sm ${msg.role === 'user' ? 'text-right' : 'text-left text-gray-500'}`}
          >
            {msg.content}
          </div>
        ))}
      </div>

      <div className="flex items-center gap-2">
        <input
          value={userInput}
          onChange={e => setUserInput(e.target.value)}
          placeholder="Digite sua mensagem..."
          className="flex-grow border p-2 rounded-xl"
        />
        <button onClick={handleSend} className="p-2 rounded-xl bg-blue-500 text-white">
          Enviar
        </button>
        <button
          onClick={isListening ? stopListening : startListening}
          className={`p-2 rounded-xl ${isListening ? 'bg-red-500' : 'bg-green-500'} text-white`}
        >
          🎤
        </button>
      </div>

      {messages.length >= 2 && (
        <button
          onClick={handleFinalize}
          className="mt-4 w-full p-2 bg-emerald-600 text-white rounded-xl"
        >
          ✅ Pronto, pode mandar
        </button>
      )}
    </div>
  );
};

export default SmartChatBot;
