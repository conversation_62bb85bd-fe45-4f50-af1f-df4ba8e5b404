{"timestamp": "2025-05-15T20:56:48.572Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 261095424, "heapTotal": 112500736, "heapUsed": 72442928, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.64387964, "cpuUsage": {"user": 2817225, "system": 428877}, "resourceUsage": {"userCPUTime": 2817293, "systemCPUTime": 428877, "maxRSS": 291972, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105414, "majorPageFault": 7, "swappedOut": 0, "fsRead": 33320, "fsWrite": 608, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8657, "involuntaryContextSwitches": 3089}}