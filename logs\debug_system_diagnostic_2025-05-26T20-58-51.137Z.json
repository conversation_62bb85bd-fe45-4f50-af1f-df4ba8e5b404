{"timestamp": "2025-05-26T20:58:51.137Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403992576, "heapTotal": 117899264, "heapUsed": 95145048, "external": 8549178, "arrayBuffers": 282977}, "uptime": 1.946670023, "cpuUsage": {"user": 2912973, "system": 361975}, "resourceUsage": {"userCPUTime": 2913015, "systemCPUTime": 361975, "maxRSS": 394524, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105800, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8566, "involuntaryContextSwitches": 2774}}