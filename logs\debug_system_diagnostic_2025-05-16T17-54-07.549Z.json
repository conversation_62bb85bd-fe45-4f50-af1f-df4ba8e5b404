{"timestamp": "2025-05-16T17:54:07.549Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 310444032, "heapTotal": 116432896, "heapUsed": 72590992, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.916511415, "cpuUsage": {"user": 2767694, "system": 401639}, "resourceUsage": {"userCPUTime": 2767752, "systemCPUTime": 401648, "maxRSS": 303168, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103292, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8735, "involuntaryContextSwitches": 3891}}