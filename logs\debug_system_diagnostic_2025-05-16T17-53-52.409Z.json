{"timestamp": "2025-05-16T17:53:52.408Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 259960832, "heapTotal": 114335744, "heapUsed": 72601632, "external": 8219482, "arrayBuffers": 243725}, "uptime": 3.135127387, "cpuUsage": {"user": 2913844, "system": 464615}, "resourceUsage": {"userCPUTime": 2913893, "systemCPUTime": 464623, "maxRSS": 295956, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104347, "majorPageFault": 0, "swappedOut": 0, "fsRead": 26856, "fsWrite": 168, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7112, "involuntaryContextSwitches": 8656}}