chunk-RPCDYKBN.js?v=d2934ca9:21551 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
firebase.ts:11 [Firebase] Initializing Firebase with config: {"apiKey":"*****","authDomain":"cavalo-409dc.firebaseapp.com","projectId":"cavalo-409dc","storageBucket":"cavalo-409dc.firebasestorage.app","messagingSenderId":"672662898514","appId":"1:672662898514:web:f1b7678d4e1baf11a05c8b"}
logger.ts:200 [2025-05-31T00:15:25.786Z] [INFO] [logger]: Sistema de log inicializado Object
AuthContext.tsx:6 [AuthContext] AuthProvider rendered
ProtectedRoute.tsx:29 ProtectedRoute: Verificando autenticação...
ProtectedRoute.tsx:34 ProtectedRoute: Usuário encontrado no localStorage
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
queryClient.ts:289 Request cancelada pelo React Query
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
main.tsx:16 Service Worker registrado com sucesso: https://63ed2eba-d4d9-45aa-800f-0ebe7fd3e860-00-122e7dsebuel9.riker.replit.dev/
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
HorseDetails.tsx:74 Buscando cavalo com ID 129
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
HorseDetails.tsx:99 Buscando manejos para o cavalo 129
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
HorseDetails.tsx:124 Buscando arquivos para o cavalo 129
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
HorseDetails.tsx:126 Arquivos recebidos: [{…}]
HorseDetails.tsx:101 Manejos recebidos: []
HorseDetails.tsx:76 Cavalo recebido: {id: 129, name: 'BELLA DONA DO OITO', breed: 'Crioulo', birthDate: '04/11/2023', peso: 444, …}altura: 44avoMaterno: "vo teste"avoPaterno: "avo "birthDate: "04/11/2023"breed: "Crioulo"cor: "COLORADA"createdAt: "2025-05-17T00:29:20.003Z"criador: "CAMILA WEBER"dataCompra: nulldataEntrada: nulldataSaida: nullid: 129inspetor: "FREDERICO VIEIRA ARAUJO CRMV - 7052/RS"isExternal: falsemaeId: 130maeNome: "RZ ÉS BUENA DA CARAPUÇA"motivoSaida: nullname: "BELLA DONA DO OITO"notes: nullnumeroRegistro: "B675850"origem: nullpaiId: 93paiNome: "RZ NO ME TOQUES II DA CARAPUÇA"pelagemId: 16peso: 444proprietario: "FELIPE CACCIA MACIEL"sexo: "FÊMEA"status: "ativo"userId: 1valorCompra: null[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (…)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
HorseDetails.tsx:74 Buscando cavalo com ID 124
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
HorseDetails.tsx:99 Buscando manejos para o cavalo 124
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
HorseDetails.tsx:124 Buscando arquivos para o cavalo 124
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
HorseDetails.tsx:126 Arquivos recebidos: []
HorseDetails.tsx:101 Manejos recebidos: []
HorseDetails.tsx:76 Cavalo recebido: {id: 124, name: 'RZ CONDOLEZA DA CARAPUÇA', breed: 'Crioulo', birthDate: '29/10/2010', peso: null, …}altura: nullavoMaterno: nullavoPaterno: nullbirthDate: "29/10/2010"breed: "Crioulo"cor: "TOSTADA REQUEIMADA RABICANA"createdAt: "2025-05-17T00:13:16.993Z"criador: "RUBENS ELIAS ZOGBI"dataCompra: nulldataEntrada: nulldataSaida: nullid: 124inspetor: "CARLOS MARQUES GONÇALVES NETO"isExternal: falsemaeId: 126maeNome: "RZ LUNA VIEJA DA CARAPUÇA"motivoSaida: nullname: "RZ CONDOLEZA DA CARAPUÇA"notes: nullnumeroRegistro: "B358314"origem: nullpaiId: 125paiNome: "RZ RAMPLA DA CARAPUÇA"pelagemId: 14peso: nullproprietario: "CAMILA WEBER"sexo: "FÊMEA"status: "ativo"userId: 1valorCompra: null[[Prototype]]: Object
Genealogia.tsx:97 Tentando obter nome do ancestral - ID: 125, Nome resolvido: null
Genealogia.tsx:97 Tentando obter nome do ancestral - ID: 126, Nome resolvido: null
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
Genealogia.tsx:47 Usando nome externo do pai: RZ RAMPLA DA CARAPUÇA
Genealogia.tsx:57 Resolvendo nomes de pai/mãe a partir dos IDs {pai_id: 125, mae_id: 126, todos_cavalos: Array(55)}
Genealogia.tsx:97 Tentando obter nome do ancestral - ID: 125, Nome resolvido: RZ RAMPLA DA CARAPUÇA
Genealogia.tsx:97 Tentando obter nome do ancestral - ID: 126, Nome resolvido: null
SimpleGenealogy.tsx:141 🧬 [SimpleGenealogy] Valores genealogia nova (dual): {paiId: 125, paiNome: 'RZ RAMPLA DA CARAPUÇA', maeId: 126, maeNome: 'RZ LUNA VIEJA DA CARAPUÇA', pai: undefined, …}mae: undefinedmaeId: 126maeNome: "RZ LUNA VIEJA DA CARAPUÇA"pai: undefinedpaiId: 125paiNome: "RZ RAMPLA DA CARAPUÇA"[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (…)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}content-type: "application/json"user-id: "1"[[Prototype]]: Object
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: {content-type: 'application/json', user-id: '1'}
