{"timestamp": "2025-05-15T21:45:18.361Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 289505280, "heapTotal": 116957184, "heapUsed": 72478104, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.766308911, "cpuUsage": {"user": 2793183, "system": 356777}, "resourceUsage": {"userCPUTime": 2793250, "systemCPUTime": 356777, "maxRSS": 282720, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104071, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 48, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9150, "involuntaryContextSwitches": 3719}}