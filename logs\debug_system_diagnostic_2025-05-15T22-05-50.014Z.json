{"timestamp": "2025-05-15T22:05:50.013Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 398524416, "heapTotal": 110665728, "heapUsed": 72486200, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.413497114, "cpuUsage": {"user": 2971560, "system": 360019}, "resourceUsage": {"userCPUTime": 2971615, "systemCPUTime": 360026, "maxRSS": 389184, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102556, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7300, "involuntaryContextSwitches": 6394}}