import { useState, useEffect } from 'react';
import { useLocation, Link } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { <PERSON><PERSON><PERSON> } from '@shared/schema';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ChevronLeft, Upload, FileImage, FileText, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const UploadPage = () => {
  const [, setLocation] = useLocation();
  const location = useLocation()[0];
  
  // Extrair horseId da URL - suporta duas formas:
  // 1. /upload?horseId=123 (forma atual via parâmetro de consulta)
  // 2. /cavalo/123/upload (forma mais RESTful)
  let horseId = null;
  
  // Verificar primeiro o formato RESTful (/cavalo/123/upload)
  const restfulMatch = location.match(/\/cavalo\/(\d+)\/upload/);
  if (restfulMatch && restfulMatch[1]) {
    horseId = restfulMatch[1];
  } else {
    // Verificar o formato de parâmetro de consulta (/upload?horseId=123)
    const params = new URLSearchParams(location.split('?')[1] || '');
    horseId = params.get('horseId');
  }
  
  // Log para depuração
  console.log("URL path:", location);
  console.log("horseId extraído:", horseId);
  
  const [user, setUser] = useState(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch {
        return null;
      }
    }
    return null;
  });
  
  const { toast } = useToast();
  
  const [fileName, setFileName] = useState('');
  const [fileType, setFileType] = useState('');
  const [description, setDescription] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  
  // Buscar informações do cavalo se houver um horseId
  const { 
    data: horse,
    isLoading: isLoadingHorse
  } = useQuery({
    queryKey: [`/api/cavalos/${horseId}`],
    enabled: !!horseId && !!user,
    queryFn: async () => {
      try {
        const cavalo = await apiRequest<Cavalo>('GET', `/api/cavalos/${horseId}`);
        return cavalo;
      } catch (error) {
        console.error("Erro ao buscar cavalo:", error);
        toast({
          title: "Erro",
          description: "Não foi possível buscar informações do cavalo",
          variant: "destructive"
        });
        return null;
      }
    }
  });
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setSelectedFile(file);
      
      // Extrair nome do arquivo - remover extensão para visualização mais limpa
      const fileNameWithoutExt = file.name.split('.').slice(0, -1).join('.');
      setFileName(fileNameWithoutExt || file.name);
      
      // Determinar o tipo de arquivo com base na extensão
      const extension = file.name.split('.').pop()?.toLowerCase() || '';
      let detectedType = 'document';
      
      if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
        detectedType = 'image';
      } else if (['mp4', 'webm', 'mov', 'avi'].includes(extension)) {
        detectedType = 'video';
      } else if (extension === 'pdf') {
        detectedType = 'pdf';
      }
      
      setFileType(detectedType);
      
      // Log de informações do arquivo para depuração
      console.log('Arquivo selecionado:', {
        name: file.name,
        size: `${Math.round(file.size / 1024)}KB`,
        extension,
        detectedType,
        mimeType: file.type
      });
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile) {
      toast({
        title: "Erro",
        description: "Por favor, selecione um arquivo para upload",
        variant: "destructive"
      });
      return;
    }
    
    if (!horseId) {
      toast({
        title: "Erro",
        description: "ID do cavalo não encontrado",
        variant: "destructive"
      });
      return;
    }
    
    if (!user) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado",
        variant: "destructive"
      });
      return;
    }
    
    setIsUploading(true);
    
    // Criar um objeto FormData para enviar o arquivo
    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('fileName', fileName);
    formData.append('fileType', fileType);
    formData.append('description', description);
    formData.append('horseId', horseId);
    
    try {
      // Verificar e converter horseId para número
      const horseIdNumber = horseId ? parseInt(horseId) : null;
      
      // Verificação adicional de segurança
      if (!horseIdNumber || isNaN(horseIdNumber)) {
        throw new Error("ID do cavalo inválido");
      }
      
      // Garantir que o arquivo tenha uma extensão apropriada com base no tipo
      let fileNameWithExt = fileName.trim();
      const extension = selectedFile.name.split('.').pop()?.toLowerCase();
      
      // Se não tiver uma extensão no nome personalizado, adicione a extensão do arquivo original
      if (extension && !fileNameWithExt.includes('.')) {
        fileNameWithExt = `${fileNameWithExt}.${extension}`;
      }
      
      // Preparar os dados para o servidor, garantindo que todos os campos obrigatórios estejam preenchidos
      const uploadData = {
        fileName: fileNameWithExt, // Nome com extensão para facilitar visualização
        filePath: `/uploads/${fileNameWithExt}`, // Caminho virtual com extensão
        fileType: fileType || 'document', // Fallback para 'document' se não estiver definido
        description: description || '', // Descrição opcional
        horseId: horseIdNumber, // ID do cavalo (obrigatório)
        userId: user!.id // ID do usuário (obrigatório)
      };
      
      console.log("Enviando dados para o servidor:", uploadData);
      
      // Enviar para o servidor
      const responseData = await apiRequest('POST', '/api/arquivos', uploadData);
      
      toast({
        title: "Sucesso",
        description: "Arquivo enviado com sucesso",
        variant: "default"
      });
      
      // Redirecionar para a página de detalhes do cavalo
      setLocation(`/cavalo/${horseId}`);
    } catch (error: any) {
      console.error("Erro ao enviar arquivo:", error);
      
      // Mostrar mensagem de erro mais específica
      let errorMessage = "Não foi possível enviar o arquivo";
      
      if (error?.message) {
        if (error.message === "ID do cavalo inválido") {
          errorMessage = "ID do cavalo inválido. Por favor, acesse esta página a partir da página de detalhes do cavalo.";
        } else {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: "Erro no upload",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };
  
  return (
    <div className="max-w-3xl mx-auto px-4 py-8">
      <div className="mb-6">
        <Link href={horseId ? `/cavalo/${horseId}` : '/'}>
          <Button variant="ghost" className="mb-4 -ml-3 text-gray-500 hover:text-gray-700">
            <ChevronLeft className="mr-1 h-4 w-4" />
            {horseId ? 'Voltar para os detalhes do cavalo' : 'Voltar para o Dashboard'}
          </Button>
        </Link>
        
        <h1 className="text-2xl font-bold text-gray-900">Upload de Arquivo</h1>
        {horse && (
          <p className="mt-1 text-gray-500">
            Adicionar arquivo para o cavalo: <span className="font-medium">{horse.name}</span>
          </p>
        )}
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Enviar Arquivo</CardTitle>
          <CardDescription>
            Escolha um arquivo para upload e preencha as informações relevantes.
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <Alert className="mb-6 bg-amber-50 text-amber-800 border-amber-200">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Aviso</AlertTitle>
              <AlertDescription>
                Funcionalidade em desenvolvimento. Nesta versão, os arquivos são apenas registrados no banco de dados, mas não são armazenados fisicamente.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-2">
              <Label htmlFor="file">Selecionar Arquivo</Label>
              <Input 
                id="file" 
                type="file" 
                onChange={handleFileChange}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="fileName">Nome do Arquivo</Label>
              <Input 
                id="fileName" 
                value={fileName} 
                onChange={(e) => setFileName(e.target.value)}
                placeholder="Nome do arquivo"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="fileType">Tipo de Arquivo</Label>
              <Select value={fileType} onValueChange={setFileType} required>
                <SelectTrigger id="fileType">
                  <SelectValue placeholder="Selecione o tipo de arquivo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="image">Imagem</SelectItem>
                  <SelectItem value="video">Vídeo</SelectItem>
                  <SelectItem value="pdf">Documento PDF</SelectItem>
                  <SelectItem value="document">Outro documento</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Descrição</Label>
              <Textarea 
                id="description" 
                value={description} 
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Descrição opcional do arquivo"
                rows={3}
              />
            </div>
            
            {!horseId && (
              <Alert variant="destructive" className="mt-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Erro</AlertTitle>
                <AlertDescription>
                  ID do cavalo não encontrado. Por favor, inicie o upload a partir da página de detalhes do cavalo.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Link href={horseId ? `/cavalo/${horseId}` : '/'}>
              <Button variant="outline" type="button">Cancelar</Button>
            </Link>
            <Button type="submit" disabled={isUploading || !horseId || !selectedFile}>
              {isUploading ? 'Enviando...' : 'Enviar Arquivo'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default UploadPage;