{"timestamp": "2025-05-23T05:31:17.577Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399470592, "heapTotal": 117637120, "heapUsed": 97445632, "external": 8529103, "arrayBuffers": 299361}, "uptime": 1.813948247, "cpuUsage": {"user": 2882294, "system": 360241}, "resourceUsage": {"userCPUTime": 2882337, "systemCPUTime": 360247, "maxRSS": 390108, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 110163, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8859, "involuntaryContextSwitches": 1519}}