{"timestamp": "2025-05-14T23:59:54.303Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 795889664, "heapTotal": 463892480, "heapUsed": 432448968, "external": 19425520, "arrayBuffers": 7680555}, "uptime": 16.765505259, "cpuUsage": {"user": 14844804, "system": 868228}, "resourceUsage": {"userCPUTime": 14844807, "systemCPUTime": 868228, "maxRSS": 777236, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 209164, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 296, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 17194, "involuntaryContextSwitches": 19620}}