{"timestamp": "2025-05-15T16:27:15.987Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 384139264, "heapTotal": 93200384, "heapUsed": 72787176, "external": 7133349, "arrayBuffers": 98802}, "uptime": 1.687685574, "cpuUsage": {"user": 2234580, "system": 313309}, "resourceUsage": {"userCPUTime": 2234627, "systemCPUTime": 313315, "maxRSS": 375136, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99427, "majorPageFault": 0, "swappedOut": 0, "fsRead": 18520, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6857, "involuntaryContextSwitches": 1887}}