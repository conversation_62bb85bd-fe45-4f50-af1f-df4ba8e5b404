client:495 [vite] connecting...
client:618 [vite] connected.
chunk-RPCDYKBN.js?v=d2934ca9:21551 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
firebase.ts:11 [Firebase] Initializing Firebase with config: {"apiKey":"*****","authDomain":"cavalo-409dc.firebaseapp.com","projectId":"cavalo-409dc","storageBucket":"cavalo-409dc.firebasestorage.app","messagingSenderId":"672662898514","appId":"1:672662898514:web:f1b7678d4e1baf11a05c8b"}
logger.ts:200 [2025-06-02T02:27:46.462Z] [INFO] [logger]: Sistema de log inicializado Object
AuthContext.tsx:6 [AuthContext] AuthProvider rendered
ProtectedRoute.tsx:29 ProtectedRoute: Verificando autenticação...
ProtectedRoute.tsx:34 ProtectedRoute: Usuário encontrado no localStorage
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
main.tsx:16 Service Worker registrado com sucesso: https://63ed2eba-d4d9-45aa-800f-0ebe7fd3e860-00-122e7dsebuel9.riker.replit.dev/
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
queryClient.ts:113 User ID para autenticação: 1
queryClient.ts:114 Headers enviados: Object
queryClient.ts:181 Erro na requisição POST /api/genealogia: Error: 500: {"message":"Erro ao criar registro de genealogia"}
    at apiRequest (queryClient.ts:164:15)
    at async handleSalvar (EditarGenealogiaCompleta.tsx:176:9)
apiRequest @ queryClient.ts:181
EditarGenealogiaCompleta.tsx:188 Erro ao salvar: Error: 500: {"message":"Erro ao criar registro de genealogia"}
    at apiRequest (queryClient.ts:164:15)
    at async handleSalvar (EditarGenealogiaCompleta.tsx:176:9)
handleSalvar @ EditarGenealogiaCompleta.tsx:188
AuthContext.tsx:6 [AuthContext] AuthProvider rendered
