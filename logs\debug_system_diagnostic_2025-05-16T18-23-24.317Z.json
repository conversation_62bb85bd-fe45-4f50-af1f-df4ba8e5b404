{"timestamp": "2025-05-16T18:23:24.316Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 392732672, "heapTotal": 109617152, "heapUsed": 72443400, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.586655823, "cpuUsage": {"user": 3179426, "system": 419577}, "resourceUsage": {"userCPUTime": 3179492, "systemCPUTime": 419577, "maxRSS": 383528, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103551, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 40, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8465, "involuntaryContextSwitches": 9522}}