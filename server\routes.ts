import express from 'express';
import { eq, and, asc, desc, or, like, sql } from "drizzle-orm";
import { z } from "zod";
import * as dotenv from 'dotenv';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import { storage, IStorage } from './storage';
import { pool } from './db';
import { enriquecerDadosComSite } from './abccc-scraper-service';
import { 
  insertUserSchema, 
  insertCavaloSchema, 
  insertManejoSchema, 
  insertTaskSchema, 
  insertArquivoSchema,
  insertEventoSchema,
  insertProcedimentoVetSchema,
  insertReproducaoSchema,
  insertMedidaFisicaSchema,
  insertNutricaoSchema,
  insertMorfologiaSchema,
  insertDesempenhoHistoricoSchema,
  insertGenealogiaSchema,
  insertSugestoesCruzamentoSchema
} from '../shared/schema';
import { authenticateUser, setupAuth } from './auth';
import { getModuleLogger, logRequest, logApiError } from './logger';
import { importarRegistroABCCC, previsualizarRegistroABCCC } from './abccc-import-service';

const routeLogger = getModuleLogger('routes');

// Configuração do Multer para upload de arquivos
const uploadsDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Diretório específico para PDFs da ABCCC
const abcccPdfsDir = path.join(process.cwd(), 'uploads', 'abccc_pdfs');
if (!fs.existsSync(abcccPdfsDir)) {
  fs.mkdirSync(abcccPdfsDir, { recursive: true });
}

// Diretório para logs de debug
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

const storage_config = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // Gerar um nome de arquivo único
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const fileExt = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + fileExt);
  }
});

// Configuração específica para PDFs da ABCCC
const abccc_storage_config = multer.diskStorage({
  destination: function(req, file, cb) {
    cb(null, abcccPdfsDir);
  },
  filename: function(req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, 'abccc-' + uniqueSuffix + extension);
  }
});

const upload = multer({ 
  storage: storage_config,
  limits: { fileSize: 10 * 1024 * 1024 }, // Limite de 10MB
  fileFilter: function(req, file, cb) {
    // Verificar tipos de arquivo permitidos
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'video/mp4'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Tipo de arquivo não suportado') as any, false);
    }
  }
});

// Upload específico para PDFs da ABCCC
const uploadAbcccPdf = multer({
  storage: abccc_storage_config,
  limits: { fileSize: 10 * 1024 * 1024 }, // Limite de 10MB
  fileFilter: function(req, file, cb) {
    // Apenas aceita PDFs
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Apenas arquivos PDF são aceitos para importação ABCCC') as any, false);
    }
  }
});

export async function addApiRoutes(app: express.Express) {
  // Rota de login direto (sem middleware complexo)
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;
      
      if (!username || !password) {
        return res.status(400).json({ message: "Nome de usuário e senha são obrigatórios" });
      }
      
      // Buscar usuário
      const user = await storage.getUserByUsername(username);
      
      if (!user) {
        return res.status(401).json({ message: "Credenciais inválidas" });
      }
      
      // Verificar senha
      const bcrypt = await import('bcrypt');
      const isValidPassword = await bcrypt.compare(password, user.password);
      
      if (!isValidPassword) {
        return res.status(401).json({ message: "Credenciais inválidas" });
      }
      
      // Retornar sucesso com dados do usuário
      res.json({ 
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          accessLevel: user.accessLevel || 'user',
          isActive: user.isActive !== false
        },
        token: 'auth-token-' + Date.now(),
        expiration: Date.now() + (24 * 60 * 60 * 1000)
      });
    } catch (error) {
      console.error("Erro no login:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });
  
  // Configurar outras rotas de autenticação
  setupAuth(app);
  
  // Rota para listar todas as pelagens cadastradas
  app.get("/api/pelagens", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      
      // Buscar todas as pelagens do banco ordenadas pelo nome
      const pelagensResult = await pool.query(`
        SELECT id, nome, descricao, fonte 
        FROM pelagens 
        ORDER BY nome ASC
      `);
      
      res.json(pelagensResult.rows || []);
    } catch (error) {
      console.error("Erro ao buscar pelagens:", error);
      res.status(500).json({ message: "Erro ao buscar pelagens" });
    }
  });
  
  // Rota de verificação de conexão com a API e o banco de dados
  app.get("/api/health", async (req, res) => {
    try {
      // Retorna informações básicas sobre o sistema, sem expor detalhes de implementação
      res.json({
        status: "online",
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Erro na verificação de status:", error);
      res.status(500).json({ message: "Serviço indisponível" });
    }
  });

  // Rotas de cavalos (Horses)
  app.get("/api/cavalos", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      
      if (!userId) {
        console.error("GET /api/cavalos: ID do usuário não fornecido no corpo ou cabeçalho");
        return res.status(400).json({ message: "ID do usuário não fornecido" });
      }
      
      // Verificar se deve incluir cavalos externos (padrão é false)
      const incluirExternos = req.query.incluir_externos === 'true';
      
      console.log(`GET /api/cavalos: Buscando cavalos para o usuário ${userId} (incluir externos: ${incluirExternos})`);
      
      // Usar getCavalos que já filtra por !isExternal (somente do plantel)
      const cavalos = await storage.getCavalos(userId);
      console.log(`GET /api/cavalos: Encontrados ${cavalos.length} cavalos do plantel`);
      
      res.json(cavalos);
    } catch (error) {
      console.error("Erro ao buscar cavalos:", error);
      res.status(500).json({ message: "Erro ao buscar cavalos" });
    }
  });
  
  // Rota para buscar todos os cavalos, incluindo os externos (para seleção de genealogia)
  app.get("/api/cavalos-genealogia", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      
      if (!userId) {
        console.error("GET /api/cavalos-genealogia: ID do usuário não fornecido no corpo ou cabeçalho");
        return res.status(400).json({ message: "ID do usuário não fornecido" });
      }
      
      console.log(`GET /api/cavalos-genealogia: Buscando todos os cavalos (incluindo externos) para o usuário ${userId}`);
      
      // Buscar todos os cavalos sem filtrar por isExternal
      // Usando um client SQL direto para simplificar
      const queryResult = await pool.query(`
        SELECT * FROM cavalos WHERE user_id = $1
      `, [userId]);
      
      console.log(`GET /api/cavalos-genealogia: Encontrados ${queryResult.rows.length} cavalos no total`);
      
      // Adicionar informação se o cavalo é do plantel ou externo na resposta
      const cavalosFormatados = queryResult.rows.map(cavalo => ({
        ...cavalo,
        tipo: cavalo.is_external ? 'externo' : 'plantel'
      }));
      
      res.json(cavalosFormatados);
    } catch (error) {
      console.error("Erro ao buscar cavalos para genealogia:", error);
      res.status(500).json({ message: "Erro ao buscar cavalos para genealogia" });
    }
  });

  // Endpoint dedicado para buscar genealogia completa de um cavalo
  app.get("/api/genealogia/:cavaloId", authenticateUser, async (req, res) => {
    try {
      const cavaloId = parseInt(req.params.cavaloId);
      const userId = req.body.userId;
      
      if (!userId) {
        console.error(`GET /api/genealogia/${cavaloId}: ID do usuário não fornecido`);
        return res.status(400).json({ message: "ID do usuário não fornecido" });
      }
      
      console.log(`GET /api/genealogia/${cavaloId}: Buscando genealogia completa para usuário ${userId}`);
      
      // Buscar cavalo principal com todos os dados
      const cavaloResult = await pool.query(`
        SELECT * FROM cavalos WHERE id = $1
      `, [cavaloId]);
      
      if (cavaloResult.rows.length === 0) {
        console.warn(`GET /api/genealogia/${cavaloId}: Cavalo não encontrado`);
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }
      
      const cavalo = cavaloResult.rows[0];
      
      // Buscar dados de genealogia da tabela genealogia
      const genealogiaResult = await pool.query(`
        SELECT * FROM genealogia WHERE horse_id = $1
      `, [cavaloId]);
      
      const genealogia = genealogiaResult.rows.length > 0 ? genealogiaResult.rows[0] : null;
      
      // Buscar pai se houver paiId
      let pai = null;
      let paiNome = null;
      if (cavalo.pai_id) {
        const paiResult = await pool.query(`SELECT * FROM cavalos WHERE id = $1`, [cavalo.pai_id]);
        if (paiResult.rows.length > 0) {
          pai = paiResult.rows[0];
        }
      } else if (cavalo.pai_nome) {
        paiNome = cavalo.pai_nome;
      }
      
      // Buscar mãe se houver maeId
      let mae = null;
      let maeNome = null;
      if (cavalo.mae_id) {
        const maeResult = await pool.query(`SELECT * FROM cavalos WHERE id = $1`, [cavalo.mae_id]);
        if (maeResult.rows.length > 0) {
          mae = maeResult.rows[0];
        }
      } else if (cavalo.mae_nome) {
        maeNome = cavalo.mae_nome;
      }
      
      // Buscar avô paterno - primeiro tenta pela genealogia, depois pela tabela cavalos
      let avoPaterno = null;
      let avoPaternoName = null;
      if (genealogia && genealogia.avo_paterno_id) {
        const avoPaternoResult = await pool.query(`SELECT * FROM cavalos WHERE id = $1`, [genealogia.avo_paterno_id]);
        if (avoPaternoResult.rows.length > 0) {
          avoPaterno = avoPaternoResult.rows[0];
        }
        avoPaternoName = genealogia.avo_paterno;
      } else if (cavalo.avo_paterno_id) {
        const avoPaternoResult = await pool.query(`SELECT * FROM cavalos WHERE id = $1`, [cavalo.avo_paterno_id]);
        if (avoPaternoResult.rows.length > 0) {
          avoPaterno = avoPaternoResult.rows[0];
        }
        avoPaternoName = cavalo.avo_paterno;
      } else {
        avoPaternoName = cavalo.avo_paterno;
      }
      
      // Buscar avô materno - primeiro tenta pela genealogia, depois pela tabela cavalos
      let avoMaterno = null;
      let avoMaternoName = null;
      if (genealogia && genealogia.avo_materno_id) {
        const avoMaternoResult = await pool.query(`SELECT * FROM cavalos WHERE id = $1`, [genealogia.avo_materno_id]);
        if (avoMaternoResult.rows.length > 0) {
          avoMaterno = avoMaternoResult.rows[0];
        }
        avoMaternoName = genealogia.avo_materno;
      } else if (cavalo.avo_materno_id) {
        const avoMaternoResult = await pool.query(`SELECT * FROM cavalos WHERE id = $1`, [cavalo.avo_materno_id]);
        if (avoMaternoResult.rows.length > 0) {
          avoMaterno = avoMaternoResult.rows[0];
        }
        avoMaternoName = cavalo.avo_materno;
      } else {
        avoMaternoName = cavalo.avo_materno;
      }
      
      // Buscar avós femininas
      let avoPaterna = null;
      let avoPaternaName = null;
      if (genealogia && genealogia.avo_paterna_id) {
        const avoPaternaResult = await pool.query(`SELECT * FROM cavalos WHERE id = $1`, [genealogia.avo_paterna_id]);
        if (avoPaternaResult.rows.length > 0) {
          avoPaterna = avoPaternaResult.rows[0];
        }
      }
      if (genealogia && genealogia.avo_paterna) {
        avoPaternaName = genealogia.avo_paterna;
      }
      
      let avoMaterna = null;
      let avoMaternaName = null;
      if (genealogia && genealogia.avo_materna_id) {
        const avoMaternaResult = await pool.query(`SELECT * FROM cavalos WHERE id = $1`, [genealogia.avo_materna_id]);
        if (avoMaternaResult.rows.length > 0) {
          avoMaterna = avoMaternaResult.rows[0];
        }
      }
      if (genealogia && genealogia.avo_materna) {
        avoMaternaName = genealogia.avo_materna;
      }
      
      // Montar resposta completa
      const genealogiaCompleta = {
        cavalo: cavalo,
        pai: pai,
        paiNome: paiNome,
        mae: mae,
        maeNome: maeNome,
        avoPaterno: avoPaterno,
        avoPaternoId: genealogia?.avo_paterno_id || null,
        avoPaternoName: avoPaternoName,
        avoMaterno: avoMaterno,
        avoMaternoId: genealogia?.avo_materno_id || null,
        avoMaternoName: avoMaternoName,
        avoPaterna: avoPaterna,
        avoPaternaId: genealogia?.avo_paterna_id || null,
        avoPaternaName: avoPaternaName,
        avoMaterna: avoMaterna,
        avoMaternaId: genealogia?.avo_materna_id || null,
        avoMaternaName: avoMaternaName,
        genealogia: genealogia,
        // Bisavós (nomes textuais da genealogia)
        bisavos: genealogia ? {
          paterno: {
            paterno: genealogia.bisavo_paterno_paterno,
            paterna: genealogia.bisavo_paterna_paterno,
            materno: genealogia.bisavo_materno_paterno,
            materna: genealogia.bisavo_materna_paterno
          },
          materno: {
            paterno: genealogia.bisavo_paterno_materno,
            paterna: genealogia.bisavo_paterna_materno,
            materno: genealogia.bisavo_materno_materno,
            materna: genealogia.bisavo_materna_materno
          }
        } : null
      };
      
      console.log(`GET /api/genealogia/${cavaloId}: Genealogia encontrada com ${pai ? 'pai' : 'sem pai'}, ${mae ? 'mãe' : 'sem mãe'}`);
      
      res.json(genealogiaCompleta);
    } catch (error) {
      console.error(`Erro ao buscar genealogia completa:`, error);
      res.status(500).json({ message: "Erro ao buscar genealogia" });
    }
  });

  app.get("/api/cavalos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      
      // O userId já está disponível no req.body após o middleware authenticateUser
      const userId = req.body.userId;
      
      console.log(`GET /api/cavalos/${id}: Buscando cavalo para usuário ${userId}`);
      
      if (!userId) {
        console.error(`GET /api/cavalos/${id}: Falha - ID do usuário não fornecido`);
        return res.status(400).json({ message: "ID do usuário não fornecido" });
      }
      
      const cavalo = await storage.getCavalo(id, userId);
      
      if (!cavalo) {
        console.warn(`GET /api/cavalos/${id}: Cavalo não encontrado para usuário ${userId}`);
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }
      
      console.log(`GET /api/cavalos/${id}: Cavalo encontrado:`, JSON.stringify({
        id: cavalo.id,
        name: cavalo.name,
        pai: cavalo.pai,
        mae: cavalo.mae
      }));
      
      res.json(cavalo);
    } catch (error) {
      const cavaloId = req.params.id;
      console.error(`GET /api/cavalos/${cavaloId}: Erro:`, error);
      res.status(500).json({ message: "Erro ao buscar cavalo" });
    }
  });

  app.post("/api/cavalos", authenticateUser, async (req, res) => {
    try {
      const cavaloData = insertCavaloSchema.parse(req.body);
      const cavalo = await storage.createCavalo(cavaloData);
      res.status(201).json(cavalo);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar cavalo" });
    }
  });

  app.put("/api/cavalos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Remove o userId do objeto de atualização para não sobrescrever o dono
      const { userId: _, ...updateData } = req.body;
      
      // Fazer log dos dados recebidos para depuração
      console.log(`PUT /api/cavalos/${id}: Recebido dados de atualização:`, JSON.stringify(updateData, null, 2));
      
      try {
        const cavalo = await storage.updateCavalo(id, userId, updateData);
        
        if (!cavalo) {
          return res.status(404).json({ message: "Cavalo não encontrado" });
        }
        
        res.json(cavalo);
      } catch (updateError: any) {
        console.error(`Erro ao atualizar cavalo ${id}:`, updateError);
        return res.status(500).json({ message: `Erro ao atualizar cavalo: ${updateError.message || 'Erro desconhecido'}` });
      }
    } catch (error) {
      console.error("Erro ao processar requisição:", error);
      res.status(500).json({ message: "Erro ao atualizar cavalo" });
    }
  });

  app.patch("/api/cavalos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Remove o userId do objeto de atualização para não sobrescrever o dono
      const { userId: _, ...updateData } = req.body;
      
      // Fazer log dos dados recebidos para depuração
      console.log(`PATCH /api/cavalos/${id}: Recebido dados de atualização:`, JSON.stringify(updateData, null, 2));
      
      try {
        const cavalo = await storage.updateCavalo(id, userId, updateData);
        
        if (!cavalo) {
          return res.status(404).json({ message: "Cavalo não encontrado" });
        }
        
        res.json(cavalo);
      } catch (updateError: any) {
        console.error(`Erro ao atualizar cavalo ${id}:`, updateError);
        return res.status(500).json({ message: `Erro ao atualizar cavalo: ${updateError.message || 'Erro desconhecido'}` });
      }
    } catch (error) {
      console.error("Erro ao processar requisição:", error);
      res.status(500).json({ message: "Erro ao atualizar cavalo" });
    }
  });

  app.delete("/api/cavalos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const success = await storage.deleteCavalo(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir cavalo" });
    }
  });

  // Rotas de manejos (Horse care tasks)
  app.get("/api/manejos", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      const manejos = await storage.getManejos(userId);
      res.json(manejos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar manejos" });
    }
  });

  app.get("/api/cavalos/:horseId/manejos", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const manejos = await storage.getManejosByHorse(horseId, userId);
      res.json(manejos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar manejos do cavalo" });
    }
  });

  app.get("/api/manejos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const manejo = await storage.getManejo(id, userId);
      
      if (!manejo) {
        return res.status(404).json({ message: "Manejo não encontrado" });
      }
      
      res.json(manejo);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar manejo" });
    }
  });

  app.post("/api/manejos", authenticateUser, async (req, res) => {
    try {
      const manejoData = insertManejoSchema.parse(req.body);
      const manejo = await storage.createManejo(manejoData);
      res.status(201).json(manejo);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar manejo" });
    }
  });

  app.put("/api/manejos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Remove o userId do objeto de atualização para não sobrescrever o dono
      const { userId: _, ...updateData } = req.body;
      
      const manejo = await storage.updateManejo(id, userId, updateData);
      
      if (!manejo) {
        return res.status(404).json({ message: "Manejo não encontrado" });
      }
      
      res.json(manejo);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar manejo" });
    }
  });

  app.delete("/api/manejos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const success = await storage.deleteManejo(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Manejo não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir manejo" });
    }
  });

  // Rotas de arquivos
  app.get("/api/arquivos", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      const arquivos = await storage.getArquivos(userId);
      res.json(arquivos);
    } catch (error) {
      console.error("Erro ao buscar arquivos:", error);
      res.status(500).json({ message: "Erro ao buscar arquivos" });
    }
  });

  app.get("/api/cavalos/:horseId/arquivos", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const arquivos = await storage.getArquivosByHorse(horseId, userId);
      res.json(arquivos);
    } catch (error) {
      console.error("Erro ao buscar arquivos do cavalo:", error);
      res.status(500).json({ message: "Erro ao buscar arquivos do cavalo" });
    }
  });

  app.get("/api/arquivos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const arquivo = await storage.getArquivo(id, userId);
      
      if (!arquivo) {
        return res.status(404).json({ message: "Arquivo não encontrado" });
      }
      
      res.json(arquivo);
    } catch (error) {
      console.error("Erro ao buscar arquivo:", error);
      res.status(500).json({ message: "Erro ao buscar arquivo" });
    }
  });
  
  // Rota para download/visualização de arquivos
  app.get("/api/arquivos/:id/download", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const arquivo = await storage.getArquivo(id, userId);
      
      if (!arquivo) {
        return res.status(404).json({ message: "Arquivo não encontrado" });
      }
      
      // Verificar se o arquivo existe no sistema
      if (!fs.existsSync(arquivo.filePath)) {
        return res.status(404).json({ message: "Arquivo físico não encontrado no servidor" });
      }
      
      // Verificar o tipo de arquivo para configurar os headers apropriados
      let contentType = 'application/octet-stream'; // padrão para download
      
      if (arquivo.fileType === 'image') {
        const ext = path.extname(arquivo.fileName).toLowerCase();
        if (ext === '.jpg' || ext === '.jpeg') {
          contentType = 'image/jpeg';
        } else if (ext === '.png') {
          contentType = 'image/png';
        } else if (ext === '.gif') {
          contentType = 'image/gif';
        }
      } else if (arquivo.fileType === 'pdf') {
        contentType = 'application/pdf';
      } else if (arquivo.fileType === 'video') {
        contentType = 'video/mp4';
      }
      
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `inline; filename="${arquivo.fileName}"`);
      
      // Enviar o arquivo como stream
      const fileStream = fs.createReadStream(arquivo.filePath);
      fileStream.pipe(res);
      
    } catch (error) {
      console.error("Erro ao baixar arquivo:", error);
      res.status(500).json({ message: "Erro ao baixar arquivo" });
    }
  });
  
  // Rota para upload de arquivo
  app.post("/api/arquivos", authenticateUser, upload.single('file'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "Nenhum arquivo enviado" });
      }
      
      const file = req.file;
      const { horseId, description, fileType } = req.body;
      const userId = req.body.userId;
      
      if (!horseId || !userId) {
        // Se o arquivo foi salvo, mas os dados estão inválidos, remover o arquivo
        fs.unlinkSync(file.path);
        return res.status(400).json({ message: "Dados de arquivo inválidos" });
      }
      
      // Verificar se o cavalo existe e pertence ao usuário
      const cavalo = await storage.getCavalo(parseInt(horseId), parseInt(userId));
      if (!cavalo) {
        // Remover arquivo se o cavalo não for encontrado
        fs.unlinkSync(file.path);
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }
      
      const arquivoData = {
        fileName: file.originalname,
        filePath: file.path,
        fileType: fileType || 'other', // Tipo padrão se não especificado
        description: description || '',
        horseId: parseInt(horseId),
        userId: parseInt(userId)
      };
      
      const arquivo = await storage.createArquivo(arquivoData);
      res.status(201).json(arquivo);
      
    } catch (error) {
      console.error("Erro ao fazer upload do arquivo:", error);
      // Se houve erro ao salvar no banco, mas o arquivo foi salvo, deveria remover o arquivo
      if (req.file && fs.existsSync(req.file.path)) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (e) {
          console.error("Erro ao remover arquivo após falha:", e);
        }
      }
      res.status(500).json({ message: "Erro ao fazer upload do arquivo" });
    }
  });
  
  // Rota para pré-visualização de PDF da ABCCC (sem salvar no banco)
  app.post("/api/importar-pdf-crioulo/preview", authenticateUser, uploadAbcccPdf.single('file'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "Nenhum arquivo PDF enviado" });
      }
      
      const userId = parseInt(req.body.userId);
      if (!userId) {
        return res.status(400).json({ message: "ID de usuário não fornecido" });
      }
      
      // Log detalhado do início do processo
      routeLogger.info(`Iniciando pré-visualização de PDF ABCCC: ${req.file.originalname} (${req.file.size} bytes) por usuário ${userId}`);
      
      // Pré-processar o PDF sem salvar no banco
      const resultado = await previsualizarRegistroABCCC(req.file.path, userId);
      
      // Retorna os dados processados para pré-visualização
      res.status(200).json({
        sucesso: true,
        mensagem: "Registro pré-visualizado com sucesso",
        dados: resultado
      });
      
    } catch (error) {
      const mensagem = error instanceof Error ? error.message : String(error);
      routeLogger.error(`Erro ao pré-visualizar PDF ABCCC: ${mensagem}`);
      
      res.status(500).json({
        sucesso: false,
        mensagem: `Erro ao processar o arquivo para pré-visualização: ${mensagem}`,
        detalhes: error instanceof Error ? error.stack : undefined
      });
    }
  });

  // Rota para importação de PDF da ABCCC
  app.post("/api/importar-pdf-crioulo", authenticateUser, uploadAbcccPdf.single('file'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "Nenhum arquivo PDF enviado" });
      }
      
      const userId = parseInt(req.body.userId);
      if (!userId) {
        return res.status(400).json({ message: "ID de usuário não fornecido" });
      }
      
      // Log detalhado do início do processo
      routeLogger.info(`Iniciando importação de PDF ABCCC: ${req.file.originalname} (${req.file.size} bytes) por usuário ${userId}`);
      
      // Processar o PDF e importar os dados
      const resultado = await importarRegistroABCCC(req.file.path, userId);
      
      // Retorna os dados processados
      res.status(200).json({
        sucesso: true,
        mensagem: "Registro processado com sucesso",
        dados: resultado
      });
      
    } catch (error) {
      const mensagem = error instanceof Error ? error.message : String(error);
      routeLogger.error(`Erro ao importar PDF ABCCC: ${mensagem}`);
      
      res.status(500).json({
        sucesso: false,
        mensagem: `Erro ao processar o arquivo: ${mensagem}`,
        detalhes: error instanceof Error ? error.stack : undefined
      });
    }
  });
  
  // Rota para obter logs de debug da importação ABCCC
  app.get("/api/importar-pdf-crioulo/logs", authenticateUser, async (req, res) => {
    try {
      // Verificar se o usuário tem permissão de admin (simplificado)
      const userId = parseInt(req.query.userId as string || '0');
      
      // Listar os arquivos de log disponíveis
      const logsDir = path.join(process.cwd(), 'logs');
      const arquivos = fs.readdirSync(logsDir)
        .filter(file => file.startsWith('debug_pdf_'))
        .sort()
        .reverse() // Mais recentes primeiro
        .slice(0, 20); // Limitar a 20 logs
      
      res.status(200).json({
        sucesso: true,
        logs: arquivos.map(arquivo => ({
          nome: arquivo,
          caminho: `/api/importar-pdf-crioulo/logs/${arquivo}`,
          data: new Date(parseInt(arquivo.replace(/\D/g, ''))).toISOString()
        }))
      });
      
    } catch (error) {
      const mensagem = error instanceof Error ? error.message : String(error);
      routeLogger.error(`Erro ao listar logs de importação ABCCC: ${mensagem}`);
      
      res.status(500).json({
        sucesso: false,
        mensagem: `Erro ao listar logs: ${mensagem}`
      });
    }
  });
  
  // Rota para visualizar um log específico
  app.get("/api/importar-pdf-crioulo/logs/:nome", authenticateUser, async (req, res) => {
    try {
      const nomeArquivo = req.params.nome;
      if (!nomeArquivo || !nomeArquivo.startsWith('debug_pdf_')) {
        return res.status(400).json({
          sucesso: false,
          mensagem: "Nome de arquivo de log inválido"
        });
      }
      
      const caminhoArquivo = path.join(process.cwd(), 'logs', nomeArquivo);
      if (!fs.existsSync(caminhoArquivo)) {
        return res.status(404).json({
          sucesso: false,
          mensagem: "Arquivo de log não encontrado"
        });
      }
      
      // Ler o conteúdo do arquivo
      const conteudo = JSON.parse(fs.readFileSync(caminhoArquivo, 'utf8'));
      
      res.status(200).json({
        sucesso: true,
        conteudo
      });
      
    } catch (error) {
      const mensagem = error instanceof Error ? error.message : String(error);
      routeLogger.error(`Erro ao ler log de importação ABCCC: ${mensagem}`);
      
      res.status(500).json({
        sucesso: false,
        mensagem: `Erro ao ler log: ${mensagem}`
      });
    }
  });

  app.delete("/api/arquivos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Primeiro buscar o arquivo para ter o caminho do arquivo físico
      const arquivo = await storage.getArquivo(id, userId);
      
      if (!arquivo) {
        return res.status(404).json({ message: "Arquivo não encontrado" });
      }
      
      // Excluir o registro do banco de dados
      const success = await storage.deleteArquivo(id, userId);
      
      if (!success) {
        return res.status(500).json({ message: "Erro ao excluir arquivo do banco de dados" });
      }
      
      // Excluir o arquivo físico (se existir)
      if (fs.existsSync(arquivo.filePath)) {
        fs.unlinkSync(arquivo.filePath);
      }
      
      res.status(204).end();
    } catch (error) {
      console.error("Erro ao excluir arquivo:", error);
      res.status(500).json({ message: "Erro ao excluir arquivo" });
    }
  });

  // Rotas de Eventos (Agenda)
  app.get("/api/eventos", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      const eventos = await storage.getEventos(userId);
      res.json(eventos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar eventos" });
    }
  });

  app.get("/api/eventos/data/:data", authenticateUser, async (req, res) => {
    try {
      const data = req.params.data; // Formato esperado: YYYY-MM-DD
      const userId = req.body.userId;
      const eventos = await storage.getEventosByDate(data, userId);
      res.json(eventos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar eventos por data" });
    }
  });

  app.get("/api/cavalos/:horseId/eventos", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const eventos = await storage.getEventosByHorse(horseId, userId);
      res.json(eventos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar eventos do cavalo" });
    }
  });

  app.get("/api/manejos/:manejoId/eventos", authenticateUser, async (req, res) => {
    try {
      const manejoId = parseInt(req.params.manejoId);
      const userId = req.body.userId;
      const eventos = await storage.getEventosByManejo(manejoId, userId);
      res.json(eventos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar eventos do manejo" });
    }
  });

  app.get("/api/eventos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const evento = await storage.getEvento(id, userId);
      
      if (!evento) {
        return res.status(404).json({ message: "Evento não encontrado" });
      }
      
      res.json(evento);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar evento" });
    }
  });

  app.post("/api/eventos", authenticateUser, async (req, res) => {
    try {
      const eventoData = insertEventoSchema.parse(req.body);
      const evento = await storage.createEvento(eventoData);
      res.status(201).json(evento);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar evento" });
    }
  });

  app.put("/api/eventos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Remove o userId do objeto de atualização
      const { userId: _, ...updateData } = req.body;
      
      const evento = await storage.updateEvento(id, userId, updateData);
      
      if (!evento) {
        return res.status(404).json({ message: "Evento não encontrado" });
      }
      
      res.json(evento);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar evento" });
    }
  });

  app.delete("/api/eventos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const success = await storage.deleteEvento(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Evento não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir evento" });
    }
  });

  // Rotas de Procedimentos Veterinários
  app.get("/api/procedimentos-vet", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      const procedimentos = await storage.getProcedimentosVet(userId);
      res.json(procedimentos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar procedimentos veterinários" });
    }
  });

  app.get("/api/cavalos/:horseId/procedimentos-vet", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const procedimentos = await storage.getProcedimentosVetByHorse(horseId, userId);
      res.json(procedimentos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar procedimentos veterinários do cavalo" });
    }
  });

  app.get("/api/manejos/:manejoId/procedimentos-vet", authenticateUser, async (req, res) => {
    try {
      const manejoId = parseInt(req.params.manejoId);
      const userId = req.body.userId;
      const procedimentos = await storage.getProcedimentosVetByManejo(manejoId, userId);
      res.json(procedimentos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar procedimentos veterinários do manejo" });
    }
  });

  app.get("/api/eventos/:eventoId/procedimentos-vet", authenticateUser, async (req, res) => {
    try {
      const eventoId = parseInt(req.params.eventoId);
      const userId = req.body.userId;
      const procedimentos = await storage.getProcedimentosVetByEvento(eventoId, userId);
      res.json(procedimentos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar procedimentos veterinários do evento" });
    }
  });

  app.get("/api/procedimentos-vet/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const procedimento = await storage.getProcedimentoVet(id, userId);
      
      if (!procedimento) {
        return res.status(404).json({ message: "Procedimento veterinário não encontrado" });
      }
      
      res.json(procedimento);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar procedimento veterinário" });
    }
  });

  app.post("/api/procedimentos-vet", authenticateUser, async (req, res) => {
    try {
      const procedimentoData = insertProcedimentoVetSchema.parse(req.body);
      const procedimento = await storage.createProcedimentoVet(procedimentoData);
      res.status(201).json(procedimento);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar procedimento veterinário" });
    }
  });

  app.put("/api/procedimentos-vet/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Remove o userId do objeto de atualização
      const { userId: _, ...updateData } = req.body;
      
      const procedimento = await storage.updateProcedimentoVet(id, userId, updateData);
      
      if (!procedimento) {
        return res.status(404).json({ message: "Procedimento veterinário não encontrado" });
      }
      
      res.json(procedimento);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar procedimento veterinário" });
    }
  });

  app.delete("/api/procedimentos-vet/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const success = await storage.deleteProcedimentoVet(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Procedimento veterinário não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir procedimento veterinário" });
    }
  });
  
  // Rotas de Reprodução
  app.get("/api/reproducao", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      const reproducoes = await storage.getReproducoes(userId);
      res.json(reproducoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de reprodução" });
    }
  });

  app.get("/api/cavalos/:horseId/reproducao", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const reproducoes = await storage.getReproducoesByHorse(horseId, userId);
      res.json(reproducoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de reprodução do cavalo" });
    }
  });

  app.get("/api/procedimentos-vet/:procedimentoId/reproducao", authenticateUser, async (req, res) => {
    try {
      const procedimentoId = parseInt(req.params.procedimentoId);
      const userId = req.body.userId;
      const reproducoes = await storage.getReproducoesByProcedimento(procedimentoId, userId);
      res.json(reproducoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de reprodução do procedimento" });
    }
  });

  app.get("/api/eventos/:eventoId/reproducao", authenticateUser, async (req, res) => {
    try {
      const eventoId = parseInt(req.params.eventoId);
      const userId = req.body.userId;
      const reproducoes = await storage.getReproducoesByEvento(eventoId, userId);
      res.json(reproducoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de reprodução do evento" });
    }
  });

  app.get("/api/reproducao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const reproducao = await storage.getReproducao(id, userId);
      
      if (!reproducao) {
        return res.status(404).json({ message: "Registro de reprodução não encontrado" });
      }
      
      res.json(reproducao);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registro de reprodução" });
    }
  });

  app.post("/api/reproducao", authenticateUser, async (req, res) => {
    try {
      const reproducaoData = insertReproducaoSchema.parse(req.body);
      const reproducao = await storage.createReproducao(reproducaoData);
      res.status(201).json(reproducao);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar registro de reprodução" });
    }
  });

  app.put("/api/reproducao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Remove o userId do objeto de atualização
      const { userId: _, ...updateData } = req.body;
      
      const reproducao = await storage.updateReproducao(id, userId, updateData);
      
      if (!reproducao) {
        return res.status(404).json({ message: "Registro de reprodução não encontrado" });
      }
      
      res.json(reproducao);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar registro de reprodução" });
    }
  });

  app.delete("/api/reproducao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const success = await storage.deleteReproducao(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Registro de reprodução não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir registro de reprodução" });
    }
  });
  
  // Rotas para Medidas Físicas
  app.get("/api/medidas-fisicas", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      const medidas = await storage.getMedidasFisicas(userId);
      res.json(medidas);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar medidas físicas" });
    }
  });

  app.get("/api/cavalos/:horseId/medidas-fisicas", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const medidas = await storage.getMedidasFisicasByHorse(horseId, userId);
      res.json(medidas);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar medidas físicas do cavalo" });
    }
  });

  app.get("/api/medidas-fisicas/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const medida = await storage.getMedidaFisica(id, userId);
      
      if (!medida) {
        return res.status(404).json({ message: "Medida física não encontrada" });
      }
      
      res.json(medida);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar medida física" });
    }
  });

  app.post("/api/medidas-fisicas", authenticateUser, async (req, res) => {
    try {
      const medidaData = insertMedidaFisicaSchema.parse(req.body);
      const medida = await storage.createMedidaFisica(medidaData);
      res.status(201).json(medida);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar medida física" });
    }
  });

  app.put("/api/medidas-fisicas/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Remove o userId do objeto de atualização
      const { userId: _, ...updateData } = req.body;
      
      const medida = await storage.updateMedidaFisica(id, userId, updateData);
      
      if (!medida) {
        return res.status(404).json({ message: "Medida física não encontrada" });
      }
      
      res.json(medida);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar medida física" });
    }
  });

  app.delete("/api/medidas-fisicas/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const success = await storage.deleteMedidaFisica(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Medida física não encontrada" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir medida física" });
    }
  });
  
  // Rotas para Nutrição
  app.get("/api/nutricao", authenticateUser, async (req, res) => {
    try {
      const userId = (req as any).user?.id;
      const nutricoes = await storage.getNutricoes(userId);
      res.json(nutricoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de nutrição" });
    }
  });

  app.get("/api/nutricao/horse/:horseId", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = (req as any).user?.id;
      const nutricoes = await storage.getNutricoesByHorse(horseId, userId);
      res.json(nutricoes);
    } catch (error) {
      console.error("Erro ao buscar nutrição por cavalo:", error);
      res.status(500).json({ message: "Erro ao buscar registros de nutrição do cavalo" });
    }
  });

  app.get("/api/cavalos/:horseId/nutricao", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = (req as any).user?.id;
      const nutricoes = await storage.getNutricoesByHorse(horseId, userId);
      res.json(nutricoes);
    } catch (error) {
      console.error("Erro ao buscar nutrição por cavalo (rota alternativa):", error);
      res.status(500).json({ message: "Erro ao buscar registros de nutrição do cavalo" });
    }
  });

  app.get("/api/nutricao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = (req as any).user?.id;
      const nutricao = await storage.getNutricao(id, userId);
      
      if (!nutricao) {
        return res.status(404).json({ message: "Registro de nutrição não encontrado" });
      }
      
      res.json(nutricao);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registro de nutrição" });
    }
  });

  app.post("/api/nutricao", authenticateUser, async (req, res) => {
    try {
      const userId = (req as any).user?.id;
      
      console.log("[NUTRIÇÃO DEBUG] userId:", userId);
      console.log("[NUTRIÇÃO DEBUG] req.body:", req.body);
      console.log("[NUTRIÇÃO DEBUG] req.user:", (req as any).user);
      
      if (!userId) {
        console.error("[NUTRIÇÃO ERROR] userId não encontrado na requisição");
        return res.status(401).json({ 
          success: false,
          error: "Usuário não autenticado",
          details: "userId não encontrado na sessão"
        });
      }
      
      const dataToValidate = {
        ...req.body,
        userId: Number(userId)
      };
      
      console.log("[NUTRIÇÃO DEBUG] Dados para validação:", dataToValidate);
      
      const nutricaoData = insertNutricaoSchema.parse(dataToValidate);
      
      console.log("[NUTRIÇÃO DEBUG] Dados validados:", nutricaoData);
      
      const nutricao = await storage.createNutricao(nutricaoData);
      
      console.log("[NUTRIÇÃO SUCCESS] Registro criado:", nutricao);
      
      return res.status(201).json({
        success: true,
        data: nutricao,
        message: "Registro de nutrição criado com sucesso"
      });
    } catch (error) {
      console.error("[NUTRIÇÃO ERROR] Erro detalhado:", error);
      
      if (error instanceof z.ZodError) {
        console.error("[NUTRIÇÃO VALIDATION ERROR] Erros de validação:", error.errors);
        return res.status(400).json({ 
          success: false,
          error: "Dados inválidos",
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            received: (err as any).received
          })),
          originalErrors: error.format()
        });
      }
      
      return res.status(500).json({ 
        success: false,
        error: "Erro interno do servidor",
        message: "Erro ao criar registro de nutrição",
        details: (error as Error).message
      });
    }
  });

  app.put("/api/nutricao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = (req as any).user?.id;
      
      // Remove o userId do objeto de atualização se estiver presente
      const { userId: _, ...updateData } = req.body;
      
      const nutricao = await storage.updateNutricao(id, userId, updateData);
      
      if (!nutricao) {
        return res.status(404).json({ message: "Registro de nutrição não encontrado" });
      }
      
      return res.json(nutricao);
    } catch (error) {
      console.error("Erro ao atualizar registro de nutrição:", error);
      return res.status(500).json({ message: "Erro ao atualizar registro de nutrição" });
    }
  });

  app.delete("/api/nutricao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = (req as any).user?.id;
      const success = await storage.deleteNutricao(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Registro de nutrição não encontrado" });
      }
      
      return res.status(204).end();
    } catch (error) {
      console.error("Erro ao excluir registro de nutrição:", error);
      return res.status(500).json({ message: "Erro ao excluir registro de nutrição" });
    }
  });
  
  // Rotas para Morfologia
  app.get("/api/morfologia", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      const morfologias = await storage.getMorfologias(userId);
      res.json(morfologias);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de morfologia" });
    }
  });

  app.get("/api/cavalos/:horseId/morfologia", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const morfologias = await storage.getMorfologiasByHorse(horseId, userId);
      res.json(morfologias);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de morfologia do cavalo" });
    }
  });

  app.get("/api/morfologia/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const morfologia = await storage.getMorfologia(id, userId);
      
      if (!morfologia) {
        return res.status(404).json({ message: "Registro de morfologia não encontrado" });
      }
      
      res.json(morfologia);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registro de morfologia" });
    }
  });

  app.post("/api/morfologia", authenticateUser, async (req, res) => {
    try {
      const morfologiaData = insertMorfologiaSchema.parse(req.body);
      const morfologia = await storage.createMorfologia(morfologiaData);
      res.status(201).json(morfologia);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar registro de morfologia" });
    }
  });

  app.put("/api/morfologia/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Remove o userId do objeto de atualização
      const { userId: _, ...updateData } = req.body;
      
      const morfologia = await storage.updateMorfologia(id, userId, updateData);
      
      if (!morfologia) {
        return res.status(404).json({ message: "Registro de morfologia não encontrado" });
      }
      
      res.json(morfologia);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar registro de morfologia" });
    }
  });

  app.delete("/api/morfologia/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const success = await storage.deleteMorfologia(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Registro de morfologia não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir registro de morfologia" });
    }
  });
  
  // Rotas para Desempenho
  app.get("/api/desempenho", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      const desempenhos = await storage.getDesempenhos(userId);
      res.json(desempenhos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de desempenho" });
    }
  });

  app.get("/api/cavalos/:horseId/desempenho", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const desempenhos = await storage.getDesempenhosByHorse(horseId, userId);
      res.json(desempenhos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de desempenho do cavalo" });
    }
  });

  app.get("/api/desempenho/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const desempenho = await storage.getDesempenho(id, userId);
      
      if (!desempenho) {
        return res.status(404).json({ message: "Registro de desempenho não encontrado" });
      }
      
      res.json(desempenho);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registro de desempenho" });
    }
  });

  app.post("/api/desempenho", authenticateUser, async (req, res) => {
    try {
      const desempenhoData = insertDesempenhoHistoricoSchema.parse(req.body);
      const desempenho = await storage.createDesempenho(desempenhoData);
      res.status(201).json(desempenho);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar registro de desempenho" });
    }
  });

  app.put("/api/desempenho/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Remove o userId do objeto de atualização
      const { userId: _, ...updateData } = req.body;
      
      const desempenho = await storage.updateDesempenho(id, userId, updateData);
      
      if (!desempenho) {
        return res.status(404).json({ message: "Registro de desempenho não encontrado" });
      }
      
      res.json(desempenho);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar registro de desempenho" });
    }
  });

  app.delete("/api/desempenho/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const success = await storage.deleteDesempenho(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Registro de desempenho não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir registro de desempenho" });
    }
  });
  
  // Rotas para Genealogia
  app.get("/api/genealogia", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      const genealogias = await storage.getGenealogias(userId);
      res.json(genealogias);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de genealogia" });
    }
  });

  app.get("/api/cavalos/:horseId/genealogia", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const genealogia = await storage.getGenealogiaByHorse(horseId, userId);
      
      if (!genealogia) {
        return res.status(404).json({ message: "Registro de genealogia não encontrado" });
      }
      
      res.json(genealogia);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registro de genealogia do cavalo" });
    }
  });

  app.post("/api/genealogia", authenticateUser, async (req, res) => {
    try {
      console.log('POST /api/genealogia - Dados recebidos:', JSON.stringify(req.body, null, 2));
      const genealogiaData = insertGenealogiaSchema.parse(req.body);
      console.log('POST /api/genealogia - Dados após validação:', JSON.stringify(genealogiaData, null, 2));
      const genealogia = await storage.createGenealogia(genealogiaData);
      console.log('POST /api/genealogia - Genealogia criada:', JSON.stringify(genealogia, null, 2));
      res.status(201).json(genealogia);
    } catch (error) {
      console.error('POST /api/genealogia - Erro:', error);
      if (error instanceof z.ZodError) {
        console.error('POST /api/genealogia - Erro de validação Zod:', error.format());
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar registro de genealogia" });
    }
  });

  app.put("/api/genealogia/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      
      // Remove o userId do objeto de atualização
      const { userId: _, ...updateData } = req.body;
      
      const genealogia = await storage.updateGenealogia(id, userId, updateData);
      
      if (!genealogia) {
        return res.status(404).json({ message: "Registro de genealogia não encontrado" });
      }
      
      res.json(genealogia);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar registro de genealogia" });
    }
  });

  app.delete("/api/genealogia/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const success = await storage.deleteGenealogia(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Registro de genealogia não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir registro de genealogia" });
    }
  });

  app.get("/api/cavalos/:horseId/consanguinidade", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const coeficiente = await storage.calcularConsanguinidade(horseId, userId);
      res.json({ coeficiente });
    } catch (error) {
      res.status(500).json({ message: "Erro ao calcular coeficiente de consanguinidade" });
    }
  });
  
  // Rotas para Sugestões de Cruzamento
  app.get("/api/sugestoes-cruzamento", authenticateUser, async (req, res) => {
    try {
      const userId = req.body.userId;
      const sugestoes = await storage.getSugestoesCruzamento(userId);
      res.json(sugestoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar sugestões de cruzamento" });
    }
  });

  app.get("/api/cavalos/:horseId/sugestoes-cruzamento", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const sugestoes = await storage.getSugestoesCruzamentoByHorse(horseId, userId);
      res.json(sugestoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar sugestões de cruzamento para o cavalo" });
    }
  });

  app.get("/api/sugestoes-cruzamento/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const sugestao = await storage.getSugestoesCruzamentoById(id, userId);
      
      if (!sugestao) {
        return res.status(404).json({ message: "Sugestão de cruzamento não encontrada" });
      }
      
      res.json(sugestao);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar sugestão de cruzamento" });
    }
  });

  app.post("/api/sugestoes-cruzamento", authenticateUser, async (req, res) => {
    try {
      const sugestaoData = insertSugestoesCruzamentoSchema.parse(req.body);
      const sugestao = await storage.createSugestaoCruzamento(sugestaoData);
      res.status(201).json(sugestao);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar sugestão de cruzamento" });
    }
  });

  app.delete("/api/sugestoes-cruzamento/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = req.body.userId;
      const success = await storage.deleteSugestaoCruzamento(id, userId);
      
      if (!success) {
        return res.status(404).json({ message: "Sugestão de cruzamento não encontrada" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir sugestão de cruzamento" });
    }
  });

  app.post("/api/cavalos/:horseId/gerar-sugestoes-cruzamento", authenticateUser, async (req, res) => {
    try {
      const horseId = parseInt(req.params.horseId);
      const userId = req.body.userId;
      const { objetivo } = req.body;
      
      if (!objetivo) {
        return res.status(400).json({ message: "Objetivo do cruzamento é obrigatório" });
      }
      
      const sugestoes = await storage.gerarSugestoesCruzamento(horseId, objetivo, userId);
      res.json(sugestoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao gerar sugestões de cruzamento" });
    }
  });

  // Rota de teste temporária para depuração da busca por registro
  app.post("/api/testar-busca-registro", authenticateUser, async (req, res) => {
    try {
      const { registro } = req.body;
      console.log("Testando busca por registro:", registro);
      
      // Importando a função do serviço de importação ABCCC
      const { buscarCavaloPorRegistro } = require('./abccc-import-service');
      
      const resultado = await buscarCavaloPorRegistro(registro);
      console.log("Resultado da busca:", resultado ? "Encontrado" : "Não encontrado");
      
      return res.json({ 
        sucesso: true, 
        resultado,
        encontrado: !!resultado,
        detalhes: resultado ? {
          id: resultado.id,
          nome: resultado.name,
          numeroRegistro: resultado.numeroRegistro
        } : null
      });
    } catch (error) {
      console.error("Erro na busca por registro:", error);
      return res.status(500).json({ 
        sucesso: false, 
        erro: (error as Error).message,
        stack: (error as Error).stack
      });
    }
  });
  
  // API para raças de cavalos
  app.get('/api/racas', authenticateUser, async (req: any, res: any) => {
    try {
      const result = await pool.query('SELECT * FROM racas ORDER BY nome');
      res.json(result.rows);
    } catch (error) {
      console.error('Erro ao buscar raças:', error);
      res.status(500).json({ error: 'Erro ao buscar raças' });
    }
  });
  
  // API para buscar dados de cavalos da ABCCC pelo registro
  app.get('/api/abccc/cavalo/:registro', authenticateUser, async (req: Request, res: Response) => {
    // Obter o registro do parâmetro da URL
    const registroParam = req.params.registro;
    
    try {
      // Validação do registro
      if (!registroParam || registroParam.trim() === '') {
        return res.status(400).json({ 
          error: 'Parâmetro inválido', 
          message: 'Número de registro é obrigatório' 
        });
      }
      
      // Formatação do registro (mantém apenas letras e números)
      const registroFormatado = registroParam.trim().toUpperCase();
      
      // Log detalhado da requisição
      const userId = req.body?.userId || 'anônimo';
      routeLogger.info(`Solicitação para buscar dados da ABCCC - Registro: ${registroFormatado}, Usuário: ${userId}`);
      
      // Removendo a lista de registros problemáticos específicos
      // pois implementamos uma solução robusta que funciona com qualquer registro

      // BUSCA AUTOMÁTICA NO SITE TEMPORARIAMENTE DESABILITADA
      // Devido a problemas de certificado SSL no site da ABCCC
      routeLogger.info(`Busca automática desabilitada para registro ${registroFormatado} - Use importação via PDF`);
      
      return res.status(503).json({
        error: 'Busca automática temporariamente indisponível',
        message: 'A busca automática no site da ABCCC está temporariamente desabilitada devido a problemas técnicos. Use a funcionalidade "Importar PDF da ABCCC" para cadastrar cavalos.',
        registro: registroFormatado,
        alternativa: {
          metodo: 'Importação via PDF',
          descricao: 'Faça o download do PDF do cavalo no site da ABCCC e use a opção "Importar PDF" no sistema',
          localizacao: 'Página de cadastro de cavalos → Botão "Importar PDF da ABCCC"'
        }
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      routeLogger.error(`Erro não tratado na rota ABCCC: ${errorMessage}`);
      
      // Log do stack trace para depuração
      if (error instanceof Error && error.stack) {
        routeLogger.debug(`Stack trace: ${error.stack}`);
      }
      
      return res.status(500).json({ 
        error: 'Erro interno do servidor', 
        message: 'Ocorreu um erro ao tentar buscar os dados do cavalo. Por favor, tente novamente mais tarde.'
      });
    }
  });

  // === NUTRITION LOGISTICS ROUTES ===
  
  // Temporarily mount inline routes until dynamic imports are fixed
  app.use('/api/feeding', (await import('./routes/feeding.js')).default);
  app.use('/api/stock', (await import('./routes/stock.js')).default);
  app.use('/api/reports', (await import('./routes/nutrition-reports.js')).default);
  
  // Rotas de upload de fotos de cavalos
  app.use('/api/cavalos', (await import('./routes/horse-photos.js')).default);
  
  // Rota para download do backup do banco de dados
  app.get('/api/backup/download', (req: express.Request, res: express.Response) => {
    try {
      const backupFiles = fs.readdirSync('.').filter(file => file.startsWith('backup_equigestor_') && file.endsWith('.sql'));
      
      if (backupFiles.length === 0) {
        return res.status(404).json({ error: 'Nenhum backup encontrado' });
      }
      
      // Pegar o backup mais recente
      const latestBackup = backupFiles.sort().reverse()[0];
      const filePath = path.join(process.cwd(), latestBackup);
      
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ error: 'Arquivo de backup não encontrado' });
      }
      
      res.setHeader('Content-Disposition', `attachment; filename="${latestBackup}"`);
      res.setHeader('Content-Type', 'application/sql');
      
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
      
      routeLogger.info(`Backup baixado: ${latestBackup}`);
    } catch (error) {
      routeLogger.error('Erro ao baixar backup:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });
}