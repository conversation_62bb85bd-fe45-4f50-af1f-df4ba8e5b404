@echo off
setlocal enabledelayedexpansion

:: Script para configurar SSH para Replit
:: Gera chave SSH ed25519 e copia a chave pública para a área de transferência

echo.
echo 🔑 Configurando SSH para Replit...
echo.

:: Definir variáveis
set "SSH_DIR=%USERPROFILE%\.ssh"
set "KEY_NAME=replit"
set "PRIVATE_KEY=%SSH_DIR%\%KEY_NAME%"
set "PUBLIC_KEY=%SSH_DIR%\%KEY_NAME%.pub"

:: Criar diretório .ssh se não existir
if not exist "%SSH_DIR%" (
    echo 📁 Criando diretório .ssh...
    mkdir "%SSH_DIR%"
)

:: Verificar se ssh-keygen está disponível
ssh-keygen --help >nul 2>&1
if errorlevel 1 (
    echo ❌ ssh-keygen não encontrado. Instale o OpenSSH ou Git for Windows.
    echo 💡 Você pode instalar via: winget install Git.Git
    pause
    exit /b 1
)

:: Verificar se a chave já existe
if exist "%PUBLIC_KEY%" (
    echo ✅ Chave SSH '%KEY_NAME%' já existe!
    echo 📍 Localização: %PUBLIC_KEY%
    echo.
    set /p "choice=Deseja usar a chave existente? (s/N): "
    if /i not "!choice!"=="s" (
        echo ❌ Operação cancelada.
        pause
        exit /b 1
    )
    goto :copy_key
)

:: Gerar nova chave SSH
echo 🔐 Gerando nova chave SSH ed25519...

:: Remover chaves existentes se existirem
if exist "%PRIVATE_KEY%" del "%PRIVATE_KEY%"
if exist "%PUBLIC_KEY%" del "%PUBLIC_KEY%"

:: Gerar chave com comentário incluindo data
for /f "tokens=2 delims==" %%i in ('wmic OS Get localdatetime /value') do set "dt=%%i"
set "current_date=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%"

ssh-keygen -t ed25519 -f "%PRIVATE_KEY%" -q -N "" -C "replit-key-%current_date%"

if errorlevel 1 (
    echo ❌ Erro ao gerar chave SSH.
    pause
    exit /b 1
)

echo ✅ Chave SSH gerada com sucesso!

:copy_key
:: Verificar se a chave pública existe
if not exist "%PUBLIC_KEY%" (
    echo ❌ Chave pública não encontrada: %PUBLIC_KEY%
    pause
    exit /b 1
)

:: Mostrar conteúdo da chave pública
echo.
echo 📋 Chave pública:
type "%PUBLIC_KEY%"
echo.

:: Copiar para área de transferência usando PowerShell
echo ✅ Copiando chave pública para a área de transferência...
powershell -Command "Get-Content '%PUBLIC_KEY%' | Set-Clipboard" 2>nul

if errorlevel 1 (
    echo ⚠️  Não foi possível copiar para área de transferência automaticamente.
    echo 💡 Você pode copiar manualmente a chave mostrada acima.
) else (
    echo ✅ Chave pública copiada para a área de transferência!
)

echo.
echo 🎯 Próximos passos:
echo 1. Acesse https://replit.com/account#ssh-keys
echo 2. Clique em 'Add SSH Key'
echo 3. Cole a chave (Ctrl+V) no campo 'Key'
echo 4. Dê um nome para a chave (ex: 'Meu PC')
echo 5. Clique em 'Add key'
echo.
echo 📁 Arquivos criados:
echo    Chave privada: %PRIVATE_KEY%
echo    Chave pública:  %PUBLIC_KEY%
echo.
echo 🔒 IMPORTANTE: Mantenha a chave privada segura e nunca a compartilhe!
echo.

:: Perguntar se deve abrir o navegador
set /p "open_browser=Deseja abrir a página de configuração SSH do Replit agora? (S/n): "
if /i not "%open_browser%"=="n" (
    echo 🌐 Abrindo página do Replit...
    start https://replit.com/account#ssh-keys
)

echo.
echo ✨ Configuração concluída!
echo.
pause
