import React, { useEffect, useState, useMemo } from 'react';
import {
  FormField,
  FormLabel,
  FormItem,
  FormControl,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Cavalo } from '@shared/schema';
import { Control } from 'react-hook-form';

// Tipo para entrada genealógica
export type TipoEntradaGenealogica = 'sistema' | 'externo' | 'nenhum';

// Interface para entrada genealógica
export interface EntradaGenealogica {
  tipo: TipoEntradaGenealogica;
  cavaloSistemaId?: string | null;
  cavaloNome?: string | null;
}

// Propriedades do componente
interface GenealogiaSelectorProps {
  cavalos: Cavalo[];
  value?: EntradaGenealogica;
  onChange: (value: EntradaGenealogica) => void;
  label: string;
  sexoFiltro?: 'Macho' | 'Fêmea' | 'Castrado' | 'Garanhão' | 'Égua';
  error?: string;
  disabled?: boolean;
  className?: string;
  name?: string;
  description?: string;
  control?: Control<any>;
  required?: boolean;
}

// Mapeamento de aliases para sexos
const sexoAliases: Record<string, string[]> = {
  Macho: ['Macho', 'Garanhão', 'Castrado'],
  Fêmea: ['Fêmea', 'Égua'],
  Garanhão: ['Macho', 'Garanhão', 'Castrado'],
  Égua: ['Fêmea', 'Égua'],
  Castrado: ['Macho', 'Garanhão', 'Castrado'],
};

export const GenealogiaSelector: React.FC<GenealogiaSelectorProps> = ({
  cavalos = [],
  value = { tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null },
  onChange,
  label,
  sexoFiltro,
  error,
  disabled = false,
  className = '',
  name,
  description,
  control,
  required = false,
}) => {
  // Estado interno
  const [tipoEntrada, setTipoEntrada] = useState<TipoEntradaGenealogica>(value.tipo);
  const [cavaloId, setCavaloId] = useState<string | null>(value.cavaloSistemaId ?? null);
  const [nomeExterno, setNomeExterno] = useState<string>(value.cavaloNome ?? '');

  // Filtragem otimizada de cavalos
  const cavalosFiltrados = useMemo(() => {
    if (!sexoFiltro) return cavalos;
    const validSexos = sexoAliases[sexoFiltro] || [sexoFiltro];
    return cavalos.filter((c) => c.sexo && validSexos.includes(c.sexo));
  }, [cavalos, sexoFiltro]);

  // Sincroniza estado com value
  useEffect(() => {
    setTipoEntrada(value.tipo);
    setCavaloId(value.cavaloSistemaId ?? null);
    setNomeExterno(value.cavaloNome ?? '');
  }, [value]);

  // Dispara onChange
  useEffect(() => {
    const novoValor: EntradaGenealogica = {
      tipo: tipoEntrada,
      cavaloSistemaId: null,
      cavaloNome: null,
    };

    if (tipoEntrada === 'sistema' && cavaloId) {
      const cavaloSelecionado = cavalosFiltrados.find((c) => c.id.toString() === cavaloId);
      if (cavaloSelecionado) {
        novoValor.cavaloSistemaId = cavaloSelecionado.id.toString();
        novoValor.cavaloNome = cavaloSelecionado.name;
      } else {
        setCavaloId(null); // Reset if invalid ID
      }
    } else if (tipoEntrada === 'externo' && nomeExterno.trim()) {
      const trimmedNome = nomeExterno.trim();
      if (trimmedNome.length <= 80) {
        novoValor.cavaloNome = trimmedNome;
      }
    }

    onChange(novoValor);
  }, [tipoEntrada, cavaloId, nomeExterno, cavalosFiltrados, onChange]);

  // Handler para troca de tipo
  const handleTipoChange = (newTipo: TipoEntradaGenealogica) => {
    setTipoEntrada(newTipo);
    if (newTipo === 'sistema') {
      setNomeExterno('');
      if (!cavaloId && cavalosFiltrados.length > 0) {
        setCavaloId(cavalosFiltrados[0].id.toString());
      }
    } else if (newTipo === 'externo') {
      setCavaloId(null);
    } else {
      setCavaloId(null);
      setNomeExterno('');
    }
  };

  // ID único para acessibilidade
  const fieldId = `genealogia-${label.toLowerCase().replace(/\s+/g, '-')}`;

  // Componente renderizado
  const renderField = () => (
    <FormItem>
      <FormLabel htmlFor={fieldId} required={required}>
        {label}
      </FormLabel>
      {description && <FormDescription>{description}</FormDescription>}
      <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-end">
        <Select
          value={tipoEntrada}
          onValueChange={(val) => handleTipoChange(val as TipoEntradaGenealogica)}
          disabled={disabled}
        >
          <FormControl>
            <SelectTrigger
              id={fieldId}
              className="w-32"
              aria-label={`Selecionar tipo de ${label}`}
              aria-required={required}
            >
              <SelectValue placeholder="Tipo" />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            <SelectItem value="nenhum">Não informado</SelectItem>
            <SelectItem value="sistema">Do Sistema</SelectItem>
            <SelectItem value="externo">Externo</SelectItem>
          </SelectContent>
        </Select>

        {tipoEntrada === 'sistema' && (
          <Select
            value={cavaloId ?? ''}
            onValueChange={(val) => setCavaloId(val || null)}
            disabled={disabled || !cavalosFiltrados.length}
          >
            <FormControl>
              <SelectTrigger
                aria-describedby={error ? `${fieldId}-error` : undefined}
                className="min-w-[200px]"
                id={`${fieldId}-sistema-select`}
                aria-required={required}
              >
                <SelectValue placeholder={`Selecione um ${sexoFiltro || 'cavalo'}`} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {cavalosFiltrados.length > 0 ? (
                cavalosFiltrados.map((c) => (
                  <SelectItem key={c.id} value={c.id.toString()}>
                    {c.name} {c.breed && <>({c.breed})</>}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="empty" disabled>
                  Nenhum cavalo disponível
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        )}

        {tipoEntrada === 'externo' && (
          <FormControl>
            <Input
              className="min-w-[200px]"
              value={nomeExterno}
              onChange={(e) => setNomeExterno(e.target.value)}
              placeholder={`Nome do ${sexoFiltro || 'cavalo'} externo`}
              aria-describedby={error ? `${fieldId}-error` : undefined}
              id={`${fieldId}-externo-input`}
              disabled={disabled}
              maxLength={80}
              autoComplete="off"
              aria-required={required}
            />
          </FormControl>
        )}
      </div>
      {error && (
        <FormMessage id={`${fieldId}-error`} role="alert">
          {error}
        </FormMessage>
      )}
    </FormItem>
  );

  return (
    <div className={`space-y-2 ${className}`}>
      {control && name ? (
        <FormField
          control={control}
          name={name}
          render={({ field }) => {
            // Garantir que field.value é EntradaGenealogica
            const fieldValue: EntradaGenealogica =
              field.value && typeof field.value === 'object'
                ? (field.value as EntradaGenealogica)
                : { tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null };

            // Sincronizar estado interno apenas se necessário
            if (JSON.stringify(fieldValue) !== JSON.stringify(value)) {
              setTipoEntrada(fieldValue.tipo);
              setCavaloId(fieldValue.cavaloSistemaId ?? null);
              setNomeExterno(fieldValue.cavaloNome ?? '');
            }

            // Combinar onChange do componente com o do react-hook-form
            const handleCombinedChange = (val: EntradaGenealogica) => {
              field.onChange(val);
              onChange(val);
            };

            return React.cloneElement(renderField(), {
              value: fieldValue,
              onChange: handleCombinedChange,
            });
          }}
        />
      ) : (
        renderField()
      )}
    </div>
  );
};

export default GenealogiaSelector;