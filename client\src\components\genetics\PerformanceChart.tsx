import React from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  ResponsiveContainer, 
  LineChart, 
  Line,
  Legend
} from 'recharts';
import { 
  Award, 
  TrendingUp, 
  AlertCircle, 
  Calendar,
  InfoIcon,
  BarChart2
} from 'lucide-react';

interface PerformanceMetric {
  id: number;
  horseId: number;
  date: string;
  velocidade: number;
  resistencia: number;
  agilidade: number;
  forca: number;
  temperamento: number;
  observacoes?: string;
}

interface PerformanceChartProps {
  metrics: PerformanceMetric[];
  horseName: string;
  className?: string;
}

/**
 * Componente para visualizar o histórico de desempenho de um cavalo
 * Inclui gráficos de diferentes métricas ao longo do tempo
 */
const PerformanceChart: React.FC<PerformanceChartProps> = ({
  metrics,
  horseName,
  className = '',
}) => {
  // Ordenar as métricas por data
  const sortedMetrics = [...metrics].sort((a, b) => 
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );
  
  // Formatar para uso nos gráficos
  const formattedData = sortedMetrics.map((metric) => ({
    ...metric,
    formattedDate: format(new Date(metric.date), 'dd/MM/yy', { locale: ptBR }),
  }));
  
  // Calcular médias
  const calculateAverage = (key: keyof PerformanceMetric) => {
    if (metrics.length === 0) return 0;
    return metrics.reduce((sum, metric) => sum + (metric[key] as number), 0) / metrics.length;
  };
  
  const averageVelocidade = calculateAverage('velocidade');
  const averageResistencia = calculateAverage('resistencia');
  const averageAgilidade = calculateAverage('agilidade');
  const averageForca = calculateAverage('forca');
  const averageTemperamento = calculateAverage('temperamento');
  
  // Encontrar a métrica mais recente
  const latestMetric = sortedMetrics.length > 0 ? sortedMetrics[sortedMetrics.length - 1] : null;
  
  // Criar dados para o gráfico radar
  const radarData = [
    { subject: 'Velocidade', A: averageVelocidade, fullMark: 10 },
    { subject: 'Resistência', A: averageResistencia, fullMark: 10 },
    { subject: 'Agilidade', A: averageAgilidade, fullMark: 10 },
    { subject: 'Força', A: averageForca, fullMark: 10 },
    { subject: 'Temperamento', A: averageTemperamento, fullMark: 10 },
  ];
  
  // Preparar dados para gráfico de barras da última avaliação
  const barData = latestMetric ? [
    { name: 'Velocidade', valor: latestMetric.velocidade },
    { name: 'Resistência', valor: latestMetric.resistencia },
    { name: 'Agilidade', valor: latestMetric.agilidade },
    { name: 'Força', valor: latestMetric.forca },
    { name: 'Temperamento', valor: latestMetric.temperamento },
  ] : [];
  
  // Calcular pontuação total e média geral
  const totalScore = metrics.reduce((sum, metric) => 
    sum + metric.velocidade + metric.resistencia + metric.agilidade + metric.forca + metric.temperamento, 0
  );
  const averageScore = totalScore / (metrics.length * 5); // Dividir pelo número de métricas (5) vezes o número de avaliações
  
  // Definir cores para o gráfico de linha
  const lineColors = {
    velocidade: '#3b82f6', // Azul
    resistencia: '#10b981', // Verde
    agilidade: '#f59e0b', // Amarelo
    forca: '#ef4444', // Vermelho
    temperamento: '#8b5cf6', // Roxo
  };
  
  const formatScore = (score: number) => score.toFixed(1);
  
  return (
    <div className={`space-y-6 ${className}`}>
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="charts">Gráficos</TabsTrigger>
          <TabsTrigger value="history">Histórico</TabsTrigger>
          <TabsTrigger value="details">Detalhes</TabsTrigger>
        </TabsList>
        
        {/* Visão Geral */}
        <TabsContent value="overview" className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <Card className="flex-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Pontuação Geral</CardTitle>
                <CardDescription>Desempenho médio em todas as categorias</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Award className="h-8 w-8 text-primary" />
                  <div>
                    <span className="text-3xl font-bold">{formatScore(averageScore)}</span>
                    <span className="text-muted-foreground text-sm">/10</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Baseado em {metrics.length} avaliações
                </p>
              </CardContent>
            </Card>
          
            <Card className="flex-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Pontos Fortes</CardTitle>
                <CardDescription>Melhores características</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-1">
                  {averageVelocidade >= 7 && (
                    <Badge>Velocidade ({formatScore(averageVelocidade)})</Badge>
                  )}
                  {averageResistencia >= 7 && (
                    <Badge>Resistência ({formatScore(averageResistencia)})</Badge>
                  )}
                  {averageAgilidade >= 7 && (
                    <Badge>Agilidade ({formatScore(averageAgilidade)})</Badge>
                  )}
                  {averageForca >= 7 && (
                    <Badge>Força ({formatScore(averageForca)})</Badge>
                  )}
                  {averageTemperamento >= 7 && (
                    <Badge>Temperamento ({formatScore(averageTemperamento)})</Badge>
                  )}
                  {averageVelocidade < 7 && averageResistencia < 7 && averageAgilidade < 7 && 
                   averageForca < 7 && averageTemperamento < 7 && (
                    <span className="text-sm text-muted-foreground">
                      Nenhum ponto forte destacado (pontuação ≥ 7)
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
          
          {latestMetric && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">Última Avaliação</CardTitle>
                <CardDescription>
                  {format(new Date(latestMetric.date), 'PPP', { locale: ptBR })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={barData} layout="vertical">
                      <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                      <XAxis type="number" domain={[0, 10]} />
                      <YAxis dataKey="name" type="category" width={100} />
                      <RechartsTooltip 
                        formatter={(value: any) => [`${value}/10`, 'Pontuação']}
                        labelFormatter={() => ''}
                      />
                      <Bar dataKey="valor" fill="#8884d8" radius={[0, 4, 4, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                
                {latestMetric.observacoes && (
                  <div className="mt-4 p-3 bg-muted rounded-md text-sm">
                    <div className="flex items-start gap-2">
                      <InfoIcon className="h-4 w-4 text-muted-foreground mt-0.5 shrink-0" />
                      <p>{latestMetric.observacoes}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        {/* Gráficos */}
        <TabsContent value="charts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Evolução ao Longo do Tempo</CardTitle>
              <CardDescription>
                Acompanhamento das métricas em todas as avaliações
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={formattedData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="formattedDate" />
                    <YAxis domain={[0, 10]} />
                    <RechartsTooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="velocidade" 
                      name="Velocidade"
                      stroke={lineColors.velocidade} 
                      activeDot={{ r: 8 }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="resistencia" 
                      name="Resistência"
                      stroke={lineColors.resistencia} 
                    />
                    <Line 
                      type="monotone" 
                      dataKey="agilidade" 
                      name="Agilidade"
                      stroke={lineColors.agilidade} 
                    />
                    <Line 
                      type="monotone" 
                      dataKey="forca" 
                      name="Força"
                      stroke={lineColors.forca} 
                    />
                    <Line 
                      type="monotone" 
                      dataKey="temperamento" 
                      name="Temperamento"
                      stroke={lineColors.temperamento} 
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">Tendências</CardTitle>
                <CardDescription>
                  Evolução individual de cada característica
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {['velocidade', 'resistencia', 'agilidade', 'forca', 'temperamento'].map((metric) => {
                  const metricKey = metric as keyof PerformanceMetric;
                  const metricName = metric.charAt(0).toUpperCase() + metric.slice(1);
                  const firstValue = sortedMetrics.length > 0 ? sortedMetrics[0][metricKey] as number : 0;
                  const lastValue = sortedMetrics.length > 0 ? sortedMetrics[sortedMetrics.length - 1][metricKey] as number : 0;
                  const difference = lastValue - firstValue;
                  const isPositive = difference > 0;
                  const isNeutral = difference === 0;
                  
                  return (
                    <div key={metric} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div 
                          className={`w-3 h-3 rounded-full ${
                            isPositive ? 'bg-green-500' : isNeutral ? 'bg-gray-400' : 'bg-red-500'
                          }`}
                        />
                        <span>{metricName}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className={
                          isPositive ? 'text-green-600' : 
                          isNeutral ? 'text-gray-500' : 
                          'text-red-600'
                        }>
                          {isPositive ? '+' : ''}{difference.toFixed(1)}
                        </span>
                        <TrendingUp 
                          className={`h-4 w-4 ${
                            isPositive ? 'text-green-600 rotate-0' : 
                            isNeutral ? 'text-gray-500 rotate-90' : 
                            'text-red-600 rotate-180'
                          }`} 
                        />
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">Estatísticas</CardTitle>
                <CardDescription>
                  Resumo de todas as avaliações
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Número de avaliações</p>
                      <p className="text-2xl font-medium">{metrics.length}</p>
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Período</p>
                      <p className="text-sm">
                        {metrics.length > 0 ? (
                          `${format(new Date(sortedMetrics[0].date), 'MMM/yyyy', { locale: ptBR })} - 
                           ${format(new Date(sortedMetrics[sortedMetrics.length - 1].date), 'MMM/yyyy', { locale: ptBR })}`
                        ) : (
                          'Sem dados'
                        )}
                      </p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Velocidade</span>
                      <span className="font-medium">{formatScore(averageVelocidade)}</span>
                    </div>
                    <div className="h-1.5 w-full bg-secondary rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-blue-500 transition-all" 
                        style={{ width: `${averageVelocidade * 10}%` }}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Resistência</span>
                      <span className="font-medium">{formatScore(averageResistencia)}</span>
                    </div>
                    <div className="h-1.5 w-full bg-secondary rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-green-500 transition-all" 
                        style={{ width: `${averageResistencia * 10}%` }}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Agilidade</span>
                      <span className="font-medium">{formatScore(averageAgilidade)}</span>
                    </div>
                    <div className="h-1.5 w-full bg-secondary rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-yellow-500 transition-all" 
                        style={{ width: `${averageAgilidade * 10}%` }}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Histórico */}
        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Histórico de Avaliações</CardTitle>
              <CardDescription>
                Registro completo de todas as medições de desempenho
              </CardDescription>
            </CardHeader>
            <CardContent>
              {metrics.length > 0 ? (
                <div className="space-y-6">
                  {sortedMetrics.slice().reverse().map((metric) => (
                    <div key={metric.id} className="border-b pb-4 last:border-0 last:pb-0">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">
                            {format(new Date(metric.date), 'PPP', { locale: ptBR })}
                          </span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          ID: {metric.id}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-3 mt-3">
                        <div className="space-y-1">
                          <p className="text-xs text-muted-foreground">Velocidade</p>
                          <div className="flex items-center">
                            <span className="text-lg font-medium">{metric.velocidade}</span>
                            <span className="text-xs text-muted-foreground ml-1">/10</span>
                          </div>
                        </div>
                        
                        <div className="space-y-1">
                          <p className="text-xs text-muted-foreground">Resistência</p>
                          <div className="flex items-center">
                            <span className="text-lg font-medium">{metric.resistencia}</span>
                            <span className="text-xs text-muted-foreground ml-1">/10</span>
                          </div>
                        </div>
                        
                        <div className="space-y-1">
                          <p className="text-xs text-muted-foreground">Agilidade</p>
                          <div className="flex items-center">
                            <span className="text-lg font-medium">{metric.agilidade}</span>
                            <span className="text-xs text-muted-foreground ml-1">/10</span>
                          </div>
                        </div>
                        
                        <div className="space-y-1">
                          <p className="text-xs text-muted-foreground">Força</p>
                          <div className="flex items-center">
                            <span className="text-lg font-medium">{metric.forca}</span>
                            <span className="text-xs text-muted-foreground ml-1">/10</span>
                          </div>
                        </div>
                        
                        <div className="space-y-1">
                          <p className="text-xs text-muted-foreground">Temperamento</p>
                          <div className="flex items-center">
                            <span className="text-lg font-medium">{metric.temperamento}</span>
                            <span className="text-xs text-muted-foreground ml-1">/10</span>
                          </div>
                        </div>
                      </div>
                      
                      {metric.observacoes && (
                        <div className="mt-3 p-2 bg-muted/50 rounded text-sm">
                          {metric.observacoes}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart2 className="h-10 w-10 mx-auto mb-3 text-muted-foreground/60" />
                  <p>Nenhuma avaliação de desempenho registrada para este cavalo.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Detalhes */}
        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Visão Detalhada</CardTitle>
              <CardDescription>
                Informações detalhadas sobre o desempenho de {horseName}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Velocidade</h4>
                    <p className="text-sm text-muted-foreground">
                      Mede a capacidade do cavalo atingir e manter velocidades elevadas em curtas e médias distâncias.
                    </p>
                    <div className="flex items-baseline mt-2">
                      <span className="text-2xl font-bold">{formatScore(averageVelocidade)}</span>
                      <span className="text-muted-foreground ml-1 text-sm">/10</span>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Resistência</h4>
                    <p className="text-sm text-muted-foreground">
                      Avalia a capacidade cardiorrespiratória e muscular para esforços prolongados.
                    </p>
                    <div className="flex items-baseline mt-2">
                      <span className="text-2xl font-bold">{formatScore(averageResistencia)}</span>
                      <span className="text-muted-foreground ml-1 text-sm">/10</span>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Agilidade</h4>
                    <p className="text-sm text-muted-foreground">
                      Mede a capacidade de mudar rapidamente de direção, equilíbrio e coordenação motora.
                    </p>
                    <div className="flex items-baseline mt-2">
                      <span className="text-2xl font-bold">{formatScore(averageAgilidade)}</span>
                      <span className="text-muted-foreground ml-1 text-sm">/10</span>
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Força</h4>
                    <p className="text-sm text-muted-foreground">
                      Avalia a potência muscular, especialmente nos membros posteriores e anteriores, importante para impulsão e saltos.
                    </p>
                    <div className="flex items-baseline mt-2">
                      <span className="text-2xl font-bold">{formatScore(averageForca)}</span>
                      <span className="text-muted-foreground ml-1 text-sm">/10</span>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Temperamento</h4>
                    <p className="text-sm text-muted-foreground">
                      Avalia aspectos comportamentais como docilidade, reatividade, concentração e adaptabilidade a diferentes situações.
                    </p>
                    <div className="flex items-baseline mt-2">
                      <span className="text-2xl font-bold">{formatScore(averageTemperamento)}</span>
                      <span className="text-muted-foreground ml-1 text-sm">/10</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Metodologia de Avaliação</h4>
                  <p className="text-sm text-muted-foreground">
                    As avaliações são realizadas por profissionais qualificados em condições padronizadas. 
                    Cada métrica é avaliada em uma escala de 1 a 10, onde 1 representa o nível mais 
                    baixo e 10 o nível máximo de performance.
                  </p>
                </div>
                
                <div className="bg-muted p-4 rounded-md">
                  <div className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-primary mt-0.5 mr-2 shrink-0" />
                    <div>
                      <h4 className="text-sm font-medium">Nota importante</h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        As métricas de desempenho não devem ser consideradas isoladamente para 
                        decisões de criação. Recomenda-se avaliar em conjunto com critérios 
                        morfológicos, idade do animal, condições de treinamento e histórico genético.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PerformanceChart;