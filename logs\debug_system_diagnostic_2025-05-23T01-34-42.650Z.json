{"timestamp": "2025-05-23T01:34:42.650Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 394833920, "heapTotal": 111869952, "heapUsed": 87912160, "external": 8429174, "arrayBuffers": 265658}, "uptime": 2.861410833, "cpuUsage": {"user": 3005331, "system": 478896}, "resourceUsage": {"userCPUTime": 3005377, "systemCPUTime": 478904, "maxRSS": 385580, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 111616, "majorPageFault": 6, "swappedOut": 0, "fsRead": 33696, "fsWrite": 968, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9123, "involuntaryContextSwitches": 4666}}