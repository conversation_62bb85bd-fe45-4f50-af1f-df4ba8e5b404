import { useState } from 'react';
import { Link, useLocation } from 'wouter';
import {
  Home,
  Database,
  Calendar,
  Heart,
  Stethoscope,
  ShoppingBag,
  Menu as MenuIcon,
  ChevronRight,
  X,
  Dna,
  Utensils,
  Settings
} from 'lucide-react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAccessControl } from '@/hooks/use-access-control';

const bottomNavItems = [
  {
    path: '/',
    label: 'Home',
    icon: <Home className="h-5 w-5" />
  },
  {
    path: '/cavalos',
    label: 'Cavalos',
    icon: <Database className="h-5 w-5" />
  },
  {
    path: '/nutricao',
    label: 'Nutrição',
    icon: <Utensils className="h-5 w-5" />
  },
  {
    path: '/agenda',
    label: 'Agenda',
    icon: <Calendar className="h-5 w-5" />
  },
  {
    path: '/veterinario',
    label: 'Veterinário',
    icon: <Stethoscope className="h-5 w-5" />
  }
];

// Componente para o drawer do menu completo
const FullMenuDrawer = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
  const { canAccessAdmin } = useAccessControl();
  
  const menuSections = [
    {
      title: "Animais",
      items: [
        { path: "/cavalos", label: "Lista de Cavalos", icon: <Database className="h-4 w-4 mr-3" /> },
        { path: "/cavalo/cadastro", label: "Cadastro de Animais", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
        { path: "/manejos", label: "Manejos", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
      ]
    },
    {
      title: "Veterinário",
      items: [
        { path: "/veterinario", label: "Veterinário", icon: <Stethoscope className="h-4 w-4 mr-3" /> },
        { path: "/procedimentos-vet", label: "Procedimentos", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
        { path: "/veterinario/vacinacoes", label: "Vacinações", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
      ]
    },
    {
      title: "Reprodução",
      items: [
        { path: "/reproducao", label: "Controle de Reprodução", icon: <Heart className="h-4 w-4 mr-3" /> },
        { path: "/reproducao/nascimentos", label: "Nascimentos", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
      ]
    },
    {
      title: "Genética",
      items: [
        { path: "/genetica", label: "Genética", icon: <Dna className="h-4 w-4 mr-3" /> },
        { path: "/genetica?tab=morfologia", label: "Morfologia", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
        { path: "/genetica?tab=genealogia", label: "Genealogia", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
        { path: "/genetica?tab=desempenho", label: "Desempenho", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
        { path: "/genetica?tab=sugestoes", label: "Sugestões de Cruzamento", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
      ]
    },
    {
      title: "Nutrição",
      items: [
        { path: "/nutricao", label: "Nutrição Básica", icon: <Utensils className="h-4 w-4 mr-3" /> },
        { path: "/nutricao-logistica", label: "Logística de Nutrição", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
      ]
    },
    {
      title: "Agenda",
      items: [
        { path: "/agenda", label: "Agenda", icon: <Calendar className="h-4 w-4 mr-3" /> },
      ]
    },
    {
      title: "Financeiro",
      items: [
        { path: "/financeiro", label: "Financeiro", icon: <ShoppingBag className="h-4 w-4 mr-3" /> },
        { path: "/financeiro/lancamentos", label: "Lançamentos", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
      ]
    }
  ];

  // Adicionar seção de administração apenas para admins
  if (canAccessAdmin()) {
    menuSections.push({
      title: "Administração",
      items: [
        { path: "/admin", label: "Painel Admin", icon: <ChevronRight className="h-4 w-4 mr-3" /> },
      ]
    });
  }
  
  const [location] = useLocation();
  
  return (
    <div className={cn(
      "fixed inset-0 bg-gray-900/40 z-50 transition-opacity duration-300",
      isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
    )}>
      <div className={cn(
        "fixed inset-y-0 right-0 w-[85%] max-w-sm bg-white shadow-xl transform transition-transform duration-300 flex flex-col h-full",
        isOpen ? "translate-x-0" : "translate-x-full"
      )}>
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-bold">Menu Principal</h2>
          <Button 
            variant="ghost" 
            size="icon"
            onClick={onClose}
            aria-label="Fechar menu"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
        
        <ScrollArea className="flex-grow">
          <div className="p-4 space-y-6">
            {menuSections.map((section) => (
              <div key={section.title} className="space-y-2">
                <h3 className="text-xs uppercase tracking-wider font-bold text-gray-400 border-b border-gray-200 pb-1">
                  {section.title}
                </h3>
                <div className="space-y-1">
                  {section.items.map((item) => (
                    <Link 
                      key={item.path} 
                      href={item.path}
                      onClick={onClose}
                    >
                      <div className={cn(
                        "flex items-center py-3 px-4 rounded-md text-sm font-medium",
                        location === item.path 
                          ? "bg-blue-100 text-blue-700" 
                          : "text-gray-700 hover:bg-gray-100"
                      )}>
                        {item.icon}
                        {item.label}
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};

export function MobileNav() {
  const [location] = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  
  return (
    <>
      {/* Nav móvel fixo na parte inferior */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 flex items-center justify-around z-40 h-16">
        {bottomNavItems.map((item) => (
          <Link key={item.path} href={item.path}>
            <div 
              className={cn(
                "flex flex-col items-center justify-center px-2 py-1 w-full h-full text-xs",
                location === item.path ? "text-blue-600" : "text-gray-600"
              )}
            >
              {item.icon}
              <span className="mt-1">{item.label}</span>
            </div>
          </Link>
        ))}
        
        <button
          className="flex flex-col items-center justify-center px-2 py-1 w-full h-full text-xs text-gray-600"
          onClick={() => setIsMenuOpen(true)}
        >
          <MenuIcon className="h-5 w-5" />
          <span className="mt-1">Menu</span>
        </button>
      </div>
      
      {/* Menu completo em formato de drawer */}
      <FullMenuDrawer isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />
    </>
  );
}

export default MobileNav;