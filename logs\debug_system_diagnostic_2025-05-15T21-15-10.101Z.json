{"timestamp": "2025-05-15T21:15:10.101Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 310566912, "heapTotal": 133308416, "heapUsed": 104333920, "external": 8375821, "arrayBuffers": 235533}, "uptime": 1.872212688, "cpuUsage": {"user": 2685901, "system": 375018}, "resourceUsage": {"userCPUTime": 2685955, "systemCPUTime": 375026, "maxRSS": 303288, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104057, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8379, "involuntaryContextSwitches": 4008}}