import { useState, useEffect, useRef } from 'react';
import { Loader2, Send, AlertCircle, ChevronDown, Image as ImageIcon, PlusCircle, Edit, List } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  detectIntent, 
  extractEntities, 
  executeAction, 
  sendMessageToAssistant, 
  type ChatMessage,
  type ExtractedEntity 
} from '@/lib/assistente';
import { useToast } from '@/hooks/use-toast';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { apiRequest } from '@/lib/queryClient';

interface SmartAssistantAIProps {
  horseId?: number;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
}

export function SmartAssistantAI({ horseId: initialHorseId, isExpanded = true, onToggleExpand }: SmartAssistantAIProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      role: 'system',
      content: 'Olá! Eu sou o assistente virtual do EquiGestor. Como posso ajudar com o gerenciamento dos seus cavalos hoje?'
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [detectedIntent, setDetectedIntent] = useState<string | null>(null);
  const [intentType, setIntentType] = useState<'action' | 'query' | null>(null);
  const [pendingAction, setPendingAction] = useState<{
    functionName: string;
    parameters: Record<string, any>;
  } | null>(null);
  const [selectedHorseId, setSelectedHorseId] = useState<number | undefined>(initialHorseId);
  const [selectedHorseName, setSelectedHorseName] = useState<string | undefined>();
  const [selectedHorseImageUrl, setSelectedHorseImageUrl] = useState<string | undefined>();
  const [cavalos, setCavalos] = useState<any[]>([]);
  const [loadingCavalos, setLoadingCavalos] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  
  // Buscar lista de cavalos para o menu de seleção
  useEffect(() => {
    async function loadCavalos() {
      try {
        setLoadingCavalos(true);
        const response = await apiRequest<any[]>("GET", "/api/cavalos");
        setCavalos(response);
        
        // Se houver um cavalo inicial selecionado, carregar seus dados
        if (initialHorseId && response.length > 0) {
          const cavaloSelecionado = response.find(c => c.id === initialHorseId);
          if (cavaloSelecionado) {
            setSelectedHorseId(cavaloSelecionado.id);
            setSelectedHorseName(cavaloSelecionado.name);
            setSelectedHorseImageUrl(cavaloSelecionado.imageUrl);
          }
        }
      } catch (error) {
        console.error("Erro ao carregar cavalos:", error);
      } finally {
        setLoadingCavalos(false);
      }
    }
    
    loadCavalos();
  }, [initialHorseId]);

  // Rolagem automática para a última mensagem
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Função para selecionar um cavalo
  const handleSelectHorse = (horse: any) => {
    setSelectedHorseId(horse.id);
    setSelectedHorseName(horse.name);
    setSelectedHorseImageUrl(horse.imageUrl);
    
    // Adiciona mensagem de confirmação
    setMessages(prev => [
      ...prev,
      { 
        role: 'assistant', 
        content: `Agora estamos conversando sobre ${horse.name}. Como posso ajudar com informações sobre este cavalo?`
      }
    ]);
  };
  
  // Função para criar um novo cavalo via chatbot
  const handleCreateHorse = async () => {
    try {
      // Adiciona mensagem informativa
      setMessages(prev => [
        ...prev,
        { 
          role: 'assistant', 
          content: `Para cadastrar um novo cavalo, por favor, forneça as seguintes informações:
          
1. Nome do cavalo
2. Raça (opcional)
3. Data de nascimento (formato DD/MM/AAAA, opcional)
4. Sexo (M/F, opcional)
5. Cor (opcional)

Exemplo: "Cadastrar cavalo: Thunder, Quarto de Milha, 15/03/2020, M, Tordilho"`
        }
      ]);
    } catch (error) {
      console.error("Erro ao iniciar criação de cavalo:", error);
      toast({
        title: "Erro",
        description: "Não foi possível iniciar o cadastro do cavalo",
        variant: "destructive"
      });
    }
  };
  
  // Função para lidar com mensagem incompleta
  const handleIncompleteMessage = (messageContent: string) => {
    // Verifica se a mensagem parece ser incompleta ou muito curta
    if (messageContent.length < 4 || !messageContent.includes(" ")) {
      setMessages(prev => [
        ...prev,
        { 
          role: 'assistant', 
          content: `Sua mensagem parece estar incompleta. 
          
Você pode estar querendo perguntar sobre:
- Informações sobre um cavalo específico? Use "Informações sobre [nome do cavalo]"
- Agendar um manejo? Use "Agendar [tipo de manejo] para [nome do cavalo]"
- Registrar um evento? Use "Registrar [tipo de evento] em [data] para [nome do cavalo]"
- Ver eventos futuros? Use "Mostrar próximos eventos"
- Listar seus cavalos? Use "Listar meus cavalos"

Como posso ajudar?`
        }
      ]);
      return true;
    }
    return false;
  };

  // Processa a mensagem do usuário
  const handleSendMessage = async () => {
    // Evita enviar mensagens vazias
    if (!inputValue.trim()) return;
    
    const userMessage = inputValue.trim();
    setInputValue('');
    
    // Adiciona a mensagem do usuário ao histórico
    setMessages(prev => [
      ...prev,
      { role: 'user', content: userMessage }
    ]);
    
    // Inicia o processamento
    setIsProcessing(true);
    
    try {
      // Verificar se a mensagem contém uma ação de vacinação
      if (userMessage.toLowerCase().includes('vacinar') || 
          userMessage.toLowerCase().includes('agendar vacinação') ||
          userMessage.toLowerCase().includes('vacina para')) {
        
        console.log("Detectada possível ação de vacinação");
        
        // Extrair entidades relevantes da mensagem
        const entities = await extractEntities(userMessage);
        console.log('Entidades extraídas:', entities);
        
        if (entities.nome_cavalo && (entities.tipo_vacina || userMessage.toLowerCase().includes('vacin'))) {
          // Preparar data - usar a data fornecida ou hoje
          const data = entities.data || new Date().toISOString().split('T')[0];
          
          // Preparar parâmetros para vacinação
          setPendingAction({
            functionName: 'registrarVacina',
            parameters: { 
              nome: entities.nome_cavalo,
              data: entities.data || 'hoje',
              tipo_vacina: entities.tipo_vacina || 'padrão',
              userId: 1 // Usar o usuário padrão
            }
          });
          
          // Adicionar mensagem de confirmação
          setMessages(prev => [
            ...prev,
            { 
              role: 'assistant', 
              content: `Posso agendar uma vacinação de ${entities.tipo_vacina || 'rotina'} para o cavalo ${entities.nome_cavalo} ${entities.data ? `em ${entities.data}` : 'para hoje'}. Deseja confirmar?`
            }
          ]);
          
          return; // Evita continuar para a consulta normal
        }
      }
      
      // Se não for detectada uma ação de vacinação, processa como consulta normal
      await handleNormalQuery(userMessage);
    } catch (error) {
      console.error('Erro ao processar mensagem:', error);
      
      // Adiciona mensagem de erro
      setMessages(prev => [
        ...prev,
        { 
          role: 'assistant', 
          content: 'Desculpe, ocorreu um erro ao processar sua mensagem. Por favor, tente novamente.'
        }
      ]);
      
      toast({
        title: 'Erro no assistente',
        description: 'Não foi possível processar sua solicitação.',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Processa uma consulta normal (sem ação específica)
  const handleNormalQuery = async (message: string) => {
    try {
      // Verifica se a mensagem parece incompleta
      if (handleIncompleteMessage(message)) {
        setIsProcessing(false);
        return;
      }
      
      // Verifica padrões de comandos específicos
      if (message.toLowerCase().startsWith("cadastrar cavalo:")) {
        await handleCreateNewHorse(message);
        return;
      }
      
      // Tenta primeiro extrair entidades localmente para melhorar o contexto
      let entities: Record<string, ExtractedEntity> = {};
      try {
        entities = await extractEntities(message, true); // useLocalOnly = true para economizar tokens
        console.log("[Assistente] Entidades extraídas localmente:", entities);
      } catch (err) {
        console.warn("[Assistente] Erro ao extrair entidades localmente:", err);
      }
      
      // Extrai nome do cavalo, se presente nos dados extraídos
      if (entities.cavaloNome && !selectedHorseId) {
        const cavaloNome = entities.cavaloNome.value as string;
        const cavaloEncontrado = cavalos.find(c => 
          c.name.toLowerCase() === cavaloNome.toLowerCase()
        );
        
        if (cavaloEncontrado) {
          console.log(`[Assistente] Cavalo detectado na mensagem: ${cavaloNome}`);
          setSelectedHorseId(cavaloEncontrado.id);
          setSelectedHorseName(cavaloEncontrado.name);
          setSelectedHorseImageUrl(cavaloEncontrado.imageUrl);
        }
      }
      
      // Cria um histórico formatado para enviar ao assistente
      const formattedHistory = messages
        .filter(msg => msg.role !== 'system') // Remove mensagens do sistema
        .slice(-4); // Mantém apenas as últimas 4 mensagens para contexto
      
      // Envia a mensagem para o assistente no backend, passando o ID do cavalo selecionado
      const response = await sendMessageToAssistant(message, formattedHistory, selectedHorseId);
      
      // Adiciona a resposta ao histórico
      setMessages(prev => [
        ...prev,
        { role: 'assistant', content: response }
      ]);
    } catch (error) {
      console.error('Erro ao obter resposta do assistente:', error);
      
      // Adiciona mensagem de erro
      setMessages(prev => [
        ...prev,
        { 
          role: 'assistant', 
          content: 'Desculpe, não consegui responder adequadamente. Nosso serviço pode estar temporariamente indisponível.'
        }
      ]);
    }
  };
  
  // Função para manipular criação de cavalos a partir de mensagens
  const handleCreateNewHorse = async (message: string) => {
    try {
      // Remove o prefixo "cadastrar cavalo:" e divide os campos por vírgula
      const horseData = message.substring(message.indexOf(':') + 1).trim();
      const fields = horseData.split(',').map(field => field.trim());
      
      // Extrai os dados do cavalo
      const name = fields[0];
      const breed = fields.length > 1 ? fields[1] : '';
      const birthDate = fields.length > 2 ? fields[2] : '';
      const sex = fields.length > 3 ? fields[3] : '';
      const color = fields.length > 4 ? fields[4] : '';
      
      // Valida o nome (único campo obrigatório)
      if (!name) {
        setMessages(prev => [
          ...prev,
          { 
            role: 'assistant', 
            content: 'Por favor, informe pelo menos o nome do cavalo para cadastrá-lo.'
          }
        ]);
        return;
      }
      
      // Cria objeto de dados para a API
      const horsePayload = {
        name,
        breed,
        birthDate,
        sex,
        color
      };
      
      // Envia para a API
      const response = await apiRequest("POST", "/api/cavalos", horsePayload);
      
      // Verifica resposta e notifica o usuário
      if (response) {
        setMessages(prev => [
          ...prev,
          { 
            role: 'assistant', 
            content: `O cavalo ${name} foi cadastrado com sucesso! Você pode adicionar mais detalhes pelo menu Cavalos.`
          }
        ]);
        
        // Atualiza a lista de cavalos
        const updatedCavalos = await apiRequest<any[]>("GET", "/api/cavalos");
        setCavalos(updatedCavalos);
      } else {
        throw new Error("Erro na resposta da API");
      }
    } catch (error) {
      console.error("Erro ao cadastrar cavalo:", error);
      
      setMessages(prev => [
        ...prev,
        { 
          role: 'assistant', 
          content: 'Não foi possível cadastrar o cavalo. Por favor, verifique os dados e tente novamente.'
        }
      ]);
    }
  };
  
  // Executa a ação pendente após confirmação do usuário
  const executeConfirmedAction = async () => {
    if (!pendingAction) return;
    
    try {
      setIsProcessing(true);
      
      // Adiciona mensagem de confirmação
      setMessages(prev => [
        ...prev,
        { role: 'user', content: 'Sim, pode executar a ação.' }
      ]);
      
      // Executa a ação no backend
      const result = await executeAction(
        pendingAction.functionName,
        pendingAction.parameters
      );
      
      // Adiciona resultado ao histórico
      if (result.success) {
        setMessages(prev => [
          ...prev,
          { role: 'assistant', content: result.message || 'Ação executada com sucesso!' }
        ]);
      } else {
        setMessages(prev => [
          ...prev,
          { 
            role: 'assistant', 
            content: `Não foi possível executar a ação: ${result.message || 'Ocorreu um erro desconhecido'}`
          }
        ]);
      }
    } catch (error) {
      console.error('Erro ao executar ação:', error);
      
      // Adiciona mensagem de erro
      setMessages(prev => [
        ...prev,
        { 
          role: 'assistant', 
          content: 'Desculpe, ocorreu um erro ao executar a ação solicitada.'
        }
      ]);
    } finally {
      setIsProcessing(false);
      setPendingAction(null); // Limpa a ação pendente
      setDetectedIntent(null); // Limpa a intenção detectada
      setIntentType(null); // Limpa o tipo de intenção
    }
  };
  
  // Cancela a ação pendente
  const cancelAction = () => {
    // Adiciona mensagem de cancelamento
    setMessages(prev => [
      ...prev,
      { role: 'user', content: 'Não, cancele a ação.' },
      { 
        role: 'assistant', 
        content: 'Certo, ação cancelada. Em que mais posso ajudar?'
      }
    ]);
    
    // Limpa os estados relacionados à ação
    setPendingAction(null);
    setDetectedIntent(null);
    setIntentType(null);
  };

  // Mapeia uma intenção para o nome da função correspondente
  const mapIntentToFunction = (intent: string): string | null => {
    // Mapeamento de intenções para nomes de funções
    const intentMap: Record<string, string> = {
      'Registro de peso': 'registrarPeso',
      'Vacinação': 'registrarVacina',
      'Treinamento': 'registrarTreino',
      'Alimentação': 'registrarAlimentacao',
      'Ferrageamento': 'registrarFerrageamento',
      'Veterinária': 'agendarVeterinario',
      'Cadastro de cavalo': 'cadastrarCavalo',
      'Consulta de dados': 'consultarDados',
      // Funções relacionadas à genética
      'Morfologia': 'consultarMorfologia',
      'Genealogia': 'consultarGenealogia',
      'Desempenho': 'consultarDesempenho',
      'Cruzamento': 'sugerirCruzamento',
      'Genética': 'consultarGenetica',
    };
    
    // Retorna o nome da função correspondente ou null se não encontrar
    for (const key in intentMap) {
      if (intent.includes(key)) {
        return intentMap[key];
      }
    }
    
    return null;
  };

  // Renderiza as mensagens do chat
  const renderMessages = () => {
    return messages.map((message, index) => {
      const isUser = message.role === 'user';
      
      // Verificar se a mensagem contém markdown que precisa ser processado
      const hasMarkdown = message.content.includes('**') || 
                          message.content.includes('•') || 
                          message.content.includes('📊') ||
                          message.content.includes('\n');
      
      // Processar quebras de linha e formatação básica
      let formattedContent = message.content;
      
      if (hasMarkdown) {
        // Substituir quebras de linha por <br>
        formattedContent = formattedContent.replace(/\n/g, '<br>');
        
        // Substituir negrito (entre **)
        formattedContent = formattedContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // Destacar emojis para ficarem maiores
        formattedContent = formattedContent.replace(/(📊|📋|📅|🔍|✅)/g, '<span class="text-lg">$1</span>');
      }
      
      return (
        <div 
          key={index} 
          className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}
        >
          <div 
            className={`max-w-[80%] rounded-lg p-3 ${
              isUser 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-muted text-foreground'
            }`}
          >
            {hasMarkdown ? (
              <div dangerouslySetInnerHTML={{ __html: formattedContent }} />
            ) : (
              message.content
            )}
          </div>
        </div>
      );
    });
  };

  if (!isExpanded) {
    return null;
  }

  return (
    <Card className="w-full max-w-md mx-auto shadow-lg border-primary/20">
      <CardHeader className="bg-primary/5 pb-2">
        <CardTitle className="flex justify-between items-center text-lg font-medium">
          <div className="flex items-center">
            <span>Assistente Virtual EquiGestor</span>
            {selectedHorseName && (
              <Badge variant="outline" className="ml-2">
                {selectedHorseName}
              </Badge>
            )}
          </div>
          
          {detectedIntent && (
            <Badge 
              variant={intentType === 'action' ? 'default' : 'outline'}
              className={intentType === 'action' ? 'bg-blue-500' : 'bg-amber-500'}
            >
              {detectedIntent}
            </Badge>
          )}
        </CardTitle>
        
        <div className="flex justify-between items-center mt-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 text-xs">
                Selecionar Cavalo <ChevronDown className="ml-1 h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuLabel>Meus Cavalos</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {loadingCavalos ? (
                <div className="flex justify-center py-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                </div>
              ) : cavalos.length > 0 ? (
                <>
                  {cavalos.map(cavalo => (
                    <DropdownMenuItem 
                      key={cavalo.id}
                      onClick={() => handleSelectHorse(cavalo)}
                    >
                      {cavalo.name} {cavalo.breed ? `(${cavalo.breed})` : ''}
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleCreateHorse}>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    <span>Cadastrar Novo Cavalo</span>
                  </DropdownMenuItem>
                </>
              ) : (
                <DropdownMenuItem onClick={handleCreateHorse}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  <span>Cadastrar Primeiro Cavalo</span>
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          
          {selectedHorseImageUrl && (
            <Button variant="ghost" size="sm" className="p-1">
              <ImageIcon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      {selectedHorseImageUrl && (
        <div className="px-4 py-2 bg-muted/30">
          <img 
            src={selectedHorseImageUrl} 
            alt={selectedHorseName || "Cavalo selecionado"} 
            className="w-full h-32 object-cover rounded-md"
          />
        </div>
      )}

      <CardContent className="p-0">
        <ScrollArea className="h-[400px] p-4">
          {renderMessages()}
          {isProcessing && (
            <div className="flex justify-center my-2">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          )}
          <div ref={messagesEndRef} />
        </ScrollArea>
      </CardContent>

      {pendingAction && (
        <>
          <Separator />
          <div className="p-3 bg-amber-50 dark:bg-amber-950/30">
            <Alert className="border-amber-500">
              <AlertCircle className="h-4 w-4 text-amber-500" />
              <AlertDescription className="text-amber-700 dark:text-amber-400">
                Deseja confirmar esta ação?
              </AlertDescription>
            </Alert>
            <div className="flex gap-2 mt-2">
              <Button 
                onClick={executeConfirmedAction} 
                variant="default" 
                className="bg-green-600 hover:bg-green-700 w-1/2"
                disabled={isProcessing}
              >
                {isProcessing ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Sim'}
              </Button>
              <Button 
                onClick={cancelAction} 
                variant="outline" 
                className="text-red-500 border-red-300 hover:bg-red-50 hover:text-red-600 w-1/2"
                disabled={isProcessing}
              >
                Não
              </Button>
            </div>
          </div>
        </>
      )}

      <CardFooter className="p-3 pt-0">
        <div className="flex w-full gap-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Digite sua mensagem..."
            className="flex-grow"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            disabled={isProcessing}
          />
          <Button 
            onClick={handleSendMessage} 
            size="icon" 
            disabled={isProcessing || inputValue.trim() === ''}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}