 [vite] connecting...
 [vite] connected.
 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
 [Firebase] Initializing Firebase with config: {"apiKey":"*****","authDomain":"cavalo-409dc.firebaseapp.com","projectId":"cavalo-409dc","storageBucket":"cavalo-409dc.firebasestorage.app","messagingSenderId":"672662898514","appId":"1:672662898514:web:f1b7678d4e1baf11a05c8b"}
 [2025-05-29T22:41:05.546Z] [INFO] [logger]: Sistema de log inicializado Object
 [AuthContext] AuthProvider rendered
 ProtectedRoute: Verificando autenticação...
 ProtectedRoute: Usuário encontrado no localStorage
 Buscando cavalo com ID 144
 User ID para autenticação: 2
 Headers enviados: Object
 Buscando manejos para o cavalo 144
 User ID para autenticação: 2
 Headers enviados: Object
 Buscando arquivos para o cavalo 144
 User ID para autenticação: 2
 Headers enviados: Object
 User ID para autenticação: 2
 Headers enviados: Object
 User ID para autenticação: 2
 Headers enviados: Object
queryClient.ts:113 User ID para autenticação: 2
queryClient.ts:114 Headers enviados: Object
main.tsx:16 Service Worker registrado com sucesso: https://63ed2eba-d4d9-45aa-800f-0ebe7fd3e860-00-122e7dsebuel9.riker.replit.dev/
HorseDetails.tsx:126 Arquivos recebidos: Array(0)
HorseDetails.tsx:101 Manejos recebidos: Array(0)
HorseDetails.tsx:76 Cavalo recebido: Object
queryClient.ts:113 User ID para autenticação: 2
queryClient.ts:114 Headers enviados: Object
queryClient.ts:113 User ID para autenticação: 2
queryClient.ts:114 Headers enviados: Object
HorseDetails.tsx:74 Buscando cavalo com ID 144
queryClient.ts:113 User ID para autenticação: 2
queryClient.ts:114 Headers enviados: Object
HorseDetails.tsx:99 Buscando manejos para o cavalo 144
queryClient.ts:113 User ID para autenticação: 2
queryClient.ts:114 Headers enviados: Object
HorseDetails.tsx:124 Buscando arquivos para o cavalo 144
queryClient.ts:113 User ID para autenticação: 2
queryClient.ts:114 Headers enviados: Object
HorseDetails.tsx:101 Manejos recebidos: Array(0)
HorseDetails.tsx:126 Arquivos recebidos: Array(0)
HorseDetails.tsx:76 Cavalo recebido: Object
EditHorseSimple.tsx:70 Preenchendo formulário com: Object
EditHorseSimple.tsx:213 Dados sendo enviados: Object
EditHorseSimple.tsx:177 Payload sendo enviado para API: Object
queryClient.ts:113 User ID para autenticação: 2
queryClient.ts:114 Headers enviados: Object
EditHorseSimple.tsx:180 Resposta da API: Object
AuthContext.tsx:6 [AuthContext] AuthProvider rendered
AuthContext.tsx:6 [AuthContext] AuthProvider rendered
EditHorseSimple.tsx:70 Preenchendo formulário com: Object
HorseAvatarWithUpload.tsx:115 Erro no upload: SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON
handleFileSelect @ HorseAvatarWithUpload.tsx:115
AuthContext.tsx:6 [AuthContext] AuthProvider rendered
