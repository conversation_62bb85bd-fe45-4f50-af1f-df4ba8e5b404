{"timestamp": "2025-05-19T12:39:01.227Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 261193728, "heapTotal": 111624192, "heapUsed": 92312640, "external": 8307307, "arrayBuffers": 282042}, "uptime": 4.985999758, "cpuUsage": {"user": 3189363, "system": 467905}, "resourceUsage": {"userCPUTime": 3189427, "systemCPUTime": 467905, "maxRSS": 295568, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102467, "majorPageFault": 0, "swappedOut": 0, "fsRead": 36048, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9695, "involuntaryContextSwitches": 15057}}