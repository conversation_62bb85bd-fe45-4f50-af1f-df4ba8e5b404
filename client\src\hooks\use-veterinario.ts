import { useState, useCallback, useMemo } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Cavalo, ProcedimentoVet } from '@shared/schema';
import { apiRequest } from '@/lib/queryClient';
import { format, addMonths, isBefore } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Hook personalizado para gerenciar procedimentos veterinários
 * 
 * Encapsula a lógica de busca, filtragem e manipulação de
 * procedimentos veterinários (consultas, vacinas, vermifugações)
 * seguindo as boas práticas de React e utilizando React Query.
 */
export function useVeterinario() {
  const queryClient = useQueryClient();
  // Estados para filtragem
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCavalo, setFilterCavalo] = useState('todos');
  const [filterTipo, setFilterTipo] = useState('todos');
  const [filterStatus, setFilterStatus] = useState('todos');
  
  // Obter informações do usuário logado para autenticação
  const getLoggedInUser = () => {
    try {
      const userJson = localStorage.getItem('user');
      if (userJson) {
        return JSON.parse(userJson);
      }
    } catch (e) {
      console.error('Erro ao obter usuário do localStorage', e);
    }
    return null;
  };

  // Consulta para buscar procedimentos veterinários
  const procedimentosQuery = useQuery({
    queryKey: ['/api/procedimentos-vet'],
    queryFn: async () => {
      // Obter ID do usuário para autenticação
      const user = getLoggedInUser();
      if (!user || !user.id) {
        throw new Error('Usuário não autenticado');
      }
      
      // Configurar headers com ID do usuário
      const headers = new Headers({
        'Content-Type': 'application/json',
        'user-id': user.id.toString()
      });
      
      const res = await fetch('/api/procedimentos-vet', { headers });
      if (!res.ok) {
        const text = await res.text();
        try {
          const error = JSON.parse(text);
          throw new Error(error.message || `Erro ${res.status}: ${res.statusText}`);
        } catch (e) {
          throw new Error(`Erro ${res.status}: ${text || res.statusText}`);
        }
      }
      return res.json();
    }
  });
  
  // Consulta para buscar cavalos
  const cavalosQuery = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      // Obter ID do usuário para autenticação
      const user = getLoggedInUser();
      if (!user || !user.id) {
        throw new Error('Usuário não autenticado');
      }
      
      // Configurar headers com ID do usuário
      const headers = new Headers({
        'Content-Type': 'application/json',
        'user-id': user.id.toString()
      });
      
      const res = await fetch('/api/cavalos', { headers });
      if (!res.ok) {
        const text = await res.text();
        try {
          const error = JSON.parse(text);
          throw new Error(error.message || `Erro ${res.status}: ${res.statusText}`);
        } catch (e) {
          throw new Error(`Erro ${res.status}: ${text || res.statusText}`);
        }
      }
      return res.json();
    }
  });
  
  // Mutation para adicionar procedimento
  const addProcedimentoMutation = useMutation({
    mutationFn: async (formData: any) => {
      // Obter ID do usuário para autenticação
      const user = getLoggedInUser();
      if (!user || !user.id) {
        throw new Error('Usuário não autenticado');
      }
      
      // Configurar headers com ID do usuário
      const headers = new Headers({
        'Content-Type': 'application/json',
        'user-id': user.id.toString()
      });
      
      const res = await fetch('/api/procedimentos-vet', {
        method: 'POST',
        headers,
        body: JSON.stringify(formData)
      });
      
      if (!res.ok) {
        const text = await res.text();
        try {
          const error = JSON.parse(text);
          throw new Error(error.message || `Erro ${res.status}: ${res.statusText}`);
        } catch (e) {
          throw new Error(`Erro ${res.status}: ${text || res.statusText}`);
        }
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/procedimentos-vet'] });
    },
  });
  
  // Mutation para atualizar procedimento
  const updateProcedimentoMutation = useMutation({
    mutationFn: async (formData: any) => {
      // Obter ID do usuário para autenticação
      const user = getLoggedInUser();
      if (!user || !user.id) {
        throw new Error('Usuário não autenticado');
      }
      
      // Configurar headers com ID do usuário
      const headers = new Headers({
        'Content-Type': 'application/json',
        'user-id': user.id.toString()
      });
      
      const { id, ...data } = formData;
      const res = await fetch(`/api/procedimentos-vet/${id}`, {
        method: 'PATCH',
        headers,
        body: JSON.stringify(data)
      });
      
      if (!res.ok) {
        const text = await res.text();
        try {
          const error = JSON.parse(text);
          throw new Error(error.message || `Erro ${res.status}: ${res.statusText}`);
        } catch (e) {
          throw new Error(`Erro ${res.status}: ${text || res.statusText}`);
        }
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/procedimentos-vet'] });
    },
  });
  
  // Mutation para excluir procedimento
  const deleteProcedimentoMutation = useMutation({
    mutationFn: async (id: number) => {
      // Obter ID do usuário para autenticação
      const user = getLoggedInUser();
      if (!user || !user.id) {
        throw new Error('Usuário não autenticado');
      }
      
      // Configurar headers com ID do usuário
      const headers = new Headers({
        'Content-Type': 'application/json',
        'user-id': user.id.toString()
      });
      
      const res = await fetch(`/api/procedimentos-vet/${id}`, {
        method: 'DELETE',
        headers
      });
      
      if (!res.ok) {
        const text = await res.text();
        try {
          const error = JSON.parse(text);
          throw new Error(error.message || `Erro ${res.status}: ${res.statusText}`);
        } catch (e) {
          throw new Error(`Erro ${res.status}: ${text || res.statusText}`);
        }
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/procedimentos-vet'] });
    },
  });
  
  // Função para filtrar as vacinações
  const vacinacoesFiltradas = useMemo(() => {
    if (!procedimentosQuery.data) return [];

    return procedimentosQuery.data
      .filter((proc: ProcedimentoVet) => 
        proc.tipo.toLowerCase().includes('vacina') || 
        proc.descricao.toLowerCase().includes('vacina') ||
        proc.medicamentos?.toLowerCase().includes('vacina')
      )
      .filter((proc: ProcedimentoVet) => 
        (searchTerm === '' || 
          proc.descricao.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.medicamentos?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.veterinario?.toLowerCase().includes(searchTerm.toLowerCase())
        ) &&
        (filterCavalo === 'todos' || proc.horseId.toString() === filterCavalo) &&
        (filterStatus === 'todos' || getStatusVacinacao(proc) === filterStatus) &&
        (filterTipo === 'todos' || getTipoVacina(proc).toLowerCase().includes(filterTipo.toLowerCase()))
      );
  }, [procedimentosQuery.data, searchTerm, filterCavalo, filterStatus, filterTipo]);
  
  // Função para filtrar as vermifugações
  const vermifugacoesFiltradas = useMemo(() => {
    if (!procedimentosQuery.data) return [];

    return procedimentosQuery.data
      .filter((proc: ProcedimentoVet) => 
        proc.tipo.toLowerCase().includes('vermifug') || 
        proc.descricao.toLowerCase().includes('vermifug') ||
        proc.medicamentos?.toLowerCase().includes('vermifug')
      )
      .filter((proc: ProcedimentoVet) => 
        (searchTerm === '' || 
          proc.descricao.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.medicamentos?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.veterinario?.toLowerCase().includes(searchTerm.toLowerCase())
        ) &&
        (filterCavalo === 'todos' || proc.horseId.toString() === filterCavalo) &&
        (filterStatus === 'todos' || getStatusVermifugacao(proc) === filterStatus)
      );
  }, [procedimentosQuery.data, searchTerm, filterCavalo, filterStatus]);
  
  // Função para filtrar os registros clínicos
  const registrosClinicosFiltrados = useMemo(() => {
    if (!procedimentosQuery.data) return [];

    return procedimentosQuery.data
      .filter((proc: ProcedimentoVet) => 
        proc.tipo.toLowerCase().includes('exame') || 
        proc.tipo.toLowerCase().includes('consult') ||
        proc.tipo.toLowerCase().includes('check')
      )
      .filter((proc: ProcedimentoVet) => 
        (searchTerm === '' || 
          proc.descricao.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.veterinario?.toLowerCase().includes(searchTerm.toLowerCase())
        ) &&
        (filterCavalo === 'todos' || proc.horseId.toString() === filterCavalo) &&
        (filterTipo === 'todos' || proc.tipo.toLowerCase().includes(filterTipo.toLowerCase()))
      );
  }, [procedimentosQuery.data, searchTerm, filterCavalo, filterTipo]);
  
  // Obter o nome do cavalo pelo ID
  const getCavaloName = useCallback((id: number) => {
    if (!cavalosQuery.data) return 'N/A';
    const cavalo = cavalosQuery.data.find((c: Cavalo) => c.id === id);
    return cavalo ? cavalo.name : 'N/A';
  }, [cavalosQuery.data]);
  
  // Extrair o tipo de vacina da descrição ou medicamentos
  const getTipoVacina = useCallback((procedimento: ProcedimentoVet): string => {
    // Primeiro tenta extrair do campo medicamentos
    if (procedimento.medicamentos) {
      const match = procedimento.medicamentos.match(/vacina\s+de\s+([^\s,;.]+)/i) ||
                    procedimento.medicamentos.match(/contra\s+([^\s,;.]+)/i) ||
                    procedimento.medicamentos.match(/([^\s,;.]+)\s+vaccine/i);
      if (match) return match[1];
    }
    
    // Depois tenta da descrição
    if (procedimento.descricao) {
      const match = procedimento.descricao.match(/vacina\s+de\s+([^\s,;.]+)/i) ||
                    procedimento.descricao.match(/contra\s+([^\s,;.]+)/i) ||
                    procedimento.descricao.match(/([^\s,;.]+)\s+vaccine/i);
      if (match) return match[1];
    }
    
    // Se não conseguir extrair, retorna o tipo genérico
    return 'Não especificada';
  }, []);
  
  // Determinar o status da vacinação
  const getStatusVacinacao = useCallback((procedimento: ProcedimentoVet): string => {
    if (!procedimento.dataProximoProcedimento) {
      try {
        // Se não há data próxima explícita mas há data da vacinação
        // Assume validade de 12 meses para a maioria das vacinas
        const dataVacinacao = new Date(procedimento.data);
        const dataEstimadaProxima = addMonths(dataVacinacao, 12);
        
        if (isBefore(dataEstimadaProxima, new Date())) {
          return 'vencida';
        }
        
        return 'válida';
      } catch {
        return 'não especificado';
      }
    }
    
    try {
      const dataProximo = new Date(procedimento.dataProximoProcedimento);
      const hoje = new Date();
      
      // Se a data de próxima vacinação já passou
      if (isBefore(dataProximo, hoje)) return 'vencida';
      
      // Se a data de próxima vacinação está a menos de 30 dias
      const diasDiferenca = Math.ceil((dataProximo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
      if (diasDiferenca <= 30) return 'próxima';
      
      return 'válida';
    } catch {
      return 'não especificado';
    }
  }, []);
  
  // Determinar o status da vermifugação
  const getStatusVermifugacao = useCallback((procedimento: ProcedimentoVet): string => {
    if (!procedimento.dataProximoProcedimento) return 'completo';
    
    try {
      const dataProximo = new Date(procedimento.dataProximoProcedimento);
      const hoje = new Date();
      
      // Se a data de próxima vermifugação já passou
      if (dataProximo < hoje) return 'atrasado';
      
      // Se a data de próxima vermifugação está a menos de 15 dias
      const diasDiferenca = Math.ceil((dataProximo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
      if (diasDiferenca <= 15) return 'próximo';
      
      return 'agendado';
    } catch {
      return 'completo';
    }
  }, []);
  
  // Obter tipos únicos de vacinas
  const tiposVacinaUnicos = useMemo((): string[] => {
    if (!vacinacoesFiltradas.length) return [];
    return Array.from(new Set(vacinacoesFiltradas.map((v: ProcedimentoVet) => getTipoVacina(v))));
  }, [vacinacoesFiltradas, getTipoVacina]);
  
  // Obter tipos únicos de procedimentos
  const tiposProcedimentosUnicos = useMemo((): string[] => {
    if (!registrosClinicosFiltrados.length) return [];
    return Array.from(new Set(registrosClinicosFiltrados.map((proc: ProcedimentoVet) => proc.tipo)));
  }, [registrosClinicosFiltrados]);
  
  // Função para formatar data
  const formatarData = useCallback((dataString: string): string => {
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy', { locale: ptBR });
    } catch (e) {
      return dataString;
    }
  }, []);
  
  // Função para truncar texto
  const truncarTexto = useCallback((texto: string, tamanhoMaximo: number): string => {
    if (!texto) return '';
    return texto.length > tamanhoMaximo ? `${texto.substring(0, tamanhoMaximo)}...` : texto;
  }, []);
  
  return {
    // Estados de filtro
    searchTerm,
    setSearchTerm,
    filterCavalo,
    setFilterCavalo,
    filterTipo,
    setFilterTipo,
    filterStatus,
    setFilterStatus,
    
    // Queries
    procedimentosQuery,
    cavalosQuery,
    
    // Dados filtrados
    vacinacoesFiltradas,
    vermifugacoesFiltradas,
    registrosClinicosFiltrados,
    
    // Mutations
    addProcedimentoMutation,
    updateProcedimentoMutation,
    deleteProcedimentoMutation,
    
    // Funções utilitárias
    getCavaloName,
    getTipoVacina,
    getStatusVacinacao,
    getStatusVermifugacao,
    formatarData,
    truncarTexto,
    
    // Dados derivados
    tiposVacinaUnicos,
    tiposProcedimentosUnicos,
  };
}