{"timestamp": "2025-05-26T19:57:02.949Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404340736, "heapTotal": 114597888, "heapUsed": 74689832, "external": 8219482, "arrayBuffers": 243725}, "uptime": 7.758830699, "cpuUsage": {"user": 3024043, "system": 439470}, "resourceUsage": {"userCPUTime": 3024043, "systemCPUTime": 439543, "maxRSS": 394864, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108645, "majorPageFault": 6, "swappedOut": 0, "fsRead": 55696, "fsWrite": 992, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9205, "involuntaryContextSwitches": 2073}}