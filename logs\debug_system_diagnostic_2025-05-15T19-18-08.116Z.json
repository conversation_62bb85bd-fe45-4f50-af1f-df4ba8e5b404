{"timestamp": "2025-05-15T19:18:08.115Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 396140544, "heapTotal": 108630016, "heapUsed": 72157544, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.986600536, "cpuUsage": {"user": 2846606, "system": 347450}, "resourceUsage": {"userCPUTime": 2846649, "systemCPUTime": 347455, "maxRSS": 386856, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102740, "majorPageFault": 0, "swappedOut": 0, "fsRead": 16840, "fsWrite": 144, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7783, "involuntaryContextSwitches": 3780}}