import React, { createContext, useState, useContext, useEffect, ReactNode } from "react";
import { useToast } from "@/hooks/use-toast";

// Função de log para informações importantes do contexto de autenticação
const log = (...args: any[]) => {
  console.log('[AuthContext]', ...args);
};

// Função para verificar a validade do token de autenticação
const isTokenValid = (): boolean => {
  try {
    const token = localStorage.getItem('auth_token');
    const expiration = localStorage.getItem('token_expiration');
    
    if (!token || !expiration) return false;
    
    const expirationTime = parseInt(expiration);
    const currentTime = new Date().getTime();
    
    return currentTime < expirationTime;
  } catch (e) {
    console.error('Erro ao verificar validade do token', e);
    return false;
  }
};

// Interface para o usuário
interface User {
  id: number;
  username: string;
  email: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  userId: number | null; // Adicionando userId diretamente como propriedade
  signup: (username: string, email: string, password: string) => Promise<void>;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

// Criar o contexto com um valor padrão para evitar o erro de undefined
const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: false,
  userId: null,
  signup: async () => { throw new Error('AuthContext não inicializado') },
  login: async () => { throw new Error('AuthContext não inicializado') },
  logout: async () => { throw new Error('AuthContext não inicializado') },
});

// Hook para usar o contexto de autenticação
const useAuth = () => {
  log('useAuth called');
  const context = useContext(AuthContext);
  log('useAuth returning context with user:', context.user?.username || 'No user');
  return context;
};

// Exportamos o hook separadamente para evitar problemas com o Fast Refresh
export { useAuth };

interface AuthProviderProps {
  children: ReactNode;
}

// Função para verificar se há um usuário no localStorage
function getUserFromStorage(): User | null {
  const userJson = localStorage.getItem('user');
  if (userJson) {
    try {
      return JSON.parse(userJson);
    } catch (e) {
      console.error('Erro ao analisar o usuário do localStorage', e);
      return null;
    }
  }
  return null;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(getUserFromStorage());
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  
  log('AuthProvider rendered');
  
  // Computar o userId a partir do usuário atual
  const userId = user ? user.id : null;

  // Salvar/remover o usuário do localStorage quando o estado mudar
  useEffect(() => {
    if (user) {
      localStorage.setItem('user', JSON.stringify(user));
    } else {
      localStorage.removeItem('user');
    }
  }, [user]);

  async function signup(username: string, email: string, password: string) {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao criar conta');
      }

      const data = await response.json();
      
      // Salvar usuário e token no localStorage
      setUser(data.user);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // Salvar o token de autenticação recebido do servidor e configurar expiração
      if (data.token) {
        localStorage.setItem('auth_token', data.token);
        
        // Configurar um tempo de expiração para o token (24 horas)
        const expirationTime = new Date().getTime() + 24 * 60 * 60 * 1000;
        localStorage.setItem('token_expiration', expirationTime.toString());
      }
      
      toast({
        title: "Conta criada",
        description: "Você se registrou com sucesso!",
      });
    } catch (error: any) {
      console.error('Signup failed:', error);
      toast({
        title: "Erro ao criar conta",
        description: error.message || 'Ocorreu um erro ao criar a conta',
        variant: "destructive"
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }

  async function login(username: string, password: string) {
    setLoading(true);
    try {
      // Realizar uma requisição para a API de login
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao fazer login');
      }

      const data = await response.json();
      
      // Salvar o usuário e o token no localStorage
      setUser(data.user);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // Salvar o token de autenticação recebido do servidor e configurar expiração
      if (data.token) {
        localStorage.setItem('auth_token', data.token);
        
        // Configurar um tempo de expiração para o token (24 horas)
        const expirationTime = new Date().getTime() + 24 * 60 * 60 * 1000;
        localStorage.setItem('token_expiration', expirationTime.toString());
      }
      
      toast({
        title: "Login realizado",
        description: "Você entrou com sucesso!",
      });
    } catch (error: any) {
      console.error('Login failed:', error);
      toast({
        title: "Falha no login",
        description: error.message || 'Ocorreu um erro ao fazer login',
        variant: "destructive"
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }

  async function logout() {
    try {
      // Limpar o estado do usuário e armazenamento local
      setUser(null);
      localStorage.removeItem('user');
      localStorage.removeItem('auth_token');
      localStorage.removeItem('token_expiration');
      
      // Fazer uma requisição para o endpoint de logout (opcional)
      try {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (e) {
        // Ignorar erros na requisição de logout - o importante é limpar o estado local
        console.warn('Erro ao fazer logout no servidor:', e);
      }
      
      toast({
        title: "Logout realizado",
        description: "Você saiu do sistema.",
      });
    } catch (error: any) {
      console.error('Erro no logout:', error);
      toast({
        title: "Falha no logout",
        description: error.message || 'Ocorreu um erro ao fazer logout',
        variant: "destructive"
      });
    }
  }

  const value = {
    user,
    loading,
    userId,
    signup,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}