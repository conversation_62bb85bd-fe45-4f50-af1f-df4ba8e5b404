{"timestamp": "2025-05-16T23:26:26.321Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 408764416, "heapTotal": 114352128, "heapUsed": 89581608, "external": 8282731, "arrayBuffers": 257466}, "uptime": 3.592303362, "cpuUsage": {"user": 3244158, "system": 415755}, "resourceUsage": {"userCPUTime": 3244224, "systemCPUTime": 415755, "maxRSS": 399184, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104736, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8046, "involuntaryContextSwitches": 13477}}