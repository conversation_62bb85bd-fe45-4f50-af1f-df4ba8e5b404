{"timestamp": "2025-05-19T17:57:06.659Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 299188224, "heapTotal": 120778752, "heapUsed": 93311320, "external": 8516313, "arrayBuffers": 261044}, "uptime": 1.794839475, "cpuUsage": {"user": 2713001, "system": 344835}, "resourceUsage": {"userCPUTime": 2713047, "systemCPUTime": 344841, "maxRSS": 292176, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105411, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9211, "involuntaryContextSwitches": 2478}}