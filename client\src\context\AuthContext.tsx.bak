import React, { createContext, useContext, useEffect, ReactNode } from "react";
import { useToast } from "@/hooks/use-toast";

// For debugging
const DEBUG = true;
const log = (...args: any[]) => {
  if (DEBUG) {
    console.log('[AuthContext]', ...args);
  }
};

// Interface para o usuário
interface User {
  id: number;
  username: string;
  email: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signup: (username: string, email: string, password: string) => Promise<void>;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  log('useAuth called');
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    log('ERROR: useAuth called outside of AuthProvider');
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  log('useAuth returning context with user:', context.user?.email || 'No user');
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

// Função para verificar se há um usuário no localStorage
function getUserFromStorage(): User | null {
  const userJson = localStorage.getItem('user');
  if (userJson) {
    try {
      return JSON.parse(userJson);
    } catch (e) {
      console.error('Erro ao analisar o usuário do localStorage', e);
      return null;
    }
  }
  return null;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = React.useState<User | null>(getUserFromStorage());
  const [loading, setLoading] = React.useState(false);
  const { toast } = useToast();
  
  log('AuthProvider rendered');

  // Salvar/remover o usuário do localStorage quando o estado mudar
  useEffect(() => {
    if (user) {
      localStorage.setItem('user', JSON.stringify(user));
    } else {
      localStorage.removeItem('user');
    }
  }, [user]);

  async function signup(username: string, email: string, password: string) {
    setLoading(true);
    try {
      // Para desenvolvimento, usamos a rota de teste
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao criar conta');
      }

      const data = await response.json();
      setUser(data.user);
      
      toast({
        title: "Conta criada",
        description: "Você se registrou com sucesso!",
      });
    } catch (error: any) {
      console.error('Signup failed:', error);
      toast({
        title: "Erro ao criar conta",
        description: error.message || 'Ocorreu um erro ao criar a conta',
        variant: "destructive"
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }

  async function login(username: string, password: string) {
    setLoading(true);
    try {
      // Usar nossa API real de login
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao fazer login');
      }

      const data = await response.json();
      setUser(data.user);
      
      toast({
        title: "Login realizado",
        description: "Você entrou com sucesso!",
      });
    } catch (error: any) {
      console.error('Login failed:', error);
      toast({
        title: "Falha no login",
        description: error.message || 'Ocorreu um erro ao fazer login',
        variant: "destructive"
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }

  async function logout() {
    try {
      setUser(null);
      toast({
        title: "Logout realizado",
        description: "Você saiu do sistema.",
      });
    } catch (error: any) {
      toast({
        title: "Falha no logout",
        description: error.message || 'Ocorreu um erro ao fazer logout',
        variant: "destructive"
      });
    }
  }

  const value = {
    user,
    loading,
    signup,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
