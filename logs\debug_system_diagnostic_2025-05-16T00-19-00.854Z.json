{"timestamp": "2025-05-16T00:19:00.853Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 264925184, "heapTotal": 113565696, "heapUsed": 89038392, "external": 8337898, "arrayBuffers": 257466}, "uptime": 2.929765622, "cpuUsage": {"user": 3024253, "system": 433126}, "resourceUsage": {"userCPUTime": 3024373, "systemCPUTime": 433126, "maxRSS": 322852, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104084, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 80, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7637, "involuntaryContextSwitches": 9233}}