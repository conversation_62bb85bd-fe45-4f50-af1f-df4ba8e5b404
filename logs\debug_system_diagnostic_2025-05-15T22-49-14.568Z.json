{"timestamp": "2025-05-15T22:49:14.563Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397078528, "heapTotal": 110403584, "heapUsed": 72537272, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.676992992, "cpuUsage": {"user": 2704941, "system": 326644}, "resourceUsage": {"userCPUTime": 2705003, "systemCPUTime": 326652, "maxRSS": 387772, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103595, "majorPageFault": 0, "swappedOut": 0, "fsRead": 32, "fsWrite": 128, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7562, "involuntaryContextSwitches": 1266}}