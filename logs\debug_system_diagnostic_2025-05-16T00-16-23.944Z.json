{"timestamp": "2025-05-16T00:16:23.943Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 406388736, "heapTotal": 113827840, "heapUsed": 94715104, "external": 8299906, "arrayBuffers": 265658}, "uptime": 2.528671766, "cpuUsage": {"user": 3017588, "system": 422158}, "resourceUsage": {"userCPUTime": 3017663, "systemCPUTime": 422158, "maxRSS": 396864, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105412, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7836, "involuntaryContextSwitches": 6836}}