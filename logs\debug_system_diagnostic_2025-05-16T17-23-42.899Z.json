{"timestamp": "2025-05-16T17:23:42.899Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 311177216, "heapTotal": 132616192, "heapUsed": 103399864, "external": 8268990, "arrayBuffers": 243725}, "uptime": 1.630243365, "cpuUsage": {"user": 2521885, "system": 367252}, "resourceUsage": {"userCPUTime": 2521950, "systemCPUTime": 367252, "maxRSS": 303884, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104088, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8454, "involuntaryContextSwitches": 1679}}