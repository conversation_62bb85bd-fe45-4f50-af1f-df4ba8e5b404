{"timestamp": "2025-05-15T18:46:08.629Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 395767808, "heapTotal": 109895680, "heapUsed": 90846160, "external": 8485376, "arrayBuffers": 257466}, "uptime": 2.489031321, "cpuUsage": {"user": 2944152, "system": 443929}, "resourceUsage": {"userCPUTime": 2944216, "systemCPUTime": 443938, "maxRSS": 386492, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105509, "majorPageFault": 0, "swappedOut": 0, "fsRead": 20120, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7148, "involuntaryContextSwitches": 6070}}