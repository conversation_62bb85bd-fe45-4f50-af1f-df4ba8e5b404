import React from 'react';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Dna } from 'lucide-react';

interface SimpleGenealogyNodeProps {
  name: string;
  id?: number | null;
  gender?: 'M' | 'F';
  breed?: string;
  color?: string;
  isHighlighted?: boolean;
  onClick?: (id: number | null) => void;
}

const SimpleGenealogyNode: React.FC<SimpleGenealogyNodeProps> = ({
  name,
  id,
  gender = 'M',
  breed,
  color,
  isHighlighted = false,
  onClick
}) => {
  const handleClick = () => {
    if (onClick && id !== undefined) {
      onClick(id);
    }
  };

  const getInitials = (name: string) => {
    if (!name) return "??";
    return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
  };

  // Determinar a cor de fundo do avatar baseado no sexo
  const getAvatarColor = () => {
    if (!gender) return 'bg-gray-400';
    return gender === 'M' ? 'bg-blue-600' : 'bg-pink-600';
  };

  return (
    <Card 
      className={`p-3 cursor-pointer transition-all duration-200 min-w-[120px] ${
        isHighlighted ? 'ring-2 ring-primary shadow-lg' : 'hover:shadow-md'
      }`}
      onClick={handleClick}
    >
      <div className="flex items-center gap-3">
        <Avatar className={`h-8 w-8 ${getAvatarColor()}`}>
          <AvatarFallback>{getInitials(name)}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium leading-none truncate" title={name}>
            {name}
          </p>
          {breed && (
            <p className="text-xs text-muted-foreground truncate">
              {breed}
            </p>
          )}
        </div>
      </div>
      {color && (
        <div className="mt-2">
          <Badge variant="outline" className="text-xs">
            {color}
          </Badge>
        </div>
      )}
    </Card>
  );
};

interface SimpleGenealogyTreeProps {
  horseId: number;
  horseName: string;
  gender?: 'M' | 'F';
  breed?: string;
  color?: string;
  fatherName?: string | null;
  fatherId?: number | null;
  motherName?: string | null;
  motherId?: number | null;
  onNodeClick?: (id: number | null) => void;
}

const SimpleGenealogyTree: React.FC<SimpleGenealogyTreeProps> = ({
  horseId,
  horseName,
  gender = 'M',
  breed,
  color,
  fatherName,
  fatherId,
  motherName,
  motherId,
  onNodeClick
}) => {
  return (
    <div className="relative w-full">
      <div className="flex flex-col items-center">
        {/* Cavalo Principal */}
        <div className="mb-8 w-[200px]">
          <SimpleGenealogyNode
            name={horseName}
            id={horseId}
            gender={gender}
            breed={breed}
            color={color}
            isHighlighted={true}
            onClick={onNodeClick}
          />
        </div>
        
        {/* Linha conectora vertical */}
        <div className="h-8 w-[1px] bg-border mb-4"></div>
        
        {/* Pais */}
        <div className="grid grid-cols-2 gap-8 w-full max-w-[500px]">
          {/* Pai */}
          <div className="flex flex-col items-center">
            {fatherName ? (
              <>
                <SimpleGenealogyNode
                  name={fatherName}
                  id={fatherId || null}
                  gender="M"
                  onClick={onNodeClick}
                />
                <div className="text-xs mt-1 text-muted-foreground">Pai</div>
              </>
            ) : (
              <div className="p-3 border border-dashed rounded-md flex flex-col items-center justify-center h-[80px] text-muted-foreground">
                <Dna className="h-5 w-5 mb-1" />
                <span className="text-xs">Pai não registrado</span>
              </div>
            )}
          </div>
          
          {/* Mãe */}
          <div className="flex flex-col items-center">
            {motherName ? (
              <>
                <SimpleGenealogyNode
                  name={motherName}
                  id={motherId || null}
                  gender="F"
                  onClick={onNodeClick}
                />
                <div className="text-xs mt-1 text-muted-foreground">Mãe</div>
              </>
            ) : (
              <div className="p-3 border border-dashed rounded-md flex flex-col items-center justify-center h-[80px] text-muted-foreground">
                <Dna className="h-5 w-5 mb-1" />
                <span className="text-xs">Mãe não registrada</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleGenealogyTree;