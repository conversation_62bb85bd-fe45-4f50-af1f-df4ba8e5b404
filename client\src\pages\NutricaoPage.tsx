import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { <PERSON><PERSON><PERSON> } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { 
  Plus, 
  Search, 
  Filter, 
  SlidersHorizontal, 
  ChevronDown, 
  Edit, 
  Trash2,
  GraduationCap,
  Utensils,
  CalendarDays,
  DollarSign,
  Clock,
  Scale,
  Clipboard
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

// Interface para o registro de nutrição
interface Nutricao {
  id: number;
  horseId: number;
  data: string;
  tipoAlimentacao: string;
  nomeAlimento: string;
  quantidade: number;
  unidadeMedida: string;
  frequenciaDiaria: number;
  horarios: string;
  custoUnitario: number;
  custoMensal: number;
  observacoes: string;
  recomendacao: string;
  status: string;
  fornecedor: string;
  createdAt: string;
}

// Schema de validação para o formulário de nutrição
const nutricaoFormSchema = z.object({
  horseId: z.number({
    required_error: "Selecione um animal",
  }),
  data: z.string().min(1, { message: "Selecione uma data" }),
  tipoAlimentacao: z.string().min(1, { message: "Selecione o tipo de alimentação" }),
  nomeAlimento: z.string().min(1, { message: "Informe o nome do alimento" }),
  quantidade: z.coerce.number().min(0.01, { message: "Quantidade deve ser maior que zero" }),
  unidadeMedida: z.string().min(1, { message: "Selecione a unidade de medida" }),
  frequenciaDiaria: z.coerce.number().min(1, { message: "Frequência deve ser no mínimo 1" }),
  horarios: z.string().optional(),
  custoUnitario: z.coerce.number().min(0, { message: "Custo deve ser um valor positivo" }),
  observacoes: z.string().optional(),
  recomendacao: z.string().optional(),
  status: z.string().default("ativo"),
  fornecedor: z.string().optional(),
});

type NutricaoFormValues = z.infer<typeof nutricaoFormSchema>;

const NutricaoPage = () => {
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedNutricao, setSelectedNutricao] = useState<Nutricao | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string | null>(null);
  const { toast } = useToast();

  // Formulário para adicionar/editar nutrição
  const form = useForm<NutricaoFormValues>({
    resolver: zodResolver(nutricaoFormSchema),
    defaultValues: {
      horseId: 0,
      data: format(new Date(), 'yyyy-MM-dd'),
      tipoAlimentacao: "",
      nomeAlimento: "",
      quantidade: 0,
      unidadeMedida: "kg",
      frequenciaDiaria: 2,
      horarios: "",
      custoUnitario: 0,
      observacoes: "",
      recomendacao: "",
      status: "ativo",
      fornecedor: "",
    },
  });

  // Consultar lista de cavalos
  const { data: cavalos = [], isLoading: isLoadingCavalos } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      return await apiRequest<Cavalo[]>('GET', '/api/cavalos');
    }
  });

  // Consultar registros de nutrição com base no cavalo selecionado
  const { data: nutricoesData = [], isLoading: isLoadingNutricao } = useQuery({
    queryKey: ['/api/nutricao', selectedHorseId],
    enabled: !!selectedHorseId,
    queryFn: async () => {
      if (selectedHorseId) {
        return await apiRequest<Nutricao[]>('GET', `/api/nutricao/horse/${selectedHorseId}`);
      }
      return [];
    }
  });

  // Filtrar registros de nutrição
  const nutricoes = nutricoesData.filter(nutricao => {
    let matchesSearch = true;
    let matchesFilter = true;

    if (searchTerm) {
      matchesSearch = 
        nutricao.nomeAlimento.toLowerCase().includes(searchTerm.toLowerCase()) ||
        nutricao.tipoAlimentacao.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (nutricao.fornecedor && nutricao.fornecedor.toLowerCase().includes(searchTerm.toLowerCase()));
    }

    if (filterType) {
      matchesFilter = nutricao.tipoAlimentacao === filterType;
    }

    return matchesSearch && matchesFilter;
  });

  // Mutação para adicionar registro de nutrição
  const addNutricaoMutation = useMutation({
    mutationFn: async (data: NutricaoFormValues) => {
      return await apiRequest<Nutricao>('POST', '/api/nutricao', {
        ...data,
        horseId: Number(data.horseId),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/nutricao', selectedHorseId] });
      setIsAddDialogOpen(false);
      form.reset();
      toast({
        title: "Sucesso",
        description: "Registro de nutrição adicionado com sucesso",
      });
    },
    onError: (error) => {
      console.error("Erro ao adicionar nutrição:", error);
      toast({
        title: "Erro",
        description: "Não foi possível adicionar o registro de nutrição",
        variant: "destructive",
      });
    }
  });

  // Mutação para atualizar registro de nutrição
  const updateNutricaoMutation = useMutation({
    mutationFn: async (data: NutricaoFormValues & { id: number }) => {
      const { id, ...updateData } = data;
      return await apiRequest<Nutricao>('PATCH', `/api/nutricao/${id}`, updateData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/nutricao', selectedHorseId] });
      setIsEditDialogOpen(false);
      setSelectedNutricao(null);
      toast({
        title: "Sucesso",
        description: "Registro de nutrição atualizado com sucesso",
      });
    },
    onError: (error) => {
      console.error("Erro ao atualizar nutrição:", error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o registro de nutrição",
        variant: "destructive",
      });
    }
  });

  // Mutação para excluir registro de nutrição
  const deleteNutricaoMutation = useMutation({
    mutationFn: async (id: number) => {
      return await apiRequest<void>('DELETE', `/api/nutricao/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/nutricao', selectedHorseId] });
      toast({
        title: "Sucesso",
        description: "Registro de nutrição excluído com sucesso",
      });
    },
    onError: (error) => {
      console.error("Erro ao excluir nutrição:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir o registro de nutrição",
        variant: "destructive",
      });
    }
  });

  // Selecionar o primeiro cavalo quando a lista for carregada
  useEffect(() => {
    if (cavalos.length > 0 && !selectedHorseId) {
      setSelectedHorseId(cavalos[0].id);
    }
  }, [cavalos, selectedHorseId]);

  // Atualizar formulário quando selecionar um registro para edição
  useEffect(() => {
    if (selectedNutricao) {
      form.reset({
        horseId: selectedNutricao.horseId,
        data: selectedNutricao.data,
        tipoAlimentacao: selectedNutricao.tipoAlimentacao,
        nomeAlimento: selectedNutricao.nomeAlimento,
        quantidade: selectedNutricao.quantidade,
        unidadeMedida: selectedNutricao.unidadeMedida,
        frequenciaDiaria: selectedNutricao.frequenciaDiaria,
        horarios: selectedNutricao.horarios || "",
        custoUnitario: selectedNutricao.custoUnitario,
        observacoes: selectedNutricao.observacoes || "",
        recomendacao: selectedNutricao.recomendacao || "",
        status: selectedNutricao.status,
        fornecedor: selectedNutricao.fornecedor || "",
      });
    }
  }, [selectedNutricao, form]);

  // Handler para o envio do formulário
  const onSubmit = (values: NutricaoFormValues) => {
    if (selectedNutricao) {
      updateNutricaoMutation.mutate({ ...values, id: selectedNutricao.id });
    } else {
      addNutricaoMutation.mutate(values);
    }
  };

  // Handler para selecionar um registro para edição
  const handleEdit = (nutricao: Nutricao) => {
    setSelectedNutricao(nutricao);
    setIsEditDialogOpen(true);
  };

  // Handler para excluir um registro
  const handleDelete = (id: number) => {
    // Confirmação antes de excluir
    if (window.confirm("Tem certeza que deseja excluir este registro de nutrição?")) {
      deleteNutricaoMutation.mutate(id);
    }
  };

  // Calcular custo mensal com base na quantidade, frequência e custo unitário
  const calculateMonthlyCost = (quantidade: number, frequencia: number, custoUnitario: number) => {
    return (quantidade * frequencia * 30 * custoUnitario).toFixed(2);
  };

  // Obter tipos únicos de alimentação para filtro
  const tiposAlimentacao = Array.from(
    new Set(nutricoesData.map(nutricao => nutricao.tipoAlimentacao))
  );

  // Renderizar carregamento
  if (isLoadingCavalos) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Encontrar o cavalo selecionado
  const selectedHorse = cavalos.find(c => c.id === selectedHorseId);

  return (
    <div className="container py-6">
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Seletor de cavalos */}
        <div className="w-full lg:w-1/4">
          <Card>
            <CardHeader>
              <CardTitle>Animais</CardTitle>
              <CardDescription>Selecione um animal para gerenciar a nutrição</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {cavalos.map((cavalo) => (
                  <Button
                    key={cavalo.id}
                    variant={cavalo.id === selectedHorseId ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setSelectedHorseId(cavalo.id)}
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        {cavalo.name.charAt(0)}
                      </div>
                      <div className="text-left">
                        <div className="font-medium">{cavalo.name}</div>
                        <div className="text-xs text-gray-500">{cavalo.breed}</div>
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Painel principal */}
        <div className="w-full lg:w-3/4">
          {selectedHorse ? (
            <Tabs defaultValue="historico" className="space-y-4">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <TabsList>
                  <TabsTrigger value="historico" className="flex items-center gap-2">
                    <Clipboard className="h-4 w-4" />
                    <span>Histórico de Alimentação</span>
                  </TabsTrigger>
                  <TabsTrigger value="resumo" className="flex items-center gap-2">
                    <GraduationCap className="h-4 w-4" />
                    <span>Resumo Nutricional</span>
                  </TabsTrigger>
                </TabsList>

                <div className="flex items-center space-x-2">
                  <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                    <DialogTrigger asChild>
                      <Button className="flex items-center gap-2">
                        <Plus className="h-4 w-4" />
                        <span>Adicionar Nutrição</span>
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[600px]">
                      <DialogHeader>
                        <DialogTitle>Adicionar Registro de Nutrição</DialogTitle>
                        <DialogDescription>
                          Cadastre um novo plano alimentar para {selectedHorse.name}
                        </DialogDescription>
                      </DialogHeader>
                      <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                          <input 
                            type="hidden" 
                            {...form.register("horseId", { valueAsNumber: true })}
                            value={selectedHorseId || undefined}
                          />
                          
                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name="data"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Data</FormLabel>
                                  <FormControl>
                                    <Input type="date" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            
                            <FormField
                              control={form.control}
                              name="tipoAlimentacao"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Tipo de Alimentação</FormLabel>
                                  <Select 
                                    onValueChange={field.onChange} 
                                    defaultValue={field.value}
                                  >
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Selecione" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="Volumoso">Volumoso</SelectItem>
                                      <SelectItem value="Concentrado">Concentrado</SelectItem>
                                      <SelectItem value="Suplemento">Suplemento</SelectItem>
                                      <SelectItem value="Mineral">Mineral</SelectItem>
                                      <SelectItem value="Forragem">Forragem</SelectItem>
                                      <SelectItem value="Ração">Ração</SelectItem>
                                      <SelectItem value="Outro">Outro</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          
                          <FormField
                            control={form.control}
                            name="nomeAlimento"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Nome do Alimento</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <div className="grid grid-cols-3 gap-4">
                            <FormField
                              control={form.control}
                              name="quantidade"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Quantidade</FormLabel>
                                  <FormControl>
                                    <Input type="number" step="0.01" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            
                            <FormField
                              control={form.control}
                              name="unidadeMedida"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Unidade</FormLabel>
                                  <Select 
                                    onValueChange={field.onChange} 
                                    defaultValue={field.value}
                                  >
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Selecione" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="kg">kg</SelectItem>
                                      <SelectItem value="g">g</SelectItem>
                                      <SelectItem value="L">L</SelectItem>
                                      <SelectItem value="ml">ml</SelectItem>
                                      <SelectItem value="unidade">unidade</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            
                            <FormField
                              control={form.control}
                              name="frequenciaDiaria"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Frequência Diária</FormLabel>
                                  <FormControl>
                                    <Input type="number" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          
                          <FormField
                            control={form.control}
                            name="horarios"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Horários (opcional)</FormLabel>
                                <FormControl>
                                  <Input {...field} placeholder="Ex: 7h, 12h, 18h" />
                                </FormControl>
                                <FormDescription>
                                  Indique os horários de fornecimento separados por vírgula
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="custoUnitario"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Custo por {form.watch("unidadeMedida")} (R$)</FormLabel>
                                <FormControl>
                                  <Input type="number" step="0.01" {...field} />
                                </FormControl>
                                <FormDescription>
                                  O custo mensal será calculado automaticamente
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="fornecedor"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Fornecedor (opcional)</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name="observacoes"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Observações (opcional)</FormLabel>
                                  <FormControl>
                                    <Textarea {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            
                            <FormField
                              control={form.control}
                              name="recomendacao"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Recomendações (opcional)</FormLabel>
                                  <FormControl>
                                    <Textarea {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          
                          <DialogFooter>
                            <Button 
                              type="button" 
                              variant="outline" 
                              onClick={() => setIsAddDialogOpen(false)}
                            >
                              Cancelar
                            </Button>
                            <Button 
                              type="submit" 
                              disabled={addNutricaoMutation.isPending}
                            >
                              {addNutricaoMutation.isPending ? 'Salvando...' : 'Salvar'}
                            </Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>

              {/* Aba de histórico */}
              <TabsContent value="historico" className="space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-xl">Plano Nutricional de {selectedHorse.name}</CardTitle>
                    <CardDescription>
                      Gerencie a alimentação e suplementação do animal
                    </CardDescription>
                    
                    <div className="flex flex-col sm:flex-row gap-2 mt-4">
                      <div className="relative flex-1">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Buscar por nome, tipo ou fornecedor..."
                          className="pl-8"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                      </div>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="flex gap-1 items-center w-full sm:w-auto">
                            <Filter className="h-4 w-4" />
                            <span>Filtrar</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem onClick={() => setFilterType(null)}>
                            Todos os tipos
                          </DropdownMenuItem>
                          {tiposAlimentacao.map((tipo) => (
                            <DropdownMenuItem key={tipo} onClick={() => setFilterType(tipo)}>
                              {tipo}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {isLoadingNutricao ? (
                      <div className="flex justify-center items-center h-48">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                      </div>
                    ) : nutricoes.length === 0 ? (
                      <div className="text-center py-8">
                        <Utensils className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900">Nenhum registro de nutrição encontrado</h3>
                        <p className="text-gray-500 mt-2">
                          {searchTerm || filterType 
                            ? "Tente ajustar os filtros de busca" 
                            : "Adicione um plano nutricional para este animal"}
                        </p>
                        <Button 
                          className="mt-4" 
                          onClick={() => {
                            setSearchTerm("");
                            setFilterType(null);
                            setIsAddDialogOpen(true);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Adicionar Nutrição
                        </Button>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableCaption>
                            {filterType 
                              ? `Mostrando ${nutricoes.length} registros de ${filterType}` 
                              : `Total de ${nutricoes.length} registros de nutrição`}
                          </TableCaption>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Alimento</TableHead>
                              <TableHead>Tipo</TableHead>
                              <TableHead>Quantidade</TableHead>
                              <TableHead>Frequência</TableHead>
                              <TableHead>Custo Mensal</TableHead>
                              <TableHead className="text-right">Ações</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {nutricoes.map((nutricao) => (
                              <TableRow key={nutricao.id}>
                                <TableCell className="font-medium">
                                  {nutricao.nomeAlimento}
                                </TableCell>
                                <TableCell>
                                  <Badge variant="outline">
                                    {nutricao.tipoAlimentacao}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  {nutricao.quantidade} {nutricao.unidadeMedida}
                                </TableCell>
                                <TableCell>
                                  {nutricao.frequenciaDiaria}x {nutricao.horarios && `(${nutricao.horarios})`}
                                </TableCell>
                                <TableCell>
                                  R$ {calculateMonthlyCost(
                                    nutricao.quantidade, 
                                    nutricao.frequenciaDiaria, 
                                    nutricao.custoUnitario
                                  )}
                                </TableCell>
                                <TableCell className="text-right">
                                  <div className="flex justify-end gap-2">
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => handleEdit(nutricao)}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => handleDelete(nutricao.id)}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Aba de resumo nutricional */}
              <TabsContent value="resumo" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Resumo Nutricional de {selectedHorse.name}</CardTitle>
                    <CardDescription>
                      Visão geral da dieta e recomendações nutricionais
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isLoadingNutricao ? (
                      <div className="flex justify-center items-center h-48">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                      </div>
                    ) : nutricoes.length === 0 ? (
                      <div className="text-center py-8">
                        <GraduationCap className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900">Sem dados nutricionais</h3>
                        <p className="text-gray-500 mt-2">
                          Adicione registros de nutrição para visualizar o resumo
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        {/* Resumo de custos */}
                        <div>
                          <h3 className="text-lg font-medium mb-4">Custo Nutricional Mensal</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <Card className="bg-gradient-to-br from-blue-50 to-green-50">
                              <CardContent className="pt-6">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <p className="text-sm text-gray-500">Custo Total Mensal</p>
                                    <h4 className="text-2xl font-bold mt-1">
                                      R$ {nutricoes.reduce((total, n) => {
                                        const monthlyCost = n.quantidade * n.frequenciaDiaria * 30 * n.custoUnitario;
                                        return total + monthlyCost;
                                      }, 0).toFixed(2)}
                                    </h4>
                                  </div>
                                  <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                                    <DollarSign className="h-6 w-6 text-green-600" />
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                            
                            <Card>
                              <CardContent className="pt-6">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <p className="text-sm text-gray-500">Consumo Diário</p>
                                    <h4 className="text-2xl font-bold mt-1">
                                      {nutricoes.reduce((total, n) => {
                                        if (n.unidadeMedida === 'kg') {
                                          return total + (n.quantidade * n.frequenciaDiaria);
                                        }
                                        if (n.unidadeMedida === 'g') {
                                          return total + ((n.quantidade / 1000) * n.frequenciaDiaria);
                                        }
                                        return total;
                                      }, 0).toFixed(2)} kg
                                    </h4>
                                  </div>
                                  <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                                    <Scale className="h-6 w-6 text-blue-600" />
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                            
                            <Card>
                              <CardContent className="pt-6">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <p className="text-sm text-gray-500">Frequência Total</p>
                                    <h4 className="text-2xl font-bold mt-1">
                                      {nutricoes.reduce((total, n) => total + n.frequenciaDiaria, 0)} vezes/dia
                                    </h4>
                                  </div>
                                  <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
                                    <Clock className="h-6 w-6 text-purple-600" />
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        </div>
                        
                        {/* Distribuição por tipo de alimento */}
                        <div>
                          <h3 className="text-lg font-medium mb-4">Distribuição por Tipo de Alimento</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {Array.from(new Set(nutricoes.map(n => n.tipoAlimentacao))).map(tipo => {
                              const nutricoesDoTipo = nutricoes.filter(n => n.tipoAlimentacao === tipo);
                              const custoTotal = nutricoesDoTipo.reduce((total, n) => {
                                return total + (n.quantidade * n.frequenciaDiaria * 30 * n.custoUnitario);
                              }, 0);
                              
                              return (
                                <Card key={tipo}>
                                  <CardHeader className="pb-2">
                                    <div className="flex justify-between items-center">
                                      <CardTitle className="text-base">{tipo}</CardTitle>
                                      <Badge variant="secondary">
                                        {nutricoesDoTipo.length} {nutricoesDoTipo.length === 1 ? 'item' : 'itens'}
                                      </Badge>
                                    </div>
                                  </CardHeader>
                                  <CardContent>
                                    <div className="space-y-2">
                                      <div className="flex justify-between">
                                        <span className="text-gray-500">Consumo Diário:</span>
                                        <span className="font-medium">
                                          {nutricoesDoTipo.reduce((total, n) => {
                                            if (n.unidadeMedida === 'kg') {
                                              return total + (n.quantidade * n.frequenciaDiaria);
                                            }
                                            if (n.unidadeMedida === 'g') {
                                              return total + ((n.quantidade / 1000) * n.frequenciaDiaria);
                                            }
                                            return total;
                                          }, 0).toFixed(2)} kg
                                        </span>
                                      </div>
                                      <div className="flex justify-between">
                                        <span className="text-gray-500">Custo Mensal:</span>
                                        <span className="font-medium">R$ {custoTotal.toFixed(2)}</span>
                                      </div>
                                    </div>
                                  </CardContent>
                                </Card>
                              );
                            })}
                          </div>
                        </div>
                        
                        {/* Recomendações */}
                        <div>
                          <h3 className="text-lg font-medium mb-4">Recomendações e Observações</h3>
                          <Card>
                            <CardContent className="pt-6">
                              <div className="space-y-4">
                                {nutricoes.filter(n => n.recomendacao || n.observacoes).length === 0 ? (
                                  <p className="text-gray-500 text-center py-4">
                                    Sem recomendações ou observações registradas
                                  </p>
                                ) : (
                                  nutricoes.filter(n => n.recomendacao || n.observacoes).map(nutricao => (
                                    <div key={nutricao.id} className="border-b pb-4 last:border-0">
                                      <h4 className="font-medium">{nutricao.nomeAlimento} ({nutricao.tipoAlimentacao})</h4>
                                      
                                      {nutricao.recomendacao && (
                                        <div className="mt-2">
                                          <h5 className="text-sm font-medium text-gray-500">Recomendação:</h5>
                                          <p className="text-sm mt-1">{nutricao.recomendacao}</p>
                                        </div>
                                      )}
                                      
                                      {nutricao.observacoes && (
                                        <div className="mt-2">
                                          <h5 className="text-sm font-medium text-gray-500">Observações:</h5>
                                          <p className="text-sm mt-1">{nutricao.observacoes}</p>
                                        </div>
                                      )}
                                    </div>
                                  ))
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center h-64">
                <Utensils className="h-12 w-12 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium text-gray-900">Selecione um animal</h3>
                <p className="text-gray-500 mt-2">
                  Escolha um animal no painel lateral para gerenciar a nutrição
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Dialog para editar nutrição */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Registro de Nutrição</DialogTitle>
            <DialogDescription>
              Atualize as informações do plano alimentar para {selectedHorse?.name}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <input 
                type="hidden" 
                {...form.register("horseId", { valueAsNumber: true })}
                value={selectedHorseId || undefined}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="data"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="tipoAlimentacao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Alimentação</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Volumoso">Volumoso</SelectItem>
                          <SelectItem value="Concentrado">Concentrado</SelectItem>
                          <SelectItem value="Suplemento">Suplemento</SelectItem>
                          <SelectItem value="Mineral">Mineral</SelectItem>
                          <SelectItem value="Forragem">Forragem</SelectItem>
                          <SelectItem value="Ração">Ração</SelectItem>
                          <SelectItem value="Outro">Outro</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="nomeAlimento"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome do Alimento</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="quantidade"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quantidade</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="unidadeMedida"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unidade</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="kg">kg</SelectItem>
                          <SelectItem value="g">g</SelectItem>
                          <SelectItem value="L">L</SelectItem>
                          <SelectItem value="ml">ml</SelectItem>
                          <SelectItem value="unidade">unidade</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="frequenciaDiaria"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Frequência Diária</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="horarios"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Horários (opcional)</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Ex: 7h, 12h, 18h" />
                    </FormControl>
                    <FormDescription>
                      Indique os horários de fornecimento separados por vírgula
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="custoUnitario"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Custo por {form.watch("unidadeMedida")} (R$)</FormLabel>
                    <FormControl>
                      <Input type="number" step="0.01" {...field} />
                    </FormControl>
                    <FormDescription>
                      O custo mensal será calculado automaticamente
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="fornecedor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fornecedor (opcional)</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="observacoes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Observações (opcional)</FormLabel>
                      <FormControl>
                        <Textarea {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="recomendacao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recomendações (opcional)</FormLabel>
                      <FormControl>
                        <Textarea {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => {
                    setIsEditDialogOpen(false);
                    setSelectedNutricao(null);
                  }}
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  disabled={updateNutricaoMutation.isPending}
                >
                  {updateNutricaoMutation.isPending ? 'Salvando...' : 'Salvar'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default NutricaoPage;