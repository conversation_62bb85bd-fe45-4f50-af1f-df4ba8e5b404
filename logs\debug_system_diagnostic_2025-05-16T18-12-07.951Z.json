{"timestamp": "2025-05-16T18:12:07.950Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 277467136, "heapTotal": 113811456, "heapUsed": 72900896, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.894886597, "cpuUsage": {"user": 2800004, "system": 359426}, "resourceUsage": {"userCPUTime": 2800050, "systemCPUTime": 359432, "maxRSS": 346780, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107473, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 48, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8671, "involuntaryContextSwitches": 3914}}