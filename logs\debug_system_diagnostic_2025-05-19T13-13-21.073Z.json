{"timestamp": "2025-05-19T13:13:21.073Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 398020608, "heapTotal": 117219328, "heapUsed": 74184384, "external": 8227674, "arrayBuffers": 251917}, "uptime": 2.370623559, "cpuUsage": {"user": 3123826, "system": 374838}, "resourceUsage": {"userCPUTime": 3123871, "systemCPUTime": 374844, "maxRSS": 388692, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105020, "majorPageFault": 0, "swappedOut": 0, "fsRead": 6792, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7656, "involuntaryContextSwitches": 4175}}