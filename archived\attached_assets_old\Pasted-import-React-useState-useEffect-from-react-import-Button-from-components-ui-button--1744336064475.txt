import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { AlertCircle, Save, Printer, Ruler, ThermometerSnowflake, Clipboard, ArrowLeft, ArrowRight, Camera } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

// SVG representando um cavalo com pontos de medição
const HorseMeasurementSVG = ({ medidas, highlighted = null }) => {
  return (
    <svg viewBox="0 0 800 600" className="w-full max-w-[700px]">
      {/* Base SVG do cavalo (simplificado) */}
      <g stroke="#333" strokeWidth="2" fill="none">
        {/* Corpo principal */}
        <path d="M200,300 C240,260 350,240 450,250 C550,260 650,300 680,340 C710,380 720,420 680,440 C640,460 620,420 600,400" />
        
        {/* Pescoço e cabeça */}
        <path d="M200,300 C180,250 170,200 180,150 C190,100 220,80 250,90 C280,100 290,120 285,140" />
        
        {/* Pernas */}
        <path d="M250,380 L250,500" /> {/* Perna dianteira */}
        <path d="M600,400 L580,520" /> {/* Perna traseira */}
        
        {/* Rabo */}
        <path d="M680,340 C700,360 710,380 700,420 C690,460 670,500 650,520" />
      </g>
      
      {/* Pontos de medição com círculos, linhas e rótulos */}
      {/* Altura na cernelha */}
      <line x1="250" y1="270" x2="250" y2="500" stroke="#3b82f6" strokeWidth="2" strokeDasharray={highlighted === 'alturaCernelha' ? "none" : "5,5"} />
      <circle cx="250" cy="270" r={highlighted === 'alturaCernelha' ? "10" : "6"} fill={highlighted === 'alturaCernelha' ? "#3b82f6" : "#6b7280"} />
      <text x="180" y="270" fontFamily="sans-serif" fontSize="12" fontWeight="bold" fill={highlighted === 'alturaCernelha' ? "#3b82f6" : "#6b7280"}>Cernelha: {medidas.alturaCernelha || '?'} cm</text>
      
      {/* Comprimento do corpo */}
      <line x1="250" y1="340" x2="600" y2="340" stroke="#10b981" strokeWidth="2" strokeDasharray={highlighted === 'comprimentoCorpo' ? "none" : "5,5"} />
      <circle cx="250" cy="340" r="4" fill="#10b981" />
      <circle cx="600" cy="340" r="4" fill="#10b981" />
      <text x="380" y="330" fontFamily="sans-serif" fontSize="12" fontWeight="bold" fill={highlighted === 'comprimentoCorpo' ? "#10b981" : "#6b7280"}>Comprimento: {medidas.comprimentoCorpo || '?'} cm</text>
      
      {/* Perímetro torácico */}
      <ellipse cx="350" cy="320" rx="100" ry="70" stroke="#ef4444" strokeWidth="2" strokeDasharray={highlighted === 'perimetroToracico' ? "none" : "5,5"} fill="none" />
      <text x="330" y="290" fontFamily="sans-serif" fontSize="12" fontWeight="bold" fill={highlighted === 'perimetroToracico' ? "#ef4444" : "#6b7280"}>Perímetro: {medidas.perimetroToracico || '?'} cm</text>
      
      {/* Comprimento da garupa */}
      <line x1="500" y1="300" x2="620" y2="320" stroke="#8b5cf6" strokeWidth="2" strokeDasharray={highlighted === 'comprimentoGarupa' ? "none" : "5,5"} />
      <circle cx="500" cy="300" r="4" fill="#8b5cf6" />
      <circle cx="620" cy="320" r="4" fill="#8b5cf6" />
      <text x="510" y="280" fontFamily="sans-serif" fontSize="12" fontWeight="bold" fill={highlighted === 'comprimentoGarupa' ? "#8b5cf6" : "#6b7280"}>Garupa: {medidas.comprimentoGarupa || '?'} cm</text>
      
      {/* Perímetro da canela */}
      <circle cx="250" cy="450" r="15" stroke="#f59e0b" strokeWidth="2" strokeDasharray={highlighted === 'perimetroCanela' ? "none" : "5,5"} fill="none" />
      <text x="280" y="450" fontFamily="sans-serif" fontSize="12" fontWeight="bold" fill={highlighted === 'perimetroCanela' ? "#f59e0b" : "#6b7280"}>Canela: {medidas.perimetroCanela || '?'} cm</text>
      
      {/* Comprimento da cabeça */}
      <line x1="180" y1="100" x2="250" y2="120" stroke="#ec4899" strokeWidth="2" strokeDasharray={highlighted === 'comprimentoCabeca' ? "none" : "5,5"} />
      <text x="170" y="80" fontFamily="sans-serif" fontSize="12" fontWeight="bold" fill={highlighted === 'comprimentoCabeca' ? "#ec4899" : "#6b7280"}>Cabeça: {medidas.comprimentoCabeca || '?'} cm</text>
      
      {/* Largura da cabeça */}
      <line x1="220" y1="100" x2="220" y2="130" stroke="#06b6d4" strokeWidth="2" strokeDasharray={highlighted === 'larguraCabeca' ? "none" : "5,5"} />
      <text x="140" y="120" fontFamily="sans-serif" fontSize="12" fontWeight="bold" fill={highlighted === 'larguraCabeca' ? "#06b6d4" : "#6b7280"}>Largura: {medidas.larguraCabeca || '?'} cm</text>
    </svg>
  );
};

// Valores de referência para raças comuns
const valoresReferencia = {
  'Quarto de Milha': {
    alturaCernelha: { min: 142, max: 160, ideal: 152 },
    comprimentoCorpo: { min: 150, max: 170, ideal: 160 },
    perimetroToracico: { min: 170, max: 190, ideal: 180 },
    comprimentoGarupa: { min: 50, max: 60, ideal: 56 },
    perimetroCanela: { min: 18, max: 22, ideal: 20 },
    comprimentoCabeca: { min: 55, max: 65, ideal: 60 },
    larguraCabeca: { min: 20, max: 25, ideal: 22 }
  },
  'Mangalarga Marchador': {
    alturaCernelha: { min: 147, max: 157, ideal: 152 },
    comprimentoCorpo: { min: 148, max: 165, ideal: 158 },
    perimetroToracico: { min: 168, max: 188, ideal: 178 },
    comprimentoGarupa: { min: 48, max: 58, ideal: 52 },
    perimetroCanela: { min: 17, max: 20, ideal: 18.5 },
    comprimentoCabeca: { min: 54, max: 62, ideal: 58 },
    larguraCabeca: { min: 19, max: 23, ideal: 21 }
  },
  'Árabe': {
    alturaCernelha: { min: 145, max: 155, ideal: 150 },
    comprimentoCorpo: { min: 145, max: 165, ideal: 155 },
    perimetroToracico: { min: 165, max: 185, ideal: 175 },
    comprimentoGarupa: { min: 46, max: 54, ideal: 50 },
    perimetroCanela: { min: 17, max: 20, ideal: 18 },
    comprimentoCabeca: { min: 50, max: 60, ideal: 55 },
    larguraCabeca: { min: 17, max: 22, ideal: 19 }
  },
  'Puro Sangue Inglês': {
    alturaCernelha: { min: 155, max: 170, ideal: 163 },
    comprimentoCorpo: { min: 160, max: 175, ideal: 168 },
    perimetroToracico: { min: 175, max: 195, ideal: 185 },
    comprimentoGarupa: { min: 52, max: 62, ideal: 58 },
    perimetroCanela: { min: 19, max: 23, ideal: 21 },
    comprimentoCabeca: { min: 58, max: 68, ideal: 63 },
    larguraCabeca: { min: 20, max: 24, ideal: 22 }
  },
  'Crioulo': {
    alturaCernelha: { min: 138, max: 150, ideal: 144 },
    comprimentoCorpo: { min: 140, max: 155, ideal: 148 },
    perimetroToracico: { min: 165, max: 180, ideal: 173 },
    comprimentoGarupa: { min: 45, max: 55, ideal: 50 },
    perimetroCanela: { min: 17, max: 21, ideal: 19 },
    comprimentoCabeca: { min: 52, max: 62, ideal: 57 },
    larguraCabeca: { min: 19, max: 23, ideal: 21 }
  }
};

// Definições das medidas com instruções e descrições
const medidasDefinicoes = {
  alturaCernelha: {
    nome: 'Altura na Cernelha',
    descricao: 'Distância vertical do solo até o ponto mais alto da cernelha (região do dorso entre as escápulas)',
    instrucao: 'Posicione o cavalo em terreno plano, com os membros paralelos. Meça verticalmente do solo até o ponto mais alto da cernelha.',
    unidade: 'cm',
    cor: '#3b82f6',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Fundamental para classificação do animal em categorias de altura e conformidade com o padrão racial.'
  },
  comprimentoCorpo: {
    nome: 'Comprimento do Corpo',
    descricao: 'Distância entre a ponta da espádua e a ponta do ísquio',
    instrucao: 'Meça horizontalmente da ponta da espádua até a ponta do ísquio (osso da ponta da nádega).',
    unidade: 'cm',
    cor: '#10b981',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Define o equilíbrio corporal do animal e sua capacidade atlética.'
  },
  perimetroToracico: {
    nome: 'Perímetro Torácico',
    descricao: 'Circunferência do tórax, atrás da cernelha e passando pelo cilhadouro',
    instrucao: 'Passe a fita métrica ao redor do tórax, logo atrás da cernelha, passando pelo cilhadouro (região de inserção da cilha).',
    unidade: 'cm',
    cor: '#ef4444',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Indica a capacidade respiratória e desenvolvimento torácico.'
  },
  comprimentoGarupa: {
    nome: 'Comprimento da Garupa',
    descricao: 'Distância entre a ponta do ílio e a ponta do ísquio',
    instrucao: 'Meça horizontalmente da ponta do ílio (tuberosidade coxal) até a ponta do ísquio (tuberosidade isquiática).',
    unidade: 'cm',
    cor: '#8b5cf6',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Relacionado à propulsão e elasticidade dos movimentos.'
  },
  perimetroCanela: {
    nome: 'Perímetro da Canela',
    descricao: 'Circunferência da região mais estreita da canela (metacarpo) do membro anterior',
    instrucao: 'Meça a circunferência da canela no membro anterior, no ponto mais estreito, normalmente na metade do osso metacarpiano.',
    unidade: 'cm',
    cor: '#f59e0b',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Indicador de resistência óssea e capacidade de suporte de peso.'
  },
  comprimentoCabeca: {
    nome: 'Comprimento da Cabeça',
    descricao: 'Distância da nuca até a ponta do focinho',
    instrucao: 'Meça da base da nuca (crista occipital) até a ponta do focinho.',
    unidade: 'cm',
    cor: '#ec4899',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Importante para a caracterização racial e proporção corporal.'
  },
  larguraCabeca: {
    nome: 'Largura da Cabeça',
    descricao: 'Distância entre os arcos zigomáticos (maçãs do rosto)',
    instrucao: 'Meça a distância entre os pontos mais largos dos arcos zigomáticos.',
    unidade: 'cm',
    cor: '#06b6d4',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Compõe a caracterização racial da cabeça.'
  }
};

// Índices de conformação corporal
const calcularIndices = (medidas) => {
  const indices = {};
  
  // Verificar se existem as medidas necessárias
  if (medidas.alturaCernelha && medidas.comprimentoCorpo) {
    // Índice corporal (comprimento/altura)
    indices.indiceRelativo = {
      valor: (medidas.comprimentoCorpo / medidas.alturaCernelha).toFixed(2),
      descricao: 'Índice Corporal Relativo',
      interpretacao: 'Relação entre comprimento e altura. Valores próximos a 1 indicam equilíbrio.',
      referencia: { baixo: 0.9, ideal: 1.0, alto: 1.1 }
    };
  }
  
  if (medidas.perimetroToracico && medidas.perimetroCanela) {
    // Índice de robustez (perímetro torácico/perímetro canela)
    indices.indiceRobustez = {
      valor: (medidas.perimetroToracico / medidas.perimetroCanela).toFixed(2),
      descricao: 'Índice de Robustez',
      interpretacao: 'Relação entre o perímetro torácico e o perímetro da canela. Indica a capacidade de suporte da estrutura óssea.',
      referencia: { baixo: 8.0, ideal: 9.0, alto: 10.0 }
    };
  }
  
  if (medidas.alturaCernelha && medidas.perimetroToracico) {
    // Índice torácico (perímetro torácico/altura)
    indices.indiceTorax = {
      valor: (medidas.perimetroToracico / medidas.alturaCernelha * 100).toFixed(2),
      descricao: 'Índice Torácico',
      interpretacao: 'Porcentagem do perímetro torácico em relação à altura. Valores maiores indicam melhor capacidade respiratória.',
      referencia: { baixo: 115, ideal: 118, alto: 125 }
    };
  }
  
  return indices;
};

// Comparação com valores de referência
const compararComReferencia = (valor, referencia) => {
  if (!valor || !referencia) return { status: 'neutro', mensagem: 'Sem dados para comparação' };
  
  if (valor < referencia.min) {
    return { 
      status: 'abaixo', 
      mensagem: `Abaixo do mínimo de referência (${referencia.min} ${medidasDefinicoes[Object.keys(referencia)[0]]?.unidade})`,
      diferenca: (valor - referencia.min).toFixed(1)
    };
  } else if (valor > referencia.max) {
    return { 
      status: 'acima', 
      mensagem: `Acima do máximo de referência (${referencia.max} ${medidasDefinicoes[Object.keys(referencia)[0]]?.unidade})`,
      diferenca: (valor - referencia.max).toFixed(1)
    };
  } else {
    const proximidadeIdeal = Math.abs(valor - referencia.ideal);
    const rangePermitido = (referencia.max - referencia.min) / 2;
    const percentualProximidade = (1 - (proximidadeIdeal / rangePermitido)) * 100;
    
    return { 
      status: 'dentro', 
      mensagem: `Dentro dos padrões de referência (ideal: ${referencia.ideal} ${medidasDefinicoes[Object.keys(referencia)[0]]?.unidade})`,
      proximidade: percentualProximidade.toFixed(0)
    };
  }
};

// Componente principal para medidas morfológicas
const MedidasMorfologicas = ({ cavalo, onSalvar }) => {
  const { toast } = useToast();
  const [medidas, setMedidas] = useState({
    alturaCernelha: '',
    comprimentoCorpo: '',
    perimetroToracico: '',
    comprimentoGarupa: '',
    perimetroCanela: '',
    comprimentoCabeca: '',
    larguraCabeca: ''
  });
  
  const [temperatura, setTemperatura] = useState('');
  const [observacoes, setObservacoes] = useState('');
  const [activeTab, setActiveTab] = useState('medicao');
  const [racaSelecionada, setRacaSelecionada] = useState('');
  const [highlightedMeasure, setHighlightedMeasure] = useState(null);
  const [errors, setErrors] = useState({});
  
  // Atualizar raça selecionada quando o cavalo mudar
  useEffect(() => {
    if (cavalo?.breed) {
      setRacaSelecionada(
        Object.keys(valoresReferencia).includes(cavalo.breed) 
          ? cavalo.breed 
          : ''
      );
    }
  }, [cavalo]);
  
  // Verificar se a raça selecionada tem valores de referência
  const temReferencia = racaSelecionada && valoresReferencia[racaSelecionada];
  
  // Atualizar medida específica
  const handleMedidaChange = (medida, valor) => {
    // Validar se é um número
    const numerico = valor.replace(/[^0-9.]/g, '');
    
    // Atualizar estado
    setMedidas(prev => ({
      ...prev,
      [medida]: numerico
    }));
    
    // Limpar erro se existir
    if (errors[medida]) {
      setErrors(prev => {
        const newErrors = {...prev};
        delete newErrors[medida];
        return newErrors;
      });
    }
  };
  
  // Calcular os índices com base nas medidas atuais
  const indices = calcularIndices(medidas);
  
  // Validar medidas antes de salvar
  const validarMedidas = () => {
    const novosErros = {};
    let valido = true;
    
    // Verificar campos obrigatórios
    Object.keys(medidasDefinicoes).forEach(medida => {
      if (!medidas[medida] || isNaN(parseFloat(medidas[medida]))) {
        novosErros[medida] = 'Este campo é obrigatório';
        valido = false;
      } else if (parseFloat(medidas[medida]) <= 0) {
        novosErros[medida] = 'Valor deve ser maior que zero';
        valido = false;
      }
    });
    
    setErrors(novosErros);
    return valido;
  };
  
  // Salvar medidas
  const salvarMedidas = async () => {
    if (!validarMedidas()) {
      toast({
        title: 'Formulário incompleto',
        description: 'Preencha todos os campos obrigatórios com valores válidos.',
        variant: 'destructive'
      });
      return;
    }

    try {
      // Preparar dados para salvar
      const novaMedida = {
        horseId: cavalo.id,
        userId: cavalo.userId,
        dataMedicao: new Date().toISOString(),
        alturaCernelha: Number(medidas.alturaCernelha),
        comprimentoCorpo: Number(medidas.comprimentoCorpo),
        perimetroToracico: Number(medidas.perimetroToracico),
        comprimentoGarupa: Number(medidas.comprimentoGarupa),
        perimetroCanela: Number(medidas.perimetroCanela),
        comprimentoCabeca: Number(medidas.comprimentoCabeca),
        larguraCabeca: Number(medidas.larguraCabeca),
        temperatura: temperatura ? Number(temperatura) : null,
        racaReferencia: racaSelecionada,
        observacoes: observacoes,
        indiceRelativo: indices.indiceRelativo?.valor ? Number(indices.indiceRelativo.valor) : null,
        indiceRobustez: indices.indiceRobustez?.valor ? Number(indices.indiceRobustez.valor) : null,
        indiceTorax: indices.indiceTorax?.valor ? Number(indices.indiceTorax.valor) : null
      };

      // Fazer chamada à API usando a URL correta
      const response = await fetch('/api/medidas-morfologicas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'user-id': cavalo.userId.toString()
        },
        body: JSON.stringify(novaMedida)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Falha ao salvar as medidas');
      }

      const result = await response.json();
      
      if (result.success) {
        toast({
          title: 'Medidas salvas',
          description: 'As medidas morfológicas foram salvas com sucesso.'
        });
        
        if (onSalvar) {
          onSalvar(result.data);
        }

        // Limpar o formulário após salvar
        setMedidas({
          alturaCernelha: '',
          comprimentoCorpo: '',
          perimetroToracico: '',
          comprimentoGarupa: '',
          perimetroCanela: '',
          comprimentoCabeca: '',
          larguraCabeca: ''
        });
        setTemperatura('');
        setObservacoes('');
      } else {
        throw new Error(result.message || 'Erro ao salvar as medidas');
      }
    } catch (error) {
      console.error('Erro ao salvar medidas:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível salvar as medidas morfológicas.',
        variant: 'destructive'
      });
    }
  };
  
  // Imprimir relatório
  const imprimirRelatorio = () => {
    window.print();
  };
  
  // Gerar um valor automático baseado na raça
  const gerarValorAutomatico = (medida) => {
    if (!racaSelecionada || !valoresReferencia[racaSelecionada]) {
      toast({
        title: 'Raça não selecionada',
        description: 'Selecione uma raça para gerar valores automáticos.',
        variant: 'destructive'
      });
      return;
    }
    
    const ref = valoresReferencia[racaSelecionada][medida];
    if (!ref) return;
    
    // Gerar um valor aleatório dentro do intervalo de referência
    const min = ref.min;
    const max = ref.max;
    const valor = (Math.random() * (max - min) + min).toFixed(1);
    
    handleMedidaChange(medida, valor);
  };
  
  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 md:grid-cols-3">
          <TabsTrigger value="medicao">Medição</TabsTrigger>
          <TabsTrigger value="analise">Análise</TabsTrigger>
          <TabsTrigger value="historico">Histórico</TabsTrigger>
        </TabsList>
        
        <TabsContent value="medicao" className="space-y-6">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Coluna do desenho do cavalo */}
            <div className="md:w-1/2">
              <Card className="overflow-hidden h-full">
                <CardHeader className="pb-0">
                  <CardTitle>Cavalo {cavalo?.name}</CardTitle>
                  <CardDescription>
                    Pontos de referência para medições morfológicas
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex justify-center pt-4">
                  <HorseMeasurementSVG 
                    medidas={medidas} 
                    highlighted={highlightedMeasure}
                  />
                </CardContent>
                <CardFooter className="flex flex-col items-start space-y-2 text-sm text-muted-foreground">
                  <p>* Passe o mouse sobre os campos de medição para destacar os pontos de referência no desenho.</p>
                  {racaSelecionada ? (
                    <p>Visualizando referências para raça: <Badge variant="outline">{racaSelecionada}</Badge></p>
                  ) : (
                    <p>Selecione uma raça para ver valores de referência.</p>
                  )}
                </CardFooter>
              </Card>
            </div>
            
            {/* Coluna do formulário de medidas */}
            <div className="md:w-1/2">
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle>Registro de Medidas</CardTitle>
                    {cavalo?.breed && (
                      <div className="flex items-center space-x-2">
                        <Label htmlFor="raca">Raça para referência:</Label>
                        <select
                          id="raca"
                          value={racaSelecionada}
                          onChange={(e) => setRacaSelecionada(e.target.value)}
                          className="px-3 py-1.5 text-sm border rounded-md"
                        >
                          <option value="">Selecione</option>
                          {Object.keys(valoresReferencia).map(raca => (
                            <option key={raca} value={raca}>{raca}</option>
                          ))}
                        </select>
                      </div>
                    )}
                  </div>
                  <CardDescription>
                    Insira as medidas morfológicas do animal
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Campos de medidas */}
                  {Object.entries(medidasDefinicoes).map(([key, definicao]) => (
                    <div 
                      key={key} 
                      className="grid grid-cols-1 gap-2"
                      onMouseEnter={() => setHighlightedMeasure(key)}
                      onMouseLeave={() => setHighlightedMeasure(null)}
                    >
                      <div className="flex justify-between items-center">
                        <Label htmlFor={key} className="flex items-center space-x-1">
                          <span className="flex items-center">
                            {definicao.icone}
                            <span className="ml-2">{definicao.nome}</span>
                          </span>
                        </Label>
                        {temReferencia && (
                          <span className="text-xs text-muted-foreground">
                            Ref: {valoresReferencia[racaSelecionada][key].min}-{valoresReferencia[racaSelecionada][key].max} {definicao.unidade}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex space-x-2">
                        <Input
                          id={key}
                          type="text"
                          placeholder={`${definicao.nome} (${definicao.unidade})`}
                          value={medidas[key]}
                          onChange={(e) => handleMedidaChange(key, e.target.value)}
                          onFocus={() => setHighlightedMeasure(key)}
                          onBlur={() => setHighlightedMeasure(null)}
                          className={errors[key] ? 'border-red-500' : ''}
                        />
                        <Button 
                          variant="outline" 
                          size="icon"
                          onClick={() => gerarValorAutomatico(key)}
                          disabled={!temReferencia}
                          title="Gerar valor automático baseado na referência da raça"
                        >
                          <Clipboard className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      {errors[key] && (
                        <p className="text-xs text-red-500">{errors[key]}</p>
                      )}
                      
                      <p className="text-xs text-muted-foreground">{definicao.instrucao}</p>
                      
                      {temReferencia && medidas[key] && (
                        <div className="mt-1">
                          <div className="w-full bg-gray-200 rounded-full h-1.5">
                            <div 
                              className={`h-1.5 rounded-full ${
                                parseFloat(medidas[key]) < valoresReferencia[racaSelecionada][key].min ? 'bg-red-500' :
                                parseFloat(medidas[key]) > valoresReferencia[racaSelecionada][key].max ? 'bg-orange-500' :
                                'bg-green-500'
                              }`}
                              style={{ 
                                width: `${Math.min(100, Math.max(0, 
                                  ((parseFloat(medidas[key]) - valoresReferencia[racaSelecionada][key].min) / 
                                  (valoresReferencia[racaSelecionada][key].max - valoresReferencia[racaSelecionada][key].min)) * 100
                                ))}%` 
                              }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  
                  <Separator className="my-4" />
                  
                  {/* Temperatura e observações */}
                  <div className="grid grid-cols-1 gap-2">
                    <Label htmlFor="temperatura" className="flex items-center">
                      <ThermometerSnowflake className="h-4 w-4 mr-2" />
                      Temperatura corporal (opcional)
                    </Label>
                    <Input
                      id="temperatura"
                      type="text"
                      placeholder="Temperatura (°C)"
                      value={temperatura}
                      onChange={(e) => setTemperatura(e.target.value.replace(/[^0-9.]/g, ''))}
                    />
                    <p className="text-xs text-muted-foreground">
                      Temperatura normal para equinos: 37.5°C - 38.5°C
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 gap-2">
                    <Label htmlFor="observacoes">Observações</Label>
                    <textarea
                      id="observacoes"
                      placeholder="Observações sobre a medição ou condições do animal"
                      value={observacoes}
                      onChange={(e) => setObservacoes(e.target.value)}
                      rows={3}
                      className="w-full p-2 border rounded-md"
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => setActiveTab('analise')}>
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Ver Análise
                  </Button>
                  <Button onClick={salvarMedidas}>
                    <Save className="h-4 w-4 mr-2" />
                    Salvar Medidas
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="analise" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Análise de Medidas Morfológicas</CardTitle>
              <CardDescription>
                Comparação com valores de referência para {racaSelecionada || 'a raça do animal'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {!temReferencia && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Raça sem referências</AlertTitle>
                  <AlertDescription>
                    Não há valores de referência disponíveis para {cavalo?.breed || 'a raça selecionada'}.
                    Selecione uma raça com valores de referência para comparação.
                  </AlertDescription>
                </Alert>
              )}
              
              {temReferencia && (
                <>
                  <div>
                    <h3 className="text-lg font-medium">Medidas Básicas</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Medida</TableHead>
                          <TableHead>Valor</TableHead>
                          <TableHead>Referência</TableHead>
                          <TableHead>Análise</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {Object.entries(medidasDefinicoes).map(([key, definicao]) => {
                          const valor = parseFloat(medidas[key]);
                          const referencia = valoresReferencia[racaSelecionada][key];
                          const analise = valor ? compararComReferencia(valor, referencia) : null;
                          
                          return (
                            <TableRow key={key}>
                              <TableCell className="font-medium">{definicao.nome}</TableCell>
                              <TableCell>{valor ? `${valor} ${definicao.unidade}` : '-'}</TableCell>
                              <TableCell>{referencia ? `${referencia.min}-${referencia.max} ${definicao.unidade}` : '-'}</TableCell>
                              <TableCell>
                                {analise ? (
                                  <div className="flex items-center">
                                    <span 
                                      className={`h-3 w-3 rounded-full mr-2 ${
                                        analise.status === 'abaixo' ? 'bg-red-500' :
                                        analise.status === 'acima' ? 'bg-orange-500' :
                                        'bg-green-500'
                                      }`}
                                    ></span>
                                    <span className="text-sm">{analise.mensagem}</span>
                                  </div>
                                ) : '-'}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="text-lg font-medium">Índices Morfométricos</h3>
                    {Object.keys(indices).length > 0 ? (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Índice</TableHead>
                            <TableHead>Valor</TableHead>
                            <TableHead>Interpretação</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {Object.entries(indices).map(([key, indice]) => (
                            <TableRow key={key}>
                              <TableCell className="font-medium">{indice.descricao}</TableCell>
                              <TableCell>{indice.valor}</TableCell>
                              <TableCell>{indice.interpretacao}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    ) : (
                      <p className="text-muted-foreground text-sm py-4">
                        Preencha todas as medidas necessárias para calcular os índices morfométricos.
                      </p>
                    )}
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-3">
                    <h3 className="text-lg font-medium">Recomendações</h3>
                    
                    {Object.entries(medidas).some(([key, valor]) => {
                      const valorNum = parseFloat(valor);
                      const ref = valoresReferencia[racaSelecionada][key];
                      return valorNum && ref && (valorNum < ref.min || valorNum > ref.max);
                    }) ? (
                      <Alert className="bg-amber-50 border-amber-200">
                        <AlertCircle className="h-4 w-4 text-amber-600" />
                        <AlertTitle className="text-amber-800">Atenção às medidas fora do padrão</AlertTitle>
                        <AlertDescription className="text-amber-700">
                          Algumas medidas estão fora do intervalo de referência para a raça {racaSelecionada}.
                          Considere realizar uma avaliação mais detalhada com um veterinário especializado.
                        </AlertDescription>
                      </Alert>
                    ) : (
                      <Alert className="bg-green-50 border-green-200">
                        <AlertCircle className="h-4 w-4 text-green-600" />
                        <AlertTitle className="text-green-800">Medidas dentro dos padrões</AlertTitle>
                        <AlertDescription className="text-green-700">
                          As medidas estão dentro dos intervalos de referência para a raça {racaSelecionada}.
                          Continue monitorando o desenvolvimento do animal.
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <Button variant="outline" onClick={imprimirRelatorio} className="w-full">
                        <Printer className="h-4 w-4 mr-2" />
                        Imprimir Relatório
                      </Button>
                      <Button onClick={salvarMedidas} className="w-full">
                        <Save className="h-4 w-4 mr-2" />
                        Salvar no Histórico
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setActiveTab('medicao')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar para Medição
              </Button>
              <Button variant="outline" onClick={() => setActiveTab('historico')}>
                <ArrowRight className="h-4 w-4 mr-2" />
                Ver Histórico
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="historico">
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Medições</CardTitle>
              <CardDescription>
                Acompanhe o desenvolvimento morfológico do animal ao longo do tempo
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-8 text-muted-foreground">
                Histórico de medições será exibido aqui.
                <br />
                <span className="text-sm">
                  Salve as medidas atuais para começar a construir o histórico.
                </span>
              </p>
              
              <div className="flex justify-center">
                <Button variant="outline" onClick={() => setActiveTab('medicao')}>
                  <Camera className="h-4 w-4 mr-2" />
                  Registrar Nova Medição
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MedidasMorfologicas;