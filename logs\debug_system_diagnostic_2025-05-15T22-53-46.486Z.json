{"timestamp": "2025-05-15T22:53:46.485Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 398946304, "heapTotal": 115908608, "heapUsed": 72490600, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.847415892, "cpuUsage": {"user": 2845139, "system": 345797}, "resourceUsage": {"userCPUTime": 2845184, "systemCPUTime": 345802, "maxRSS": 389596, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105175, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 136, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8026, "involuntaryContextSwitches": 2606}}