{"timestamp": "2025-05-19T12:20:00.937Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 292556800, "heapTotal": 112918528, "heapUsed": 89207296, "external": 8516135, "arrayBuffers": 291169}, "uptime": 2.288610354, "cpuUsage": {"user": 2650511, "system": 346573}, "resourceUsage": {"userCPUTime": 2650577, "systemCPUTime": 346573, "maxRSS": 285700, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104310, "majorPageFault": 1, "swappedOut": 0, "fsRead": 34152, "fsWrite": 40, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9168, "involuntaryContextSwitches": 2837}}