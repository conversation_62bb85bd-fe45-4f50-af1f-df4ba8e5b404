{"timestamp": "2025-05-15T19:20:03.389Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399507456, "heapTotal": 111714304, "heapUsed": 72479536, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.710855019, "cpuUsage": {"user": 2730228, "system": 370807}, "resourceUsage": {"userCPUTime": 2730274, "systemCPUTime": 370813, "maxRSS": 390144, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103976, "majorPageFault": 0, "swappedOut": 0, "fsRead": 29184, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7346, "involuntaryContextSwitches": 4467}}