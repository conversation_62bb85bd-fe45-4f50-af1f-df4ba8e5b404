{"timestamp": "2025-05-14T14:42:16.131Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 382386176, "heapTotal": 104206336, "heapUsed": 62213112, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.771278383, "cpuUsage": {"user": 2325096, "system": 318466}, "resourceUsage": {"userCPUTime": 2325165, "systemCPUTime": 318466, "maxRSS": 373424, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100834, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 16, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6452, "involuntaryContextSwitches": 4347}}