/**
 * Rotas de Alimentação - EquiGestor AI
 * API endpoints para planos de alimentação diários
 */

import { Router, Request, Response } from "express";
import { nutritionService } from "../services/nutrition.service";
import { stockService } from "../services/stock.service";
import { authenticateUser } from "../auth";
import { z } from "zod";

const router = Router();

// Aplicar autenticação em todas as rotas
router.use(authenticateUser);

/**
 * GET /api/feeding/today - Lista planos de alimentação do dia
 */
router.get("/today", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    const date = req.query.date as string || new Date().toISOString().split('T')[0];
    
    const plans = await nutritionService.getTodayPlans(userId, date);
    
    // Adicionar estatísticas resumidas
    const totalPlans = plans.length;
    const pendingPlans = plans.filter(p => p.status === "pending").length;
    const completedPlans = plans.filter(p => p.status === "done").length;
    const plansWithLeftover = plans.filter(p => p.status === "leftover").length;
    
    res.json({
      plans,
      statistics: {
        total: totalPlans,
        pending: pendingPlans,
        completed: completedPlans,
        withLeftover: plansWithLeftover
      }
    });
  } catch (error) {
    console.error("Erro ao buscar planos de alimentação:", error);
    res.status(500).json({ error: "Erro ao buscar planos de alimentação" });
  }
});

/**
 * POST /api/feeding/generate - Gera planos de alimentação para uma data
 */
router.post("/generate", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    const { date } = req.body;
    
    if (!date) {
      return res.status(400).json({ error: "Data é obrigatória" });
    }
    
    const plans = await nutritionService.generateDailyPlans(userId, date);
    res.status(201).json(plans);
  } catch (error) {
    console.error("Erro ao gerar planos de alimentação:", error);
    res.status(500).json({ error: "Erro ao gerar planos de alimentação" });
  }
});

/**
 * PATCH /api/feeding/:id/mark - Marca plano como concluído ou registra sobra
 */
router.patch("/:id/mark", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    const planId = Number(req.params.id);
    const { leftoverPct } = req.body;
    
    if (isNaN(planId)) {
      return res.status(400).json({ error: "ID do plano inválido" });
    }
    
    // Validar percentual de sobra se fornecido
    if (leftoverPct !== undefined) {
      const leftoverSchema = z.number().min(0).max(100);
      const validatedLeftover = leftoverSchema.parse(leftoverPct);
      
      await nutritionService.markPlanComplete(planId, userId, validatedLeftover);
    } else {
      await nutritionService.markPlanComplete(planId, userId);
    }
    
    res.json({ success: true, message: "Plano atualizado com sucesso" });
  } catch (error: any) {
    console.error("Erro ao marcar plano:", error);
    
    if (error?.name === 'ZodError') {
      return res.status(400).json({ 
        error: "Percentual de sobra deve ser entre 0 e 100", 
        details: error.errors 
      });
    }
    
    res.status(500).json({ error: "Erro ao atualizar plano" });
  }
});

/**
 * GET /api/feeding/statistics - Estatísticas de desperdício
 */
router.get("/statistics", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    const days = Number(req.query.days) || 30;
    
    const wasteStats = await nutritionService.getWasteStatistics(userId, days);
    const avgCostPerAnimal = await stockService.calculateAverageCostPerAnimal(userId, days);
    
    res.json({
      avgWastePct: wasteStats.avgWastePct,
      totalPlans: wasteStats.totalPlans,
      plansWithWaste: wasteStats.plansWithWaste,
      avgCostPerAnimalPerDay: avgCostPerAnimal,
      period: `${days} dias`
    });
  } catch (error) {
    console.error("Erro ao buscar estatísticas:", error);
    res.status(500).json({ error: "Erro ao buscar estatísticas" });
  }
});

/**
 * GET /api/feeding/templates - Lista templates de alimentação
 */
router.get("/templates", async (req: Request, res: Response) => {
  try {
    const templates = await nutritionService.getTemplates();
    res.json(templates);
  } catch (error) {
    console.error("Erro ao buscar templates:", error);
    res.status(500).json({ error: "Erro ao buscar templates" });
  }
});

/**
 * POST /api/feeding/templates - Cria novo template de alimentação
 */
router.post("/templates", async (req: Request, res: Response) => {
  try {
    const userId = Number((req as any).user?.id);
    
    const templateSchema = z.object({
      category: z.string().min(1),
      forageMnPctMs: z.number().min(0).max(10),
      concentrateMaxKgPer100kg: z.number().min(0).max(5),
      notes: z.string().optional()
    });
    
    const validatedData = templateSchema.parse(req.body);
    const template = await nutritionService.createTemplate({
      ...validatedData,
      createdBy: userId
    });
    
    res.status(201).json(template);
  } catch (error: any) {
    console.error("Erro ao criar template:", error);
    
    if (error?.name === 'ZodError') {
      return res.status(400).json({ 
        error: "Dados inválidos", 
        details: error.errors 
      });
    }
    
    res.status(500).json({ error: "Erro ao criar template" });
  }
});

export default router;