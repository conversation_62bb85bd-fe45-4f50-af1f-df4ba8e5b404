{"timestamp": "2025-05-15T19:24:31.281Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 392036352, "heapTotal": 107520000, "heapUsed": 72373456, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.5449784, "cpuUsage": {"user": 2818578, "system": 396923}, "resourceUsage": {"userCPUTime": 2818640, "systemCPUTime": 396931, "maxRSS": 382848, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101955, "majorPageFault": 0, "swappedOut": 0, "fsRead": 29336, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8563, "involuntaryContextSwitches": 5302}}