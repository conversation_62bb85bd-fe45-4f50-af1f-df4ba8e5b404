import React, { useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON><PERSON> } from "@shared/schema";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { FormField, FormLabel, FormItem, FormControl, FormMessage } from "@/components/ui/form";

type GenealogiaTipo = "sistema" | "externo";

export interface EntradaGenealogica {
  tipo: GenealogiaTipo;
  cavaloSistemaId?: string; // ID do cavalo do sistema
  cavaloNome?: string | null; // Nome de cavalo externo
}

interface GenealogiaSelectorProps {
  label: string;
  value?: EntradaGenealogica;
  onChange: (val: EntradaGenealogica) => void;
  cavalos: Cavalo[];
  sexoFiltro?: "Macho" | "Fêmea" | "Castrado" | "Garanhão" | "Égua";
  error?: string;
  disabled?: boolean;
}

export const GenealogiaSelector: React.FC<GenealogiaSelectorProps> = ({
  label,
  value,
  onChange,
  cavalos,
  sexoFiltro,
  error,
  disabled = false,
}) => {
  // Tipagem consistente, value nunca é undefined
  const { tipo = "sistema", cavaloSistemaId = "", cavaloNome = "" } = value || {};

  // Estado interno dos campos
  const [tipoEntrada, setTipoEntrada] = useState<GenealogiaTipo>(tipo);
  const [cavaloId, setCavaloId] = useState<string>(cavaloSistemaId);
  const [nomeExterno, setNomeExterno] = useState<string>(cavaloNome ?? "");

  // Filtrar cavalos de acordo com sexoFiltro
  const cavalosFiltrados = useMemo(() => {
    if (!sexoFiltro) return cavalos;
    const aliases: Record<string, string[]> = {
      "Macho": ["Macho", "Garanhão", "Castrado"],
      "Fêmea": ["Fêmea", "Égua"]
    };
    const validSexos = aliases[sexoFiltro] || [sexoFiltro];
    return cavalos.filter(c => validSexos.includes(c.sexo || ""));
  }, [cavalos, sexoFiltro]);

  // Atualiza estados ao receber novo value
  useEffect(() => {
    setTipoEntrada(tipo);
    setCavaloId(cavaloSistemaId);
    setNomeExterno(cavaloNome ?? "");
  }, [tipo, cavaloSistemaId, cavaloNome]);

  // Dispara onChange sempre que algo muda
  useEffect(() => {
    if (tipoEntrada === "sistema") {
      const cavaloSelecionado = cavalos.find(c => c.id === cavaloId);
      onChange({
        tipo: "sistema",
        cavaloSistemaId: cavaloSelecionado ? cavaloSelecionado.id : "",
        cavaloNome: cavaloSelecionado ? cavaloSelecionado.name : "",
      });
    } else {
      onChange({
        tipo: "externo",
        cavaloSistemaId: "",
        cavaloNome: nomeExterno || null,
      });
    }
    // eslint-disable-next-line
  }, [tipoEntrada, cavaloId, nomeExterno, cavalos]);

  // Handler para troca do tipo de entrada
  const handleTipoChange = (newTipo: GenealogiaTipo) => {
    setTipoEntrada(newTipo);
    if (newTipo === "sistema") setNomeExterno("");
    else setCavaloId("");
  };

  return (
    <FormField>
      <FormItem>
        <FormLabel>{label}</FormLabel>
        <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-end">
          {/* Seletor de tipo */}
          <Select
            value={tipoEntrada}
            onValueChange={val => handleTipoChange(val as GenealogiaTipo)}
            disabled={disabled}
          >
            <FormControl>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="sistema">Cavalo do Sistema</SelectItem>
              <SelectItem value="externo">Cavalo Externo</SelectItem>
            </SelectContent>
          </Select>
          {/* Campo de seleção ou texto, de acordo com tipo */}
          {tipoEntrada === "sistema" ? (
            <Select
              value={cavaloId}
              onValueChange={val => setCavaloId(val)}
              disabled={disabled}
            >
              <FormControl>
                <SelectTrigger aria-describedby={error ? "erro-campo-genealogia" : undefined} className="min-w-[200px]">
                  <SelectValue placeholder="Selecione o cavalo" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="">Nenhum</SelectItem>
                {cavalosFiltrados.map(c => (
                  <SelectItem key={c.id} value={c.id}>
                    {c.name} {c.breed && <>({c.breed})</>}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Input
              className="min-w-[200px]"
              value={nomeExterno}
              onChange={e => setNomeExterno(e.target.value)}
              placeholder="Digite o nome do cavalo externo"
              aria-describedby={error ? "erro-campo-genealogia" : undefined}
              disabled={disabled}
              maxLength={80}
              autoComplete="off"
            />
          )}
        </div>
        {/* Mensagem de erro acessível */}
        {error && <FormMessage id="erro-campo-genealogia">{error}</FormMessage>}
      </FormItem>
    </FormField>
  );
};

export default GenealogiaSelector;
