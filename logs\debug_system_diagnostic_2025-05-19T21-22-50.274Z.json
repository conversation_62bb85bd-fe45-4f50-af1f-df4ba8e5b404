{"timestamp": "2025-05-19T21:22:50.273Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 409837568, "heapTotal": 116064256, "heapUsed": 92027688, "external": 8251630, "arrayBuffers": 265658}, "uptime": 4.954444275, "cpuUsage": {"user": 2989327, "system": 500756}, "resourceUsage": {"userCPUTime": 2989372, "systemCPUTime": 500764, "maxRSS": 400232, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108594, "majorPageFault": 6, "swappedOut": 0, "fsRead": 53960, "fsWrite": 1160, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9439, "involuntaryContextSwitches": 5299}}