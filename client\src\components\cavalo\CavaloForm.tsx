import React from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, Search } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format, isValid, parse } from "date-fns";
import { toast } from "@/hooks/use-toast";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { ptBR } from "date-fns/locale";
import { GenealogiaSelector, EntradaGenealogica } from "./GenealogiaSelector";
import { usePelagens } from "@/hooks/use-pelagens";
import { useABCCCImport } from "@/hooks/use-abccc-import";
import { useAuth } from "@/context/AuthContext";
import { pino } from "pino";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Logger configurável
const logger = pino({ enabled: process.env.NODE_ENV !== "production" });

// Schema Zod unificado (baseado em shared/schema.ts)
const cavaloSchema = z.object({
  nome: z.string().min(2, "O nome deve ter pelo menos 2 caracteres"),
  breed: z.string().min(1, "Raça é obrigatória"),
  birthDate: z
    .string()
    .optional()
    .transform((val) => (val ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  sexo: z
    .enum(["Macho", "Fêmea", "Garanhão", "Égua", "Castrado", "nao_informado"])
    .optional(),
  pelagem: z.string().optional(),
  peso: z
    .number()
    .positive()
    .max(2000, "Peso deve ser até 2000kg")
    .optional()
    .nullable(),
  altura: z
    .number()
    .min(0.5)
    .max(2.5, "Altura deve ser entre 0.5m e 2.5m")
    .optional()
    .nullable(),
  status: z
    .enum([
      "Ativo",
      "Vendido",
      "Falecido",
      "Doado",
      "Reprodução",
      "Competição",
      "Tratamento",
      "Aposentado",
      "nao_informado",
    ])
    .optional(),
  observacoes: z.string().optional(),
  origem: z.string().optional(),
  numeroRegistro: z
    .string()
    .regex(/^[A-Z][0-9]{6}$/, "Formato inválido (ex: B123456)")
    .optional(),
  criador: z.string().optional(),
  proprietario: z.string().optional(),
  valorCompra: z
    .number()
    .nonnegative("Valor deve ser positivo")
    .optional()
    .nullable(),
  dataCompra: z
    .string()
    .optional()
    .transform((val) => (val ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  dataEntrada: z
    .string()
    .optional()
    .transform((val) => (val ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  dataSaida: z
    .string()
    .optional()
    .transform((val) => (val ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  pai: z
    .object({
      tipo: z.enum(["sistema", "externo", "nenhum"]),
      cavaloSistemaId: z.string().nullable(),
      cavaloNome: z.string().nullable(),
    })
    .optional(),
  mae: z
    .object({
      tipo: z.enum(["sistema", "externo", "nenhum"]),
      cavaloSistemaId: z.string().nullable(),
      cavaloNome: z.string().nullable(),
    })
    .optional(),
  userId: z.number(),
});

// Interface para os valores do formulário
export type CavaloFormValues = z.infer<typeof cavaloSchema>;

interface CavaloFormProps {
  onSubmit: (values: CavaloFormValues) => Promise<void>;
  defaultValues?: Partial<CavaloFormValues>;
  isSubmitting?: boolean;
  cavalos?: any[];
  onGenealogiaChange?: (
    tipo: "pai" | "mae",
    entrada: EntradaGenealogica,
  ) => void;
}

// Função auxiliar para inicializar genealogia
const inicializarGenealogiaDefault = (
  cavaloId: number | null | undefined,
  cavaloNome: string | null | undefined,
  cavalos: any[],
  sexoFiltro: "Macho" | "Fêmea" | "Garanhão" | "Égua",
): EntradaGenealogica => {
  logger.info(
    `Inicializando genealogia com cavaloId=${cavaloId}, cavaloNome=${cavaloNome}`,
  );

  if (cavaloId && !isNaN(Number(cavaloId))) {
    const id = Number(cavaloId);
    const cavaloEncontrado = cavalos.find((c) => c.id === id);

    if (cavaloEncontrado) {
      return {
        tipo: cavaloEncontrado.isExternal ? "externo" : "sistema",
        cavaloSistemaId: id.toString(),
        cavaloNome: cavaloEncontrado.nome,
      };
    }

    if (cavaloNome) {
      return {
        tipo: "externo",
        cavaloSistemaId: id.toString(),
        cavaloNome,
      };
    }
  }

  if (cavaloNome && cavaloNome !== "nao_informado" && cavaloNome !== "") {
    return {
      tipo: "externo",
      cavaloSistemaId: null,
      cavaloNome,
    };
  }

  return {
    tipo: "nenhum",
    cavaloSistemaId: null,
    cavaloNome: null,
  };
};

/**
 * Componente de formulário reutilizável para cadastro e edição de cavalos
 */
export function CavaloForm({
  onSubmit,
  defaultValues,
  isSubmitting = false,
  cavalos = [],
  onGenealogiaChange,
}: CavaloFormProps) {
  const { userId } = useAuth(); // Obter userId do contexto
  const { pelagens, isLoading: isPelagensLoading } = usePelagens();
  const {
    importData,
    isLoading: isABCCCLoading,
    error: abcccError,
  } = useABCCCImport();
  const [isConfirmDialogOpen, setConfirmDialogOpen] = React.useState(false);
  const [formValues, setFormValues] = React.useState<CavaloFormValues | null>(
    null,
  );

  const form = useForm<CavaloFormValues>({
    resolver: zodResolver(cavaloSchema),
    defaultValues: {
      nome: defaultValues?.nome || "",
      breed: defaultValues?.breed || "",
      birthDate: defaultValues?.birthDate || null,
      sexo: defaultValues?.sexo || "nao_informado",
      pelagem: defaultValues?.pelagem || "nao_informado",
      peso: defaultValues?.peso || null,
      altura: defaultValues?.altura || null,
      status: defaultValues?.status || "nao_informado",
      observacoes: defaultValues?.observacoes || "",
      origem: defaultValues?.origem || "",
      numeroRegistro: defaultValues?.numeroRegistro || "",
      criador: defaultValues?.criador || "",
      proprietario: defaultValues?.proprietario || "",
      valorCompra: defaultValues?.valorCompra || null,
      dataCompra: defaultValues?.dataCompra || null,
      dataEntrada: defaultValues?.dataEntrada || null,
      dataSaida: defaultValues?.dataSaida || null,
      pai: inicializarGenealogiaDefault(
        defaultValues?.paiId,
        defaultValues?.paiNome,
        cavalos,
        "Macho",
      ),
      mae: inicializarGenealogiaDefault(
        defaultValues?.maeId,
        defaultValues?.maeNome,
        cavalos,
        "Fêmea",
      ),
      userId,
    },
  });

  // Lidar com busca ABCCC
  const handleBuscarABCCC = async (registro: string) => {
    try {
      const data = await importData(registro);
      logger.info(`Dados ABCCC recebidos para registro ${registro}`);

      // Atualizar formulário com dados retornados
      form.setValue("nome", data.nome);
      form.setValue("sexo", data.sexo === "M" ? "Macho" : "Fêmea");
      form.setValue("birthDate", data.nascimento);
      form.setValue("pelagem", data.pelagem);
      form.setValue("criador", data.criador);
      form.setValue("proprietario", data.proprietario);
      form.setValue("numeroRegistro", data.registro);
      form.setValue("pai", {
        tipo: "externo",
        cavaloSistemaId: null,
        cavaloNome: data.pai,
      });
      form.setValue("mae", {
        tipo: "externo",
        cavaloSistemaId: null,
        cavaloNome: data.mae,
      });
    } catch (error) {
      logger.error(`Erro ao buscar ABCCC: ${error.message}`);
      // Erros já são tratados pelo useABCCCImport via toast
    }
  };

  // Processar submissão
  const handleSubmit = async (values: CavaloFormValues) => {
    try {
      logger.info("Processando dados do formulário", values);

      // Transformar datas para formato ISO
      const valuesAjustados = {
        ...values,
        birthDate: values.birthDate
          ? format(values.birthDate, "yyyy-MM-dd")
          : null,
        dataCompra: values.dataCompra
          ? format(values.dataCompra, "yyyy-MM-dd")
          : null,
        dataEntrada: values.dataEntrada
          ? format(values.dataEntrada, "yyyy-MM-dd")
          : null,
        dataSaida: values.dataSaida
          ? format(values.dataSaida, "yyyy-MM-dd")
          : null,
        paiId:
          values.pai?.tipo === "sistema" && values.pai.cavaloSistemaId
            ? parseInt(values.pai.cavaloSistemaId)
            : values.pai?.tipo === "externo" && values.pai.cavaloSistemaId
              ? parseInt(values.pai.cavaloSistemaId)
              : null,
        paiNome:
          values.pai?.tipo === "externo" && !values.pai.cavaloSistemaId
            ? values.pai.cavaloNome
            : null,
        maeId:
          values.mae?.tipo === "sistema" && values.mae.cavaloSistemaId
            ? parseInt(values.mae.cavaloSistemaId)
            : values.mae?.tipo === "externo" && values.mae.cavaloSistemaId
              ? parseInt(values.mae.cavaloSistemaId)
              : null,
        maeNome:
          values.mae?.tipo === "externo" && !values.mae.cavaloSistemaId
            ? values.mae.cavaloNome
            : null,
      };

      delete valuesAjustados.pai;
      delete valuesAjustados.mae;

      // Abrir diálogo de confirmação
      setFormValues(valuesAjustados);
      setConfirmDialogOpen(true);
    } catch (error) {
      logger.error(`Erro ao processar formulário: ${error.message}`);
      toast({
        title: "Erro ao processar dados",
        description:
          "Não foi possível processar os dados do formulário. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  // Confirmar submissão
  const confirmSubmit = async () => {
    if (!formValues) return;

    try {
      await onSubmit(formValues);
      form.reset();
      setConfirmDialogOpen(false);
      toast({
        title: "Cavalo salvo",
        description: "Os dados do cavalo foram salvos com sucesso.",
      });
    } catch (error) {
      logger.error(`Erro ao salvar cavalo: ${error.message}`);
      toast({
        title: "Erro ao salvar",
        description: error.message || "Não foi possível salvar o cavalo.",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 sm:space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {/* Nome */}
            <FormField
              control={form.control}
              name="nome"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome *</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome do cavalo" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Raça */}
            <FormField
              control={form.control}
              name="breed"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Raça *</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione a raça" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Crioulo">Crioulo</SelectItem>
                      <SelectItem value="Quarto de Milha">
                        Quarto de Milha
                      </SelectItem>
                      <SelectItem value="Mangalarga">Mangalarga</SelectItem>
                      <SelectItem value="Mangalarga Marchador">
                        Mangalarga Marchador
                      </SelectItem>
                      <SelectItem value="Árabe">Árabe</SelectItem>
                      <SelectItem value="Paint Horse">Paint Horse</SelectItem>
                      <SelectItem value="Appaloosa">Appaloosa</SelectItem>
                      <SelectItem value="Campolina">Campolina</SelectItem>
                      <SelectItem value="Lusitano">Lusitano</SelectItem>
                      <SelectItem value="Pônei">Pônei</SelectItem>
                      <SelectItem value="Puro Sangue Inglês">
                        Puro Sangue Inglês
                      </SelectItem>
                      <SelectItem value="Outros">Outros</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Data Nascimento */}
            <FormField
              control={form.control}
              name="birthDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Data de Nascimento</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground",
                          )}
                          aria-label="Selecionar data de nascimento"
                        >
                          {field.value ? (
                            format(field.value, "dd/MM/yyyy", { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1970-01-01")
                        }
                        initialFocus
                        locale={ptBR}
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Sexo */}
            <FormField
              control={form.control}
              name="sexo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sexo</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o sexo" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="nao_informado">
                        Não informado
                      </SelectItem>
                      <SelectItem value="Macho">Macho</SelectItem>
                      <SelectItem value="Fêmea">Fêmea</SelectItem>
                      <SelectItem value="Garanhão">Garanhão</SelectItem>
                      <SelectItem value="Égua">Égua</SelectItem>
                      <SelectItem value="Castrado">Castrado</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Pelagem */}
            <FormField
              control={form.control}
              name="pelagem"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pelagem</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isPelagensLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            isPelagensLoading
                              ? "Carregando pelagens..."
                              : "Selecione a pelagem"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="nao_informado">
                        Não informado
                      </SelectItem>
                      {pelagens.map((pelagem) => (
                        <SelectItem key={pelagem.id} value={pelagem.nome}>
                          {pelagem.nome}
                        </SelectItem>
                      ))}
                      {!isPelagensLoading && pelagens.length === 0 && (
                        <SelectItem value="outros" disabled>
                          Nenhuma pelagem cadastrada
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Peso */}
            <FormField
              control={form.control}
              name="peso"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Peso (kg)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      placeholder="Peso em kg"
                      value={field.value ?? ""}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value ? parseFloat(e.target.value) : null,
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Altura */}
            <FormField
              control={form.control}
              name="altura"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Altura (m)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Altura em metros"
                      value={field.value ?? ""}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value ? parseFloat(e.target.value) : null,
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Status */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="nao_informado">
                        Não informado
                      </SelectItem>
                      <SelectItem value="Ativo">Ativo</SelectItem>
                      <SelectItem value="Vendido">Vendido</SelectItem>
                      <SelectItem value="Falecido">Falecido</SelectItem>
                      <SelectItem value="Doado">Doado</SelectItem>
                      <SelectItem value="Reprodução">Reprodução</SelectItem>
                      <SelectItem value="Competição">Competição</SelectItem>
                      <SelectItem value="Tratamento">Tratamento</SelectItem>
                      <SelectItem value="Aposentado">Aposentado</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Origem */}
            <FormField
              control={form.control}
              name="origem"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Haras/Fazenda de Origem</FormLabel>
                  <FormControl>
                    <Input placeholder="Origem do cavalo" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Criador */}
            <FormField
              control={form.control}
              name="criador"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Criador</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome do criador" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Proprietário */}
            <FormField
              control={form.control}
              name="proprietario"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Proprietário</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome do proprietário" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Número de Registro */}
            <FormField
              control={form.control}
              name="numeroRegistro"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Número de Registro</FormLabel>
                  <div className="flex flex-col sm:flex-row w-full items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                    <FormControl>
                      <Input
                        placeholder="Número de registro (ex: B123456)"
                        {...field}
                      />
                    </FormControl>
                    <Button
                      type="button"
                      variant="outline"
                      className="shrink-0 w-full sm:w-auto"
                      onClick={() =>
                        field.value && handleBuscarABCCC(field.value)
                      }
                      disabled={!field.value || isABCCCLoading || isSubmitting}
                      aria-label="Buscar cavalo na ABCCC"
                    >
                      {isABCCCLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          <span className="hidden sm:inline">Buscando</span>
                          <span className="sm:hidden">Buscando...</span>
                        </>
                      ) : (
                        <>
                          <Search className="mr-2 h-4 w-4" />
                          <span className="hidden sm:inline">Buscar ABCCC</span>
                          <span className="sm:hidden">Buscar na ABCCC</span>
                        </>
                      )}
                    </Button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Valor de Compra */}
            <FormField
              control={form.control}
              name="valorCompra"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Valor de Compra (R$)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Valor em reais"
                      value={field.value ?? ""}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value ? parseFloat(e.target.value) : null,
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Data de Compra */}
            <FormField
              control={form.control}
              name="dataCompra"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Data de Compra</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground",
                          )}
                          aria-label="Selecionar data de compra"
                        >
                          {field.value ? (
                            format(field.value, "dd/MM/yyyy", { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1970-01-01")
                        }
                        initialFocus
                        locale={ptBR}
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Data de Entrada */}
            <FormField
              control={form.control}
              name="dataEntrada"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Data de Entrada</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground",
                          )}
                          aria-label="Selecionar data de entrada"
                        >
                          {field.value ? (
                            format(field.value, "dd/MM/yyyy", { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1970-01-01")
                        }
                        initialFocus
                        locale={ptBR}
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Data de Saída */}
            <FormField
              control={form.control}
              name="dataSaida"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Data de Saída</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground",
                          )}
                          aria-label="Selecionar data de saída"
                        >
                          {field.value ? (
                            format(field.value, "dd/MM/yyyy", { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1970-01-01")
                        }
                        initialFocus
                        locale={ptBR}
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="space-y-4 mt-2">
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-medium">Genealogia</h3>
              <div className="border rounded-md bg-muted px-2 py-1 text-xs">
                Apenas cavalos principais são cadastrados em seu plantel
              </div>
            </div>

            <div className="border rounded-md p-4 bg-muted/20">
              <p className="text-sm text-muted-foreground mb-4">
                Ao importar um certificado ABCCC, o cavalo principal é
                adicionado ao seu plantel, enquanto os antepassados são
                registrados como cavalos externos.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Pai */}
                <FormField
                  control={form.control}
                  name="pai"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pai</FormLabel>
                      <FormControl>
                        <GenealogiaSelector
                          label="Pai"
                          sexoFiltro="Macho"
                          value={field.value}
                          cavalos={cavalos.filter((c) =>
                            ["Macho", "Garanhão"].includes(c.sexo),
                          )}
                          onChange={(value) => {
                            field.onChange(value);
                            onGenealogiaChange?.("pai", value);
                          }}
                          description="Reprodutor registrado no sistema ou externo"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Mãe */}
                <FormField
                  control={form.control}
                  name="mae"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mãe</FormLabel>
                      <FormControl>
                        <GenealogiaSelector
                          label="Mãe"
                          sexoFiltro="Fêmea"
                          value={field.value}
                          cavalos={cavalos.filter((c) =>
                            ["Fêmea", "Égua"].includes(c.sexo),
                          )}
                          onChange={(value) => {
                            field.onChange(value);
                            onGenealogiaChange?.("mae", value);
                          }}
                          description="Matriz registrada no sistema ou externa"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          {/* Observações */}
          <FormField
            control={form.control}
            name="observacoes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Observações</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Observações adicionais"
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full mt-4" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Salvando...
              </>
            ) : (
              "Salvar"
            )}
          </Button>
        </form>
      </Form>

      {/* Diálogo de Confirmação */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="w-[95vw] max-w-md sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Confirmar Cadastro</DialogTitle>
            <DialogDescription>
              Por favor, confirme os dados do cavalo antes de salvar:
              <ul className="mt-2 space-y-1 text-sm">
                <li>
                  <strong>Nome:</strong> {formValues?.nome}
                </li>
                <li>
                  <strong>Raça:</strong> {formValues?.breed}
                </li>
                <li>
                  <strong>Registro:</strong>{" "}
                  {formValues?.numeroRegistro || "Não informado"}
                </li>
              </ul>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              Cancelar
            </Button>
            <Button onClick={confirmSubmit} className="w-full sm:w-auto">
              Confirmar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
