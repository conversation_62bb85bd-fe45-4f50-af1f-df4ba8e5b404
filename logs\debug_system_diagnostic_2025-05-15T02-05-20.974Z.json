{"timestamp": "2025-05-15T02:05:20.974Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 384737280, "heapTotal": 96882688, "heapUsed": 69031456, "external": 6976932, "arrayBuffers": 60485}, "uptime": 2.978945535, "cpuUsage": {"user": 2431871, "system": 364113}, "resourceUsage": {"userCPUTime": 2431914, "systemCPUTime": 364113, "maxRSS": 375720, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99063, "majorPageFault": 0, "swappedOut": 0, "fsRead": 22256, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6018, "involuntaryContextSwitches": 8954}}