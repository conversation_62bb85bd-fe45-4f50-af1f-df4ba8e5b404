Você é um engenheiro de software sênior especialista em Node.js e React, com foco em qualidade de código, segurança e automação de testes. Seu objetivo é revisar todo o repositório do projeto — backend em Node.js/Express + frontend React — e aplicar apenas as melhorias de infraestrutura, configuração e boas práticas, **sem alterar a lógica de negócio ou quebrar funcionalidades existentes**. Por favor, gere um diff ou patch que inclua:

1. **package.json**  
   - Substituir `set NODE_ENV=production && node dist/index.js` por `cross-env NODE_ENV=production node dist/index.js`.  
2. **Tratamento de erros no Express**  
   - Instalar e importar `express-async-errors` para capturar rejections em handlers `async`.  
   - Garantir que o middleware de erro global esteja registrado após todas as rotas.  
3. **Validação de entrada**  
   - Adicionar validação de `req.body`, `req.params` e `req.query` usando **Zod** (ou **Joi**), criando schemas para cada rota existente.  
4. **Segurança e headers**  
   - Inserir `app.use(helmet())` e `app.use(cors({ origin: ['https://seu-dominio.com'] }))`.  
   - Configurar `express-rate-limit` em rotas críticas (login, cadastro).  
5. **Migrações de banco**  
   - Remover scripts manuais e consolidar tudo no **drizzle-kit** (`drizzle.config.ts` + `npm run drizzle`).  
6. **Testes automatizados**  
   - Criar estrutura de testes com **Jest** para unitários e **Supertest** para integração (SQLite in-memory).  
   - Adicionar script `test:coverage` e cobertura mínima de 80%.  
7. **CI/CD**  
   - Adicionar workflow GitHub Actions com jobs: lint (ESLint+Prettier), test (Jest+coverage), sonarcloud, snyk.  
   - Exemplo de YAML configurado com secrets.  
8. **Frontend React**  
   - Implementar `React.lazy` + `Suspense` em rotas pesadas.  
   - Adicionar testes de componentes com React Testing Library.  
9. **Lint e formatação**  
   - Incluir `.eslintrc.js` e `.prettierrc`, cobrindo regras gerais e de segurança (eslint-plugin-security).  
10. **Docker & Health Check**  
   - Criar `Dockerfile` multistage para produção.  
   - Adicionar endpoint `/health` que valide conexão ao banco.  

Gere a saída como um **diff unificado**, comentando brevemente cada bloco de mudanças para que possamos aplicar sem risco. Não modifique a lógica ou nomes de variáveis usados no código existente — apenas melhore a infraestrutura, a confiabilidade e a manutenibilidade.
