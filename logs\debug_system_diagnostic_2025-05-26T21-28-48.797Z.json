{"timestamp": "2025-05-26T21:28:48.796Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 394035200, "heapTotal": 110141440, "heapUsed": 74368456, "external": 8201124, "arrayBuffers": 225367}, "uptime": 3.511739767, "cpuUsage": {"user": 3390033, "system": 422739}, "resourceUsage": {"userCPUTime": 3390073, "systemCPUTime": 422744, "maxRSS": 384800, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102574, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8763, "involuntaryContextSwitches": 13241}}