import { useState, useMemo, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/hooks/use-auth';
import { toast } from '@/hooks/use-toast';
import { format, isValid, differenceInYears, differenceInMonths } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { z } from 'zod';

// Definir tipos para dados dos cavalos
export const cavaloSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(2, 'O nome deve ter pelo menos 2 caracteres'),
  breed: z.string().min(1, 'Raça é obrigatória'),
  birthDate: z.string().nullable().optional(),
  sexo: z.string().nullable().optional(),
  cor: z.string().nullable().optional(), // Campo de pelagem/coloração do cavalo
  peso: z.number().nullable().optional(),
  altura: z.number().nullable().optional(),
  status: z.string().nullable().optional(),
  observacoes: z.string().nullable().optional(),
  origem: z.string().nullable().optional(),
  numeroRegistro: z.string().nullable().optional(),
  paiId: z.number().nullable().optional(),
  maeId: z.number().nullable().optional(),
  criador: z.string().nullable().optional(),
  proprietario: z.string().nullable().optional(),
  valorCompra: z.number().nullable().optional(),
  dataCompra: z.string().nullable().optional(),
  dataEntrada: z.string().nullable().optional(),
  dataSaida: z.string().nullable().optional(),
  userId: z.number(),
  createdAt: z.string().nullable().optional(),
});

// Exportar tipo para uso no frontend
export type Cavalo = z.infer<typeof cavaloSchema>;

// Schema para criar/atualizar cavalos (sem id e campos gerados)
export const cavaloInputSchema = cavaloSchema.omit({ id: true, createdAt: true });
export type CavaloInput = z.infer<typeof cavaloInputSchema>;

// Interface para os parâmetros de filtro
interface CavalosFiltrosParams {
  raca?: string;
  sexo?: string;
  status?: string;
  searchTerm?: string;
}

/**
 * Hook personalizado para gerenciar operações relacionadas a cavalos
 * 
 * Este hook encapsula a lógica de negócio para buscar, adicionar, atualizar e excluir
 * cavalos, além de fornecer funções utilitárias e estado de filtros.
 * 
 * Utiliza o React Query para gerenciamento avançado de estado e cache.
 * 
 * @returns Objeto com operações CRUD, dados de cavalos, estados de filtros e utilitários
 */
export function useCavalo() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  // Estados para filtros
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRaca, setFilterRaca] = useState('');
  const [filterSexo, setFilterSexo] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  
  // Consulta para buscar cavalos do usuário atual com React Query (somente do plantel)
  const cavalosQuery = useQuery({
    queryKey: ['/api/cavalos'],
    enabled: !!user,
    staleTime: 60000, // Considerar dados atualizados por 1 minuto
    
    // Verificação de dados inválidos retornados pela API
    select: (data: any) => {
      if (!Array.isArray(data)) {
        console.error('API retornou dados inválidos para cavalos:', data);
        return [];
      }
      return data;
    },
    
    // Melhorar mensagem de erro
    onError: (error: Error) => {
      toast({
        title: 'Erro ao carregar cavalos',
        description: error.message,
        variant: 'destructive',
      });
    }
  });
  
  // Consulta para buscar todos os cavalos, incluindo externos (para genealogia)
  const todosCavalosQuery = useQuery({
    queryKey: ['/api/cavalos-genealogia'],
    enabled: !!user,
    staleTime: 60000, // Considerar dados atualizados por 1 minuto
    
    select: (data: any) => {
      if (!Array.isArray(data)) {
        console.error('API retornou dados inválidos para todos os cavalos:', data);
        return [];
      }
      return data;
    },
    
    onError: (error: Error) => {
      toast({
        title: 'Erro ao carregar lista completa de cavalos',
        description: error.message,
        variant: 'destructive',
      });
    }
  });
  
  /**
   * Busca um cavalo específico pelo ID
   */
  const useCavaloById = (id?: number) => {
    return useQuery({
      queryKey: ['/api/cavalos', id?.toString()],
      queryFn: () => apiRequest('GET', `/api/cavalos/${id}`),
      enabled: !!id && !!user,
    });
  };
  
  /**
   * Busca medidas físicas de um cavalo
   */
  const useMedidasFisicas = (horseId?: number) => {
    return useQuery({
      queryKey: ['/api/medidas-fisicas', horseId?.toString()],
      queryFn: () => apiRequest('GET', `/api/medidas-fisicas/horse/${horseId}`),
      enabled: !!horseId && !!user,
    });
  };
  
  // Mutation para adicionar um novo cavalo
  const addCavaloMutation = useMutation({
    mutationFn: (cavaloData: CavaloInput) => 
      apiRequest('POST', '/api/cavalos', cavaloData),
      
    onSuccess: (data) => {
      // Invalidar cache para recarregar dados
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      
      toast({
        title: 'Cavalo adicionado',
        description: `${data.name} foi cadastrado com sucesso.`,
        variant: 'default',
      });
    },
    
    onError: (error: Error) => {
      toast({
        title: 'Erro ao adicionar cavalo',
        description: error.message,
        variant: 'destructive',
      });
    }
  });
  
  // Mutation para atualizar um cavalo existente
  const updateCavaloMutation = useMutation({
    mutationFn: (cavaloData: Partial<Cavalo> & { id: number }) => 
      apiRequest('PUT', `/api/cavalos/${cavaloData.id}`, cavaloData),
      
    onSuccess: (data) => {
      // Atualizar cache forçando recarregamento completo
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos', data.id?.toString()] });
      // Forçar recarregamento completo removendo consultas
      queryClient.removeQueries({ queryKey: ['/api/cavalos', data.id?.toString()] });
      
      toast({
        title: 'Cavalo atualizado',
        description: `${data.name} foi atualizado com sucesso.`,
        variant: 'default',
      });
    },
    
    onError: (error: Error) => {
      toast({
        title: 'Erro ao atualizar cavalo',
        description: error.message,
        variant: 'destructive',
      });
    }
  });
  
  // Mutation para excluir um cavalo
  const deleteCavaloMutation = useMutation({
    mutationFn: (id: number) => 
      apiRequest('DELETE', `/api/cavalos/${id}`),
      
    onSuccess: (_, variables) => {
      // Remover do cache
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.removeQueries({ queryKey: ['/api/cavalos', variables.toString()] });
      
      toast({
        title: 'Cavalo excluído',
        description: 'O animal foi removido com sucesso.',
        variant: 'default',
      });
    },
    
    onError: (error: Error) => {
      toast({
        title: 'Erro ao excluir cavalo',
        description: error.message,
        variant: 'destructive',
      });
    }
  });
  
  // Formatar data para exibição amigável ao usuário
  const formatarData = useCallback((dataString: string | null): string => {
    if (!dataString) return '—';
    
    const data = new Date(dataString);
    if (!isValid(data)) return '—';
    
    return format(data, 'dd/MM/yyyy', { locale: ptBR });
  }, []);
  
  // Calcular idade do cavalo baseada na data de nascimento
  const calcularIdade = useCallback((dataString: string | null): string => {
    if (!dataString) return '—';
    
    const dataNascimento = new Date(dataString);
    if (!isValid(dataNascimento)) return '—';
    
    const hoje = new Date();
    const anos = differenceInYears(hoje, dataNascimento);
    
    if (anos < 1) {
      const meses = differenceInMonths(hoje, dataNascimento);
      return `${meses} ${meses === 1 ? 'mês' : 'meses'}`;
    }
    
    return `${anos} ${anos === 1 ? 'ano' : 'anos'}`;
  }, []);
  
  // Lista de raças únicas para filtro
  const racasUnicas = useMemo(() => {
    if (!cavalosQuery.data) return [];
    
    const racas = new Set<string>();
    cavalosQuery.data.forEach((cavalo: Cavalo) => {
      if (cavalo.breed) racas.add(cavalo.breed);
    });
    
    return Array.from(racas).sort();
  }, [cavalosQuery.data]);
  
  // Lista de sexos disponíveis para filtro
  const sexosDisponiveis = useMemo(() => {
    if (!cavalosQuery.data) return [];
    
    const sexos = new Set<string>();
    cavalosQuery.data.forEach((cavalo: Cavalo) => {
      if (cavalo.sexo) sexos.add(cavalo.sexo);
    });
    
    return Array.from(sexos).sort();
  }, [cavalosQuery.data]);
  
  // Lista de status disponíveis para filtro
  const statusDisponiveis = useMemo(() => {
    if (!cavalosQuery.data) return [];
    
    const status = new Set<string>();
    cavalosQuery.data.forEach((cavalo: Cavalo) => {
      if (cavalo.status) status.add(cavalo.status);
    });
    
    return Array.from(status).sort();
  }, [cavalosQuery.data]);
  
  // Lista de cavalos filtrada de acordo com os filtros aplicados
  const cavalosFiltrados = useMemo(() => {
    if (!cavalosQuery.data) return [];
    
    return cavalosQuery.data.filter((cavalo: Cavalo) => {
      // Filtro por termo de busca (nome do cavalo)
      const nameMatch = !searchTerm || 
        cavalo.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Filtro por raça
      const breedMatch = !filterRaca || cavalo.breed === filterRaca;
      
      // Filtro por sexo
      const sexoMatch = !filterSexo || cavalo.sexo === filterSexo;
      
      // Filtro por status
      const statusMatch = !filterStatus || cavalo.status === filterStatus;
      
      return nameMatch && breedMatch && sexoMatch && statusMatch;
    });
  }, [cavalosQuery.data, searchTerm, filterRaca, filterSexo, filterStatus]);
  
  // Limpar todos os filtros
  const limparFiltros = useCallback(() => {
    setSearchTerm('');
    setFilterRaca('');
    setFilterSexo('');
    setFilterStatus('');
  }, []);
  
  return {
    // Queries e mutations
    cavalosQuery,
    todosCavalosQuery, // Nova query que inclui cavalos externos (para genealogia)
    useCavaloById,
    useMedidasFisicas,
    addCavaloMutation,
    updateCavaloMutation,
    deleteCavaloMutation,
    
    // Estados de filtros e setters
    searchTerm,
    setSearchTerm,
    filterRaca,
    setFilterRaca,
    filterSexo,
    setFilterSexo,
    filterStatus,
    setFilterStatus,
    limparFiltros,
    
    // Dados calculados
    racasUnicas,
    sexosDisponiveis,
    statusDisponiveis,
    cavalosFiltrados,
    
    // Utilitários
    formatarData,
    calcularIdade
  };
}