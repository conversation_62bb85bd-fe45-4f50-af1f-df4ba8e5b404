import React, { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useMutation } from '@tanstack/react-query';
import { queryClient } from '@/lib/queryClient';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// ícones aliasados para evitar conflito com File global
import {
  File as FileIcon,
  UploadCloud as UploadCloudIcon,
  Database as DatabaseIcon,
  CheckCircle2 as CheckCircleIcon,
  AlertCircle as AlertCircleIcon,
  Info as InfoIcon,
  ListTree as ListTreeIcon,
  FileText as FileTextIcon,
  ChevronRight as ChevronRightIcon,
  ArrowRight as ArrowRightIcon,
  Upload as UploadIcon,
} from 'lucide-react';

// Tipo global File do DOM

interface ResumoImportacao {
  cavaloPrincipal: {
    nome: string;
    registro: string;
    novo: boolean;
    id?: number;
    sexo?: string;
    nascimento?: string;
    pelagem?: string;
    criador?: string;
    proprietario?: string;
    inspetor?: string;
  };
  familiares: Array<{
    tipo: string;
    nome: string;
    registro?: string;
    novo: boolean;
    id?: number;
    sexo?: string;
    nascimento?: string;
    pelagem?: string;
    criador?: string;
    proprietario?: string;
    inspetor?: string;
  }>;
  detalhes: {
    nomeArquivo: string;
    dataImportacao: string;
    totalAnimais: number;
    novosAnimais: number;
    atualizados: number;
    erros: string[];
    dadosComplementares?: boolean;
  };
  logDebug: string;
  dadosOriginaisPdf?: unknown;
  dadosComplementares?: unknown;
}

interface ImportadorPdfABCCCProps {
  onImportacaoCompleta?: (dados: ResumoImportacao) => void;
  className?: string;
}

export function ImportadorPdfABCCC({
  onImportacaoCompleta,
  className = '',
}: ImportadorPdfABCCCProps) {
  const { toast } = useToast();

  // estados
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isPreProcessing, setIsPreProcessing] = useState(false);
  const [processando, setProcessando] = useState(false);
  const [dadosPreVisualizacao, setDadosPreVisualizacao] = useState<ResumoImportacao | null>(null);
  const [dadosExtraidos, setDadosExtraidos] = useState<ResumoImportacao | null>(null);
  const [erroProcessamento, setErroProcessamento] = useState<string | null>(null);
  const [logMensagens, setLogMensagens] = useState<string[]>([]);
  const [tabAtiva, setTabAtiva] = useState<'upload' | 'preview' | 'resultado' | 'logs'>('upload');

  // usuário
  const [user] = useState(() => {
    const raw = localStorage.getItem('user');
    if (!raw) return null;
    try {
      return JSON.parse(raw);
    } catch {
      return null;
    }
  });

  // Helper para logs
  const adicionarLog = (mensagem: string) => {
    setLogMensagens((logs) => [...logs, `[${new Date().toLocaleTimeString()}] ${mensagem}`]);
  };

  // Mutação de pré-visualização
  const previewMutation = useMutation<
    { sucesso: boolean; dados: ResumoImportacao; mensagem?: string },
    Error,
    File
  >({
    mutationFn: async (file) => {
      if (!user?.id) throw new Error('Usuário não autenticado');
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', String(user.id));

      adicionarLog('Upload do PDF para pré-visualização...');
      setIsPreProcessing(true);

      const res = await fetch('/api/importar-pdf-crioulo/preview', {
        method: 'POST',
        body: formData,
        headers: { 'user-id': String(user.id) },
      });
      if (!res.ok) {
        const err = await res.json();
        throw new Error(err.mensagem || 'Erro na pré-visualização');
      }
      const data = await res.json();
      adicionarLog('Pré-visualização concluída');
      return data;
    },
    onSuccess: (data) => {
      setIsPreProcessing(false);
      if (data.sucesso) {
        setDadosPreVisualizacao(data.dados);
        setTabAtiva('preview');
        toast({
          title: 'Pré-visualização concluída',
          description: `${data.dados.detalhes.totalAnimais} animais identificados`,
        });
      } else {
        setErroProcessamento(data.mensagem || 'Erro desconhecido');
      }
    },
    onError: (error) => {
      setIsPreProcessing(false);
      setErroProcessamento(error.message);
      adicionarLog(`Erro: ${error.message}`);
      toast({
        title: 'Erro na pré-visualização',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Mutação de importação final
  const uploadMutation = useMutation<
    { sucesso: boolean; dados: ResumoImportacao; mensagem?: string },
    Error,
    File
  >({
    mutationFn: async (file) => {
      if (!user?.id) throw new Error('Usuário não autenticado');
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', String(user.id));

      adicionarLog('Upload do PDF para importação...');
      setProcessando(true);

      const res = await fetch('/api/importar-pdf-crioulo', {
        method: 'POST',
        body: formData,
        headers: { 'user-id': String(user.id) },
      });
      if (!res.ok) {
        const err = await res.json();
        throw new Error(err.mensagem || 'Erro na importação');
      }
      const data = await res.json();
      adicionarLog('Importação concluída');
      return data;
    },
    onSuccess: (data) => {
      setProcessando(false);
      if (data.sucesso) {
        setDadosExtraidos(data.dados);
        setTabAtiva('resultado');
        toast({
          title: 'Importação finalizada',
          description: `${data.dados.detalhes.novosAnimais} novos cadastros`,
        });
        queryClient.invalidateQueries(['/api/cavalos']);
        onImportacaoCompleta?.(data.dados);
      } else {
        setErroProcessamento(data.mensagem || 'Erro desconhecido');
      }
    },
    onError: (error) => {
      setProcessando(false);
      setErroProcessamento(error.message);
      adicionarLog(`Erro: ${error.message}`);
      toast({
        title: 'Erro na importação',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Handlers
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    setErroProcessamento(null);
    setDadosPreVisualizacao(null);
    setDadosExtraidos(null);
    setLogMensagens([]);
    if (file) {
      if (file.type !== 'application/pdf') {
        setErroProcessamento('Selecione um arquivo PDF');
        return;
      }
      adicionarLog(`Arquivo selecionado: ${file.name}`);
    }
  };

  const iniciarPreVisualizacao = () => {
    if (selectedFile) previewMutation.mutate(selectedFile);
  };

  const iniciarProcessamento = () => {
    if (selectedFile && dadosPreVisualizacao) uploadMutation.mutate(selectedFile);
  };

  return (
    <div className={`w-full ${className}`}>...</div>
  );
}
