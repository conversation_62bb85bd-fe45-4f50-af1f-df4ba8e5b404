{"timestamp": "2025-05-15T19:19:14.236Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 295428096, "heapTotal": 159662080, "heapUsed": 149232856, "external": 14298167, "arrayBuffers": 1332441}, "uptime": 68.107594507, "cpuUsage": {"user": 16071667, "system": 1068618}, "resourceUsage": {"userCPUTime": 16071726, "systemCPUTime": 1068621, "maxRSS": 835272, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 239635, "majorPageFault": 0, "swappedOut": 0, "fsRead": 30392, "fsWrite": 496, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 22169, "involuntaryContextSwitches": 22304}}