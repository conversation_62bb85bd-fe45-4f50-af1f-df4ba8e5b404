# Contexto do Projeto
Este projeto é um sistema web com:
- Backend em Node.js + Express + TypeScript
- Frontend em React + Vite + Tailwind
- Banco de dados PostgreSQL via Drizzle ORM

Já existe um sistema básico de debug/log, mas ele é simples e pouco inteligente. Preciso de uma refatoração para tornar o debug eficiente, útil e robusto para produção.

# Objetivos para melhoria do sistema de debug:
1. Implementar logging estruturado (preferencialmente usando Pino ou Winston) com níveis (`error`, `warn`, `info`, `debug`).
2. Incluir correlation IDs em cada requisição para rastreamento ponta a ponta.
3. Permitir fácil rastreamento de erros no backend, com contexto da requisição (rota, usuário, params, body).
4. Integrar sistema de captura de exceções como Sentry ou similar.
5. Incluir scripts/configuração para debugging interativo com VSCode (`node --inspect` e source maps).
6. No frontend, sugerir práticas para capturar e reportar erros críticos de forma estruturada.
7. Garantir que nada do sistema atual seja quebrado e que a refatoração seja incremental, com comentários claros no código.
8. (Opcional) Sugerir como centralizar logs e monitoramento de saúde do sistema.

# Critérios de aceitação:
- Todos os logs devem ser em JSON estruturado, contendo no mínimo timestamp, level, traceId, rota, userId (quando aplicável).
- Deve ser possível identificar rapidamente a origem e a causa de qualquer erro.
- Debug interativo funcionando em ambiente local com TypeScript.
- A integração de Sentry (ou similar) deve capturar exceções não tratadas e relatar stacktrace completo.
- Não alterar contratos de API, nomes de rotas ou lógica de negócio já existente.

# Restrições:
- Não remover código funcional existente, apenas aprimorar e refatorar.
- Não adicionar dependências pesadas sem justificativa.
- Explicar brevemente cada mudança com comentário inline.

# Formato de resposta esperado:
- Plano de ação com etapas numeradas antes de modificar o código.
- Após aprovação, apresentar um diff ou resumo dos arquivos alterados, com comentários explicando cada mudança.
- Sugestão de commit: `refactor: melhoria do sistema de debug com logging estruturado e rastreamento inteligente`

# Observação:
Se identificar outros pontos para tornar o debug ainda mais inteligente (ex: logs automáticos de performance, hooks de monitoramento de uso), pode sugerir no plano de ação.

