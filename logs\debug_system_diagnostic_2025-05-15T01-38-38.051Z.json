{"timestamp": "2025-05-15T01:38:38.051Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 294236160, "heapTotal": 104730624, "heapUsed": 62025672, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.416641048, "cpuUsage": {"user": 2217378, "system": 289583}, "resourceUsage": {"userCPUTime": 2217445, "systemCPUTime": 289583, "maxRSS": 287340, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101275, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 104, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6612, "involuntaryContextSwitches": 1400}}