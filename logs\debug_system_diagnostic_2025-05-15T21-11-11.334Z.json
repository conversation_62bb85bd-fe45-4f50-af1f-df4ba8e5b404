{"timestamp": "2025-05-15T21:11:11.334Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405561344, "heapTotal": 115384320, "heapUsed": 72677936, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.967276608, "cpuUsage": {"user": 2839027, "system": 354842}, "resourceUsage": {"userCPUTime": 2839074, "systemCPUTime": 354842, "maxRSS": 396056, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103918, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7807, "involuntaryContextSwitches": 4343}}