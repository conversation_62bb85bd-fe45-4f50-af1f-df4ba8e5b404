{"timestamp": "2025-05-23T21:31:00.505Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 256040960, "heapTotal": 116060160, "heapUsed": 88966600, "external": 8283445, "arrayBuffers": 251917}, "uptime": 2.949973113, "cpuUsage": {"user": 2993363, "system": 410948}, "resourceUsage": {"userCPUTime": 2993429, "systemCPUTime": 410957, "maxRSS": 290828, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108301, "majorPageFault": 1, "swappedOut": 0, "fsRead": 18232, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8166, "involuntaryContextSwitches": 8554}}