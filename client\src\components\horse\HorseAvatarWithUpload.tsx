/**
 * Avatar do cavalo com upload integrado para formulários
 */
import React, { useState, useRef } from 'react';
import { Camera, User, Upload, X, Loader2 } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { queryClient, apiRequest } from '@/lib/queryClient';

interface HorseAvatarWithUploadProps {
  horseId?: number; // Opcional para cavalos novos
  horseName: string;
  currentPhotoUrl?: string;
  onPhotoUploaded?: (photoUrl: string) => void;
  size?: 'sm' | 'md' | 'lg';
  showUploadButton?: boolean;
  className?: string;
}

export function HorseAvatarWithUpload({
  horseId,
  horseName,
  currentPhotoUrl,
  onPhotoUploaded,
  size = 'md',
  showUploadButton = true,
  className = ''
}: HorseAvatarWithUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const sizeClasses = {
    sm: 'h-12 w-12',
    md: 'h-20 w-20',
    lg: 'h-32 w-32'
  };

  const getHorseInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .substring(0, 2)
      .toUpperCase();
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !horseId) return;

    // Validar tipo de arquivo
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Formato inválido",
        description: "Selecione apenas arquivos de imagem.",
        variant: "destructive",
      });
      return;
    }

    // Validar tamanho (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "Arquivo muito grande",
        description: "A imagem deve ter no máximo 5MB.",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    try {
      // Criar preview local
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Fazer upload
      const formData = new FormData();
      formData.append('photo', file);
      formData.append('isProfilePhoto', 'true'); // Sempre definir como foto principal
      formData.append('description', `Foto de perfil de ${horseName}`);

      // Obter dados de autenticação do localStorage
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const token = localStorage.getItem('auth_token') || '';
      
      // Fazer upload direto com fetch para evitar problemas com headers
      const response = await fetch(`/api/cavalos/${horseId}/photos`, {
        method: 'POST',
        headers: {
          'user-id': user.id?.toString() || '',
          ...(token && { 'Authorization': `Bearer ${token}` })
        },
        credentials: 'include',
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || 'Erro ao fazer upload');
      }

      // Invalidar cache das fotos
      queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${horseId}/photos`] });
      
      const photoUrl = `/api/cavalos/${horseId}/photo`;
      onPhotoUploaded?.(photoUrl);

      toast({
        title: "Sucesso!",
        description: "Foto de perfil atualizada.",
      });

    } catch (error) {
      console.error('Erro no upload:', error);
      setPreviewUrl(null);
      toast({
        title: "Erro no upload",
        description: error instanceof Error ? error.message : "Erro desconhecido.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const displayUrl = previewUrl || currentPhotoUrl;

  return (
    <div className={`flex flex-col items-center gap-2 ${className}`}>
      <div className="relative group">
        <Avatar className={sizeClasses[size]}>
          <AvatarImage 
            src={displayUrl} 
            alt={`Foto de ${horseName}`}
            className="object-cover"
          />
          <AvatarFallback className="bg-blue-100 text-blue-700">
            <User className="h-1/2 w-1/2" />
          </AvatarFallback>
        </Avatar>

        {/* Overlay de upload */}
        {showUploadButton && horseId && (
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100">
            <Button
              size="sm"
              variant="secondary"
              className="p-2"
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                fileInputRef.current?.click();
              }}
              disabled={isUploading}
            >
              {isUploading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Camera className="h-4 w-4" />
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Botão de upload para cavalos novos */}
      {showUploadButton && !horseId && (
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
        >
          {isUploading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Enviando...
            </>
          ) : (
            <>
              <Upload className="h-4 w-4 mr-2" />
              Adicionar Foto
            </>
          )}
        </Button>
      )}

      {!horseId && (
        <p className="text-xs text-gray-500 text-center">
          Salve o cavalo primeiro para adicionar fotos
        </p>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />
    </div>
  );
}

export default HorseAvatarWithUpload;