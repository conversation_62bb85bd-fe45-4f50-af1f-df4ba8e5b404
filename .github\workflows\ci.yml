name: CI/CD Pipeline

on:
  push:
    branches: [ main, feat/observability ]
  pull_request:
    branches: [ main ]

jobs:
  lint-and-test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18, 20]
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: equigestor_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout código
      uses: actions/checkout@v4

    - name: Configurar Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Instalar dependências
      run: npm ci

    - name: Executar lint
      run: npm run lint

    - name: Executar verificação de tipos
      run: npm run check

    - name: Executar testes
      run: npm run test
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/equigestor_test
        NODE_ENV: test
        FIREBASE_PROJECT_ID: test-project
        FIREBASE_PRIVATE_KEY: test-key
        FIREBASE_CLIENT_EMAIL: <EMAIL>
        OPENAI_API_KEY: test-api-key

    - name: Build da aplicação
      run: npm run build

    - name: Upload dos artefatos de teste
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: test-results-${{ matrix.node-version }}
        path: |
          coverage/
          test-results/

  security-scan:
    runs-on: ubuntu-latest
    needs: lint-and-test
    
    steps:
    - name: Checkout código
      uses: actions/checkout@v4

    - name: Executar auditoria de segurança
      run: npm audit --audit-level=moderate

    - name: Verificar vulnerabilidades conhecidas
      run: npm audit --audit-level=high --production

  performance-test:
    runs-on: ubuntu-latest
    needs: lint-and-test
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout código
      uses: actions/checkout@v4

    - name: Configurar Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 20
        cache: 'npm'

    - name: Instalar dependências
      run: npm ci

    - name: Build da aplicação
      run: npm run build

    - name: Teste de performance básico
      run: |
        npm start &
        sleep 10
        curl -f http://localhost:5000/healthz || exit 1
        curl -f http://localhost:5000/metrics || exit 1
        pkill -f "npm start"

  deploy-ready:
    runs-on: ubuntu-latest
    needs: [lint-and-test, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deployment Ready
      run: |
        echo "✅ Todos os testes passaram"
        echo "✅ Verificações de segurança concluídas"
        echo "🚀 Aplicação pronta para deploy"
        echo "Deploy será feito através do Replit Deployments"