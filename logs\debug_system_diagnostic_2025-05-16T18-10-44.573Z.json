{"timestamp": "2025-05-16T18:10:44.572Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 262676480, "heapTotal": 116170752, "heapUsed": 72571424, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.863487013, "cpuUsage": {"user": 2807700, "system": 376810}, "resourceUsage": {"userCPUTime": 2807757, "systemCPUTime": 376818, "maxRSS": 285160, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102988, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8761, "involuntaryContextSwitches": 3601}}