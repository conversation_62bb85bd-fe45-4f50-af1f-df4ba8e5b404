// Interface para dados de cavalo

export interface Cavalo {
  id: number;
  name: string;
  breed?: string;
  birthDate?: string;
  userId: number;
  
  // Dados físicos
  peso?: number;
  altura?: number;
  sexo?: string;
  cor?: string; // Pelagem do cavalo
  
  // Status e identificação
  status?: string;
  registroNumero?: string;
  chip?: string;
  
  // Dados de criação
  criador?: string;
  proprietario?: string;
  
  // Família
  paiId?: number;
  maeId?: number;
  
  // Arquivos e observações
  fotoUrl?: string;
  notes?: string;
  
  // Metadados
  createdAt?: string;
  updatedAt?: string;
}