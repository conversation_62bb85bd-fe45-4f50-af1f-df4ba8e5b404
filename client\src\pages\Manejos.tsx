import { useState, useEffect, useMemo } from 'react';
import { Calendar, Clock, Info, Plus, Search, Trash } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { CavalosSelector } from '@/components/CavalosSelector';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { Manejo } from '@shared/schema';

// Schema de validação para os manejos
const manejoSchema = z.object({
  tipo: z.string().min(1, "O tipo de manejo é obrigatório"),
  data: z.string().min(1, "A data é obrigatória"),
  observacoes: z.string().optional().nullable(),
  horseId: z.number().optional(),
  dataVencimento: z.string().optional().nullable(),
  status: z.enum(["pendente", "em_andamento", "concluido", "cancelado"]).default("pendente"),
  prioridade: z.enum(["baixa", "normal", "alta"]).default("normal"),
});

// Definir tipo para dados do formulário
type ManejoFormData = z.infer<typeof manejoSchema>;

interface ManejosProps {
  tipoManejo?: string;
}

const Manejos = ({ tipoManejo }: ManejosProps) => {
  // Verificar parâmetros da URL para obter horseId
  const [location] = useLocation();
  const searchParams = new URLSearchParams(window.location.search);
  const selectedHorseId = searchParams.get('horseId') ? Number(searchParams.get('horseId')) : undefined;
  
  // Estados para filtros
  const [filterValue, setFilterValue] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string | null>(null);
  
  // Estado do usuário
  const [user, setUser] = useState<any>(null);
  
  // Carregar usuário do localStorage ao iniciar
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
        console.log("Usuário carregado:", parsedUser);
      } catch (error) {
        console.error("Erro ao analisar usuário:", error);
        // Redirecionar para login se houver erro
        window.location.href = '/login';
      }
    } else {
      console.log("Nenhum usuário encontrado, redirecionando para login");
      // Redirecionar para login se não houver usuário
      window.location.href = '/login';
    }
  }, []);
  
  const [submitting, setSubmitting] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Atualizar o formulário quando o horseId mudar
  useEffect(() => {
    if (selectedHorseId) {
      console.log("Atualizando horseId no formulário:", selectedHorseId);
      form.setValue('horseId', selectedHorseId);
    }
  }, [selectedHorseId]);
  
  // Buscar informações do cavalo se o horseId for fornecido
  const { data: cavaloSelecionado } = useQuery({
    queryKey: [`/api/cavalos/${selectedHorseId}`],
    queryFn: async () => {
      try {
        if (!selectedHorseId || !user) return null;
        const cavalo = await fetchWithAuth(`/api/cavalos/${selectedHorseId}`);
        return cavalo;
      } catch (error) {
        console.error("Erro ao buscar detalhes do cavalo:", error);
        return null;
      }
    },
    enabled: !!selectedHorseId && !!user
  });

  // Setup do formulário com react-hook-form e validação zod
  const form = useForm<ManejoFormData>({
    resolver: zodResolver(manejoSchema),
    defaultValues: {
      tipo: tipoManejo || "",
      data: new Date().toISOString().substring(0, 10), // Default to today's date
      observacoes: "",
      horseId: selectedHorseId || undefined,
      dataVencimento: "",
      status: "pendente",
      prioridade: "normal"
    },
  });

  // Função auxiliar para realizar requisições com o ID do usuário
  const fetchWithAuth = async (url: string, options: RequestInit = {}) => {
    if (!user) {
      throw new Error("Usuário não autenticado");
    }
    
    console.log("User ID para autenticação:", user.id);
    
    // Create a new headers object 
    const headers = new Headers(options.headers);
    // Add the user-id header
    headers.set('user-id', user.id.toString());
    
    console.log("Headers enviados:", Object.fromEntries(headers.entries()));
    
    const response = await fetch(url, {
      ...options,
      headers
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || "Ocorreu um erro na requisição");
    }

    return response.json();
  };

  // Buscar todos os cavalos do usuário
  const { data: cavalos = [] } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      try {
        const result = await fetchWithAuth('/api/cavalos');
        return result || [];
      } catch (error) {
        console.error("Error fetching cavalos:", error);
        return [];
      }
    },
    enabled: !!user
  });
  
  // Criar um mapa de ID -> Nome para fácil acesso
  const cavaloNamesMap = useMemo(() => {
    const map = new Map();
    cavalos.forEach((cavalo: any) => {
      map.set(cavalo.id, cavalo.name);
    });
    return map;
  }, [cavalos]);

  // Buscar manejos com base no filtro atual
  const { data: manejosData = [], isLoading: loading } = useQuery({
    queryKey: [selectedHorseId ? `/api/cavalos/${selectedHorseId}/manejos` : '/api/manejos'],
    queryFn: async () => {
      try {
        if (selectedHorseId) {
          console.log(`Buscando manejos para o cavalo ${selectedHorseId}`);
          const result = await fetchWithAuth(`/api/cavalos/${selectedHorseId}/manejos`);
          return result || [];
        } else {
          // Caso contrário, busca todos os manejos do usuário
          const result = await fetchWithAuth('/api/manejos');
          return result || [];
        }
      } catch (error) {
        console.error("Error fetching manejos:", error);
        toast({
          title: "Erro",
          description: "Não foi possível buscar os manejos",
          variant: "destructive"
        });
        return [] as Manejo[];
      }
    },
    enabled: !!user
  });
  
  // Aplicar todos os filtros e ordenação
  const manejos = useMemo(() => {
    return [...manejosData]
      // Filtro de rota (tipo específico de manejo)
      .filter(manejo => tipoManejo ? manejo.tipo === tipoManejo : true)
      // Filtro de tipo selecionado pelo usuário
      .filter(manejo => typeFilter ? manejo.tipo === typeFilter : true)
      // Filtro por texto de busca (nas observações ou nome do cavalo)
      .filter(manejo => {
        if (!filterValue) return true;
        const searchLower = filterValue.toLowerCase();
        
        // Buscar nas observações
        if (manejo.observacoes && manejo.observacoes.toLowerCase().includes(searchLower)) {
          return true;
        }
        
        // Buscar no nome do cavalo
        if (manejo.horseId && cavaloNamesMap.has(manejo.horseId)) {
          const horseName = cavaloNamesMap.get(manejo.horseId);
          if (horseName && horseName.toLowerCase().includes(searchLower)) {
            return true;
          }
        }
        
        // Buscar por tipo traduzido para português
        const tipoTraduzido = 
          manejo.tipo === 'veterinary' ? 'Visita Veterinária' :
          manejo.tipo === 'farrier' ? 'Ferrageamento' :
          manejo.tipo === 'vaccination' ? 'Vacinação' :
          manejo.tipo === 'deworming' ? 'Vermifugação' :
          manejo.tipo === 'dental' ? 'Cuidado Dentário' :
          manejo.tipo === 'training' ? 'Treinamento' :
          manejo.tipo === 'other' ? 'Outro' : '';
          
        if (tipoTraduzido.toLowerCase().includes(searchLower)) {
          return true;
        }
        
        return false;
      })
      // Ordenar por data (mais próxima primeiro) e depois por prioridade
      .sort((a, b) => {
        // Para priorizar datas futuras mais próximas primeiro, seguidas pelas datas passadas mais recentes
        const now = new Date().getTime();
        const dateA = new Date(a.data).getTime();
        const dateB = new Date(b.data).getTime();
        
        // Dividir em eventos futuros e passados
        const aFuturo = dateA >= now;
        const bFuturo = dateB >= now;
        
        // Se um é futuro e o outro é passado, o futuro vem primeiro
        if (aFuturo && !bFuturo) return -1;
        if (!aFuturo && bFuturo) return 1;
        
        // Se ambos são futuros, o mais próximo vem primeiro
        if (aFuturo && bFuturo) {
          return dateA - dateB; // Ordem crescente de data para eventos futuros
        }
        
        // Se ambos são passados, o mais recente (mais próximo de hoje) vem primeiro
        return dateB - dateA; // Ordem decrescente de data para eventos passados
      });
  }, [manejosData, tipoManejo, typeFilter, filterValue, cavaloNamesMap]);

  // Mutation for creating a new manejo
  const createManejoMutation = useMutation({
    mutationFn: async (manejoData: Omit<ManejoFormData, 'horseId'> & { horseId?: number }) => {
      return fetchWithAuth('/api/manejos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(manejoData)
      });
    },
    onSuccess: (data) => {
      setSubmitting(false);
      // Reset form mantendo o tipo se foi preenchido pela rota
      form.reset({
        tipo: tipoManejo || "",
        data: new Date().toISOString().substring(0, 10),
        observacoes: "",
        horseId: selectedHorseId || undefined,
        dataVencimento: "",
        status: "pendente",
        prioridade: "normal"
      });
      
      // Invalidate query to update list and also force a refresh
      if (selectedHorseId) {
        queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${selectedHorseId}/manejos`] });
        queryClient.refetchQueries({ queryKey: [`/api/cavalos/${selectedHorseId}/manejos`] });
      }
      
      // Atualização global de manejos para todas as telas
      queryClient.invalidateQueries({ queryKey: ['/api/manejos'] });
      queryClient.refetchQueries({ queryKey: ['/api/manejos'] });
      
      // Atualização forçada da lista de cavalos para refletir mudanças relacionadas
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.refetchQueries({ queryKey: ['/api/cavalos'] });
      
      toast({
        title: "Sucesso",
        description: "Manejo adicionado com sucesso!",
      });
    },
    onError: (error: any) => {
      setSubmitting(false);
      toast({
        title: "Erro",
        description: error.message || "Falha ao adicionar manejo. Tente novamente.",
        variant: "destructive",
      });
    },
  });

  // Mutation for deleting a manejo
  const deleteManejoMutation = useMutation({
    mutationFn: async (id: number) => {
      return fetchWithAuth(`/api/manejos/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userId: user?.id }) // Garantir que o userId seja incluído no corpo
      });
    },
    onSuccess: () => {
      // Invalidar consultas para atualizar a lista de manejos e forçar refresh
      queryClient.invalidateQueries({ queryKey: ['/api/manejos'] });
      queryClient.refetchQueries({ queryKey: ['/api/manejos'] });
      
      // Se houver um cavalo selecionado, também invalidar os manejos específicos do cavalo
      if (selectedHorseId) {
        queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${selectedHorseId}/manejos`] });
        queryClient.refetchQueries({ queryKey: [`/api/cavalos/${selectedHorseId}/manejos`] });
      }
      
      // Atualização forçada da lista de cavalos para refletir mudanças relacionadas
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.refetchQueries({ queryKey: ['/api/cavalos'] });
      
      toast({
        title: "Sucesso",
        description: "Manejo excluído com sucesso!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro",
        description: error.message || "Falha ao excluir manejo. Tente novamente.",
        variant: "destructive",
      });
    }
  });

  // Handle form submission to add a new manejo
  const onSubmit = async (data: ManejoFormData) => {
    if (!user) return;
    
    setSubmitting(true);
    
    const manejoData = {
      tipo: data.tipo,
      data: data.data,
      observacoes: data.observacoes || "",
      horseId: data.horseId,
      dataVencimento: data.dataVencimento || undefined,
      status: data.status,
      prioridade: data.prioridade
    };
    
    createManejoMutation.mutate(manejoData);
  };

  // Handle deleting a manejo
  const handleDelete = async (id: number) => {
    if (!confirm("Tem certeza que deseja excluir este manejo?")) return;
    deleteManejoMutation.mutate(id);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };
  
  // Calcular o status do manejo com base nas datas
  const calculateManejoStatus = (manejo: Manejo) => {
    // Se status já for concluído ou cancelado, retornamos o mesmo
    if (manejo.status === 'concluido' || manejo.status === 'cancelado') {
      return manejo.status;
    }
    
    // Se não tiver data de vencimento, mantemos o status atual
    if (!manejo.dataVencimento) {
      return manejo.status;
    }
    
    const hoje = new Date();
    const dataVencimento = new Date(manejo.dataVencimento);
    
    // Verificar se o prazo já passou
    if (dataVencimento < hoje) {
      return 'atrasado'; // Status personalizado para indicar atraso
    }
    
    // Retornar o status atual se não estiver atrasado
    return manejo.status;
  };
  
  // Obter classe CSS com base no status e prioridade
  const getManejoStatusClass = (manejo: Manejo) => {
    const status = calculateManejoStatus(manejo);
    
    if (status === 'atrasado') {
      return 'bg-red-100 text-red-800 border-red-200';
    }
    
    if (status === 'concluido') {
      return 'bg-green-100 text-green-800 border-green-200';
    }
    
    if (status === 'em_andamento') {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    }
    
    if (status === 'cancelado') {
      return 'bg-gray-100 text-gray-800 border-gray-200';
    }
    
    // Status pendente, verificar prioridade
    if (manejo.prioridade === 'alta') {
      return 'bg-orange-100 text-orange-800 border-orange-200';
    }
    
    return 'bg-yellow-100 text-yellow-800 border-yellow-200';
  };
  
  // Calcular tempo restante ou tempo atrasado em dias
  const calcularTempoRestante = (dataVencimento: string | undefined | null): { dias: number, atrasado: boolean } | null => {
    if (!dataVencimento) return null;
    
    const hoje = new Date();
    const dataVenc = new Date(dataVencimento);
    
    // Calcular diferença em dias
    const diffMs = dataVenc.getTime() - hoje.getTime();
    const diffDias = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    
    return {
      dias: Math.abs(diffDias),
      atrasado: diffDias < 0
    };
  };
  
  // Formatar a exibição do tempo restante ou atrasado
  const formatarTempoRestante = (manejo: Manejo): string => {
    if (!manejo.dataVencimento) return '';
    
    const tempo = calcularTempoRestante(manejo.dataVencimento);
    if (!tempo) return '';
    
    if (tempo.atrasado) {
      return `Atrasado há ${tempo.dias} ${tempo.dias === 1 ? 'dia' : 'dias'}`;
    } else {
      if (tempo.dias === 0) {
        return 'Vence hoje!';
      }
      return `Faltam ${tempo.dias} ${tempo.dias === 1 ? 'dia' : 'dias'}`;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Manejos</h1>
        <p className="mt-1 text-sm text-gray-500">Gerencie os manejos e tratamentos dos seus cavalos.</p>
        
        {tipoManejo && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <Info className="h-5 w-5 text-green-500 mr-2" />
              <div>
                <p className="text-sm font-medium text-green-800">
                  Filtrando manejos do tipo: <span className="font-bold capitalize">
                    {tipoManejo === 'veterinary' ? 'Visita Veterinária' :
                    tipoManejo === 'farrier' ? 'Ferrageamento' :
                    tipoManejo === 'vaccination' ? 'Vacinação' :
                    tipoManejo === 'deworming' ? 'Vermifugação' :
                    tipoManejo === 'dental' ? 'Cuidado Dentário' :
                    tipoManejo === 'training' ? 'Treinamento' :
                    tipoManejo === 'other' ? 'Outro' :
                    tipoManejo.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                </p>
              </div>
            </div>
          </div>
        )}
        
        {cavaloSelecionado && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center">
              <Info className="h-5 w-5 text-blue-500 mr-2" />
              <div>
                <p className="text-sm font-medium text-blue-800">
                  Você está adicionando um manejo para: <span className="font-bold">{cavaloSelecionado.name}</span>
                </p>
                <p className="text-xs text-blue-600 mt-0.5">
                  Raça: {cavaloSelecionado.breed} | Cadastrado em: {new Date(cavaloSelecionado.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
      
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Add new manejo form */}
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Adicionar Novo Manejo</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="tipo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo de manejo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="veterinary">Visita Veterinária</SelectItem>
                          <SelectItem value="farrier">Ferrageamento</SelectItem>
                          <SelectItem value="vaccination">Vacinação</SelectItem>
                          <SelectItem value="deworming">Vermifugação</SelectItem>
                          <SelectItem value="dental">Cuidado Dentário</SelectItem>
                          <SelectItem value="training">Treinamento</SelectItem>
                          <SelectItem value="other">Outro</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="horseId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cavalo</FormLabel>
                      <FormControl>
                        <CavalosSelector
                          value={field.value || null}
                          onChange={(id) => {
                            field.onChange(id);
                          }}
                          includeAllOption={false}
                          label="Selecionar cavalo"
                          placeholder="Digite para buscar..."
                        />
                      </FormControl>
                      <FormDescription>
                        Opcional. Deixe em branco para um manejo geral.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="data"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="dataVencimento"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data de Vencimento</FormLabel>
                      <FormDescription>
                        Data limite para quando o manejo precisa ser concluído
                      </FormDescription>
                      <FormControl>
                        <Input type="date" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o status atual" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="pendente">Pendente</SelectItem>
                          <SelectItem value="em_andamento">Em Andamento</SelectItem>
                          <SelectItem value="concluido">Concluído</SelectItem>
                          <SelectItem value="cancelado">Cancelado</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="prioridade"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Prioridade</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a prioridade" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="baixa">Baixa</SelectItem>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="alta">Alta</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="observacoes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Observações</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Observações opcionais sobre o manejo" 
                          rows={3}
                          {...field} value={field.value || ""} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <Button 
                  type="submit" 
                  disabled={submitting}
                  className="w-full"
                >
                  {submitting ? (
                    <div className="flex items-center">
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-white"></div>
                      <span>Adicionando...</span>
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <Plus className="mr-2 h-4 w-4" />
                      <span>Adicionar Manejo</span>
                    </div>
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
        
        {/* List of manejos */}
        <div className="col-span-1 lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Manejos</CardTitle>
            </CardHeader>

            <CardContent className="pb-0">
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="text-base">Filtros</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <FormLabel>Buscar</FormLabel>
                      <div className="relative mt-1">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Filtrar manejos..."
                          className="pl-8"
                          value={filterValue}
                          onChange={(e) => setFilterValue(e.target.value)}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <FormLabel>Cavalo</FormLabel>
                      <div className="mt-1">
                        <CavalosSelector 
                          value={selectedHorseId || null}
                          onChange={(id: number | null) => {
                            if (id) {
                              window.location.href = `/manejos?horseId=${id}`;
                            } else {
                              window.location.href = `/manejos`;
                            }
                          }}
                          className="w-full"
                          placeholder="Selecione um cavalo..."
                        />
                      </div>
                    </div>
                    
                    <div>
                      <FormLabel>Tipo de Manejo</FormLabel>
                      <Select 
                        value={typeFilter || "all"}
                        onValueChange={(value) => setTypeFilter(value === "all" ? null : value)}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Todos os tipos</SelectItem>
                          <SelectItem value="veterinary">Visita Veterinária</SelectItem>
                          <SelectItem value="farrier">Ferrageamento</SelectItem>
                          <SelectItem value="vaccination">Vacinação</SelectItem>
                          <SelectItem value="deworming">Vermifugação</SelectItem>
                          <SelectItem value="dental">Cuidado Dentário</SelectItem>
                          <SelectItem value="training">Treinamento</SelectItem>
                          <SelectItem value="other">Outro</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="flex justify-end mt-4">
                    <Button 
                      variant="outline" 
                      className="mr-2"
                      onClick={() => {
                        setFilterValue('');
                        setTypeFilter(null);
                        if (selectedHorseId) {
                          window.location.href = '/manejos';
                        }
                      }}
                    >
                      Limpar Filtros
                    </Button>
                    <Button
                      onClick={() => {
                        // A implementação atual já aplica filtros automaticamente
                        // Este botão está aqui apenas para feedback visual
                        toast({
                          title: "Filtros aplicados",
                          description: "Os resultados foram filtrados conforme solicitado"
                        });
                      }}
                    >
                      Aplicar Filtros
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </CardContent>

            <div className="p-4 overflow-hidden">
              {loading ? (
                <div className="p-8 flex justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : manejos.length === 0 ? (
                <div className="py-8 px-4 text-center">
                  <p className="text-gray-500">Nenhum manejo registrado ainda.</p>
                  <p className="text-xs text-gray-400 mt-2">Adicione seu primeiro manejo utilizando o formulário.</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {manejos.map((manejo: Manejo) => (
                    <div key={manejo.id} className={`p-4 hover:bg-gray-50 flex justify-between items-start ${
                      calculateManejoStatus(manejo) === 'atrasado' ? 'border-l-4 border-red-400' : 
                      manejo.prioridade === 'alta' ? 'border-l-4 border-orange-400' : ''
                    }`}>
                      <div className="w-full">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <h3 className="text-sm font-medium text-primary-600 capitalize">
                              {manejo.tipo === 'veterinary' ? 'Visita Veterinária' :
                              manejo.tipo === 'farrier' ? 'Ferrageamento' :
                              manejo.tipo === 'vaccination' ? 'Vacinação' :
                              manejo.tipo === 'deworming' ? 'Vermifugação' :
                              manejo.tipo === 'dental' ? 'Cuidado Dentário' :
                              manejo.tipo === 'training' ? 'Treinamento' :
                              manejo.tipo === 'other' ? 'Outro' :
                              manejo.tipo.replace(/([A-Z])/g, ' $1').trim()}
                            </h3>
                            <span className="ml-2 px-2 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800">
                              {formatDate(manejo.data)}
                            </span>
                          </div>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="text-gray-400 hover:text-red-500"
                            onClick={() => handleDelete(manejo.id)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        {/* Exibir cavalo associado se houver */}
                        {manejo.horseId && cavaloNamesMap.has(manejo.horseId) && (
                          <div className="mt-1">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <span className="mr-1">🐴</span>
                              Cavalo: {cavaloNamesMap.get(manejo.horseId)}
                            </span>
                          </div>
                        )}
                        {!manejo.horseId && (
                          <div className="mt-1">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              <span className="mr-1">📋</span>
                              Manejo geral (sem cavalo específico)
                            </span>
                          </div>
                        )}
                        
                        {/* Mostrar status e prioridade */}
                        <div className="flex items-center mt-1 space-x-2">
                          {manejo.status && (
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              calculateManejoStatus(manejo) === 'atrasado' ? 'bg-red-100 text-red-800' :
                              manejo.status === 'concluido' ? 'bg-green-100 text-green-800' :
                              manejo.status === 'em_andamento' ? 'bg-blue-100 text-blue-800' :
                              manejo.status === 'cancelado' ? 'bg-gray-100 text-gray-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {calculateManejoStatus(manejo) === 'atrasado' ? 'Atrasado' :
                               manejo.status === 'concluido' ? 'Concluído' :
                               manejo.status === 'em_andamento' ? 'Em Andamento' :
                               manejo.status === 'cancelado' ? 'Cancelado' : 'Pendente'}
                            </span>
                          )}
                          
                          {manejo.prioridade && (
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              manejo.prioridade === 'alta' ? 'bg-red-100 text-red-800' :
                              manejo.prioridade === 'baixa' ? 'bg-gray-100 text-gray-800' :
                              'bg-orange-100 text-orange-800'
                            }`}>
                              Prioridade: {manejo.prioridade.charAt(0).toUpperCase() + manejo.prioridade.slice(1)}
                            </span>
                          )}
                          
                          {manejo.dataVencimento && (
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              calculateManejoStatus(manejo) === 'atrasado' ? 'bg-red-100 text-red-800' : 'bg-purple-100 text-purple-800'
                            }`}>
                              {calculateManejoStatus(manejo) === 'atrasado' ? (
                                <span className="flex items-center">
                                  <Clock className="mr-1 h-3 w-3" />
                                  {formatarTempoRestante(manejo)}
                                </span>
                              ) : (
                                <span className="flex items-center">
                                  <Calendar className="mr-1 h-3 w-3" />
                                  {formatarTempoRestante(manejo)}
                                </span>
                              )}
                            </span>
                          )}
                        </div>
                        
                        {manejo.observacoes && (
                          <p className="mt-1 text-xs text-gray-500">{manejo.observacoes}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Manejos;