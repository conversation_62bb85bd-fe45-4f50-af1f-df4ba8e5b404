{"timestamp": "2025-05-15T00:31:06.551Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 379154432, "heapTotal": 91361280, "heapUsed": 69760080, "external": 6882463, "arrayBuffers": 98802}, "uptime": 1.742413782, "cpuUsage": {"user": 2238182, "system": 319830}, "resourceUsage": {"userCPUTime": 2238227, "systemCPUTime": 319837, "maxRSS": 370268, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 97187, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6664, "involuntaryContextSwitches": 5044}}