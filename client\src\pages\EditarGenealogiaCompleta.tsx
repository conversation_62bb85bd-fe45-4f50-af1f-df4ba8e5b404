import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { GitBranchPlus, Heart, User, TreePine, Save, ArrowLeft } from "lucide-react";
import { apiRequest } from '@/lib/queryClient';
import { Cavalo } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { extractCleanName } from '@/utils/genealogy';

interface EditarGenealogiaCompletaProps {
  cavaloId: string;
}

interface HorseInfo {
  id: number | null;
  name: string;
  tipo: 'sistema' | 'externo' | 'nenhum';
  sexo?: string | null;
  breed?: string | null;
  cor?: string | null;
}

interface GenealogiaDados {
  cavalo: any;
  pai: any;
  paiNome: string | null;
  mae: any;
  maeNome: string | null;
  avoPaterno: any;
  avoMaterno: any;
  avoPaternoName: string | null;
  avoMaternoName: string | null;
  avoPaterna: any;
  avoMaterna: any;
  avoPaternaName: string | null;
  avoMaternaName: string | null;
  genealogia: any;
  bisavos: any;
}

export function EditarGenealogiaCompleta({ cavaloId }: EditarGenealogiaCompletaProps) {
  const [genealogiaDados, setGenealogiaDados] = useState<GenealogiaDados | null>(null);
  const [cavalosDisponiveis, setCavalosDisponiveis] = useState<Cavalo[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();
  const [, setLocation] = useLocation();

  // Estados para edição
  const [paiSelecionado, setPaiSelecionado] = useState<{ tipo: 'sistema' | 'externo' | 'nenhum', id?: number, nome?: string }>({ tipo: 'nenhum' });
  const [maeSelecionada, setMaeSelecionada] = useState<{ tipo: 'sistema' | 'externo' | 'nenhum', id?: number, nome?: string }>({ tipo: 'nenhum' });
  const [avoPaternoSelecionado, setAvoPaternoSelecionado] = useState<{ tipo: 'sistema' | 'externo' | 'nenhum', id?: number, nome?: string }>({ tipo: 'nenhum' });
  const [avoMaternoSelecionado, setAvoMaternoSelecionado] = useState<{ tipo: 'sistema' | 'externo' | 'nenhum', id?: number, nome?: string }>({ tipo: 'nenhum' });
  const [avoPatenaSelecionada, setAvoPatenaSelecionada] = useState<{ tipo: 'sistema' | 'externo' | 'nenhum', id?: number, nome?: string }>({ tipo: 'nenhum' });
  const [avoMaternaSelecionada, setAvoMaternaSelecionada] = useState<{ tipo: 'sistema' | 'externo' | 'nenhum', id?: number, nome?: string }>({ tipo: 'nenhum' });
  
  // Bisavós (apenas nomes - campos de texto)
  const [bisavoPaternoPaterno, setBisavoPaternoPaterno] = useState('');
  const [bisavoPaternaPaterno, setBisavoPaternaPaterno] = useState('');
  const [bisavoMaternoPaterno, setBisavoMaternoPaterno] = useState('');
  const [bisavoMaternaPaterno, setBisavoMaternaPaterno] = useState('');
  const [bisavoPaternoMaterno, setBisavoPaternoMaterno] = useState('');
  const [bisavoPaternaMaterno, setBisavoPaternaMaterno] = useState('');
  const [bisavoMaternoMaterno, setBisavoMaternoMaterno] = useState('');
  const [bisavoMaternaMaterno, setBisavoMaternaMaterno] = useState('');

  useEffect(() => {
    carregarDados();
  }, [cavaloId]);

  const carregarDados = async () => {
    try {
      setLoading(true);
      
      // Buscar genealogia completa
      const genealogia = await apiRequest<GenealogiaDados>('GET', `/api/genealogia/${cavaloId}`);
      setGenealogiaDados(genealogia);
      
      // Buscar todos os cavalos para seleção
      const cavalos = await apiRequest<Cavalo[]>('GET', '/api/cavalos-genealogia');
      setCavalosDisponiveis(cavalos);
      
      // Preencher estados com dados existentes
      if (genealogia) {
        // Pai
        if (genealogia.pai) {
          setPaiSelecionado({ tipo: 'sistema', id: genealogia.pai.id, nome: genealogia.pai.name });
        } else if (genealogia.paiNome) {
          setPaiSelecionado({ tipo: 'externo', nome: genealogia.paiNome });
        }
        
        // Mãe
        if (genealogia.mae) {
          setMaeSelecionada({ tipo: 'sistema', id: genealogia.mae.id, nome: genealogia.mae.name });
        } else if (genealogia.maeNome) {
          setMaeSelecionada({ tipo: 'externo', nome: genealogia.maeNome });
        }
        
        // Avô Paterno
        if (genealogia.avoPaterno) {
          setAvoPaternoSelecionado({ tipo: 'sistema', id: genealogia.avoPaterno.id, nome: genealogia.avoPaterno.name });
        } else if (genealogia.avoPaternoName) {
          setAvoPaternoSelecionado({ tipo: 'externo', nome: genealogia.avoPaternoName });
        }
        
        // Avô Materno
        if (genealogia.avoMaterno) {
          setAvoMaternoSelecionado({ tipo: 'sistema', id: genealogia.avoMaterno.id, nome: genealogia.avoMaterno.name });
        } else if (genealogia.avoMaternoName) {
          setAvoMaternoSelecionado({ tipo: 'externo', nome: genealogia.avoMaternoName });
        }
        
        // Avó Paterna
        if (genealogia.avoPaterna) {
          setAvoPatenaSelecionada({ tipo: 'sistema', id: genealogia.avoPaterna.id, nome: genealogia.avoPaterna.name });
        } else if (genealogia.avoPaternaName) {
          setAvoPatenaSelecionada({ tipo: 'externo', nome: genealogia.avoPaternaName });
        }
        
        // Avó Materna
        if (genealogia.avoMaterna) {
          setAvoMaternaSelecionada({ tipo: 'sistema', id: genealogia.avoMaterna.id, nome: genealogia.avoMaterna.name });
        } else if (genealogia.avoMaternaName) {
          setAvoMaternaSelecionada({ tipo: 'externo', nome: genealogia.avoMaternaName });
        }
        
        // Bisavós
        if (genealogia.bisavos) {
          setBisavoPaternoPaterno(genealogia.bisavos.paterno?.paterno || '');
          setBisavoPaternaPaterno(genealogia.bisavos.paterno?.paterna || '');
          setBisavoMaternoPaterno(genealogia.bisavos.paterno?.materno || '');
          setBisavoMaternaPaterno(genealogia.bisavos.paterno?.materna || '');
          setBisavoPaternoMaterno(genealogia.bisavos.materno?.paterno || '');
          setBisavoPaternaMaterno(genealogia.bisavos.materno?.paterna || '');
          setBisavoMaternoMaterno(genealogia.bisavos.materno?.materno || '');
          setBisavoMaternaMaterno(genealogia.bisavos.materno?.materna || '');
        }
      }
      
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados da genealogia",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSalvar = async () => {
    try {
      setSaving(true);
      
      // Preparar dados para salvar
      const dadosParaSalvar = {
        // Atualizar cavalo principal
        paiId: paiSelecionado.tipo === 'sistema' ? paiSelecionado.id : null,
        paiNome: paiSelecionado.tipo === 'externo' ? paiSelecionado.nome : null,
        maeId: maeSelecionada.tipo === 'sistema' ? maeSelecionada.id : null,
        maeNome: maeSelecionada.tipo === 'externo' ? maeSelecionada.nome : null,
      };
      
      // Atualizar cavalo principal
      await apiRequest('PUT', `/api/cavalos/${cavaloId}`, dadosParaSalvar);
      
      // Preparar dados de genealogia
      const genealogiaData = {
        horseId: parseInt(cavaloId),
        avoPaternoId: avoPaternoSelecionado.tipo === 'sistema' ? avoPaternoSelecionado.id : null,
        avoPaterno: avoPaternoSelecionado.tipo === 'externo' ? avoPaternoSelecionado.nome : null,
        avoMaternoId: avoMaternoSelecionado.tipo === 'sistema' ? avoMaternoSelecionado.id : null,
        avoMaterno: avoMaternoSelecionado.tipo === 'externo' ? avoMaternoSelecionado.nome : null,
        avoPaternaId: avoPatenaSelecionada.tipo === 'sistema' ? avoPatenaSelecionada.id : null,
        avoPaterna: avoPatenaSelecionada.tipo === 'externo' ? avoPatenaSelecionada.nome : null,
        avoMaternaId: avoMaternaSelecionada.tipo === 'sistema' ? avoMaternaSelecionada.id : null,
        avoMaterna: avoMaternaSelecionada.tipo === 'externo' ? avoMaternaSelecionada.nome : null,
        // Bisavós
        bisavoPaternoPaterno,
        bisavoPaternaPaterno,
        bisavoMaternoPaterno,
        bisavoMaternaPaterno,
        bisavoPaternoMaterno,
        bisavoPaternaMaterno,
        bisavoMaternoMaterno,
        bisavoMaternaMaterno,
      };
      
      // Salvar ou atualizar genealogia
      if (genealogiaDados?.genealogia?.id) {
        await apiRequest('PUT', `/api/genealogia/${genealogiaDados.genealogia.id}`, genealogiaData);
      } else {
        await apiRequest('POST', '/api/genealogia', genealogiaData);
      }
      
      toast({
        title: "Sucesso",
        description: "Genealogia atualizada com sucesso!",
      });
      
      // Recarregar dados
      await carregarDados();
      
    } catch (error) {
      console.error('Erro ao salvar:', error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a genealogia",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const renderCavaloCard = (titulo: string, cavalo: any, icone: React.ReactNode) => (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 text-lg">
          {icone}
          {titulo}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {cavalo ? (
          <div className="space-y-2">
            <div className="font-semibold text-lg">{cavalo.name}</div>
            {cavalo.breed && <Badge variant="secondary">{cavalo.breed}</Badge>}
            {cavalo.sexo && <div className="text-sm text-muted-foreground">Sexo: {cavalo.sexo}</div>}
            {cavalo.cor && <div className="text-sm text-muted-foreground">Cor: {cavalo.cor}</div>}
            <Badge variant="outline">Do Sistema</Badge>
          </div>
        ) : (
          <div className="text-muted-foreground">Não informado</div>
        )}
      </CardContent>
    </Card>
  );

  const renderAvoCard = (titulo: string, avo: any, nomeAvo: string | null, icone: React.ReactNode) => (
    <Card className="h-full">
      <CardHeader className="pb-1">
        <CardTitle className="flex items-center gap-2 text-sm font-medium">
          {icone}
          {titulo}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-1">
        {avo ? (
          <div className="space-y-1">
            <div className="font-semibold text-sm">{avo.name}</div>
            {avo.breed && <Badge variant="secondary" className="text-xs">{avo.breed}</Badge>}
            <Badge variant="outline" className="text-xs">Do Sistema</Badge>
          </div>
        ) : nomeAvo ? (
          <div className="space-y-1">
            <div className="font-semibold text-sm">{nomeAvo}</div>
            <Badge variant="secondary" className="text-xs">Externo</Badge>
          </div>
        ) : (
          <div className="text-muted-foreground text-sm">Não informado</div>
        )}
      </CardContent>
    </Card>
  );

  const renderSeletorCavalo = (
    titulo: string,
    selecionado: { tipo: 'sistema' | 'externo' | 'nenhum', id?: number, nome?: string },
    setSelecionado: (valor: any) => void,
    filtroSexo?: string
  ) => {
    const cavalosFiltered = filtroSexo 
      ? cavalosDisponiveis.filter(c => c.sexo === filtroSexo)
      : cavalosDisponiveis;

    return (
      <div className="space-y-3">
        <Label className="text-base font-semibold">{titulo}</Label>
        
        <Select value={selecionado.tipo} onValueChange={(tipo) => {
          if (tipo === 'nenhum') {
            setSelecionado({ tipo: 'nenhum' });
          } else {
            setSelecionado({ ...selecionado, tipo });
          }
        }}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="nenhum">Não informado</SelectItem>
            <SelectItem value="sistema">Do sistema</SelectItem>
            <SelectItem value="externo">Externo</SelectItem>
          </SelectContent>
        </Select>

        {selecionado.tipo === 'sistema' && (
          <Select 
            value={selecionado.id?.toString() || ''} 
            onValueChange={(id) => {
              const cavalo = cavalosFiltered.find(c => c.id.toString() === id);
              if (cavalo) {
                setSelecionado({ tipo: 'sistema', id: cavalo.id, nome: cavalo.name });
              }
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione um cavalo" />
            </SelectTrigger>
            <SelectContent>
              {cavalosFiltered.map(cavalo => (
                <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                  {cavalo.name} ({cavalo.breed || 'Sem raça'})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {selecionado.tipo === 'externo' && (
          <Input
            placeholder="Nome do animal externo"
            value={selecionado.nome || ''}
            onChange={(e) => setSelecionado({ tipo: 'externo', nome: e.target.value })}
          />
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">Carregando dados da genealogia...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Cabeçalho */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Editar Genealogia</h1>
          <p className="text-muted-foreground">
            {genealogiaDados?.cavalo?.name} - Edição completa da árvore genealógica
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setLocation(`/cavalo/${cavaloId}`)}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
          <Button onClick={handleSalvar} disabled={saving}>
            <Save className="w-4 h-4 mr-2" />
            {saving ? 'Salvando...' : 'Salvar'}
          </Button>
        </div>
      </div>

      {/* Visualização atual */}
      <Card>
        <CardHeader>
          <CardTitle>Genealogia Atual</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Layout mais compacto - Animal centralizado */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
            {renderAvoCard("Pai", genealogiaDados?.pai, genealogiaDados?.paiNome || null, <User className="w-4 h-4 text-blue-600" />)}
            {renderCavaloCard("Animal", genealogiaDados?.cavalo, <Heart className="w-4 h-4 text-red-600" />)}
            {renderAvoCard("Mãe", genealogiaDados?.mae, genealogiaDados?.maeNome || null, <User className="w-4 h-4 text-pink-600" />)}
          </div>
          
          {/* Avós em layout compacto */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {renderAvoCard("Avô Paterno", genealogiaDados?.avoPaterno, genealogiaDados?.avoPaternoName || null, <TreePine className="w-4 h-4 text-green-600" />)}
            {renderAvoCard("Avô Materno", genealogiaDados?.avoMaterno, genealogiaDados?.avoMaternoName || null, <TreePine className="w-4 h-4 text-green-600" />)}
          </div>
        </CardContent>
      </Card>

      {/* Formulário de edição */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pais */}
        <Card>
          <CardHeader>
            <CardTitle>Pais</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {renderSeletorCavalo("Pai", paiSelecionado, setPaiSelecionado, "Macho")}
            <Separator />
            {renderSeletorCavalo("Mãe", maeSelecionada, setMaeSelecionada, "Fêmea")}
          </CardContent>
        </Card>

        {/* Avós */}
        <Card>
          <CardHeader>
            <CardTitle>Avós</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {renderSeletorCavalo("Avô Paterno", avoPaternoSelecionado, setAvoPaternoSelecionado, "Macho")}
            {renderSeletorCavalo("Avó Paterna", avoPatenaSelecionada, setAvoPatenaSelecionada, "Fêmea")}
            <Separator />
            {renderSeletorCavalo("Avô Materno", avoMaternoSelecionado, setAvoMaternoSelecionado, "Macho")}
            {renderSeletorCavalo("Avó Materna", avoMaternaSelecionada, setAvoMaternaSelecionada, "Fêmea")}
          </CardContent>
        </Card>
      </div>

      {/* Bisavós */}
      <Card>
        <CardHeader>
          <CardTitle>Bisavós</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Lado Paterno */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Lado Paterno</h3>
              <div className="space-y-3">
                <div>
                  <Label>Bisavô Paterno (pai do avô paterno)</Label>
                  <Input
                    value={bisavoPaternoPaterno}
                    onChange={(e) => setBisavoPaternoPaterno(e.target.value)}
                    placeholder="Nome do bisavô paterno"
                  />
                </div>
                <div>
                  <Label>Bisavó Paterna (mãe do avô paterno)</Label>
                  <Input
                    value={bisavoPaternaPaterno}
                    onChange={(e) => setBisavoPaternaPaterno(e.target.value)}
                    placeholder="Nome da bisavó paterna"
                  />
                </div>
                <div>
                  <Label>Bisavô Materno (pai da avó paterna)</Label>
                  <Input
                    value={bisavoMaternoPaterno}
                    onChange={(e) => setBisavoMaternoPaterno(e.target.value)}
                    placeholder="Nome do bisavô materno"
                  />
                </div>
                <div>
                  <Label>Bisavó Materna (mãe da avó paterna)</Label>
                  <Input
                    value={bisavoMaternaPaterno}
                    onChange={(e) => setBisavoMaternaPaterno(e.target.value)}
                    placeholder="Nome da bisavó materna"
                  />
                </div>
              </div>
            </div>

            {/* Lado Materno */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Lado Materno</h3>
              <div className="space-y-3">
                <div>
                  <Label>Bisavô Paterno (pai do avô materno)</Label>
                  <Input
                    value={bisavoPaternoMaterno}
                    onChange={(e) => setBisavoPaternoMaterno(e.target.value)}
                    placeholder="Nome do bisavô paterno"
                  />
                </div>
                <div>
                  <Label>Bisavó Paterna (mãe do avô materno)</Label>
                  <Input
                    value={bisavoPaternaMaterno}
                    onChange={(e) => setBisavoPaternaMaterno(e.target.value)}
                    placeholder="Nome da bisavó paterna"
                  />
                </div>
                <div>
                  <Label>Bisavô Materno (pai da avó materna)</Label>
                  <Input
                    value={bisavoMaternoMaterno}
                    onChange={(e) => setBisavoMaternoMaterno(e.target.value)}
                    placeholder="Nome do bisavô materno"
                  />
                </div>
                <div>
                  <Label>Bisavó Materna (mãe da avó materna)</Label>
                  <Input
                    value={bisavoMaternaMaterno}
                    onChange={(e) => setBisavoMaternaMaterno(e.target.value)}
                    placeholder="Nome da bisavó materna"
                  />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default EditarGenealogiaCompleta;