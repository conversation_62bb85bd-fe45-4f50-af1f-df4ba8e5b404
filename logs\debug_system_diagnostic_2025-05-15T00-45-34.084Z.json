{"timestamp": "2025-05-15T00:45:34.083Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 247304192, "heapTotal": 102633472, "heapUsed": 62205720, "external": 6815481, "arrayBuffers": 60485}, "uptime": 6.140638316, "cpuUsage": {"user": 2389275, "system": 358072}, "resourceUsage": {"userCPUTime": 2389367, "systemCPUTime": 358085, "maxRSS": 322940, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102123, "majorPageFault": 6, "swappedOut": 0, "fsRead": 42264, "fsWrite": 376, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6835, "involuntaryContextSwitches": 2742}}