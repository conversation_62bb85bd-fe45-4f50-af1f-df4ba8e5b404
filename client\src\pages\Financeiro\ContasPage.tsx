import { useState } from 'react';
import { LayoutWrapper } from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { 
  Plus, Edit, Trash2, Filter, Search, Eye,
  RefreshCw, Wallet, CreditCard, Building, 
  MoreHorizontal, DollarSign, ArrowUpDown
} from 'lucide-react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

/**
 * Interface para contas financeiras
 */
interface ContaFinanceira {
  id: number;
  nome: string;
  tipo: string;
  saldo: number;
  descricao: string;
  ativo: boolean;
  ultimaMovimentacao: string | null;
}

/**
 * Interface para movimentações de conta
 */
interface MovimentacaoConta {
  id: number;
  contaId: number;
  data: string;
  tipo: 'entrada' | 'saida';
  descricao: string;
  valor: number;
}

/**
 * Página de contas financeiras
 */
export default function ContasPage() {
  const { toast } = useToast();
  const [tipoFiltro, setTipoFiltro] = useState<string>("todos");
  const [statusFiltro, setStatusFiltro] = useState<string>("todos");
  const [termoBusca, setTermoBusca] = useState<string>("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [movimentacaoDialogOpen, setMovimentacaoDialogOpen] = useState(false);
  const [contaDetalhes, setContaDetalhes] = useState<ContaFinanceira | null>(null);
  
  const [formData, setFormData] = useState<Partial<ContaFinanceira>>({
    tipo: 'Conta Corrente',
    ativo: true,
    saldo: 0
  });
  
  const [formMovimentacao, setFormMovimentacao] = useState<Partial<MovimentacaoConta>>({
    tipo: 'entrada',
    data: new Date().toISOString().split('T')[0]
  });
  
  // Dados simulados para demonstração
  const contas: ContaFinanceira[] = [
    {
      id: 1,
      nome: 'Banco do Brasil',
      tipo: 'Conta Corrente',
      saldo: 5340.20,
      descricao: 'Conta principal para recebimentos',
      ativo: true,
      ultimaMovimentacao: '2025-03-28'
    },
    {
      id: 2,
      nome: 'Caixa',
      tipo: 'Dinheiro',
      saldo: 2189.30,
      descricao: 'Dinheiro em espécie',
      ativo: true,
      ultimaMovimentacao: '2025-03-25'
    },
    {
      id: 3,
      nome: 'Banco Itaú',
      tipo: 'Conta Corrente',
      saldo: 0,
      descricao: 'Conta para pagamentos de fornecedores',
      ativo: true,
      ultimaMovimentacao: null
    },
    {
      id: 4,
      nome: 'Investimentos',
      tipo: 'Investimento',
      saldo: 15000.00,
      descricao: 'Aplicações e reservas',
      ativo: true,
      ultimaMovimentacao: '2025-02-15'
    },
    {
      id: 5,
      nome: 'Cartão de Crédito',
      tipo: 'Cartão',
      saldo: -1250.80,
      descricao: 'Fatura mensal do cartão',
      ativo: true,
      ultimaMovimentacao: '2025-03-20'
    },
    {
      id: 6,
      nome: 'Conta Antiga',
      tipo: 'Conta Corrente',
      saldo: 0,
      descricao: 'Conta bancária desativada',
      ativo: false,
      ultimaMovimentacao: '2024-12-10'
    }
  ];
  
  // Movimentações simuladas
  const movimentacoes: MovimentacaoConta[] = [
    {
      id: 1,
      contaId: 1,
      data: '2025-03-28',
      tipo: 'entrada',
      descricao: 'Recebimento de pensão',
      valor: 2500.00
    },
    {
      id: 2,
      contaId: 1,
      data: '2025-03-20',
      tipo: 'saida',
      descricao: 'Pagamento de ração',
      valor: 850.75
    },
    {
      id: 3,
      contaId: 2,
      data: '2025-03-25',
      tipo: 'entrada',
      descricao: 'Aula de equitação',
      valor: 150.00
    },
    {
      id: 4,
      contaId: 2,
      data: '2025-03-22',
      tipo: 'saida',
      descricao: 'Ferrageamento',
      valor: 280.00
    },
    {
      id: 5,
      contaId: 5,
      data: '2025-03-20',
      tipo: 'saida',
      descricao: 'Compra de medicamentos',
      valor: 375.50
    },
  ];
  
  // Tipos de conta
  const tiposConta = [
    'Conta Corrente',
    'Dinheiro',
    'Cartão',
    'Investimento',
    'Poupança',
    'Outro'
  ];
  
  // Aplicar filtros
  const contasFiltradas = contas.filter(conta => {
    // Filtro por tipo
    const tipoMatch = tipoFiltro === 'todos' || conta.tipo === tipoFiltro;
    
    // Filtro por status (ativo/inativo)
    const statusMatch = statusFiltro === 'todos' || 
                      (statusFiltro === 'ativo' && conta.ativo) ||
                      (statusFiltro === 'inativo' && !conta.ativo);
    
    // Filtro por busca
    const buscaMatch = termoBusca === '' || 
                      conta.nome.toLowerCase().includes(termoBusca.toLowerCase()) ||
                      conta.descricao.toLowerCase().includes(termoBusca.toLowerCase());
    
    return tipoMatch && statusMatch && buscaMatch;
  });
  
  // Saldos totais
  const saldoTotal = contas
    .filter(c => c.ativo)
    .reduce((sum, c) => sum + c.saldo, 0);
  
  const saldoPositivo = contas
    .filter(c => c.ativo && c.saldo > 0)
    .reduce((sum, c) => sum + c.saldo, 0);
  
  const saldoNegativo = contas
    .filter(c => c.ativo && c.saldo < 0)
    .reduce((sum, c) => sum + c.saldo, 0);
  
  // Formatar valor monetário
  const formatMoney = (value: number) => {
    return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  };
  
  // Formatar data
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Nunca';
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR');
  };
  
  // Obter movimentações de uma conta
  const getMovimentacoesConta = (contaId: number) => {
    return movimentacoes.filter(m => m.contaId === contaId);
  };
  
  // Obter ícone do tipo de conta
  const getContaIcon = (tipo: string) => {
    switch (tipo) {
      case 'Conta Corrente':
      case 'Poupança':
        return <CreditCard className="h-4 w-4" />;
      case 'Dinheiro':
        return <Wallet className="h-4 w-4" />;
      case 'Investimento':
        return <Building className="h-4 w-4" />;
      case 'Cartão':
        return <CreditCard className="h-4 w-4" />;
      default:
        return <Wallet className="h-4 w-4" />;
    }
  };
  
  // Limpar filtros
  const limparFiltros = () => {
    setTipoFiltro("todos");
    setStatusFiltro("todos");
    setTermoBusca("");
  };
  
  // Funções para o formulário de conta
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Aqui você implementaria a lógica para salvar a conta
    toast({
      title: "Conta salva com sucesso",
      description: `${formData.nome} foi ${formData.id ? 'atualizada' : 'adicionada'} como conta financeira.`,
    });
    
    setDialogOpen(false);
    setFormData({ 
      tipo: 'Conta Corrente',
      ativo: true,
      saldo: 0
    });
  };
  
  // Funções para o formulário de movimentação
  const handleMovimentacaoSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!contaDetalhes) return;
    
    // Aqui você implementaria a lógica para registrar a movimentação
    toast({
      title: "Movimentação registrada",
      description: `${formMovimentacao.tipo === 'entrada' ? 'Entrada' : 'Saída'} de ${formatMoney(Number(formMovimentacao.valor || 0))} registrada na conta ${contaDetalhes.nome}.`,
    });
    
    setMovimentacaoDialogOpen(false);
    setFormMovimentacao({ 
      tipo: 'entrada',
      data: new Date().toISOString().split('T')[0]
    });
  };
  
  // Editar conta
  const handleEdit = (conta: ContaFinanceira) => {
    setFormData(conta);
    setDialogOpen(true);
  };
  
  // Adicionar movimentação
  const handleNovaMovimentacao = (conta: ContaFinanceira) => {
    setContaDetalhes(conta);
    setFormMovimentacao({
      ...formMovimentacao,
      contaId: conta.id
    });
    setMovimentacaoDialogOpen(true);
  };
  
  // Ver detalhes
  const handleVerDetalhes = (conta: ContaFinanceira) => {
    setContaDetalhes(conta);
    // Aqui você poderia navegar para uma página de detalhes ou abrir um modal
    toast({
      title: "Detalhes da conta",
      description: `Visualizando detalhes da conta ${conta.nome}.`,
    });
  };
  
  // Excluir conta
  const handleDelete = (id: number) => {
    // Aqui você implementaria a lógica para excluir a conta
    toast({
      title: "Conta excluída com sucesso",
      description: "A conta foi removida permanentemente.",
      variant: "destructive"
    });
  };
  
  // Alternar status da conta
  const toggleStatus = (id: number, novoStatus: boolean) => {
    // Aqui você implementaria a lógica para alternar o status
    toast({
      title: novoStatus ? "Conta ativada" : "Conta desativada",
      description: `A conta foi ${novoStatus ? 'ativada' : 'desativada'} com sucesso.`,
    });
  };
  
  // Lidar com mudanças no formulário
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Lidar com mudanças no formulário de movimentação
  const handleMovimentacaoInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormMovimentacao(prev => ({ ...prev, [name]: value }));
  };
  
  return (
    <LayoutWrapper pageTitle="Contas Financeiras" showBackButton backUrl="/financeiro">
      <div className="container mx-auto p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-blue-700">Contas Financeiras</h1>
            <p className="text-gray-600 mt-1">
              Gerencie suas contas e acompanhe seus saldos
            </p>
          </div>
          
          <div className="flex gap-3 mt-4 md:mt-0">
            <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="mr-2 h-4 w-4" /> Nova Conta
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>
                    {formData.id ? 'Editar Conta' : 'Nova Conta'}
                  </DialogTitle>
                  <DialogDescription>
                    {formData.id 
                      ? 'Edite os detalhes da conta financeira.'
                      : 'Preencha os dados para criar uma nova conta financeira.'}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="nome">Nome</Label>
                        <Input 
                          id="nome" 
                          name="nome" 
                          placeholder="Nome da conta"
                          value={formData.nome || ''}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="tipo">Tipo</Label>
                        <Select 
                          name="tipo" 
                          value={formData.tipo} 
                          onValueChange={(value) => setFormData(prev => ({ ...prev, tipo: value }))}
                        >
                          <SelectTrigger id="tipo">
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                          <SelectContent position="popper">
                            {tiposConta.map(tipo => (
                              <SelectItem key={tipo} value={tipo}>
                                {tipo}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-1.5">
                      <Label htmlFor="descricao">Descrição</Label>
                      <Input 
                        id="descricao" 
                        name="descricao" 
                        placeholder="Descrição da conta"
                        value={formData.descricao || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                    
                    <div className="flex flex-col space-y-1.5">
                      <Label htmlFor="saldo">Saldo Inicial</Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                        <Input 
                          id="saldo" 
                          name="saldo" 
                          type="number"
                          step="0.01"
                          placeholder="0,00"
                          className="pl-10"
                          value={formData.saldo || 0}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="ativo"
                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        checked={formData.ativo}
                        onChange={(e) => setFormData(prev => ({ ...prev, ativo: e.target.checked }))}
                      />
                      <Label htmlFor="ativo" className="text-sm font-medium">
                        Conta ativa
                      </Label>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setDialogOpen(false)}
                    >
                      Cancelar
                    </Button>
                    <Button type="submit">
                      {formData.id ? 'Atualizar' : 'Cadastrar'}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
            
            {/* Dialog para nova movimentação */}
            <Dialog open={movimentacaoDialogOpen} onOpenChange={setMovimentacaoDialogOpen}>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>
                    Nova Movimentação
                  </DialogTitle>
                  <DialogDescription>
                    {contaDetalhes && `Registrar movimentação na conta: ${contaDetalhes.nome}`}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleMovimentacaoSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="movTipo">Tipo</Label>
                        <Select 
                          name="tipo" 
                          value={formMovimentacao.tipo} 
                          onValueChange={(value) => setFormMovimentacao(prev => ({ ...prev, tipo: value as 'entrada' | 'saida' }))}
                        >
                          <SelectTrigger id="movTipo">
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                          <SelectContent position="popper">
                            <SelectItem value="entrada">Entrada</SelectItem>
                            <SelectItem value="saida">Saída</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="movData">Data</Label>
                        <Input 
                          id="movData" 
                          name="data" 
                          type="date"
                          value={formMovimentacao.data || ''}
                          onChange={handleMovimentacaoInputChange}
                        />
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-1.5">
                      <Label htmlFor="movDescricao">Descrição</Label>
                      <Input 
                        id="movDescricao" 
                        name="descricao" 
                        placeholder="Descrição da movimentação"
                        value={formMovimentacao.descricao || ''}
                        onChange={handleMovimentacaoInputChange}
                      />
                    </div>
                    
                    <div className="flex flex-col space-y-1.5">
                      <Label htmlFor="movValor">Valor</Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                        <Input 
                          id="movValor" 
                          name="valor" 
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0,00"
                          className="pl-10"
                          value={formMovimentacao.valor || ''}
                          onChange={handleMovimentacaoInputChange}
                        />
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setMovimentacaoDialogOpen(false)}
                    >
                      Cancelar
                    </Button>
                    <Button type="submit">
                      Registrar
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        
        {/* Cards de resumo */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card className="border-blue-100">
            <CardContent className="p-4 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-blue-700">Saldo Total</p>
                <p className={`text-2xl font-bold ${saldoTotal >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                  {formatMoney(saldoTotal)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {contas.filter(c => c.ativo).length} contas ativas
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Wallet className="h-5 w-5 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-green-100">
            <CardContent className="p-4 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-green-700">Saldo Positivo</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatMoney(saldoPositivo)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {contas.filter(c => c.ativo && c.saldo > 0).length} contas com saldo positivo
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <ArrowUpDown className="h-5 w-5 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-red-100">
            <CardContent className="p-4 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-red-700">Saldo Negativo</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatMoney(saldoNegativo)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {contas.filter(c => c.ativo && c.saldo < 0).length} contas com saldo negativo
                </p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <ArrowUpDown className="h-5 w-5 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Filtros */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <Filter className="h-5 w-5 mr-1.5" />
              Filtros
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Tipo</Label>
                <Select value={tipoFiltro} onValueChange={setTipoFiltro}>
                  <SelectTrigger>
                    <SelectValue placeholder="Tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    {tiposConta.map(tipo => (
                      <SelectItem key={tipo} value={tipo}>
                        {tipo}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Status</Label>
                <Select value={statusFiltro} onValueChange={setStatusFiltro}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    <SelectItem value="ativo">Ativos</SelectItem>
                    <SelectItem value="inativo">Inativos</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Busca</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                  <Input 
                    placeholder="Buscar..." 
                    className="pl-10"
                    value={termoBusca}
                    onChange={(e) => setTermoBusca(e.target.value)}
                  />
                </div>
              </div>
            </div>
            
            <div className="flex justify-end mt-4">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={limparFiltros}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Limpar Filtros
              </Button>
            </div>
          </CardContent>
        </Card>
        
        {/* Tabela de contas */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Contas Financeiras</CardTitle>
            <CardDescription>
              Gerencie suas contas e saldos disponíveis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">Nome</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead className="hidden md:table-cell">Descrição</TableHead>
                  <TableHead>Última Movimentação</TableHead>
                  <TableHead className="text-right">Saldo</TableHead>
                  <TableHead className="text-center">Status</TableHead>
                  <TableHead className="text-center">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {contasFiltradas.map((conta) => (
                  <TableRow key={conta.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <div className="p-1.5 rounded-full bg-blue-100 mr-2">
                          {getContaIcon(conta.tipo)}
                        </div>
                        {conta.nome}
                      </div>
                    </TableCell>
                    <TableCell>{conta.tipo}</TableCell>
                    <TableCell className="hidden md:table-cell">
                      {conta.descricao}
                    </TableCell>
                    <TableCell>
                      {formatDate(conta.ultimaMovimentacao)}
                    </TableCell>
                    <TableCell className="text-right">
                      <span className={conta.saldo >= 0 ? 'text-green-600' : 'text-red-600'}>
                        {formatMoney(conta.saldo)}
                      </span>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant={conta.ativo ? 'default' : 'secondary'}>
                        {conta.ativo ? 'Ativo' : 'Inativo'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex justify-center">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Ações</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Opções</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleEdit(conta)}>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>Editar</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleVerDetalhes(conta)}>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>Detalhes</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => toggleStatus(conta.id, !conta.ativo)}
                            >
                              <span className={`mr-2 inline-block h-3 w-3 rounded-full ${conta.ativo ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                              <span>{conta.ativo ? 'Desativar' : 'Ativar'}</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleNovaMovimentacao(conta)}
                              disabled={!conta.ativo}
                            >
                              <ArrowUpDown className="mr-2 h-4 w-4" />
                              <span>Nova Movimentação</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={() => handleDelete(conta.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              <span>Excluir</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                
                {contasFiltradas.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      Nenhuma conta encontrada com os filtros aplicados.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </LayoutWrapper>
  );
}