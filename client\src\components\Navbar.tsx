import { useState, useEffect } from "react";
import { Link, useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Tractor as Horse, 
  ClipboardList, 
  LayoutDashboard, 
  LogOut, 
  Menu,
  X, 
  Calendar,
  BarChart,
  ShieldAlert,
  FileText,
  Dna
} from "lucide-react";

/**
 * Navbar component
 * 
 * Componente de navegação principal do EquiGestor AI,
 * implementando uma navegação responsiva com suporte 
 * para exibição em dispositivos móveis e desktop.
 */
const Navbar = () => {
  const [user, setUser] = useState(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch {
        return null;
      }
    }
    return null;
  });
  
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [location] = useLocation();
  
  // Função de logout
  const handleLogout = async () => {
    try {
      localStorage.removeItem('user');
      setUser(null);
      window.location.href = '/login';
    } catch (error) {
      console.error("Erro ao sair:", error);
    }
  };

  const closeMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Lista de navegação principal
  const mainNavItems = [
    { path: "/", label: "Dashboard", icon: <LayoutDashboard className="mr-2 h-4 w-4" /> },
    { path: "/manejos", label: "Manejos", icon: <ClipboardList className="mr-2 h-4 w-4" /> },
    { path: "/agenda", label: "Agenda", icon: <Calendar className="mr-2 h-4 w-4" /> },
    { path: "/genetica", label: "Genética", icon: <Dna className="mr-2 h-4 w-4" /> }
  ];
  
  // Lista de navegação secundária (gerenciamento de cavalos)
  const horseNavItems = [
    { path: "/cavalos", label: "Meus Cavalos", icon: <Horse className="mr-2 h-4 w-4" /> },
    { path: "/estatisticas", label: "Estatísticas", icon: <BarChart className="mr-2 h-4 w-4" /> },
    { path: "/alertas", label: "Alertas", icon: <ShieldAlert className="mr-2 h-4 w-4" /> }
  ];
  
  return (
    <nav className="bg-gradient-to-r from-blue-700 to-blue-900 shadow-md sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Link href="/">
                <div className="flex items-center cursor-pointer">
                  <Horse className="h-8 w-8 text-white" />
                  <span className="ml-2 text-white font-bold text-xl tracking-tight">EquiGestor AI</span>
                </div>
              </Link>
            </div>
            
            {/* Menu principal para desktop */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-1">
                {mainNavItems.map((item) => (
                  <Link 
                    key={item.path}
                    href={item.path}
                  >
                    <div className={`px-3 py-2 rounded-md text-sm font-medium flex items-center ${
                      location === item.path 
                        ? 'bg-blue-800 bg-opacity-75 text-white' 
                        : 'text-blue-100 hover:bg-blue-600 hover:bg-opacity-75 hover:text-white'
                    } transition-colors duration-200 cursor-pointer`}>
                      {item.icon}
                      {item.label}
                    </div>
                  </Link>
                ))}
                
                {/* Dropdown (futuro) */}
                <div className="ml-3 relative">
                  <div className="h-8 border-r border-blue-500 mx-2"></div>
                </div>
                
                {/* Itens de navegação secundária */}
                {horseNavItems.map((item) => (
                  <Link 
                    key={item.path}
                    href={item.path}
                  >
                    <div className={`px-3 py-2 rounded-md text-sm font-medium flex items-center ${
                      location === item.path 
                        ? 'bg-blue-800 bg-opacity-75 text-white' 
                        : 'text-blue-100 hover:bg-blue-600 hover:bg-opacity-75 hover:text-white'
                    } transition-colors duration-200 cursor-pointer`}>
                      {item.icon}
                      {item.label}
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
          
          {/* Área do usuário para desktop */}
          <div className="hidden md:block">
            <div className="ml-4 flex items-center md:ml-6">
              {user && (
                <div className="flex items-center">
                  <div className="bg-blue-800 bg-opacity-50 rounded-full p-1 mr-3">
                    <div className="h-7 w-7 rounded-full bg-blue-200 flex items-center justify-center text-blue-800 font-medium text-sm">
                      {user.username.charAt(0).toUpperCase()}
                    </div>
                  </div>
                  <span className="text-white mr-4 text-sm font-medium">{user.username}</span>
                  <Button
                    variant="outline"
                    onClick={handleLogout}
                    className="flex items-center text-white bg-blue-800 bg-opacity-50 hover:bg-blue-700 border-blue-700"
                    size="sm"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Sair
                  </Button>
                </div>
              )}
            </div>
          </div>
          
          {/* Botão de menu móvel */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-white hover:bg-blue-600 focus:outline-none"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Menu móvel */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-blue-900 shadow-lg pb-3 pt-2">
          <div className="px-2 space-y-1 sm:px-3">
            {/* Principal */}
            <div className="text-xs text-blue-300 uppercase font-bold mt-2 mb-1 px-3">Principal</div>
            {mainNavItems.map((item) => (
              <Link 
                key={item.path} 
                href={item.path}
                onClick={closeMenu}
              >
                <div className={`block px-3 py-2 rounded-md text-base font-medium flex items-center ${
                  location === item.path 
                    ? 'bg-blue-700 text-white' 
                    : 'text-blue-100 hover:bg-blue-600 hover:text-white'
                } transition-colors duration-200 cursor-pointer`}>
                  {item.icon}
                  {item.label}
                </div>
              </Link>
            ))}
            
            {/* Gerenciamento de Cavalos */}
            <div className="text-xs text-blue-300 uppercase font-bold mt-3 mb-1 px-3">Cavalos</div>
            {horseNavItems.map((item) => (
              <Link 
                key={item.path} 
                href={item.path}
                onClick={closeMenu}
              >
                <div className={`block px-3 py-2 rounded-md text-base font-medium flex items-center ${
                  location === item.path 
                    ? 'bg-blue-700 text-white' 
                    : 'text-blue-100 hover:bg-blue-600 hover:text-white'
                } transition-colors duration-200 cursor-pointer`}>
                  {item.icon}
                  {item.label}
                </div>
              </Link>
            ))}
            
            {/* Perfil de usuário */}
            {user && (
              <div className="pt-4 border-t border-blue-800 mt-4">
                <div className="px-3 py-2 text-blue-100 flex items-center">
                  <div className="h-8 w-8 rounded-full bg-blue-200 flex items-center justify-center text-blue-800 font-medium">
                    {user.username.charAt(0).toUpperCase()}
                  </div>
                  <span className="ml-3">{user.username}</span>
                </div>
                <button
                  onClick={() => {
                    handleLogout();
                    closeMenu();
                  }}
                  className="w-full flex items-center px-3 py-2 text-base font-medium rounded-md text-blue-100 hover:bg-blue-600 hover:text-white"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Sair
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;