import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Link } from 'wouter';
import { format, parseISO } from 'date-fns';
import { pt } from 'date-fns/locale';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bar<PERSON>hart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  ChevronLeft, 
  Bar<PERSON>hart as BarChartIcon, 
  Pie<PERSON>hart as PieChartIcon, 
  Line<PERSON>hart as LineChartIcon, 
  Calculator, 
  Calendar, 
  Heart, 
  Activity 
} from 'lucide-react';

interface Reproducao {
  id: number;
  horseId: number;
  padreiroId: number | null;
  dataCobertura: string;
  tipoCobertura: string;
  estado: string;
  dataPrevistaInseminacao: string | null;
  dataPrevistaEmbriao: string | null;
  dataPrevistaParto: string | null;
  dataDiagnosticoGestacao: string | null;
  resultadoDiagnostico: string | null;
  observacoes: string | null;
  procedimentoVetId: number | null;
  eventoId: number | null;
  userId: number;
  createdAt: string;
  cavalo?: {
    name: string;
    breed: string;
  };
  padreiro?: {
    name: string;
    breed: string;
  } | null;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

const EstatisticasReproducao = () => {
  const [user, setUser] = useState<any>(null);
  const [horseId, setHorseId] = useState<number | null>(null);
  const [horseName, setHorseName] = useState<string | null>(null);
  const [estatisticas, setEstatisticas] = useState({
    totalReproducoes: 0,
    sucessos: 0,
    fracassos: 0,
    emAndamento: 0,
    taxaSucesso: 0,
    tiposCobertura: [] as {name: string, value: number}[],
    resultadosPorRaca: [] as {name: string, sucesso: number, fracasso: number}[],
    evolucaoAnual: [] as {ano: string, quantidade: number, taxa: number}[],
  });
  
  // Verificar parâmetros de URL para estatísticas específicas de uma égua
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const horseIdParam = params.get('horseId');
    
    if (horseIdParam && !isNaN(Number(horseIdParam))) {
      setHorseId(Number(horseIdParam));
    }
  }, []);

  // Carregar usuário do localStorage
  useEffect(() => {
    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
    } catch (error) {
      console.error("Erro ao carregar usuário:", error);
    }
  }, []);

  // Consultar dados do cavalo se tiver horseId
  const { data: cavaloData } = useQuery({
    queryKey: [`/api/cavalos/${horseId}`],
    enabled: !!user && !!horseId,
    queryFn: async () => {
      try {
        const result = await apiRequest('GET', `/api/cavalos/${horseId}`);
        if (result && result.name) {
          setHorseName(result.name);
        }
        return result;
      } catch (error) {
        console.error("Erro ao buscar dados do cavalo:", error);
        return null;
      }
    }
  });

  // Consultar dados de reprodução
  const { 
    data: reproducoes = [], 
    isLoading, 
    error 
  } = useQuery({
    queryKey: ['/api/reproducao', horseId],
    enabled: !!user,
    queryFn: async () => {
      try {
        let url = '/api/reproducao';
        let result;
        
        // Se tiver um horseId, filtra apenas as reproduções daquele cavalo
        if (horseId) {
          result = await apiRequest<Reproducao[]>('GET', `/api/cavalos/${horseId}/reproducao`);
        } else {
          result = await apiRequest<Reproducao[]>('GET', url);
        }
        
        // Processar estatísticas quando os dados são carregados
        processarEstatisticas(result);
        
        return result;
      } catch (error) {
        console.error("Erro ao buscar dados de reprodução:", error);
        return [];
      }
    }
  });

  // Função para processar estatísticas
  const processarEstatisticas = (dados: Reproducao[]) => {
    if (!dados || dados.length === 0) {
      return;
    }

    // Contadores básicos
    let sucessos = 0;
    let fracassos = 0;
    let emAndamento = 0;
    
    // Mapas para contagem
    const tiposCoberturaMap = new Map<string, number>();
    const resultadosPorRacaMap = new Map<string, {sucesso: number, fracasso: number}>();
    const evolucaoAnualMap = new Map<string, {quantidade: number, sucessos: number}>();
    
    // Processar cada registro
    dados.forEach(registro => {
      // Contagem de estados
      if ((registro.estado === 'gestante' || registro.estado === 'parto_realizado')) {
        sucessos++;
      } else if (registro.estado === 'aborto' || registro.estado === 'falha_embriao' || registro.estado === 'nao_prenhe') {
        fracassos++;
      } else {
        emAndamento++;
      }
      
      // Contagem por tipo de cobertura
      const tipoCobertura = registro.tipoCobertura || 'Não especificado';
      tiposCoberturaMap.set(tipoCobertura, (tiposCoberturaMap.get(tipoCobertura) || 0) + 1);
      
      // Contagem por raça
      if (registro.cavalo?.breed) {
        const raca = registro.cavalo.breed;
        const dadosRaca = resultadosPorRacaMap.get(raca) || {sucesso: 0, fracasso: 0};
        
        if (registro.estado === 'gestante' || registro.estado === 'parto_realizado') {
          dadosRaca.sucesso++;
        } else if (registro.estado === 'aborto' || registro.estado === 'falha_embriao' || registro.estado === 'nao_prenhe') {
          dadosRaca.fracasso++;
        }
        resultadosPorRacaMap.set(raca, dadosRaca);
      }
      
      // Evolução anual
      if (registro.dataCobertura) {
        try {
          const ano = format(new Date(registro.dataCobertura), 'yyyy');
          const dadosAno = evolucaoAnualMap.get(ano) || {quantidade: 0, sucessos: 0};
          
          dadosAno.quantidade++;
          if (registro.estado === 'gestante' || registro.estado === 'parto_realizado') {
            dadosAno.sucessos++;
          }
          
          evolucaoAnualMap.set(ano, dadosAno);
        } catch (e) {
          console.error("Erro ao processar data:", e);
        }
      }
    });
    
    // Converter mapas para arrays para uso nos gráficos
    const tiposCobertura = Array.from(tiposCoberturaMap.entries()).map(([name, value]) => ({name, value}));
    const resultadosPorRaca = Array.from(resultadosPorRacaMap.entries()).map(([name, {sucesso, fracasso}]) => ({name, sucesso, fracasso}));
    const evolucaoAnual = Array.from(evolucaoAnualMap.entries())
      .map(([ano, {quantidade, sucessos}]) => ({
        ano, 
        quantidade, 
        taxa: quantidade > 0 ? (sucessos / quantidade) * 100 : 0
      }))
      .sort((a, b) => a.ano.localeCompare(b.ano));
    
    // Calcular taxa de sucesso global
    const taxaSucesso = sucessos + fracassos > 0 
      ? (sucessos / (sucessos + fracassos)) * 100 
      : 0;
    
    // Atualizar o estado com as estatísticas calculadas
    setEstatisticas({
      totalReproducoes: dados.length,
      sucessos,
      fracassos,
      emAndamento,
      taxaSucesso,
      tiposCobertura,
      resultadosPorRaca,
      evolucaoAnual,
    });
  };

  // Formatar números com porcentagem
  const formatPercent = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-3xl mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-red-800">Erro ao carregar estatísticas</h3>
          <p className="mt-2 text-sm text-red-700">
            Não foi possível carregar as estatísticas de reprodução. Por favor, tente novamente mais tarde.
          </p>
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()}
            className="mt-4"
          >
            Tentar novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6">
        <Link href="/reproducao">
          <Button variant="ghost" className="mb-4 -ml-3 text-gray-500 hover:text-gray-700">
            <ChevronLeft className="mr-1 h-4 w-4" />
            Voltar para Controle Reprodutivo
          </Button>
        </Link>
        
        <h1 className="text-2xl font-bold text-gray-900">
          {horseId && horseName 
            ? `Estatísticas de Reprodução: ${horseName}` 
            : "Estatísticas de Reprodução"}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          {horseId 
            ? `Análise detalhada do histórico reprodutivo da égua ${horseName || `#${horseId}`}` 
            : "Análise detalhada do desempenho reprodutivo do plantel"}
        </p>
      </div>
      
      {reproducoes.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <Heart className="mx-auto h-12 w-12 text-red-200" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">Nenhum dado reprodutivo registrado</h3>
            <p className="mt-1 text-sm text-gray-500">
              Ainda não há registros de reprodução no sistema para análise estatística.
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Visão geral */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="bg-white">
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-500 mb-1">Total de Reproduções</p>
                  <p className="text-3xl font-bold text-blue-600">{estatisticas.totalReproducoes}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-white">
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-500 mb-1">Taxa de Sucesso</p>
                  <p className="text-3xl font-bold text-green-600">{formatPercent(estatisticas.taxaSucesso)}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-white">
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-500 mb-1">Em Andamento</p>
                  <p className="text-3xl font-bold text-amber-500">{estatisticas.emAndamento}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-white">
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-500 mb-1">Fracassos</p>
                  <p className="text-3xl font-bold text-red-500">{estatisticas.fracassos}</p>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Tabs com diferentes visualizações */}
          <Tabs defaultValue="resumo">
            <TabsList className="mb-6">
              <TabsTrigger value="resumo">Resumo</TabsTrigger>
              <TabsTrigger value="tiposCobertura">Tipos de Cobertura</TabsTrigger>
              <TabsTrigger value="racas">Análise por Raças</TabsTrigger>
              <TabsTrigger value="evolucao">Evolução Temporal</TabsTrigger>
            </TabsList>
            
            {/* Tab de Resumo */}
            <TabsContent value="resumo">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="col-span-1">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <PieChartIcon className="mr-2 h-5 w-5 text-primary" />
                      Distribuição de Resultados
                    </CardTitle>
                    <CardDescription>
                      Proporção entre sucessos, fracassos e reproduções em andamento
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80 w-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={[
                              { name: 'Sucessos', value: estatisticas.sucessos },
                              { name: 'Fracassos', value: estatisticas.fracassos },
                              { name: 'Em Andamento', value: estatisticas.emAndamento },
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={true}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            nameKey="name"
                            label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            <Cell key="sucesso" fill="#4ade80" />
                            <Cell key="fracasso" fill="#f87171" />
                            <Cell key="andamento" fill="#fbbf24" />
                          </Pie>
                          <Tooltip formatter={(value) => [value, 'Quantidade']} />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="col-span-1">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <BarChartIcon className="mr-2 h-5 w-5 text-primary" />
                      Efetividade por Tipo de Cobertura
                    </CardTitle>
                    <CardDescription>
                      Distribuição dos métodos de reprodução utilizados
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80 w-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart 
                          data={estatisticas.tiposCobertura}
                          margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="value" name="Quantidade" fill="#8884d8" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            {/* Tab de Tipos de Cobertura */}
            <TabsContent value="tiposCobertura">
              <Card>
                <CardHeader>
                  <CardTitle>Análise por Tipo de Cobertura</CardTitle>
                  <CardDescription>
                    Comparação detalhada entre os diferentes métodos de reprodução utilizados
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-96 w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={estatisticas.tiposCobertura}
                        margin={{
                          top: 20,
                          right: 30,
                          left: 20,
                          bottom: 70,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="name" 
                          angle={-45} 
                          textAnchor="end"
                          height={80}
                        />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="value" name="Quantidade" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="mt-8 space-y-4 text-sm">
                    <h4 className="font-medium text-gray-900">Interpretação dos Dados:</h4>
                    <p className="text-gray-700">
                      Este gráfico mostra a distribuição dos diferentes métodos de reprodução utilizados. 
                      A análise desses dados pode ajudar a identificar quais técnicas são mais frequentemente 
                      aplicadas no plantel, permitindo uma melhor gestão de recursos e planejamento.
                    </p>
                    <p className="text-gray-700">
                      Para uma análise mais aprofundada da efetividade de cada método, recomenda-se 
                      comparar estes dados com as taxas de sucesso específicas de cada tipo de cobertura.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Tab de Análise por Raças */}
            <TabsContent value="racas">
              <Card>
                <CardHeader>
                  <CardTitle>Desempenho Reprodutivo por Raça</CardTitle>
                  <CardDescription>
                    Análise comparativa do sucesso reprodutivo entre diferentes raças
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-96 w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={estatisticas.resultadosPorRaca}
                        margin={{
                          top: 20,
                          right: 30,
                          left: 20,
                          bottom: 70,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="name" 
                          angle={-45} 
                          textAnchor="end"
                          height={80}
                        />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="sucesso" name="Sucessos" fill="#4ade80" />
                        <Bar dataKey="fracasso" name="Fracassos" fill="#f87171" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="mt-8 space-y-4 text-sm">
                    <h4 className="font-medium text-gray-900">Análise por Raça:</h4>
                    <p className="text-gray-700">
                      Este gráfico apresenta uma comparação do desempenho reprodutivo entre diferentes raças. 
                      A análise desses dados pode revelar padrões importantes sobre quais raças apresentam 
                      melhores resultados reprodutivos sob as condições específicas do plantel.
                    </p>
                    <p className="text-gray-700">
                      Fatores como adaptabilidade ao clima, manejo nutricional específico e predisposições 
                      genéticas podem influenciar estas estatísticas. Recomenda-se considerar estes fatores
                      ao interpretar os resultados.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Tab de Evolução Temporal */}
            <TabsContent value="evolucao">
              <Card>
                <CardHeader>
                  <CardTitle>Evolução Anual</CardTitle>
                  <CardDescription>
                    Tendências de reprodução ao longo do tempo
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-96 w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={estatisticas.evolucaoAnual}
                        margin={{
                          top: 20,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="ano" />
                        <YAxis yAxisId="left" />
                        <YAxis yAxisId="right" orientation="right" domain={[0, 100]} />
                        <Tooltip />
                        <Legend />
                        <Line 
                          yAxisId="left"
                          type="monotone" 
                          dataKey="quantidade" 
                          name="Total de Reproduções" 
                          stroke="#8884d8" 
                          activeDot={{ r: 8 }} 
                        />
                        <Line 
                          yAxisId="right"
                          type="monotone" 
                          dataKey="taxa" 
                          name="Taxa de Sucesso (%)" 
                          stroke="#82ca9d" 
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="mt-8 space-y-4 text-sm">
                    <h4 className="font-medium text-gray-900">Análise Temporal:</h4>
                    <p className="text-gray-700">
                      Este gráfico mostra a evolução do número total de tentativas reprodutivas e da taxa 
                      de sucesso ao longo dos anos. Esta visualização permite identificar tendências e 
                      possíveis melhorias ou deteriorações no desempenho reprodutivo do plantel.
                    </p>
                    <p className="text-gray-700">
                      Mudanças na gestão, técnicas utilizadas, condições climáticas ou a introdução de 
                      novos animais podem explicar variações observadas neste gráfico. Recomenda-se 
                      correlacionar estas informações com eventos significativos na gestão do plantel.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
};

export default EstatisticasReproducao;