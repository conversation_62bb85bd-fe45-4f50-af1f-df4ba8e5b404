{"timestamp": "2025-05-15T19:23:03.752Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 260395008, "heapTotal": 113549312, "heapUsed": 72446840, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.715586775, "cpuUsage": {"user": 2768988, "system": 411449}, "resourceUsage": {"userCPUTime": 2769034, "systemCPUTime": 411456, "maxRSS": 295024, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101942, "majorPageFault": 0, "swappedOut": 0, "fsRead": 30104, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8270, "involuntaryContextSwitches": 5299}}