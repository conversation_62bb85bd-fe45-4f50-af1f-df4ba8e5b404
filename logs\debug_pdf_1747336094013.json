{"dados": "preview_1747336094013", "logs": "{\n  \"dados\": {\n    \"cavalo\": {\n      \"nome\": \"RZ ÉS BUENA DA CARAPUÇA\",\n      \"registro\": \"B405132\",\n      \"rp\": \"1482\",\n      \"sexo\": \"FEMEA\",\n      \"nascimento\": \"31/12/2012\",\n      \"pelagem\": \"BAIA SALGO ESQUERDO\"\n    },\n    \"pai\": {\n      \"nome\": \"BT JANELA\",\n      \"registro\": \"B136158\"\n    },\n    \"mae\": {\n      \"nome\": \"BT LAMBISGÓIA\"\n    },\n    \"avoPai\": {\n      \"nome\": \"BT LAMBORGUINE\"\n    },\n    \"avaMae\": {\n      \"nome\": \"DESCOBERTA DO ITAPORORÓ\"\n    },\n    \"avoPai2\": {\n      \"nome\": \"LOS TORUNOS CHAGUAL\"\n    },\n    \"avaMae2\": {\n      \"nome\": \"LA INVERNADA TORTOLITA IV\"\n    }\n  },\n  \"log\": \"[2025-05-15T19:08:09.482Z] [INFO] Iniciando processamento com OpenAI\\n[2025-05-15T19:08:14.012Z] [INFO] Recebido resposta da OpenAI\\n[2025-05-15T19:08:14.012Z] [DEBUG] Dados extraídos: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"RZ ÉS BUENA DA CARAPUÇA\\\",\\n    \\\"registro\\\": \\\"B405132\\\",\\n    \\\"rp\\\": \\\"1482\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"31/12/2012\\\",\\n    \\\"pelagem\\\": \\\"BAIA SALGO ESQUERDO\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"BT JANELA\\\",\\n    \\\"registro\\\": \\\"B136158\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"BT LAMBISGÓIA\\\"\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"BT LAMBORGUINE\\\"\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"DESCOBERTA DO ITAPORORÓ\\\"\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"LOS TORUNOS CHAGUAL\\\"\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"LA INVERNADA TORTOLITA IV\\\"\\n  }\\n}\\n[2025-05-15T19:08:14.013Z] [INFO] Dados do cavalo principal extraídos: RZ ÉS BUENA DA CARAPUÇA (B405132)\\n[2025-05-15T19:08:14.013Z] [INFO] Pai: BT JANELA (B136158)\\n[2025-05-15T19:08:14.013Z] [INFO] Mãe: BT LAMBISGÓIA (sem registro)\\n[2025-05-15T19:08:14.013Z] [INFO] Dados convertidos com sucesso\"\n}", "timestamp": "2025-05-15T19:08:14.013Z"}