{"timestamp": "2025-05-23T20:09:08.430Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 408334336, "heapTotal": 118792192, "heapUsed": 74358008, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.131349774, "cpuUsage": {"user": 3013014, "system": 376150}, "resourceUsage": {"userCPUTime": 3013088, "systemCPUTime": 376159, "maxRSS": 398764, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104519, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8789, "involuntaryContextSwitches": 7144}}