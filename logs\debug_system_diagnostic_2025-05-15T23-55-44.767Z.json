{"timestamp": "2025-05-15T23:55:44.767Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 394489856, "heapTotal": 108568576, "heapUsed": 89003720, "external": 8272959, "arrayBuffers": 265658}, "uptime": 1.784715109, "cpuUsage": {"user": 2885159, "system": 348241}, "resourceUsage": {"userCPUTime": 2885209, "systemCPUTime": 348247, "maxRSS": 385244, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103068, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7984, "involuntaryContextSwitches": 1835}}