{"timestamp": "2025-05-26T20:11:27.769Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 400457728, "heapTotal": 111190016, "heapUsed": 74304832, "external": 8227674, "arrayBuffers": 251917}, "uptime": 8.991705447, "cpuUsage": {"user": 3353865, "system": 462173}, "resourceUsage": {"userCPUTime": 3353865, "systemCPUTime": 462250, "maxRSS": 391072, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 107772, "majorPageFault": 16, "swappedOut": 0, "fsRead": 31608, "fsWrite": 952, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9685, "involuntaryContextSwitches": 11930}}