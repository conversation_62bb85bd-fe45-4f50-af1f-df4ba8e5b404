{"timestamp": "2025-05-16T23:44:11.375Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 263086080, "heapTotal": 116432896, "heapUsed": 73564344, "external": 8088406, "arrayBuffers": 243721}, "uptime": 2.546740567, "cpuUsage": {"user": 3151000, "system": 424905}, "resourceUsage": {"userCPUTime": 3151074, "systemCPUTime": 424905, "maxRSS": 290484, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104911, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 160, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9331, "involuntaryContextSwitches": 7942}}