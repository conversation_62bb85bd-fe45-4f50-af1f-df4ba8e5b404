{"timestamp": "2025-05-26T20:57:28.507Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 262336512, "heapTotal": 110669824, "heapUsed": 89003760, "external": 8349603, "arrayBuffers": 265658}, "uptime": 2.222095576, "cpuUsage": {"user": 3095400, "system": 410359}, "resourceUsage": {"userCPUTime": 3095448, "systemCPUTime": 410365, "maxRSS": 291568, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104961, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8506, "involuntaryContextSwitches": 4883}}