{"timestamp": "2025-05-15T22:52:16.219Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 410574848, "heapTotal": 112500736, "heapUsed": 72481576, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.124115263, "cpuUsage": {"user": 2963736, "system": 352000}, "resourceUsage": {"userCPUTime": 2963776, "systemCPUTime": 352004, "maxRSS": 400952, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108159, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 24, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8623, "involuntaryContextSwitches": 3819}}