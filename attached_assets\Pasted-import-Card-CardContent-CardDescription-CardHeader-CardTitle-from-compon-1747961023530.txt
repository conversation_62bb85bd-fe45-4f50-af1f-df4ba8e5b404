import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
// Avatar e AvatarFallback não são usados diretamente aqui
// import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Loader2,
  Edit,
  ChevronDown,
  Download,
  PlusCircle,
  LayoutDashboard,
  // Ícones não utilizados: PieChart, Dna, Search
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"; // Não usado diretamente, mas pode ser usado por componentes internos
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useState } from "react";

// Componentes personalizados para genealogia
import { GenealogyNode } from "@/components/genetics/GenealogyTree"; // Assumindo que GenealogyNode é um tipo usado ou necessário
import EnhancedGenealogyTree from "@/components/genetics/EnhancedGenealogyTree";
// GenealogyViewer e SimpleGenealogyTree não são usados diretamente aqui
// import GenealogyViewer from "@/components/genetics/GenealogyViewer";
// import SimpleGenealogyTree from "@/components/genetics/SimpleGenealogyTree";
import GenealogyEditForm from "@/components/genetics/GenealogyEditForm";
import ConsanguinityIndicator from "@/components/genetics/ConsanguinityIndicator";

// Hook personalizado para dados de genealogia
import { useGenealogyData } from "@/hooks/use-genealogy-data";

export default function GenealogiaPage() {
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<"card" | "full">("card");

  // Utilizando o hook personalizado para gerenciar os dados e lógica da genealogia
  const {
    selectedHorseId,
    setSelectedHorseId,
    selectedHorse,
    isEditDialogOpen,
    setIsEditDialogOpen,
    cavalos,
    genealogia,
    isLoadingCavalos,
    isLoadingGenealogia,
    handleEditGenealogia: handleEditarGenealogia, // a renomeação aqui é opcional, poderia usar handleEditGenealogia diretamente
    handleParentClick,
    convertToGenealogyNode,
  } = useGenealogyData();

  // Manipulador para exportar genealogia (a ser implementado)
  const handleExportarGenealogia = () => {
    toast({
      title: "Exportação em desenvolvimento",
      description:
        "A exportação de dados genealógicos será disponibilizada em breve.",
    });
  };

  // Renderizar carregamento inicial de cavalos
  if (isLoadingCavalos) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Carregando cavalos...</span>
      </div>
    );
  }

  // Constrói a árvore genealógica usando os dados do cavalo selecionado
  // Certifique-se que convertToGenealogyNode lida bem com selectedHorse sendo null/undefined
  const rootNode = selectedHorse ? convertToGenealogyNode(selectedHorse) : null;

  return (
    <div className="space-y-6 max-w-[1400px] mx-auto px-4">
      {/* Diálogo de edição de genealogia */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>
              {genealogia ? "Editar Genealogia" : "Cadastrar Genealogia"}
            </DialogTitle>
            <DialogDescription>
              {selectedHorse?.name
                ? `Gerencie informações genealógicas de ${selectedHorse.name}`
                : "Adicione informações sobre os ancestrais deste animal."}
            </DialogDescription>
          </DialogHeader>

          {selectedHorseId && (
            <GenealogyEditForm
              horseId={selectedHorseId}
              cavalos={Array.isArray(cavalos) ? cavalos : []}
              genealogiaExistente={genealogia}
              onClose={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Cabeçalho da página e controles principais */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Genealogia e Pedigree</h1>
          <p className="text-muted-foreground">
            Gerencie e visualize a árvore genealógica dos seus animais
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setViewMode(viewMode === "card" ? "full" : "card")}
          >
            <LayoutDashboard className="h-4 w-4 mr-2" />
            {viewMode === "card"
              ? "Visualização Completa"
              : "Visualização Compacta"}
          </Button>

          <Button variant="outline" onClick={handleExportarGenealogia}>
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>

          <Button
            onClick={handleEditarGenealogia}
            disabled={!selectedHorseId}
          >
            <Edit className="h-4 w-4 mr-2" />
            {genealogia ? "Editar" : "Cadastrar"}
          </Button>
        </div>
      </div>

      {/* Seleção de animal */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Selecione um Animal</CardTitle>
          <CardDescription>
            Escolha o animal para visualizar sua árvore genealógica
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row md:items-center gap-4">
            <div className="w-full md:w-1/2">
              <Select
                value={selectedHorseId?.toString() || ""}
                onValueChange={(value) => setSelectedHorseId(Number(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um cavalo" />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(cavalos) &&
                    cavalos.map((cavalo) => (
                      <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                        {cavalo.name}{" "}
                        {cavalo.breed ? `(${cavalo.breed})` : ""}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            {selectedHorse && (
              <div className="flex-1">
                <div className="flex flex-wrap gap-2">
                  {selectedHorse.breed && (
                    <Badge variant="outline" className="bg-primary/10">
                      {selectedHorse.breed}
                    </Badge>
                  )}
                  {selectedHorse.sexo && (
                    <Badge
                      variant="outline"
                      className={
                        selectedHorse.sexo === "M"
                          ? "bg-blue-50" // Assumindo que 'M' é macho
                          : "bg-pink-50" // Assumindo que qualquer outra coisa (ex: 'F') é fêmea
                      }
                    >
                      {selectedHorse.sexo === "M" ? "Macho" : "Fêmea"}
                    </Badge>
                  )}
                  {selectedHorse.cor && (
                    <Badge variant="outline" className="bg-gray-50">
                      {selectedHorse.cor}
                    </Badge>
                  )}
                  {selectedHorse.dataNascimento && (
                    <Badge variant="outline" className="bg-gray-50">
                      Nascimento:{" "}
                      {new Date(
                        selectedHorse.dataNascimento
                      ).toLocaleDateString()}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Conteúdo principal */}
      {selectedHorseId && selectedHorse && ( // Adicionado selectedHorse aqui para garantir que não é null
        <>
          {viewMode === "card" ? (
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
              {/* Árvore Genealógica Simplificada */}
              <div className="lg:col-span-8">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle>Árvore Genealógica</CardTitle>
                      {genealogia?.consanguinidade !== undefined &&
                        genealogia?.consanguinidade !== null && (
                          <ConsanguinityIndicator
                            value={Number(genealogia.consanguinidade) / 100}
                            size="md"
                          />
                        )}
                    </div>
                    <CardDescription>
                      Visualize os ancestrais do animal até a 1ª geração (pais)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isLoadingGenealogia ? (
                      <div className="flex items-center justify-center py-10">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        <span className="ml-2">Carregando genealogia...</span>
                      </div>
                    ) : genealogia || // Verifica se há dados de genealogia (pode ser suficiente)
                      (selectedHorse.paiNome || selectedHorse.maeNome) ? ( // Ou se há pelo menos um pai/mãe nomeado
                      <>
                        {/* Removido o console.log daqui */}
                        <div className="flex flex-col items-center">
                          <div className="mb-6 w-[300px] max-w-full">
                            <Card className="p-3 bg-primary/5 border-2 border-primary/20">
                              <div className="flex items-center gap-3">
                                <div
                                  className={`h-10 w-10 flex items-center justify-center rounded-full ${
                                    selectedHorse.sexo === "F" // Mais específico para Fêmea
                                      ? "bg-pink-600"
                                      : "bg-blue-600" // Default para Macho ou outros
                                  }`}
                                >
                                  <span className="text-white font-medium">
                                    {selectedHorse.name
                                      ?.substring(0, 2)
                                      .toUpperCase() || "CS"}
                                  </span>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="text-base font-medium leading-none mb-1">
                                    {selectedHorse.name || "Cavalo sem nome"}
                                  </p>
                                  <Badge variant="outline" className="mr-1">
                                    {selectedHorse.breed ||
                                      selectedHorse.raca ||
                                      "Sem raça definida"}
                                  </Badge>
                                  {(selectedHorse.cor ||
                                    selectedHorse.color) && (
                                    <Badge variant="outline">
                                      {selectedHorse.cor || selectedHorse.color}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </Card>
                          </div>

                          {/* Linha conectora vertical */}
                          {(selectedHorse.paiNome || selectedHorse.maeNome || selectedHorse.nomePai || selectedHorse.nomeMae) && (
                             <div className="h-8 w-[1px] bg-border mb-4"></div>
                          )}

                          {/* Pais */}
                          {(selectedHorse.paiNome || selectedHorse.maeNome || selectedHorse.nomePai || selectedHorse.nomeMae) && (
                            <div className="grid grid-cols-2 gap-8 w-full max-w-[600px]">
                              {/* Pai */}
                              <div className="flex flex-col items-center">
                                {selectedHorse.paiNome ||
                                selectedHorse.nomePai ? (
                                  <Card
                                    className="p-3 cursor-pointer hover:shadow-md w-full"
                                    onClick={() =>
                                      selectedHorse.paiId &&
                                      handleParentClick(selectedHorse.paiId)
                                    }
                                  >
                                    <div className="flex items-center gap-3">
                                      <div className="h-8 w-8 bg-blue-600 flex items-center justify-center rounded-full">
                                        <span className="text-white text-xs font-medium">
                                          {(
                                            selectedHorse.paiNome ||
                                            selectedHorse.nomePai
                                          )
                                            ?.substring(0, 2)
                                            .toUpperCase() || "PA"}
                                        </span>
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium leading-none truncate">
                                          {selectedHorse.paiNome ||
                                            selectedHorse.nomePai}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                          Pai
                                        </p>
                                      </div>
                                    </div>
                                  </Card>
                                ) : (
                                  <div className="p-3 border border-dashed rounded-md flex flex-col items-center justify-center h-[80px] text-muted-foreground w-full">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mb-1">
                                      <path d="M2 15c6.667-6 13.333 0 20-6" /><path d="M9 22c1-1 4-3 6-3" /><path d="M15 22c-1-1-4-3-6-3" /><path d="M12 19v-9" /><path d="M9 3l3 3 3-3" />
                                    </svg>
                                    <span className="text-xs">
                                      Pai não registrado
                                    </span>
                                  </div>
                                )}
                              </div>

                              {/* Mãe */}
                              <div className="flex flex-col items-center">
                                {selectedHorse.maeNome ||
                                selectedHorse.nomeMae ? (
                                  <Card
                                    className="p-3 cursor-pointer hover:shadow-md w-full"
                                    onClick={() =>
                                      selectedHorse.maeId &&
                                      handleParentClick(selectedHorse.maeId)
                                    }
                                  >
                                    <div className="flex items-center gap-3">
                                      <div className="h-8 w-8 bg-pink-600 flex items-center justify-center rounded-full">
                                        <span className="text-white text-xs font-medium">
                                          {(
                                            selectedHorse.maeNome ||
                                            selectedHorse.nomeMae
                                          )
                                            ?.substring(0, 2)
                                            .toUpperCase() || "MA"}
                                        </span>
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium leading-none truncate">
                                          {selectedHorse.maeNome ||
                                            selectedHorse.nomeMae}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                          Mãe
                                        </p>
                                      </div>
                                    </div>
                                  </Card>
                                ) : (
                                  <div className="p-3 border border-dashed rounded-md flex flex-col items-center justify-center h-[80px] text-muted-foreground w-full">
                                     <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mb-1">
                                      <path d="M2 15c6.667-6 13.333 0 20-6" /><path d="M9 22c1-1 4-3 6-3" /><path d="M15 22c-1-1-4-3-6-3" /><path d="M12 19v-9" /><path d="M9 3l3 3 3-3" />
                                    </svg>
                                    <span className="text-xs">
                                      Mãe não registrada
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </>
                    ) : (
                      <div className="border rounded-md p-6 bg-muted/20 flex flex-col items-center justify-center min-h-[200px]">
                        {/* <Dna className="h-12 w-12 text-muted-foreground mb-4" /> Ícone Dna não importado mais */}
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-git-fork text-muted-foreground mb-4">
                          <circle cx="12" cy="18" r="3"/><circle cx="6" cy="6" r="3"/><circle cx="18" cy="6" r="3"/><path d="M18 9v1a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2V9"/><path d="M12 12v3"/>
                        </svg>
                        <h3 className="text-lg font-medium mb-1">
                          Genealogia não encontrada
                        </h3>
                        <p className="text-sm text-muted-foreground text-center max-w-md mb-4">
                          Não há dados genealógicos para este animal ou eles
                          ainda não foram carregados. Tente cadastrar se não houver.
                        </p>
                         <Button
                            variant="outline"
                            onClick={handleEditarGenealogia} // Usa a função do hook
                          >
                            <PlusCircle className="h-4 w-4 mr-2" />
                            Cadastrar Genealogia
                          </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Análise de Consanguinidade */}
              <div className="lg:col-span-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Análise de Consanguinidade</CardTitle>
                    <CardDescription>
                      Avaliação do coeficiente de consanguinidade
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {genealogia ? (
                      <div className="space-y-6">
                        <div className="flex justify-center">
                          <ConsanguinityIndicator
                            value={
                              genealogia.consanguinidade
                                ? genealogia.consanguinidade / 100
                                : 0
                            }
                            size="lg"
                            showLabel={true}
                          />
                        </div>

                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-1">
                              Nível de Consanguinidade
                            </h4>
                            <p className="text-sm">
                              {genealogia.consanguinidade === 0
                                ? "Não há consanguinidade detectada."
                                : genealogia.consanguinidade <= 3.125
                                ? "Nível baixo de consanguinidade, dentro dos padrões aceitáveis para cruzamentos."
                                : genealogia.consanguinidade <= 12.5
                                ? "Nível moderado de consanguinidade. Recomenda-se monitorar próximos cruzamentos."
                                : "Nível alto de consanguinidade. Recomenda-se evitar novos cruzamentos endogâmicos."}
                            </p>
                          </div>

                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-1">
                              Pedigree Completo
                            </h4>
                            <p className="text-sm">
                              {genealogia.bisavosConhecidos !== undefined
                                ? `${(
                                    (genealogia.bisavosConhecidos / 8) *
                                    100
                                  ).toFixed(0)}% completo (${
                                    genealogia.bisavosConhecidos
                                  }/8 bisavós conhecidos)`
                                : "Informação não disponível"}
                            </p>
                          </div>

                          {genealogia.observacoes && (
                            <Collapsible>
                              <CollapsibleTrigger className="flex items-center w-full text-sm font-medium text-gray-500 hover:text-primary">
                                <ChevronDown className="h-4 w-4 mr-2 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                                Observações
                              </CollapsibleTrigger>
                              <CollapsibleContent className="mt-2 text-sm bg-muted/20 p-3 rounded-md border">
                                {genealogia.observacoes}
                              </CollapsibleContent>
                            </Collapsible>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground text-sm">
                        <p className="mb-4">
                          Nenhum dado genealógico cadastrado para análise.
                        </p>
                        <Button
                          variant="outline"
                          onClick={handleEditarGenealogia}
                        >
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Cadastrar Genealogia
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : (
            // Visualização Completa (EnhancedGenealogyTree)
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Árvore Genealógica Completa</CardTitle>
                  {genealogia?.consanguinidade !== undefined &&
                    genealogia?.consanguinidade !== null && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          Consanguinidade:
                        </span>
                        <ConsanguinityIndicator
                          value={genealogia.consanguinidade / 100}
                          size="md"
                        />
                      </div>
                    )}
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {isLoadingGenealogia ? (
                  <div className="flex items-center justify-center py-20 min-h-[400px]">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">
                      Carregando árvore genealógica...
                    </span>
                  </div>
                ) : rootNode ? ( // Verifica se rootNode existe e é válido
                  <EnhancedGenealogyTree
                    rootNode={rootNode}
                    onNodeClick={handleParentClick}
                    maxGenerations={4} // Ou qualquer outro valor desejado
                    className="h-[700px] w-full" // Ajuste a altura conforme necessário
                  />
                ) : (
                  <div className="text-center py-20 px-4 min-h-[400px] flex flex-col items-center justify-center">
                    {/* <Dna className="h-12 w-12 text-muted-foreground mb-4" /> */}
                     <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-git-fork text-muted-foreground mb-4">
                        <circle cx="12" cy="18" r="3"/><circle cx="6" cy="6" r="3"/><circle cx="18" cy="6" r="3"/><path d="M18 9v1a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2V9"/><path d="M12 12v3"/>
                      </svg>
                    <p className="text-muted-foreground mb-4">
                      Nenhum dado genealógico para construir a árvore completa
                      deste animal.
                    </p>
                    <Button onClick={handleEditarGenealogia}>
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Cadastrar Genealogia
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}
      {/* Mensagem caso nenhum animal seja selecionado */}
      {!selectedHorseId && !isLoadingCavalos && (
         <div className="border rounded-md p-10 bg-muted/30 flex flex-col items-center justify-center mt-8 min-h-[300px]">
            <LayoutDashboard className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
            <h3 className="text-xl font-medium mb-2 text-muted-foreground">Nenhum animal selecionado</h3>
            <p className="text-sm text-muted-foreground text-center max-w-md">
              Por favor, selecione um animal na lista acima para visualizar ou gerenciar sua genealogia.
            </p>
          </div>
      )}
    </div>
  );
}