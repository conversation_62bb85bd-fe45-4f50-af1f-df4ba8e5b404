{"timestamp": "2025-05-16T23:15:05.518Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404242432, "heapTotal": 117481472, "heapUsed": 73392992, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.596105159, "cpuUsage": {"user": 3219930, "system": 429073}, "resourceUsage": {"userCPUTime": 3219988, "systemCPUTime": 429073, "maxRSS": 394768, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104948, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 16, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8734, "involuntaryContextSwitches": 8156}}