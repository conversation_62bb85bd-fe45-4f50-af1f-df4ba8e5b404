{"timestamp": "2025-05-19T20:21:09.436Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402984960, "heapTotal": 119209984, "heapUsed": 100060088, "external": 8658143, "arrayBuffers": 307553}, "uptime": 1.894961668, "cpuUsage": {"user": 2776050, "system": 371337}, "resourceUsage": {"userCPUTime": 2776089, "systemCPUTime": 371342, "maxRSS": 393540, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103992, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8178, "involuntaryContextSwitches": 2452}}