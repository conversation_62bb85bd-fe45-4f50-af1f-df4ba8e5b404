

# Prompt para Integração do Assistente Virtual no EquiGestor AI - respeitando a nossa arquitetura.

**Objetivo:** Integrar um **assistente virtual (chatbot)** ao aplicativo **EquiHorse** (frontend em React, backend em Node.js com PostgreSQL) de forma robusta e profissional. O assistente deverá usar a API da OpenAI (GPT-4 se disponível) para fornecer respostas contextuais e personalizadas. A seguir, detalhamos os requisitos e orientações para que o Replit Agent gere a estrutura de código necessária, abrangendo frontend, backend, banco de dados, suporte offline e boas práticas de segurança.

## Funcionalidades e Requisitos Principais

1. **API OpenAI para Respostas Contextuais:** O chatbot deve usar a API da OpenAI (preferencialmente GPT-4) para produzir respostas inteligentes e relevantes às perguntas dos usuários sobre cuidados com cavalos, treinamento, vacinas etc., levando em conta o contexto fornecido (por exemplo, histórico do animal).

2. **Integração no Frontend React:** Incorporar o assistente virtual como um componente de chatbot na interface React. A interface deve ser intuitiva, amigável e acessível (respeitando boas práticas de acessibilidade, como ARIA roles para chat e navegação por teclado).

3. **Backend Node.js para Intermediação:** Implementar endpoints Node.js (por exemplo, usando Express) que receberão as perguntas do frontend, interagirão com a API da OpenAI e consultarão o banco PostgreSQL conforme necessário. O backend atuará como intermediário seguro entre frontend, OpenAI e o banco de dados.

4. **Consultas ao Banco de Dados (PostgreSQL):** O assistente deve personalizar respostas baseadas no histórico de cada cavalo. Isso implica consultas ao banco de dados para obter informações relevantes (por exemplo, datas de vacinas, últimos check-ups, plano de treinamento atual) e inserir esses dados no contexto da pergunta antes de chamar a API da OpenAI.

5. **Fallback Offline (Modo Offline):** O sistema deve permitir funcionalidade limitada mesmo sem conexão de internet. Quando offline, o usuário deve conseguir **continuar interagindo** com o chatbot de forma restrita:
   - Acessar respostas já obtidas anteriormente (cache local).
   - Registrar novas perguntas em uma fila local (usando IndexedDB) para serem enviadas ao backend quando a conexão retornar.
   - Oferecer feedback adequado (por exemplo, aviso "Você está offline, mostrando respostas salvas e agendando novas perguntas para sincronizar depois").

6. **Modularidade e Segurança:** A solução deve ser modular, separando claramente frontend, backend, e serviços, facilitando manutenção e extensibilidade. Deve também ser **segura**, respeitando limites de requisição à API da OpenAI (para evitar custos excessivos ou banimento) e protegendo dados sensíveis dos animais (e possivelmente dos usuários). Isso inclui utilizar variáveis de ambiente para chaves API, não expor segredos no frontend e implementar validações de dados rigorosas.

## Frontend (React) – Integração do Chatbot

- **Componente de Chat:** Crie um componente React (por exemplo, `<Chatbot />`) que contenha a interface de conversa. Esse componente incluirá:
  - Uma área de histórico de mensagens (perguntas do usuário e respostas do bot).
  - Um campo de entrada de texto para a pergunta do usuário e um botão de envio.
  - Pode ser útil ter subcomponentes como `<ChatMessage />` (para mensagens individuais) e `<ChatInput />` (para a entrada e botão) para organizar melhor o código.

- **UI Intuitiva e Acessível:** Estilize o chat de forma limpa (pode usar CSS ou bibliotecas como Chakra UI, Material-UI, etc., se desejado) garantindo contraste adequado e fontes legíveis. Inclua atributos de acessibilidade:
  - Utilize `role="log"` ou similar na área de mensagens para que leitores de tela anunciem novas mensagens.
  - Forneça labels ou aria-labels para o campo de input e botão de enviar.
  - Permita enviar a mensagem tanto clicando no botão quanto pressionando Enter, para acessibilidade de teclado.

- **Gerenciamento de Estado e Dados:** Use **React Query** (TanStack Query) para gerenciar as requisições ao backend e cache das respostas:
  - Configure um **mutation** do React Query para enviar a pergunta do usuário ao endpoint backend (`/api/chat`) usando **axios**. Por exemplo, `useMutation(sendQuestion, {...})` onde `sendQuestion` faz um POST via axios.
  - Utilize o **estado do React** ou o cache do React Query para armazenar localmente o histórico da conversa, assim as mensagens persistem na interface mesmo em atualizações.
  - React Query também pode ajudar a manejar revalidações e atualizações em background das respostas.

- **Chamada ao Backend:** Quando o usuário enviar uma pergunta, o frontend fará uma requisição HTTP (via axios) para o endpoint Node (`POST /api/chat`). Inclua no payload da requisição dados relevantes:
  - Texto da pergunta do usuário.
  - Opcionalmente um identificador do cavalo ou contexto (por exemplo, `horseId` ou `userId`) para que o backend saiba de qual animal buscar informações.
  - Talvez informações de localização ou idioma, se for relevante para a resposta (no caso de diferentes línguas, etc.).

- **Atualização da UI:** Otimize a experiência exibindo imediatamente na UI a pergunta do usuário como mensagem enviada. Enquanto aguarda a resposta do servidor, pode mostrar um indicador de carregamento (spinner ou texto "pensando..."). Assim que a resposta chegar, adicioná-la ao histórico de mensagens.

- **Cache e IndexedDB no Frontend:** Implemente cache local para suporte offline:
  - Utilize **IndexedDB** (por exemplo via biblioteca `idb` ou `Dexie` para simplificar, ou diretamente através do `window.indexedDB`) para armazenar:
    - Histórico da conversa (para poder exibir mensagens passadas mesmo offline ou após reiniciar o app).
    - Dados relevantes dos cavalos (por exemplo, informações básicas ou últimas recomendações, para consulta offline).
    - Fila de perguntas feitas offline.
  - Quando uma nova pergunta for feita offline, em vez de chamar o backend diretamente, salve-a em uma lista de **requisições pendentes** no IndexedDB. Notifique o usuário que a pergunta foi armazenada e será enviada quando possível.
  - Para implementar essa fila, você pode criar um pequeno módulo no frontend, por exemplo `offlineQueue.js`, com funções `enqueueQuestion(question)` e `flushQueue()` (essa última tenta enviar todas as perguntas pendentes quando a conexão for restabelecida).

- **Service Worker e Modo Offline:** Configure um **Service Worker** no aplicativo React para ajudar no funcionamento offline:
  - O service worker pode ser criado manualmente ou usando o **Workbox**. Registre-o no `index.jsx` do React (`navigator.serviceWorker.register('/service-worker.js')`).
  - Utilize o service worker para:
    - **Pré-cache** dos recursos estáticos essenciais (HTML, CSS, JS do app) para que a interface carregue offline.
    - Interceptar requisições de rede do chat: se a requisição falhar por falta de conexão, salvar os dados no IndexedDB (como mencionado) e talvez retornar uma resposta imediata indicando modo offline.
    - Implementar **Background Sync**: se o navegador suportar (via `ServiceWorkerRegistration.sync`), registre um evento de sincronização para enviar as perguntas pendentes assim que houver conexão ([Make Your PWA Work Offline Part 2 - Dynamic Data | Monterail](https://www.monterail.com/blog/pwa-offline-dynamic-data#:~:text=Then%2C%20you%20just%20need%20to,to%20have%20a%20different%20tag)) ([Make Your PWA Work Offline Part 2 - Dynamic Data | Monterail](https://www.monterail.com/blog/pwa-offline-dynamic-data#:~:text=An%20Alternative%20for%20Background%20Sync)). Por exemplo, `registration.sync.register('sync-chat-queue')` e no service worker ouvir o evento `'sync'` para processar a fila.
    - Cachear respostas da API se fizer sentido (talvez não necessário, pois as respostas são dinâmicas, mas poderia armazenar as últimas N respostas em cache).

- **Exemplo (Frontend - axios com React Query):** Configurar uma função de requisição e usá-la com React Query:
  ```jsx
  // src/services/api.js
  import axios from 'axios';
  export const sendQuestion = async ({ question, horseId }) => {
    const response = await axios.post('/api/chat', { question, horseId });
    return response.data; // contém resposta do assistente
  };
  ```
  ```jsx
  // Dentro de um componente React (por exemplo Chatbot.jsx)
  import { useMutation } from 'react-query';
  import { sendQuestion } from './services/api';

  const { mutate: askQuestion, data: answerData, isLoading } = useMutation(sendQuestion, {
    onSuccess: (data) => {
      // data.answer contém a resposta da IA
      setMessages((msgs) => [...msgs, { sender: 'bot', text: data.answer }]);
    },
    onError: (error) => {
      console.error("Erro ao obter resposta:", error);
      // Tratar erro (exibir mensagem ao usuário informando a falha)
    }
  });

  const handleSend = (userQuestion) => {
    // Adiciona mensagem do usuário imediatamente no chat
    setMessages((msgs) => [...msgs, { sender: 'user', text: userQuestion }]);
    // Envia pergunta ao backend
    askQuestion({ question: userQuestion, horseId: selectedHorseId });
  };
  ```
  *Explicação:* Nesse exemplo, `setMessages` gerencia o estado local do histórico de mensagens. Ao enviar, adicionamos a mensagem do usuário e usamos a mutation `askQuestion` para enviar ao backend. Na resposta com sucesso, adicionamos a mensagem do bot. Em caso de erro (por exemplo, offline), poderíamos interceptar aqui para salvar no IndexedDB ao invés de simplesmente logar o erro.

## Backend (Node.js/Express) – API e Integração com OpenAI e PostgreSQL

- **Configuração do Servidor:** Utilize **Express** para criar o servidor Node.js com os endpoints necessários. Configure o parsing de JSON (`app.use(express.json())`) e habilite CORS se o frontend estiver em domínio diferente (com o pacote `cors`).

- **Endpoint de Chat (/api/chat):** Implemente uma rota POST (por exemplo, `/api/chat`) que recebe a pergunta do usuário e outros dados relevantes. Fluxo dentro deste endpoint:
  1. **Validação de Entrada:** Utilize **Zod** para validar o formato dos dados recebidos. Por exemplo, espere um objeto `{ question: string, horseId?: number }`. Valide que `question` existe e é uma string não-vazia, e que `horseId` (se presente) é numérico. Em caso de falha na validação, retorne código 400 com mensagem de erro.
  2. **Consulta ao Banco de Dados:** Se um `horseId` foi fornecido (ou se o backend consegue deduzir o contexto do usuário autenticado), recupere informações do PostgreSQL sobre aquele cavalo. Isso pode envolver consultas a tabelas como `horses`, `health_records`, `training_plans`, etc. Use um cliente PostgreSQL (biblioteca **pg**) ou um ORM (como **Prisma** ou Sequelize) para fazer a query de forma segura (consultas parametrizadas). Por exemplo:
     ```js
     // Exemplo usando node-postgres (pg)
     const { Pool } = require('pg');
     const pool = new Pool({ connectionString: process.env.DATABASE_URL });
     async function getHorseInfo(horseId) {
       const result = await pool.query('SELECT nome, idade, ultima_vacina, historico_treino FROM cavalos WHERE id = $1', [horseId]);
       return result.rows[0];
     }
     ```
     Supondo que `ultima_vacina` e `historico_treino` estejam armazenados, essa função retorna um objeto com informações para inserir no contexto.
  3. **Montagem do Prompt para OpenAI:** Com a pergunta do usuário e os dados do cavalo obtidos, construa um prompt consolidado para enviar à API OpenAI. Você pode criar uma função utilitária (ex: `generatePrompt(question, horseInfo)`) que devolve uma string ou objeto de mensagens no formato esperado pela API da OpenAI. Exemplo simples:
     ```js
     function generatePrompt(userQuestion, horseInfo) {
       let context = "";
       if (horseInfo) {
         context = `Dados do cavalo:\nNome: ${horseInfo.nome}, Idade: ${horseInfo.idade} anos, Última vacina: ${horseInfo.ultima_vacina}, Histórico de treino: ${horseInfo.historico_treino}.\n`;
       }
       // Prompt final combina contexto + pergunta do usuário
       const prompt = `${context}Pergunta: ${userQuestion}\nResposta do especialista:`;
       return prompt;
     }
     ```
     Aqui incluímos informações relevantes do cavalo antes da pergunta. O formato exato pode variar; se estiver usando a API de chat (`createChatCompletion`), você enviaria algo como `messages: [{role: "system", content: contextoBase}, {role: "user", content: perguntaDoUsuario}]`.

  4. **Chamada à API da OpenAI:** Utilize a biblioteca **openai** oficial ou **axios** para enviar o prompt e receber a resposta da IA:
     - Com a biblioteca `openai`:
       ```js
       // services/openaiService.js
       const { Configuration, OpenAIApi } = require("openai");
       const config = new Configuration({ apiKey: process.env.OPENAI_API_KEY });
       const openai = new OpenAIApi(config);

       async function getAICompletion(prompt) {
         const response = await openai.createCompletion({
           model: "text-davinci-003", // ou "gpt-4" se disponível via createChatCompletion
           prompt: prompt,
           max_tokens: 150,
           temperature: 0.7
         });
         return response.data.choices[0].text;
       }
       module.exports = { getAICompletion };
       ```
       *Obs:* Se GPT-4 estiver disponível, usaria `openai.createChatCompletion` com `model: "gpt-4"` e formataria mensagens. Mas no geral acima demonstra o uso. Também considere ajustar `max_tokens` e parâmetros conforme necessidade.
     - Com axios (chamada direta HTTP):
       ```js
       const axios = require('axios');
       async function getAICompletion(prompt) {
         const apiKey = process.env.OPENAI_API_KEY;
         const response = await axios.post('https://api.openai.com/v1/completions', {
           model: "text-davinci-003",
           prompt: prompt,
           max_tokens: 150,
           temperature: 0.7
         }, {
           headers: { Authorization: `Bearer ${apiKey}` }
         });
         return response.data.choices[0].text;
       }
       ```
       Em ambos os casos, extraia o texto da resposta (`choices[0].text` ou similar). É importante envolver a chamada em try/catch no endpoint para capturar erros da API (como timeout, ou resposta de erro).

  5. **Resposta do Endpoint:** Formate a resposta JSON devolvida ao frontend contendo a mensagem da IA. Por exemplo: `{ "answer": "texto da resposta gerada pela IA" }`. Inclua também quaisquer dados adicionais se necessário (por exemplo, talvez um campo com informações do cavalo que foram usadas, ou um indicador de fonte da resposta).

  6. **Tratamento de Erros e Limites:** Se a chamada à OpenAI falhar (erro de rede ou status !== 200), retorne um erro apropriado (e talvez uma mensagem genérica ao usuário como "Desculpe, não foi possível obter resposta no momento"). **Opcional:** implemente lógica para diferenciar erros:
     - Se for erro de limite de uso da API (rate limit), talvez retornar um código 429.
     - Logar erros no servidor para monitoramento.
     - Em casos de falha, o frontend pode decidir enfileirar a pergunta para tentar de novo depois (especialmente se o erro for falta de conexão).

- **Outros Endpoints Possíveis:** Você pode criar endpoints adicionais conforme necessário:
  - `/api/horses/:id` para obter dados detalhados de um cavalo (usado pelo frontend para exibir info ou pré-carregar contexto).
  - `/api/offline-sync` para receber em lote perguntas enviadas quando voltar online (embora o `/api/chat` possa ser reutilizado para cada item da fila).
  - Mantenha as rotas bem definidas e documentadas.

- **Segurança no Backend:** 
  - **Chaves e Credenciais:** Armazene a chave da OpenAI e a string de conexão do PostgreSQL em variáveis de ambiente. No Replit, use o sistema de Secrets ou um arquivo `.env` (adicione esse .env no .gitignore para não vazar). No código, acesse via `process.env.OPENAI_API_KEY` etc.
  - **Proteção de Dados:** Se houver autenticação de usuários no app, valide os tokens/sessão nos endpoints para garantir que um usuário só acesse dados do seu próprio cavalo. Por exemplo, se `horseId` pertence a outro usuário, retornar 403. (Caso o app não tenha multiusuário, este ponto pode não ser aplicável agora, mas é uma consideração de segurança).
  - **Rate Limiting:** Para evitar uso abusivo do endpoint de chat (que consome a API do OpenAI), implemente um limitador de requisições. Pode usar middleware como `express-rate-limit` para restringir, por exemplo, cada IP a X requisições por minuto. Isso previne também spam e altos custos.
  - **Sanitização:** Use bibliotecas ou funções para escapar outputs se necessário. Como as respostas vêm da OpenAI, pode ser prudente filtrá-las para remover qualquer dado potencialmente sensível ou formatar de forma segura (embora neste caso específico, provavelmente não haverá problemas de XSS pois a resposta é texto exibido, mas mantenha em mente ao inserir HTML dinamicamente).

- **Exemplo (Backend - Express Route `/api/chat`):** 
  ```js
  // routes/chat.js
  const express = require('express');
  const router = express.Router();
  const { getHorseInfo } = require('../services/dbService');
  const { getAICompletion } = require('../services/openaiService');
  const { chatSchema } = require('../schemas/chatSchema'); // Zod schema for validation

  router.post('/chat', async (req, res) => {
    try {
      // Validação com Zod
      const parsed = chatSchema.parse(req.body);
      const { question, horseId } = parsed;

      // Obter informações do cavalo, se ID foi fornecido
      let horseInfo = null;
      if (horseId) {
        horseInfo = await getHorseInfo(horseId);
      }

      // Gerar prompt combinando pergunta e contexto do cavalo (se houver)
      const prompt = generatePrompt(question, horseInfo);

      // Chamar API da OpenAI para obter resposta
      const aiText = await getAICompletion(prompt);

      // Enviar resposta de volta ao cliente
      return res.json({ answer: aiText });
    } catch (err) {
      console.error("Erro no endpoint /api/chat:", err);
      if (err.name === 'ZodError') {
        return res.status(400).json({ error: "Dados de entrada inválidos", details: err.errors });
      }
      // Diferenciar erros da OpenAI, etc., se necessário
      return res.status(500).json({ error: "Falha ao processar a pergunta." });
    }
  });

  module.exports = router;
  ```
  *Detalhes:* Nesse código, `chatSchema` seria um schema Zod definido algo como: 
  ```js
  // schemas/chatSchema.js
  const { z } = require('zod');
  const chatSchema = z.object({
    question: z.string().min(1),
    horseId: z.number().int().positive().optional()
  });
  module.exports = { chatSchema };
  ```
  O serviço `getHorseInfo` faz query no DB, e `getAICompletion` consulta a OpenAI conforme exemplos anteriores. A função `generatePrompt` (não mostrada aqui) insere dados do horseInfo na pergunta. 

- **Estrutura Modular:** Note que separamos lógica em módulos: rotas, serviços, schemas de validação, etc. Isso facilita o entendimento e a manutenção. O arquivo principal (por ex. `app.js` ou `index.js` no backend) importaria as rotas e iniciaria o servidor Express:
  ```js
  // app.js (resumo)
  const express = require('express');
  const app = express();
  const chatRoutes = require('./routes/chat');
  app.use(express.json());
  app.use('/api', chatRoutes);
  // ... (outros middlewares, error handling, etc.)
  app.listen(process.env.PORT || 3000, () => console.log("Server running"));
  ```
  
## Banco de Dados (PostgreSQL) – Integração de Dados do Cavalo

- **Modelagem de Dados:** Assegure-se de ter tabelas no PostgreSQL que armazenem os dados necessários:
  - Exemplo: Tabela `cavalos` com colunas (`id`, `nome`, `idade`, etc).
  - Tabela `vacinas` (ou registros de saúde) para controlar histórico de vacinas por cavalo (chave estrangeira para `cavalos`).
  - Tabela `treinos` para histórico/planejamento de treinamentos.
  - Essas são sugestões; o importante é conseguir obter, dado um `horseId`, informações relevantes para responder perguntas típicas (próxima vacina, último peso, recomendação de alimentação, etc.).
  
- **Camada de Acesso ao BD:** No backend, crie funções (services) para acessar esses dados:
  - `getHorseInfo(id)` – retorna informações gerais.
  - `getTrainingPlan(horseId)` – por exemplo, retorna o plano de treino atual.
  - `getUpcomingVaccinations(horseId)` – retorna próximas vacinas pendentes.
  - Ou uma única função que agregue tudo que for relevante. Isso depende de como você quer montar o prompt para a IA.
  - Utilize consultas parametrizadas com o **pg** (node-postgres) ou métodos do ORM escolhido, para evitar SQL injection e garantir eficiência. 

- **Exemplo (Banco de Dados - usando pg):** 
  ```js
  // services/dbService.js
  const { Pool } = require('pg');
  const pool = new Pool(); // configurações podem vir de variáveis de ambiente

  async function getHorseInfo(horseId) {
    const { rows } = await pool.query(
      `SELECT nome, TO_CHAR(ultima_vacina, 'YYYY-MM-DD') as ultima_vacina,
              plano_treino, resumo_saude
       FROM cavalos 
       WHERE id = $1`, [horseId]
    );
    return rows[0]; // retorna objeto com dados do cavalo
  }
  module.exports = { getHorseInfo };
  ```
  *Observação:* Aqui formata a data da última vacina para string. `plano_treino` e `resumo_saude` seriam colunas hipotéticas contendo, respectivamente, descrição do plano de treino atual e um resumo do estado de saúde, que podem ser incorporados no prompt da IA. Ajuste conforme seu esquema real.

- Certifique-se de que o backend **não demore muito** nas consultas ao banco antes de responder ao usuário. Se necessário, carregue previamente alguns dados do cavalo no frontend (cache local) para usar instantaneamente no prompt – porém, como a chamada à IA acontece no backend, provavelmente é mais simples buscar no backend mesmo. Apenas tenha certeza de otimizar consultas (índices no BD se necessário) para desempenho.

## Modo Offline e Sincronização

- **Detecção de Offline/Online:** No frontend, use o objeto `navigator.onLine` e eventos `window.addEventListener('online')` / `...('offline')` para detectar mudanças de conectividade. Isso permite atualizar a UI (ex: um ícone ou mensagem "Offline" visível).

- **Armazenamento de Conversa Offline:** Sempre que chegar uma nova resposta do chatbot, armazene-a em IndexedDB (junto com a pergunta que a originou). Dessa forma, se o usuário ficar offline, ainda consegue ver o histórico completo da conversa. Poderá até navegar por respostas antigas de interesse (porque estão em cache local).

- **Enfileiramento de Perguntas:** Conforme mencionado, implemente uma fila local para perguntas feitas enquanto offline:
  - Por exemplo, uma object store chamada "pendingQuestions" em IndexedDB onde cada entrada tem { id, question, horseId, timestamp }.
  - Ao tentar enviar uma pergunta e falhar (erro de rede detectado no catch da requisição axios, ou no onError da mutation do React Query), em vez de descartar o erro simplesmente, detectar condição offline e guardar a pergunta na "pendingQuestions".
  - Imediatamente refletir na UI que a pergunta foi registrada (pode mostrar no chat com um status "pendente (offline)" para o usuário saber que não foi respondida ainda).

- **Sincronização Pós-Restore:** Quando a conexão voltar (`'online'` event disparado ou via Service Worker Sync):
  - Recupere as perguntas pendentes do IndexedDB.
  - Para cada pergunta, envie a requisição ao `/api/chat` e ao receber a resposta, mostre no chat normalmente. Marque essa pergunta como processada/remova da fila.
  - Isso pode ser automatizado via **Background Sync API** no service worker:
    - No momento de falha, junto com salvar no IndexedDB, chame `registration.sync.register('sync-chat')`.
    - No código do service worker (arquivo `service-worker.js`), ouça:
      ```js
      self.addEventListener('sync', event => {
        if (event.tag === 'sync-chat') {
          event.waitUntil(syncPendingQuestions());
        }
      });
      ```
    - `syncPendingQuestions()` seria uma função que abre o IndexedDB (pode usar `idb` lib no contexto do SW) e reenvia as requests pendentes usando `fetch` mesmo (já que estamos no SW, não no React code). Em seguida, provavelmente usar o Cache API ou postMessage para comunicar ao cliente que novas respostas chegaram. *Essa abordagem é avançada e depende de suporte do navegador.* ([Make Your PWA Work Offline Part 2 - Dynamic Data | Monterail](https://www.monterail.com/blog/pwa-offline-dynamic-data#:~:text=Then%2C%20you%20just%20need%20to,to%20have%20a%20different%20tag)) ([Make Your PWA Work Offline Part 2 - Dynamic Data | Monterail](https://www.monterail.com/blog/pwa-offline-dynamic-data#:~:text=Background%20Sync%20API%20still%20doesn%E2%80%99t,all%20the%20major%20browsers))

  - Se background sync não for usado, alternativamente, no componente React ao detectar `navigator.onLine` true, chamar manualmente uma função que faz o flush da fila:
    ```jsx
    useEffect(() => {
      const goOnlineHandler = () => {
        flushPendingQuestions(); // função que pega do IndexedDB e envia ao backend
      };
      window.addEventListener('online', goOnlineHandler);
      return () => window.removeEventListener('online', goOnlineHandler);
    }, []);
    ```
    Essa `flushPendingQuestions()` poderia simplesmente ler todas pendentes e, para cada, executar `askQuestion({ question, horseId })` (reutilizando a mutation ou axios). **Importante:** evitar enviar duplicado se o background sync já cuidou ou se já foram enviadas; talvez limpar a fila ao enviar para não repetir.

- **Respostas Offline Limitadas:** Considere oferecer feedback útil enquanto offline:
  - Você pode ter algumas **respostas pré-definidas** para perguntas comuns, armazenadas localmente, caso faça sentido para a aplicação. Por exemplo, se o usuário perguntar "Quais cuidados devo ter com meu cavalo hoje?" e está offline, talvez retornar uma mensagem genérica tipo "Estou offline no momento. Revise a dieta e água do seu cavalo e cheque se as vacinas estão em dia." Isso pode ser codificado como fallback.
  - Contudo, como as perguntas podem ser variadas, muitas vezes a melhor resposta offline é informar que não é possível obter uma resposta inteligente até reconectar. 
  - Outra funcionalidade offline: permitir ao usuário navegar pelos **dados do cavalo** que já estejam em cache (ex: aba de informações do cavalo, ou últimas recomendações armazenadas). Assim, parte do app continua útil mesmo sem internet.

- **Teste de Modo Offline:** Ao desenvolver, simule o app offline (usando ferramentas do navegador ou desligando a rede) para garantir que:
  - A interface do chat continua funcional (mesmo que não retorne respostas novas, não quebre).
  - As perguntas são devidamente armazenadas e depois enviadas.
  - O usuário entende o que está acontecendo (inclua mensagens de estado, como por exemplo, estilizar mensagens pendentes em outra cor ou com um label "offline - aguardando envio").

## Segurança, Limites e Boas Práticas

- **Limite de Requisições à OpenAI:** Defina um mecanismo para evitar bombardear a API da OpenAI:
  - No backend, aplique **debounce** ou simplesmente rejeite novas perguntas se uma está em processamento para o mesmo usuário (evita que cliquem enviar várias vezes seguidas).
  - Implemente **contadores**: por exemplo, no máx. 5 perguntas por minuto por usuário. Isso pode ser feito em memória (um Map contando timestamps) ou usando `express-rate-limit` facilmente.
  - Monitore o retorno da OpenAI. Se receber código 429 (Too Many Requests), pode notificar o usuário ou implementar um backoff (esperar alguns segundos antes de tentar novamente automaticamente).

- **Proteção de Dados Sensíveis:** Embora se trate de dados de animais, pode haver informações sensíveis (nome do proprietário, valor do animal, etc.). Assegure-se de:
  - Não retornar mais dados ao frontend do que o necessário. O frontend só precisa das respostas da IA e possivelmente de alguns dados brutos para exibir (mas não dados confidenciais que não sejam usados).
  - Sanitizar e verificar o que a IA retornou. Evitar expor no front alguma informação que o usuário não deveria ver (por exemplo, se o prompt do OpenAI acidentalmente incluísse dados internos).
  - Comunicações sempre via HTTPS (se for deploy real; em desenvolvimento usar http://localhost tudo bem). No Replit, use os domínios seguros providos.

- **Autenticação e Autorização:** Se o app exigir login do usuário para acessar os dados do cavalo:
  - Implemente no backend middleware de autenticação (JWT, session, etc.) para proteger os endpoints (ex: verificar token no header antes de permitir `/api/chat` usar dados do cavalo).
  - Assim, garantimos que uma pessoa não acesse dados do cavalo de outra pessoa através do chatbot.
  - No prompt não foi detalhado, mas é uma boa prática mencionar se for aplicável.

- **Modularidade do Código:** Estruture o projeto separando responsabilidades:
  - **Frontend:** componentes separados, serviços separados (por exemplo, um módulo para APIs externas/local storage), e talvez um contexto para estado global se necessário (ex: contexto do usuário ou do cavalo selecionado).
  - **Backend:** como descrito, usar pastas para routes, services, schemas, etc. Facilita o Replit Agent gerar o código em arquivos distintos.
  - Use convenções claras de nome e organização para que quem leia o código gerado entenda facilmente.

- **Validação com Zod:** Já comentado, mas reforce o uso do Zod ou outra biblioteca de validação tanto no backend quanto no frontend (pode usar Zod no front também para validar formulários antes de enviar). Isso ajuda a capturar erros cedo e documentar o formato esperado dos dados.

- **Logs e Monitoramento:** Adicione alguns logs no backend (usando `console.log` simples ou uma biblioteca de log) para registrar chamadas à OpenAI, tempo de resposta, e quaisquer erros. Isso ajuda a depuração e monitoramento em produção.

## Organização de Arquivos (Estrutura Sugerida)

Para manter o projeto organizado, uma estrutura de diretórios recomendada é:

```
equi-horse-project/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── Chatbot.jsx        # Componente principal do chat
│   │   │   ├── ChatMessage.jsx    # Componente para exibir uma mensagem individual
│   │   │   └── ChatInput.jsx      # Componente para campo de input e botão enviar
│   │   ├── services/
│   │   │   ├── api.js            # Funções para chamadas axios ao backend
│   │   │   ├── offline.js        # Gerenciamento de IndexedDB, fila offline
│   │   │   └── openaiCache.js    # (Opcional) cache de respostas ou dados do OpenAI
│   │   ├── context/
│   │   │   └── HorseContext.jsx  # (Opcional) contexto com dados do cavalo selecionado
│   │   ├── App.jsx               # Configuração das rotas e inclusão do Chatbot
│   │   ├── index.jsx             # Ponto de entrada React, registra Service Worker
│   │   └── service-worker.js     # Service Worker para offline/PWA (gerado com Workbox ou manual)
│   └── package.json              # Dependências do frontend (react, react-query, axios, etc.)
└── backend/
    ├── src/
    │   ├── routes/
    │   │   └── chat.js           # Define rota /api/chat
    │   ├── services/
    │   │   ├── openaiService.js  # Funções para chamar API OpenAI
    │   │   ├── dbService.js      # Funções de acesso ao banco de dados
    │   │   └── offlineService.js # (Opcional) talvez para gerenciar sync, se feito via backend
    │   ├── schemas/
    │   │   └── chatSchema.js     # Definição Zod para validar req.body do chat
    │   ├── utils/
    │   │   └── promptUtil.js     # Função generatePrompt e possivelmente outras utilidades
    │   ├── app.js                # Inicialização do Express, middleware, uso das rotas
    │   └── config.js             # (Opcional) configurações diversas, ex: porta, DB URL
    └── package.json              # Dependências do backend (express, openai, pg, zod, etc.)
```

**Notas sobre a estrutura:**
- A separação acima facilita o Replit Agent gerar arquivos múltiplos se suportado, ou ao menos entender onde cada parte do código deve ficar.
- O `service-worker.js` geralmente fica no nível público do frontend (às vezes fora de src dependendo de como é configurado). Se usar Workbox CLI, ele pode ser gerado automaticamente.
- O `context/HorseContext.jsx` seria usado se quisermos ter globalmente qual cavalo está ativo (caso usuário tenha vários cavalos e selecione um). Esse contexto pode fornecer `horseId` para o Chatbot e outras partes do app.
- `offline.js` no frontend lida com IndexedDB: pode usar `indexedDB.open` e criar object stores "messages" e "pendingQuestions". Pode também conter funções para adicionar e ler essas stores. Poderia ser substituído/auxiliado pelo React Query persist (TanStack Query tem `persistQueryClient` que salva cache no IndexedDB facilmente ([Powering offline-ready web apps with React Query - Kyle Reblora](https://www.kylereblora.com/posts/powering-offline-ready-apps-with-react-query#:~:text=Powering%20offline,to%20cache%20data%20on)), mas como precisamos de uma fila de requests, a implementação manual pode ser necessária).

## Bibliotecas e Ferramentas Recomendadas

Ao implementar esta funcionalidade, as seguintes bibliotecas são sugeridas para atender os requisitos:

- **React Query (TanStack Query):** Facilita o gerenciamento de estado das requisições no frontend, oferecendo cache, refetch, e integração com persistência (útil para modo offline) ([Powering offline-ready web apps with React Query - Kyle Reblora](https://www.kylereblora.com/posts/powering-offline-ready-apps-with-react-query#:~:text=Powering%20offline,to%20cache%20data%20on)). Ajuda a evitar código manual de fetch e loading states.

- **Axios:** Biblioteca para realizar requisições HTTP tanto no frontend quanto no backend. No front, simplifica chamadas ao backend; no back, pode ser usada para chamar a OpenAI API se não usar a lib oficial. Axios lida bem com JSON e tem suporte a interceptors (que poderiam interceptar erros de rede para acionar lógica offline, por exemplo).

- **OpenAI Node SDK (`openai`):** A biblioteca oficial da OpenAI para Node.js torna mais simples chamar os endpoints de chat/completion sem montar manualmente requisições HTTP. Abstrai detalhes de headers, endpoints, e fornece métodos como `createCompletion` e `createChatCompletion`.

- **Zod:** Biblioteca de validação de esquemas que funciona muito bem no Node (e no browser). Útil para validar `req.body` das requisições recebidas no backend, garantindo que os dados estão no formato esperado antes de processar. Também pode ser usada no frontend para validar dados de formulários ou respostas da API.

- **Express:** Framework web para Node, já amplamente usado. Facilita a criação dos endpoints REST e a estruturação do servidor (middlewares, roteamento). Provê também middleware para estáticos, JSON parsing etc. Combinado com bibliotecas como `cors` e `helmet` para segurança, forma uma base sólida.

- **node-postgres (`pg`) ou Prisma/Sequelize:** Para interação com PostgreSQL. O `pg` (node-postgres) é leve e direto para consultas. ORMs como Prisma podem acelerar desenvolvimento definindo modelos em schema e oferecendo migrações. A escolha depende da preferência; para início rápido, `pg` é suficiente.

- **IndexedDB API (via Dexie ou idb):** Interagir com IndexedDB diretamente pode ser verboso; bibliotecas como Dexie.js ou idb simplificam as operações de ler/gravar. Dexie, por exemplo, permite definir versões do banco, stores e usar sintaxe async/await nas queries ao IndexedDB.

- **Service Workers / Workbox:** Para implementar o offline, usar um service worker é essencial. O Workbox da Google pode gerenciar facilmente o precache de assets (workbox-precaching) e fornece módulos para estratégia de cache de APIs e suporte a background sync (workbox-background-sync) ([Make Your PWA Work Offline Part 2 - Dynamic Data | Monterail](https://www.monterail.com/blog/pwa-offline-dynamic-data#:~:text=One%20such%20option%20involves%20the,What%20does%20it%20do)). Alternativamente, escrever manualmente o service worker dá controle total:
  - Usar o evento `fetch` no SW para responder com cache quando offline.
  - Usar `indexedDB` dentro do SW para armazenar e ler requests pendentes.
  - Workbox Abstract: Workbox BackgroundSync module cria uma fila que guarda requests falhadas automaticamente e re-envia quando online ([Workbox SW keep requests with error in queue - Stack Overflow](https://stackoverflow.com/questions/79142098/workbox-sw-keep-requests-with-error-in-queue#:~:text=Overflow%20stackoverflow,trigger%20sync%29.%20To)).
  
- **React (Hooks API):** Base do frontend. Hooks como `useState`, `useEffect` serão usados intensivamente (ex: para estado do chat, efeito ao entrar online/offline, etc.). Biblioteca base, já implícita.

- **Outras** (opcionais mas úteis):
  - **react-toastify** ou similar: para mostrar notificações ao usuário (ex: "Você está offline" ou "Sua pergunta será enviada quando voltar a conexão").
  - **lodash**: pode ajudar em debouncing (lodash.debounce) se necessário limitar frequência de perguntas no frontend.
  - **express-rate-limit**: já citado, para limitar requisições.
  - **helmet** (no Express): para adicionar cabeçalhos de segurança HTTP.
  - **cors** (no Express): se o frontend e backend estiverem em origens diferentes durante desenvolvimento.
  - **workbox-window**: se quiser controlar o service worker a partir do app React (ex: ouvir eventos de atualização, etc.).
  
Certifique-se de adicionar todas as dependências no `package.json` do respectivo projeto (frontend ou backend) antes de usar.

## Exemplos de Integração e Fluxo de Código

A seguir estão alguns exemplos ilustrativos de partes críticas da integração, para servir de referência e acelerar a implementação. O Replit Agent pode se basear neles ao gerar o código completo:

### 1. Componente Chatbot (Frontend) - Estrutura Básica

```jsx
// src/components/Chatbot.jsx
import React, { useState, useEffect } from 'react';
import { useMutation } from 'react-query';
import { sendQuestion } from '../services/api';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';

const Chatbot = () => {
  const [messages, setMessages] = useState([]);        // histórico de mensagens {sender, text}
  const [offline, setOffline] = useState(!navigator.onLine); // estado offline/online

  // Mutation para enviar pergunta
  const { mutate: askQuestion, isLoading: sending } = useMutation(sendQuestion, {
    onSuccess: (data) => {
      // Adiciona resposta do bot ao chat
      setMessages(prev => [...prev, { sender: 'bot', text: data.answer }]);
      // TODO: salvar no IndexedDB a pergunta e resposta
    },
    onError: (error) => {
      console.error("Erro ao obter resposta:", error);
      if (!navigator.onLine) {
        // Offline: guardar pergunta em cache e avisar usuário
        addQuestionToQueue(pendingQuestionRef.current);
        setMessages(prev => [...prev, { sender: 'bot', text: "Estou offline, sua pergunta foi salva e será respondida quando a conexão retornar." }]);
      } else {
        // Tratar outros erros (exibir mensagem de erro genérica)
        setMessages(prev => [...prev, { sender: 'bot', text: "Desculpe, ocorreu um erro ao obter a resposta." }]);
      }
    }
  });

  // Efeito para monitorar conexão
  useEffect(() => {
    const handleOnline = () => setOffline(false);
    const handleOffline = () => setOffline(true);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Função para enviar pergunta
  const pendingQuestionRef = React.useRef(""); // para armazenar a pergunta em caso offline
  const sendUserQuestion = (text) => {
    setMessages(prev => [...prev, { sender: 'user', text }]);
    pendingQuestionRef.current = text;
    askQuestion({ question: text, horseId: currentHorseId }); 
  };

  return (
    <div className="chatbot-container">
      <div className="chat-messages" role="log" aria-live="polite">
        {messages.map((msg, index) => (
          <ChatMessage key={index} sender={msg.sender} text={msg.text} />
        ))}
        {sending && <ChatMessage sender="bot" text="•••" />} 
      </div>
      <ChatInput onSend={sendUserQuestion} disabled={sending} offline={offline} />
    </div>
  );
};

export default Chatbot;
```

**Explicação:** Esse componente:
- Usa estado para mensagens e estado para conexão offline.
- Usa React Query mutation `askQuestion` para enviar pergunta.
- No `onError`, verifica `navigator.onLine` para diferenciar erro por offline (nesse caso, chama `addQuestionToQueue` – função imaginária que salvaria a pergunta no IndexedDB através de `offline.js` – e adiciona uma mensagem informando que a pergunta será respondida depois).
- Exibe `ChatMessage` para cada mensagem (o componente poderia formatar diferente se sender é 'user' ou 'bot'). Enquanto `sending` (pergunta em andamento), mostra "•••" como placeholder de digitação do bot.
- O componente `ChatInput` receberia `onSend` e poderia também mostrar estado desabilitado se `offline` (talvez impedir enviar? Aqui decidimos permitir enviar mesmo offline, então não desabilitamos pelo offline, apenas informamos no placeholder ou algo assim).
- Integra a escuta de eventos online/offline para atualizar estado.

### 2. Exemplo de Service Worker (trecho) – Cache e Sync

```js
// public/service-worker.js (ou src/service-worker.js dependendo da config)
// Este é um exemplo simplificado de um service worker manual:
const CACHE_NAME = "equi-horse-cache-v1";
const urlsToCache = [ "/", "/index.html", "/static/js/", /* outros assets */ ];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME).then(cache => cache.addAll(urlsToCache))
  );
  self.skipWaiting();
});

self.addEventListener('activate', event => {
  // limpeza de caches antigos se necessário
  event.waitUntil(
    caches.keys().then(keyList => {
      return Promise.all(keyList.map(key => {
        if (key !== CACHE_NAME) {
          return caches.delete(key);
        }
      }));
    })
  );
  self.clients.claim();
});

// Intercepta fetch para API
self.addEventListener('fetch', event => {
  const req = event.request;
  const url = new URL(req.url);
  if (url.pathname === '/api/chat') {
    // Tentativa de chamada de API do chat
    event.respondWith(
      fetch(req).catch(async () => {
        // Em caso de falha (offline):
        const clone = req.clone();
        const body = await clone.json();
        // Armazena a pergunta em IndexedDB (pseudo-código, assumindo função disponível)
        await saveQuestionOffline(body);
        // Retorna uma resposta genérica imediata para o frontend
        return new Response(JSON.stringify({
          answer: "Estou offline no momento. Sua pergunta foi salva e será enviada assim que possível."
        }), { headers: { 'Content-Type': 'application/json' }});
      })
    );
  } else {
    // Para outras requisições (assets), tenta rede primeiro, depois cache
    event.respondWith(
      fetch(req).then(res => {
        // Atualiza cache com nova resposta
        const resClone = res.clone();
        caches.open(CACHE_NAME).then(cache => cache.put(req, resClone));
        return res;
      }).catch(() => caches.match(req))
    );
  }
});

// Background Sync (para navegadores compatíveis)
self.addEventListener('sync', event => {
  if (event.tag === 'sync-chat-queue') {
    event.waitUntil(processQueue());
  }
});
```

**Descrição:** Esse service worker:
- Durante o fetch de `/api/chat`, se a fetch falhar (provavelmente offline), captura o corpo da requisição (pergunta do usuário), salva offline (`saveQuestionOffline` seria implementada usando IndexedDB via IDB API no contexto do SW) e responde imediatamente com uma mensagem padrão em JSON. Assim o frontend recebe uma resposta rapidamente, evitando ficar pendente indefinidamente.
- Para outros requests de arquivos, aplica estratégia network-first (tenta buscar online, se falhar pega do cache).
- No evento `sync`, chama `processQueue()` que enviaria as perguntas pendentes do IndexedDB ao servidor (usando `fetch` para cada e possivelmente removendo-as). Essa função precisaria ser implementada dentro do SW ou importada de um script.

*Observações:* O uso do service worker para interceptar `/api/chat` é opcional; poderíamos lidar com offline totalmente no frontend. Mas fazer no SW garante que mesmo se o aplicativo React não estiver rodando (ex: fechou e reabriu sem net), as perguntas ainda serão armazenadas. Isso adiciona complexidade, mas aumenta robustez. O Replit Agent, ao gerar o código, poderia inicialmente fornecer uma implementação básica e deixar comentários para aprimorar com background sync se desejado.

### 3. Exemplo de Prompt de Contexto para OpenAI (utilitário)

```js
// utils/promptUtil.js
function generatePrompt(question, horseInfo) {
  // Início do prompt com contexto do sistema (opcional, útil para moldar personalidade do bot)
  const systemMessage = "Você é um assistente virtual especializado em cuidados com cavalos, integrando dados específicos do animal nas respostas.";
  
  // Contextualiza com dados do cavalo, se disponíveis
  let context = "";
  if (horseInfo) {
    context += `Informações do cavalo:\n`;
    if (horseInfo.nome) context += `Nome: ${horseInfo.nome}\n`;
    if (horseInfo.idade) context += `Idade: ${horseInfo.idade} anos\n`;
    if (horseInfo.ultima_vacina) context += `Última vacina: ${horseInfo.ultima_vacina}\n`;
    if (horseInfo.plano_treino) context += `Plano de treino atual: ${horseInfo.plano_treino}\n`;
    if (horseInfo.resumo_saude) context += `Saúde: ${horseInfo.resumo_saude}\n`;
  }

  // Monta a mensagem do usuário
  const userMessage = `Usuário: ${question}`;

  // Caso use modelo de chat GPT, podemos retornar array de mensagens:
  return [
    { role: "system", content: systemMessage + "\n" + context },
    { role: "user", content: question }
  ];
  
  // Caso use completions simples, montar string:
  // return systemMessage + "\n" + context + "\nPergunta: " + question + "\nResposta:";
}
```

**Comentário:** Essa função considera dois cenários:
- Se usando a API de chat (gpt-3.5-turbo ou gpt-4), retorna um array de mensagens com um role "system" contendo instruções e contexto, e a mensagem do usuário. O `getAICompletion` precisaria então passar isso para `openai.createChatCompletion({ model: "...", messages: generatePrompt(...) })`.
- Se usando a API de completion (davinci), concatenaria texto. O exemplo mostra ambos (um retornado e o outro comentado).
- De qualquer forma, o importante é inserir as informações do cavalo no prompt para personalizar a resposta. O agente da OpenAI então pode usar esses dados ao gerar a resposta, por exemplo: "O Thunder (7 anos) recebeu a última vacina em 2023-05-10, então a próxima vacina dele...".

### 4. Exemplo de Uso do Zod para Validação

```js
// schemas/chatSchema.js
const { z } = require('zod');

const chatSchema = z.object({
  question: z.string().min(1, "A pergunta não pode ser vazia."),
  horseId: z.number().int().positive().optional()
});

module.exports = { chatSchema };
```

```js
// Uso no endpoint (como mostrado anteriormente)
try {
  const parsed = chatSchema.parse(req.body);
  // ... usar parsed.question e parsed.horseId com confiança
} catch (err) {
  if (err instanceof ZodError) {
    return res.status(400).json({ error: "Dados inválidos", issues: err.errors });
  }
}
```

**Nota:** A validação garante que `question` existe e não é uma string vazia, e que `horseId` se presente é um inteiro > 0. Isso previne chamadas incorretas que poderiam quebrar lógica do servidor ou gerar exceções no DB.

## Conclusão

O prompt acima instrui o Replit Agent a criar uma solução completa para integrar um chatbot de assistência ao EquiHorse. Seguindo essas diretrizes, espera-se gerar a estrutura de código (frontend React e backend Node) de forma organizada, utilizando as bibliotecas adequadas (**axios**, **react-query**, **openai**, **zod**, **IndexedDB/Service Worker** para offline, etc.) e incluindo exemplos de implementação. O resultado deverá ser um assistente virtual funcional, capaz de responder questões sobre cuidados equinos com contexto personalizado, operando mesmo diante de conexões instáveis, e projetado com boas práticas de modularidade e segurança em mente.

