{"timestamp": "2025-05-16T23:12:11.613Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 288632832, "heapTotal": 112762880, "heapUsed": 85352688, "external": 8247173, "arrayBuffers": 243725}, "uptime": 3.123449883, "cpuUsage": {"user": 3060093, "system": 430949}, "resourceUsage": {"userCPUTime": 3060132, "systemCPUTime": 430954, "maxRSS": 281868, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102796, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8806, "involuntaryContextSwitches": 12237}}