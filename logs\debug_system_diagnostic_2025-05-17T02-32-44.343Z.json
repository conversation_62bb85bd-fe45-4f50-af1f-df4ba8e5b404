{"timestamp": "2025-05-17T02:32:44.342Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 408403968, "heapTotal": 115273728, "heapUsed": 88807440, "external": 8289005, "arrayBuffers": 251917}, "uptime": 2.529797778, "cpuUsage": {"user": 2868370, "system": 354101}, "resourceUsage": {"userCPUTime": 2868422, "systemCPUTime": 354101, "maxRSS": 398832, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106955, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 120, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7662, "involuntaryContextSwitches": 4298}}