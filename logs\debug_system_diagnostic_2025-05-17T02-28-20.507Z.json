{"timestamp": "2025-05-17T02:28:20.506Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 278196224, "heapTotal": 109637632, "heapUsed": 89285224, "external": 8279336, "arrayBuffers": 282042}, "uptime": 4.717558667, "cpuUsage": {"user": 2887223, "system": 424591}, "resourceUsage": {"userCPUTime": 2887270, "systemCPUTime": 424597, "maxRSS": 345608, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 109924, "majorPageFault": 6, "swappedOut": 0, "fsRead": 33704, "fsWrite": 856, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9215, "involuntaryContextSwitches": 9496}}