{"timestamp": "2025-05-16T18:14:18.356Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 259616768, "heapTotal": 112762880, "heapUsed": 72605944, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.894412997, "cpuUsage": {"user": 2836948, "system": 351845}, "resourceUsage": {"userCPUTime": 2836990, "systemCPUTime": 351845, "maxRSS": 331552, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102559, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8272, "involuntaryContextSwitches": 2915}}