{"timestamp": "2025-05-14T14:46:01.933Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 383688704, "heapTotal": 97390592, "heapUsed": 67973400, "external": 6843945, "arrayBuffers": 60485}, "uptime": 1.540701207, "cpuUsage": {"user": 2346290, "system": 278456}, "resourceUsage": {"userCPUTime": 2346364, "systemCPUTime": 278456, "maxRSS": 374696, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100928, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 128, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6180, "involuntaryContextSwitches": 2674}}