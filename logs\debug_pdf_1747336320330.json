{"dados": "preview_1747336320330", "logs": "{\n  \"dados\": {\n    \"cavalo\": {\n      \"nome\": \"RZ ÉS BUENA DA CARAPUÇA\",\n      \"registro\": \"B405132\",\n      \"rp\": \"1482\",\n      \"sexo\": \"FEMEA\",\n      \"nascimento\": \"31/12/2012\",\n      \"pelagem\": \"BAIA SALGO ESQUERDO\"\n    },\n    \"pai\": {\n      \"nome\": \"BT JANELA\",\n      \"registro\": \"B136158\"\n    },\n    \"mae\": {\n      \"nome\": \"BT LAMBISGÓIA\",\n      \"registro\": \"SEM IDENTIFICAÇÃO\"\n    },\n    \"avoPai\": {\n      \"nome\": \"\"\n    },\n    \"avaMae\": {\n      \"nome\": \"\"\n    },\n    \"avoPai2\": {\n      \"nome\": \"\"\n    },\n    \"avaMae2\": {\n      \"nome\": \"\"\n    }\n  },\n  \"log\": \"[2025-05-15T19:11:58.829Z] [INFO] Iniciando processamento com OpenAI\\n[2025-05-15T19:12:00.329Z] [INFO] Recebido resposta da OpenAI\\n[2025-05-15T19:12:00.329Z] [DEBUG] Dados extraídos: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"RZ ÉS BUENA DA CARAPUÇA\\\",\\n    \\\"registro\\\": \\\"B405132\\\",\\n    \\\"rp\\\": \\\"1482\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"31/12/2012\\\",\\n    \\\"pelagem\\\": \\\"BAIA SALGO ESQUERDO\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"BT JANELA\\\",\\n    \\\"registro\\\": \\\"B136158\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"BT LAMBISGÓIA\\\",\\n    \\\"registro\\\": \\\"SEM IDENTIFICAÇÃO\\\"\\n  }\\n}\\n[2025-05-15T19:12:00.329Z] [INFO] Dados do cavalo principal extraídos: RZ ÉS BUENA DA CARAPUÇA (B405132)\\n[2025-05-15T19:12:00.329Z] [INFO] Pai: BT JANELA (B136158)\\n[2025-05-15T19:12:00.329Z] [INFO] Mãe: BT LAMBISGÓIA (SEM IDENTIFICAÇÃO)\\n[2025-05-15T19:12:00.329Z] [INFO] Dados convertidos com sucesso\"\n}", "timestamp": "2025-05-15T19:12:00.330Z"}