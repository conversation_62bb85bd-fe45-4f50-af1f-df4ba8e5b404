{"timestamp": "2025-05-26T21:26:09.352Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 394207232, "heapTotal": 117899264, "heapUsed": 97662064, "external": 8656839, "arrayBuffers": 273850}, "uptime": 1.897063765, "cpuUsage": {"user": 2859506, "system": 397990}, "resourceUsage": {"userCPUTime": 2859548, "systemCPUTime": 397995, "maxRSS": 384968, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 112894, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 40, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8468, "involuntaryContextSwitches": 3095}}