{"timestamp": "2025-05-16T13:50:45.165Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 400035840, "heapTotal": 113287168, "heapUsed": 72662264, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.981469944, "cpuUsage": {"user": 3297339, "system": 401513}, "resourceUsage": {"userCPUTime": 3297423, "systemCPUTime": 401513, "maxRSS": 390660, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102356, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8006, "involuntaryContextSwitches": 12298}}