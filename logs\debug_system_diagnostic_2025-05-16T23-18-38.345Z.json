{"timestamp": "2025-05-16T23:18:38.344Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399036416, "heapTotal": 112504832, "heapUsed": 88730648, "external": 8922640, "arrayBuffers": 257466}, "uptime": 2.714478653, "cpuUsage": {"user": 3191949, "system": 349963}, "resourceUsage": {"userCPUTime": 3192010, "systemCPUTime": 349969, "maxRSS": 389684, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104463, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7731, "involuntaryContextSwitches": 6343}}