@echo off
:: Script rápido baseado no comando original
:: Gera chave SSH para Replit e copia automaticamente

echo 🔑 Gerando chave SSH para Replit...

:: Verificar se o diretório .ssh existe, se não, criar
if not exist "%USERPROFILE%\.ssh" mkdir "%USERPROFILE%\.ssh"

:: Comando melhorado baseado no original
:: Verifica se a chave já existe, se não, gera uma nova
if not exist "%USERPROFILE%\.ssh\replit.pub" (
    echo 🔐 Gerando nova chave SSH...
    ssh-keygen -t ed25519 -f "%USERPROFILE%\.ssh\replit" -q -N "" -C "replit-key"
    if errorlevel 1 (
        echo ❌ Erro ao gerar chave SSH. Verifique se o ssh-keygen está instalado.
        pause
        exit /b 1
    )
    echo ✅ Chave SSH gerada!
) else (
    echo ✅ Chave SSH já existe!
)

:: Mostrar e copiar a chave pública
echo.
echo 📋 Sua chave pública SSH:
type "%USERPROFILE%\.ssh\replit.pub"
echo.

:: Copiar para área de transferência
powershell -Command "Get-Content '%USERPROFILE%\.ssh\replit.pub' | Set-Clipboard" 2>nul
if errorlevel 1 (
    echo ⚠️  Copie manualmente a chave acima
) else (
    echo ✅ Chave copiada para área de transferência!
)

echo.
echo 🎯 Agora cole a chave em: https://replit.com/account#ssh-keys
echo.

:: Abrir página automaticamente
set /p "open=Abrir página do Replit? (S/n): "
if /i not "%open%"=="n" start https://replit.com/account#ssh-keys

pause
