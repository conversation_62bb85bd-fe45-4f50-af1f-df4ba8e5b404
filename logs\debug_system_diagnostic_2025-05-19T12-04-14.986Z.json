{"timestamp": "2025-05-19T12:04:14.985Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 262021120, "heapTotal": 114335744, "heapUsed": 73958392, "external": 8211290, "arrayBuffers": 235533}, "uptime": 8.298989406, "cpuUsage": {"user": 2875310, "system": 417902}, "resourceUsage": {"userCPUTime": 2875310, "systemCPUTime": 417967, "maxRSS": 340508, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106173, "majorPageFault": 6, "swappedOut": 0, "fsRead": 55600, "fsWrite": 880, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9544, "involuntaryContextSwitches": 2354}}