{"timestamp": "2025-05-15T23:32:27.744Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 410791936, "heapTotal": 118812672, "heapUsed": 90678840, "external": 8268990, "arrayBuffers": 243725}, "uptime": 1.791477561, "cpuUsage": {"user": 2753219, "system": 343714}, "resourceUsage": {"userCPUTime": 2753271, "systemCPUTime": 343721, "maxRSS": 401164, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106584, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8401, "involuntaryContextSwitches": 2260}}