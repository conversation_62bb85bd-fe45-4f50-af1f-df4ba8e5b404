{"timestamp": "2025-05-16T18:17:21.592Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 398327808, "heapTotal": 116957184, "heapUsed": 72634400, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.763702557, "cpuUsage": {"user": 2860582, "system": 343924}, "resourceUsage": {"userCPUTime": 2860582, "systemCPUTime": 343972, "maxRSS": 388992, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105060, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7939, "involuntaryContextSwitches": 2183}}