import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON><PERSON> } from "@shared/schema";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import GenealogiaSelector, { EntradaGenealogica } from "@/components/cavalo/GenealogiaSelector";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  Card<PERSON><PERSON>le,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft } from "lucide-react";
import HorseAvatarWithUpload from "@/components/horse/HorseAvatarWithUpload";

// Zod Schema (igual ao InsertCavalo, padronizado!)
const horseSchema = z.object({
  name: z.string().min(2, "Nome deve ter no mínimo 2 caracteres"),
  breed: z.string().min(1, "Selecione uma raça"),
  birthDate: z.string().min(1, "Data de nascimento é obrigatória"),
  peso: z.string().optional(),
  altura: z.string().optional(),
  sexo: z.string().optional(),
  cor: z.string().optional(),
  pelagemId: z.number().nullable().optional(),
  status: z.string().optional(),
  dataEntrada: z.string().optional(),
  dataSaida: z.string().optional(),
  numeroRegistro: z.string().optional(),
  criador: z.string().optional(),
  proprietario: z.string().optional(),
  inspetor: z.string().optional(),
  origem: z.string().optional(),
  observacoes: z.string().optional(),
  motivoSaida: z.string().optional(),
  
  // Novos campos para o modelo de genealogia dual
  paiId: z.number().nullable().optional(),
  paiNome: z.string().nullable().optional(),
  maeId: z.number().nullable().optional(),
  maeNome: z.string().nullable().optional(),
  
  // Campos para o componente GenealogiaSelector
  pai: z.object({
    tipo: z.enum(['sistema', 'externo', 'nenhum']),
    cavaloSistemaId: z.string().nullable().optional(),
    cavaloNome: z.string().nullable().optional()
  }).optional(),
  
  mae: z.object({
    tipo: z.enum(['sistema', 'externo', 'nenhum']),
    cavaloSistemaId: z.string().nullable().optional(),
    cavaloNome: z.string().nullable().optional()
  }).optional(),
  
  // Campos adicionais de genealogia completa
  avoPaterno: z.string().optional(),
  avoPaterna: z.string().optional(), // Nova - avó paterna
  avoMaterno: z.string().optional(),
  avoMaterna: z.string().optional(), // Nova - avó materna
  
  // Outros campos
  notes: z.string().optional(),
});

type HorseFormData = z.infer<typeof horseSchema>;

const EditHorsePage = () => {
  const [user, setUser] = useState(() => {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch {
        return null;
      }
    }
    return null;
  });

  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  // ID do cavalo na URL: /cavalo/123/editar
  const location = useLocation()[0];
  const horseId = location.split("/")[2];

  // Buscar cavalo pelo ID
  const {
    data: horse,
    isLoading,
    error,
  } = useQuery({
    queryKey: [`/api/cavalos/${horseId}`],
    enabled: !!horseId && !!user,
    queryFn: async () => {
      try {
        console.log(`=== BUSCANDO CAVALO ${horseId} ===`);
        const result = await apiRequest<Cavalo>("GET", `/api/cavalos/${horseId}`);
        console.log("DADOS RECEBIDOS DO SERVIDOR:", result);
        return result;
      } catch (error) {
        console.error("ERRO AO BUSCAR CAVALO:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os dados do cavalo",
          variant: "destructive",
        });
        return null;
      }
    },
  });
  
  // Buscar todos os cavalos (incluindo externos) para seleção de pai/mãe - com cache otimizado
  const { 
    data: todosCavalos = [], 
    isLoading: isLoadingCavalos 
  } = useQuery({
    queryKey: ['/api/cavalos-genealogia'],
    enabled: !!user && !!horse, // Só buscar após carregar o cavalo
    staleTime: 10 * 60 * 1000, // Cache por 10 minutos
    cacheTime: 30 * 60 * 1000, // Manter em cache por 30 minutos
    queryFn: async () => {
      try {
        return await apiRequest<Cavalo[]>("GET", "/api/cavalos-genealogia");
      } catch (error) {
        console.error("Erro ao carregar lista de cavalos para genealogia:", error);
        return [];
      }
    },
  });

  // Formulário controlado
  const form = useForm<HorseFormData>({
    resolver: zodResolver(horseSchema),
    defaultValues: {
      name: "",
      breed: "",
      birthDate: "",
      peso: "",
      altura: "",
      sexo: "",
      status: "ativo",
      dataEntrada: "",
      dataSaida: "",
      motivoSaida: "",
      // Usando paiId e maeId em vez de pai/mae para compatibilidade com CavaloForm
      paiId: null,
      paiNome: "",
      maeId: null,
      maeNome: "",
      // Campos para o componente GenealogiaSelector
      pai: {
        tipo: 'nenhum',
        cavaloSistemaId: null,
        cavaloNome: null
      },
      mae: {
        tipo: 'nenhum',
        cavaloSistemaId: null,
        cavaloNome: null
      },
      avoPaterno: "",
      avoMaterno: "",
      notes: "",
      cor: "",
    },
  });

  // Preencher formulário com dados do cavalo
  useEffect(() => {
    if (horse) {
      console.log('=== CARREGANDO DADOS DO CAVALO ===');
      console.log('Dados recebidos:', horse);
      
      // Converter data se necessário
      let formattedBirthDate = "";
      if (horse.birthDate) {
        if (horse.birthDate.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
          const parts = horse.birthDate.split('/');
          formattedBirthDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
        } else {
          formattedBirthDate = horse.birthDate;
        }
      }

      // Preparar dados de genealogia
      const paiData = horse.paiId ? {
        tipo: "sistema" as const,
        cavaloSistemaId: horse.paiId.toString(),
        cavaloNome: horse.paiNome || null
      } : horse.paiNome ? {
        tipo: "externo" as const,
        cavaloSistemaId: null,
        cavaloNome: horse.paiNome
      } : {
        tipo: "nenhum" as const,
        cavaloSistemaId: null,
        cavaloNome: null
      };

      const maeData = horse.maeId ? {
        tipo: "sistema" as const,
        cavaloSistemaId: horse.maeId.toString(),
        cavaloNome: horse.maeNome || null
      } : horse.maeNome ? {
        tipo: "externo" as const,
        cavaloSistemaId: null,
        cavaloNome: horse.maeNome
      } : {
        tipo: "nenhum" as const,
        cavaloSistemaId: null,
        cavaloNome: null
      };

      // Preencher formulário
      const formData = {
        name: horse.name || "",
        breed: horse.breed || "",
        birthDate: formattedBirthDate,
        peso: horse.peso ? horse.peso.toString() : "",
        altura: horse.altura ? horse.altura.toString() : "",
        sexo: horse.sexo || "",
        cor: horse.cor || "",
        pelagemId: horse.pelagemId || null,
        status: horse.status || "ativo",
        dataEntrada: horse.dataEntrada || "",
        dataSaida: horse.dataSaida || "",
        motivoSaida: horse.motivoSaida || "",
        numeroRegistro: horse.numeroRegistro || "",
        criador: horse.criador || "",
        proprietario: horse.proprietario || "",
        inspetor: horse.inspetor || "",
        origem: horse.origem || "",
        observacoes: horse.notes || "",
        paiId: horse.paiId || null,
        paiNome: horse.paiNome || "",
        maeId: horse.maeId || null,
        maeNome: horse.maeNome || "",
        pai: paiData,
        mae: maeData,
        avoPaterno: horse.avoPaterno || "",
        avoPaterna: "",
        avoMaterno: horse.avoMaterno || "",
        avoMaterna: "",
        notes: horse.notes || "",
      };

      console.log('Dados para o formulário:', formData);
      form.reset(formData);
      console.log('=== FORMULÁRIO PREENCHIDO ===');
    }
  }, [horse, form]);

  // Payload pronto para não perder dados!
  const onSubmit = async (data: HorseFormData) => {
    if (!user) return;
    setLoading(true);
    
    // Logs detalhados para diagnóstico
    console.log('************ INICIANDO SUBMISSÃO DO FORMULÁRIO ************');
    console.log('DADOS DO FORM:', data);
    console.log('CAVALO ORIGINAL:', horse);
    
    // Diagnóstico detalhado da genealogia
    console.log('DIAGNÓSTICO GENEALOGIA:', {
      form_paiId: data.paiId,
      form_maeId: data.maeId,
      form_pai: data.pai,
      form_mae: data.mae,
      db_pai: horse?.pai,
      db_mae: horse?.mae,
      paiId_type: typeof data.paiId,
      maeId_type: typeof data.maeId
    });

    try {
      // Extrair e processar dados de genealogia
      // Usar dados do pai e mãe inseridos pelo GenealogiaSelector
      const infoPai = data.pai || { tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null };
      const infoMae = data.mae || { tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null };
      
      // Determinar os valores finais para paiId/paiNome e maeId/maeNome
      const paiIdFinal = infoPai.tipo === 'sistema' && infoPai.cavaloSistemaId 
        ? Number(infoPai.cavaloSistemaId) 
        : null;
      
      const paiNomeFinal = infoPai.tipo === 'externo' && infoPai.cavaloNome 
        ? infoPai.cavaloNome 
        : null;
        
      const maeIdFinal = infoMae.tipo === 'sistema' && infoMae.cavaloSistemaId 
        ? Number(infoMae.cavaloSistemaId) 
        : null;
      
      const maeNomeFinal = infoMae.tipo === 'externo' && infoMae.cavaloNome 
        ? infoMae.cavaloNome 
        : null;
      
      // Montar o payload para atualização
      const updatePayload = {
        name: data.name,
        breed: data.breed,
        birthDate: data.birthDate,
        peso: data.peso ? parseFloat(data.peso) : null,
        altura: data.altura ? parseFloat(data.altura) : null,
        sexo: data.sexo || null,
        cor: data.cor || null,
        status: data.status || "ativo",
        dataEntrada: data.dataEntrada || null,
        dataSaida: data.dataSaida || null,
        motivoSaida: data.motivoSaida || null,
        
        // Novo modelo dual para genealogia
        paiId: paiIdFinal,
        paiNome: paiNomeFinal,
        maeId: maeIdFinal,
        maeNome: maeNomeFinal,
        
        // Removendo os campos pai/mae que são estruturas complexas para evitar erro na API
        pai: undefined,
        mae: undefined,
        
        // Log detalhado das informações de genealogia para depuração
        _debug_genealogia: JSON.stringify({
          pai_completo: data.pai,
          mae_completo: data.mae,
          paiId_final: paiIdFinal,
          paiNome_final: paiNomeFinal,
          maeId_final: maeIdFinal,
          maeNome_final: maeNomeFinal
        }),
        
        avoPaterno: data.avoPaterno || null,
        avoMaterno: data.avoMaterno || null,
        notes: data.notes || null,
      };
      // Logs de depuração para entender o fluxo e conversão de valores
      console.log('************ DEBUG - EDIÇÃO DE CAVALO ************');
      console.log('FORMULÁRIO SUBMETIDO (data):', data);
      console.log('MAPEAMENTO GENEALOGIA:', {
        infoPai: infoPai,
        infoMae: infoMae,
        paiId_final: paiIdFinal,
        paiNome_final: paiNomeFinal,
        maeId_final: maeIdFinal,
        maeNome_final: maeNomeFinal
      });
      console.log('PAYLOAD DE ATUALIZAÇÃO:', updatePayload);
      console.log('************ FIM DEBUG ************');
      
      // Opcional: remover campos vazios do payload
      Object.keys(updatePayload).forEach((key) => {
        if (updatePayload[key as keyof typeof updatePayload] === "") {
          (updatePayload as any)[key] = null;
        }
      });

      await apiRequest("PUT", `/api/cavalos/${horseId}`, updatePayload);

      // Mostrar toast incluindo mensagem específica sobre pais
      if (data.paiId || data.maeId) {
        toast({
          title: "Cavalo atualizado",
          description: `${data.name} foi atualizado com sucesso! Informações de genealogia salvas.`,
        });
      } else {
        toast({
          title: "Cavalo atualizado",
          description: `${data.name} foi atualizado com sucesso!`,
        });
      }
      
      // Atualizar o cache de cavalos para mostrar dados atualizados
      queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${horseId}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      
      // Pequeno atraso para garantir que as atualizações sejam processadas
      setTimeout(() => {
        navigate(`/cavalo/${horseId}`);
      }, 300);
    } catch (error: any) {
      toast({
        title: "Falha na atualização",
        description: error.message || "Não foi possível atualizar o cavalo",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(`/cavalo/${horseId}`);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (error || !horse) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div>
              <h3 className="text-red-800 font-medium">Erro ao carregar</h3>
              <p className="text-red-700 mt-1">
                Não foi possível carregar os dados do cavalo. Por favor, tente
                novamente.
              </p>
              <Button
                variant="outline"
                onClick={() => navigate("/")}
                className="mt-4"
              >
                Voltar para o Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="mb-6">
        <button
          onClick={handleCancel}
          className="inline-flex items-center mb-4 text-gray-500 hover:text-gray-700"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Voltar para detalhes do cavalo
        </button>
        <h1 className="text-2xl font-bold text-gray-900">
          Editar Cavalo: {horse.name}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Atualize as informações do cavalo.
        </p>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Avatar com upload */}
              <div className="flex justify-center mb-6">
                <HorseAvatarWithUpload
                  horseId={parseInt(horseId)}
                  horseName={horse.name || "Cavalo"}
                  currentPhotoUrl={`/api/cavalos/${horseId}/photo`}
                  size="lg"
                />
              </div>

              {/* Campos obrigatórios */}
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Nome *</FormLabel>
                      <FormControl>
                        <Input placeholder="Nome do cavalo" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="breed"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Raça *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a raça" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="arabian">Árabe</SelectItem>
                          <SelectItem value="quarter">
                            Quarto de Milha
                          </SelectItem>
                          <SelectItem value="thoroughbred">
                            Puro-Sangue
                          </SelectItem>
                          <SelectItem value="andalusian">Andaluz</SelectItem>
                          <SelectItem value="friesian">Frisão</SelectItem>
                          <SelectItem value="appaloosa">Appaloosa</SelectItem>
                          <SelectItem value="mangalarga">Mangalarga</SelectItem>
                          <SelectItem value="crioulo">Crioulo</SelectItem>
                          <SelectItem value="other">Outra</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="birthDate"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Data de Nascimento *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Abas com campos opcionais */}
              <div className="mt-8">
                <Tabs defaultValue="fisico" className="w-full">
                  <TabsList className="mb-4 grid grid-cols-4 w-full">
                    <TabsTrigger value="fisico">
                      Características Físicas
                    </TabsTrigger>
                    <TabsTrigger value="genealogia">Genealogia</TabsTrigger>
                    <TabsTrigger value="status">Status</TabsTrigger>
                    <TabsTrigger value="observacoes">Observações</TabsTrigger>
                  </TabsList>

                  {/* Características Físicas */}
                  <TabsContent value="fisico">
                    <Card>
                      <CardHeader>
                        <CardTitle>Características Físicas</CardTitle>
                        <CardDescription>
                          Informações sobre o porte e características físicas do
                          cavalo (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="peso"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Peso (kg)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.1"
                                    min="0"
                                    placeholder="Ex: 450.5"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="altura"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Altura (m)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    placeholder="Ex: 1.68"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="sexo"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Sexo</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione o sexo" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="Macho">Macho</SelectItem>
                                    <SelectItem value="Fêmea">Fêmea</SelectItem>
                                    <SelectItem value="Castrado">
                                      Castrado
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Genealogia */}
                  <TabsContent value="genealogia">
                    <Card>
                      <CardHeader>
                        <CardTitle>Genealogia</CardTitle>
                        <CardDescription>
                          Informações sobre a linhagem e pedigree do cavalo
                          (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          {/* Seleção de Pai usando o novo componente GenealogiaSelector */}
                          <FormField
                            control={form.control}
                            name="pai"
                            render={({ field }) => (
                              <div className="sm:col-span-3">
                                <GenealogiaSelector
                                  label="Pai"
                                  description="Selecione um cavalo do sistema ou informe o nome de um cavalo externo"
                                  cavalos={todosCavalos}
                                  sexoFiltro="Macho"
                                  value={field.value}
                                  onChange={(val) => {
                                    field.onChange(val);
                                    // Atualizar os campos auxiliares paiId e paiNome
                                    if (val.tipo === 'sistema') {
                                      // Para cavalos do plantel, usamos o ID
                                      form.setValue('paiId', val.cavaloSistemaId ? Number(val.cavaloSistemaId) : null);
                                      form.setValue('paiNome', null);
                                    } else if (val.tipo === 'externo') {
                                      // Para cavalos externos, agora buscamos/criamos um ID
                                      // mas também mantemos o nome para exibição
                                      if (val.cavaloSistemaId) {
                                        // Se já tiver um ID (de um externo cadastrado)
                                        form.setValue('paiId', Number(val.cavaloSistemaId));
                                        form.setValue('paiNome', val.cavaloNome); // Mantém nome para referência
                                      } else {
                                        // Aqui o ID será gerado ao salvar o formulário
                                        form.setValue('paiId', null);
                                        form.setValue('paiNome', val.cavaloNome);
                                      }
                                    } else {
                                      // Nenhum cavalo selecionado
                                      form.setValue('paiId', null);
                                      form.setValue('paiNome', null);
                                    }
                                  }}
                                  error={form.formState.errors.pai?.message}
                                />
                              </div>
                            )}
                          />
                          
                          {/* Seleção de Mãe usando o novo componente GenealogiaSelector */}
                          <FormField
                            control={form.control}
                            name="mae"
                            render={({ field }) => (
                              <div className="sm:col-span-3">
                                <GenealogiaSelector
                                  label="Mãe"
                                  description="Selecione uma égua do sistema ou informe o nome de uma égua externa"
                                  cavalos={todosCavalos}
                                  sexoFiltro="Fêmea"
                                  value={field.value}
                                  onChange={(val) => {
                                    field.onChange(val);
                                    // Atualizar os campos auxiliares maeId e maeNome
                                    if (val.tipo === 'sistema') {
                                      // Para cavalos do plantel, usamos o ID
                                      form.setValue('maeId', val.cavaloSistemaId ? Number(val.cavaloSistemaId) : null);
                                      form.setValue('maeNome', null);
                                    } else if (val.tipo === 'externo') {
                                      // Para cavalos externos, agora buscamos/criamos um ID
                                      // mas também mantemos o nome para exibição
                                      if (val.cavaloSistemaId) {
                                        // Se já tiver um ID (de um externo cadastrado)
                                        form.setValue('maeId', Number(val.cavaloSistemaId));
                                        form.setValue('maeNome', val.cavaloNome); // Mantém nome para referência
                                      } else {
                                        // Aqui o ID será gerado ao salvar o formulário
                                        form.setValue('maeId', null);
                                        form.setValue('maeNome', val.cavaloNome);
                                      }
                                    } else {
                                      // Nenhum cavalo selecionado
                                      form.setValue('maeId', null);
                                      form.setValue('maeNome', null);
                                    }
                                  }}
                                  error={form.formState.errors.mae?.message}
                                />
                              </div>
                            )}
                          />
                          <div className="mt-4 mb-1">
                            <h3 className="text-md font-semibold">Avós Paternos</h3>
                          </div>
                          <FormField
                            control={form.control}
                            name="avoPaterno"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Avô Paterno</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome do avô paterno"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="avoPaterna"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Avó Paterna</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome da avó paterna"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="mt-4 mb-1">
                            <h3 className="text-md font-semibold">Avós Maternos</h3>
                          </div>
                          <FormField
                            control={form.control}
                            name="avoMaterno"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Avô Materno</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome do avô materno"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="avoMaterna"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Avó Materna</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome da avó materna"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Status */}
                  <TabsContent value="status">
                    <Card>
                      <CardHeader>
                        <CardTitle>Status</CardTitle>
                        <CardDescription>
                          Situação atual do cavalo no plantel (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="status"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Status</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione o status" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="ativo">Ativo</SelectItem>
                                    <SelectItem value="vendido">
                                      Vendido
                                    </SelectItem>
                                    <SelectItem value="falecido">
                                      Falecido
                                    </SelectItem>
                                    <SelectItem value="doado">Doado</SelectItem>
                                    <SelectItem value="emprestado">
                                      Emprestado
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="dataEntrada"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Data de Entrada</FormLabel>
                                <FormControl>
                                  <Input type="date" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="dataSaida"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Data de Saída</FormLabel>
                                <FormControl>
                                  <Input type="date" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="motivoSaida"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-6">
                                <FormLabel>Motivo da Saída</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Explique o motivo da saída, se aplicável"
                                    rows={2}
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Observações */}
                  <TabsContent value="observacoes">
                    <Card>
                      <CardHeader>
                        <CardTitle>Observações</CardTitle>
                        <CardDescription>
                          Informações adicionais, histórico ou particularidades
                          (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <FormField
                          control={form.control}
                          name="notes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Observações Gerais</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Informações adicionais sobre o cavalo, como histórico médico, comportamento, necessidades especiais, etc."
                                  rows={5}
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>

              <div className="pt-5 border-t border-gray-200">
                <div className="flex justify-between">
                  <p className="text-sm text-gray-500">* Campos obrigatórios</p>
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      className="mr-3"
                    >
                      Cancelar
                    </Button>
                    <Button type="submit" disabled={loading}>
                      {loading ? "Salvando..." : "Salvar Alterações"}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default EditHorsePage;
