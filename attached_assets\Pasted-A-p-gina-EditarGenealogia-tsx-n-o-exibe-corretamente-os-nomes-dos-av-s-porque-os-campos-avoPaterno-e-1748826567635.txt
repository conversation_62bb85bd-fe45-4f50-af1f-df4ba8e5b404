A página EditarGenealogia.tsx não exibe corretamente os nomes dos avós porque os campos avoPaterno e avoMaterno saem do banco como strings JSON aninhadas ("{\"tipo\":\"externo\", ... \"cavaloNome\":\"{\\\"tipo...").

O componente ExpandedGenealogy.tsx já usa a mesma busca e deve continuar funcionando.

Precisamos desaninhar esse JSON ao exibir e evitar gerar novas camadas quando salvar.

🎯 Objetivo
Criar um parser genérico extractCleanName que desfaça qualquer profundidade de aninhamento (até 5 níveis) e devolva apenas a string do nome.

Reutilizar esse parser tanto em EditarGenealogia.tsx quanto em ExpandedGenealogy.tsx (coloque em client/src/utils/genealogy.ts para não duplicar código).

Ajustar a lógica de mutationFn (PATCH /api/cavalos/:id) em EditarGenealogia.tsx para stringifyar apenas uma vez os campos de avô/avó.

Pequenas correções de UX (selects sempre com value definido, defaultValue no input dos avós etc.).

Garantir que nenhum outro fluxo de cadastro/edição de cavalo seja quebrado.

| Caminho                                                | Ação                                                                                   |
| ------------------------------------------------------ | -------------------------------------------------------------------------------------- |
| `client/src/utils/genealogy.ts`                        | **NOVO** – exporta `extractCleanName` (ver implementação abaixo).                      |
| `client/src/pages/EditarGenealogia.tsx`                | Importa o util, usa no form, corrige `mutationFn`, ajuste de selects e `defaultValue`. |
| `client/src/components/genetics/ExpandedGenealogy.tsx` | Importa o util e remove a versão local de `extractCleanName` (se existir).             |


 Implementações solicitadas
1. Conteúdo de extractCleanName
ts
Copiar
Editar
/**
 * Desfaz recursivamente JSON aninhado em cavaloNome até restar a string.
 * Limita a 5 iterações para evitar loops infinitos em dados corrompidos.
 */
export const extractCleanName = (raw: string | null | undefined): string => {
  let current: unknown = raw;
  let depth = 0;

  while (typeof current === "string" && current.trim().startsWith("{") && depth < 5) {
    try {
      const obj = JSON.parse(current);
      current = obj?.cavaloNome ?? "";
      depth += 1;
    } catch {
      break;
    }
  }

  return typeof current === "string" ? current : "";
};
2. Ajuste do PATCH
ts
Copiar
Editar
const normalizarAvos = (nome: string | null) =>
  nome
    ? JSON.stringify({
        tipo: "externo",
        cavaloSistemaId: null,
        cavaloNome: nome.trim(),
      })
    : null;

// dentro do mutationFn:
const body = {
  paiId: data.paiId,
  maeId: data.maeId,
  avoPaterno: normalizarAvos(data.avoPaterno),
  avoMaterno: normalizarAvos(data.avoMaterno),
};
3. Pequenos detalhes
Nos <Select> de pai/mãe use value={formData.paiId?.toString() ?? "0"} para nunca passar undefined.

Nos inputs de avós troque value={…} por defaultValue={extractCleanName(cavalo.avoPaterno)} para evitar controlled-state travando.

Remova chamadas duplicadas de useLocation().

 Critérios de Aceitação
Abrir /cavalo/:id/genealogia/editar mostra corretamente os nomes de avô/avó sem {}.

Salvar alterações não cria novas camadas de JSON na coluna – verificável no banco (string com apenas um nível).

O componente ExpandedGenealogy continua exibindo todos os nomes corretamente.

Todos os testes (se existirem) e lint passam; nenhum outro fluxo de cavalos quebra.

🚀 Próximos passos para você, Replit AI
Localize e edite os arquivos listados.

Implemente as alterações exatamente como acima, mantendo tipos e imports organizados.

Rode o app (npm run dev) e teste os casos descritos.

Commit:

javascript
Copiar
Editar
feat(genealogy): fix nested JSON parsing & prevent new layers
Avise no log se encontrou algum arquivo extra afetado.

Importante: não mude nada além do necessário; a lógica existente do sistema deve permanecer intacta.

