{"dados": "preview_1747439180198", "logs": "{\n  \"dados\": {\n    \"cavalo\": {\n      \"nome\": \"HONESTA 305 DA VENDRAMIN\",\n      \"registro\": \"B388719\",\n      \"rp\": \"305\",\n      \"sexo\": \"FÊMEA\",\n      \"nascimento\": \"05/12/2011\",\n      \"pelagem\": \"COL<PERSON>AD<PERSON>\",\n      \"criador\": \"ALDO VENDRAMIN\",\n      \"proprietario\": \"CAMILA WEBER\",\n      \"inspetor\": \"MARCOS GOMES ANTUNES\"\n    },\n    \"pai\": {\n      \"nome\": \"LUIZA DO CARRACHI\",\n      \"registro\": \"B279306\"\n    },\n    \"mae\": {\n      \"nome\": \"BT JURADO\"\n    },\n    \"avoPai\": {\n      \"nome\": \"LA INVERNADA PASCUERO\"\n    },\n    \"avaMae\": {\n      \"nome\": \"BELA DONA DO RETIRO DO OURO\"\n    },\n    \"avoPai2\": {\n      \"nome\": \"REG.MÉRITO\"\n    },\n    \"avaMae2\": {\n      \"nome\": \"TREN TREN ARREBOL\"\n    }\n  },\n  \"log\": \"[2025-05-16T23:46:16.617Z] [INFO] Iniciando processamento com OpenAI\\n[2025-05-16T23:46:20.196Z] [DEBUG] Conteúdo bruto da resposta: \\n{\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"HONESTA 305 DA VENDRAMIN\\\",\\n    \\\"registro\\\": \\\"B388719\\\",\\n    \\\"rp\\\": \\\"305\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"05/12/2011\\\",\\n    \\\"pelagem\\\": \\\"COLORADA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"ALDO VENDRAMIN\\\",\\n    \\\"inspetor_tecnico\\\": \\\"MARCOS GOMES ANTUNES\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"LUIZA DO CARRACHI\\\",\\n    \\\"registro\\\": \\\"B279306\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"BT JURADO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"LA INVERNADA PASCUERO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"BELA DONA DO RETIRO DO OURO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"REG.MÉRITO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"TREN TREN ARREBOL\\\",\\n    \\\"registro\\\": null\\n  }\\n}\\n[2025-05-16T23:46:20.197Z] [INFO] Recebido resposta da OpenAI e parseado com sucesso\\n[2025-05-16T23:46:20.197Z] [DEBUG] Dados extraídos: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"HONESTA 305 DA VENDRAMIN\\\",\\n    \\\"registro\\\": \\\"B388719\\\",\\n    \\\"rp\\\": \\\"305\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"05/12/2011\\\",\\n    \\\"pelagem\\\": \\\"COLORADA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"ALDO VENDRAMIN\\\",\\n    \\\"inspetor_tecnico\\\": \\\"MARCOS GOMES ANTUNES\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"LUIZA DO CARRACHI\\\",\\n    \\\"registro\\\": \\\"B279306\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"BT JURADO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"LA INVERNADA PASCUERO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"BELA DONA DO RETIRO DO OURO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"REG.MÉRITO\\\",\\n    \\\"registro\\\": null\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"TREN TREN ARREBOL\\\",\\n    \\\"registro\\\": null\\n  }\\n}\\n[2025-05-16T23:46:20.197Z] [INFO] Dados do cavalo principal extraídos: {\\n  \\\"nome\\\": \\\"HONESTA 305 DA VENDRAMIN\\\",\\n  \\\"registro\\\": \\\"B388719\\\",\\n  \\\"rp\\\": \\\"305\\\",\\n  \\\"sexo\\\": \\\"FEMEA\\\",\\n  \\\"nascimento\\\": \\\"05/12/2011\\\",\\n  \\\"pelagem\\\": \\\"COLORADA\\\",\\n  \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n  \\\"criador\\\": \\\"ALDO VENDRAMIN\\\",\\n  \\\"inspetor_tecnico\\\": \\\"MARCOS GOMES ANTUNES\\\"\\n}\\n[2025-05-16T23:46:20.198Z] [INFO] Dados do cavalo principal extraídos: HONESTA 305 DA VENDRAMIN (B388719)\\n[2025-05-16T23:46:20.198Z] [INFO] Pai: LUIZA DO CARRACHI (B279306)\\n[2025-05-16T23:46:20.198Z] [INFO] Mãe: BT JURADO (sem registro)\\n[2025-05-16T23:46:20.198Z] [INFO] Dados convertidos com sucesso\"\n}", "timestamp": "2025-05-16T23:46:20.198Z"}