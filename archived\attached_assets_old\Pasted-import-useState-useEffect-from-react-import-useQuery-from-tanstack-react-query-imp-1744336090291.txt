import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Loader2, PlusCircle, BarChart3, Ruler, LineChart } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

// Interfaces e componentes genéticos
import { AvaliacaoMorfologica } from "@/types/genetica";
import MorfologiaForm from "@/components/genetics/MorfologiaForm";
import MorfologiaChart from "@/components/genetics/MorfologiaChart";
import MedidasMorfologicas from "@/components/genetics/MedidasMorfologicas";

// Interface para cavalos
interface Cavalo {
  id: number;
  name: string;
  breed?: string;
  [key: string]: any; // Para propriedades adicionais
}

export default function MorfologiaPage() {
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState("avaliacao");
  const { toast } = useToast();

  // Buscar cavalos
  const {
    data: cavalos = [], // Valor padrão para evitar erros de undefined
    isLoading: isLoadingCavalos,
    error: cavalosError,
  } = useQuery<Cavalo[]>({
    queryKey: ["/api/cavalos"],
  });

  // Buscar avaliações morfológicas do cavalo selecionado
  const {
    data: avaliacoes = [], // Valor padrão para evitar erros de undefined
    isLoading: isLoadingAvaliacoes,
    error: avaliacoesError,
  } = useQuery<AvaliacaoMorfologica[]>({
    queryKey: ["/api/cavalos", selectedHorseId, "morfologia"],
    enabled: selectedHorseId !== null,
  });

  // Atualizar visualização quando mudar o cavalo
  useEffect(() => {
    if (cavalos && cavalos.length > 0 && !selectedHorseId) {
      setSelectedHorseId(cavalos[0].id);
    }
  }, [cavalos, selectedHorseId]);

  // Mostrar erros
  useEffect(() => {
    if (cavalosError) {
      toast({
        title: "Erro ao carregar cavalos",
        description: "Não foi possível carregar a lista de cavalos.",
        variant: "destructive",
      });
    }
    if (avaliacoesError) {
      toast({
        title: "Erro ao carregar avaliações",
        description: "Não foi possível carregar as avaliações morfológicas.",
        variant: "destructive",
      });
    }
  }, [cavalosError, avaliacoesError, toast]);

  // Estados para modais
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isChartModalOpen, setIsChartModalOpen] = useState(false);
  const [selectedAvaliacao, setSelectedAvaliacao] = useState<AvaliacaoMorfologica | null>(null);
  
  // Manipulador para adicionar nova avaliação
  const handleAdicionarAvaliacao = () => {
    setIsModalOpen(true);
  };
  
  // Fechar modal e recarregar dados
  const handleAvaliacaoSuccess = () => {
    setIsModalOpen(false);
    // Recarregar dados de avaliações
    queryClient.invalidateQueries({ queryKey: ["/api/cavalos", selectedHorseId, "morfologia"] });
    toast({
      title: "Avaliação salva com sucesso",
      description: "A avaliação morfológica foi adicionada ao histórico.",
    });
  };

  // Manipulador para visualizar gráfico
  const handleVisualizarGrafico = (avaliacao: AvaliacaoMorfologica) => {
    setSelectedAvaliacao(avaliacao);
    setIsChartModalOpen(true);
  };

  // Manipulador para salvar medidas morfológicas
  const handleSalvarMedidas = async (dadosMedidas: any) => {
    console.log("Salvando medidas morfológicas:", dadosMedidas);

    try {
      // Chamar a API para salvar as medidas
      const response = await fetch('/api/medidas-morfologicas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosMedidas),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Falha ao salvar as medidas morfológicas');
      }

      const result = await response.json();
      console.log('Resposta da API:', result);

      if (result.success) {
        toast({
          title: "Medidas salvas com sucesso",
          description: "As medidas morfológicas foram registradas no histórico do animal."
        });

        // Recarregar os dados, se necessário
        queryClient.invalidateQueries({ queryKey: ["/api/medidas-morfologicas", selectedHorseId] });
      } else {
        throw new Error(result.message || 'Erro ao salvar as medidas morfológicas');
      }
    } catch (error) {
      console.error('Erro ao salvar medidas morfológicas:', error);
      toast({
        title: "Erro ao salvar medidas",
        description: error instanceof Error ? error.message : "Ocorreu um erro ao salvar as medidas morfológicas.",
        variant: "destructive"
      });
    }
  };

  // Renderizar carregamento
  if (isLoadingCavalos) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Carregando cavalos...</span>
      </div>
    );
  }

  // Encontrar o nome do cavalo selecionado
  const selectedHorse = selectedHorseId 
    ? cavalos.find((c: any) => c.id === selectedHorseId)
    : null;
  
  const selectedHorseName = selectedHorse?.name || "Cavalo Selecionado";

  return (
    <div className="space-y-6">
      {/* Modal para adicionar nova avaliação */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Nova Avaliação Morfológica</DialogTitle>
            <DialogDescription>
              Preencha os dados da avaliação morfológica para {selectedHorseName}
            </DialogDescription>
          </DialogHeader>
          
          {selectedHorseId && (
            <MorfologiaForm 
              horseId={selectedHorseId} 
              horseName={selectedHorseName} 
              onSuccess={handleAvaliacaoSuccess}
              onCancel={() => setIsModalOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
      
      {/* Modal para visualizar gráfico da avaliação */}
      <Dialog open={isChartModalOpen} onOpenChange={setIsChartModalOpen}>
        <DialogContent className="max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Análise Morfológica
              {selectedAvaliacao?.dataMedicao && (
                <span className="text-muted-foreground text-sm font-normal ml-2">
                  {format(new Date(selectedAvaliacao.dataMedicao), "dd/MM/yyyy", { locale: ptBR })}
                </span>
              )}
            </DialogTitle>
            <DialogDescription>
              Visualização detalhada das pontuações e análise
            </DialogDescription>
          </DialogHeader>
          
          {selectedAvaliacao && (
            <MorfologiaChart avaliacao={selectedAvaliacao} />
          )}
          
          <div className="flex justify-end mt-6">
            <Button variant="outline" onClick={() => setIsChartModalOpen(false)}>
              Fechar
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-primary">Avaliação Morfológica</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Selecione um Cavalo</CardTitle>
          <CardDescription>
            Avalie as características morfológicas do animal
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select
            value={selectedHorseId?.toString() || ""}
            onValueChange={(value) => setSelectedHorseId(Number(value))}
          >
            <SelectTrigger className="w-full md:w-[300px]">
              <SelectValue placeholder="Selecione um cavalo" />
            </SelectTrigger>
            <SelectContent>
              {cavalos?.map((cavalo) => (
                <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                  {cavalo.name} ({cavalo.breed})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {selectedHorseId && selectedHorse && (
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="avaliacao" className="flex items-center">
              <LineChart className="h-4 w-4 mr-2" />
              Avaliação por Pontuação
            </TabsTrigger>
            <TabsTrigger value="medicao" className="flex items-center">
              <Ruler className="h-4 w-4 mr-2" />
              Medidas Morfológicas
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="avaliacao" className="space-y-6 mt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">
                Histórico de Avaliações - {selectedHorseName}
              </h3>
              <Button onClick={handleAdicionarAvaliacao} disabled={!selectedHorseId}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Nova Avaliação
              </Button>
            </div>
            
            {isLoadingAvaliacoes ? (
              <div className="flex items-center justify-center py-10">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Carregando avaliações...</span>
              </div>
            ) : avaliacoes && avaliacoes.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Data</TableHead>
                    <TableHead>Avaliador</TableHead>
                    <TableHead>Pontuação Total</TableHead>
                    <TableHead>Destaques</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {avaliacoes.map((avaliacao: AvaliacaoMorfologica) => {
                    // Calcular destaques (pontuações >= 8)
                    const destaques = [];
                    if (avaliacao.pontuacaoCabeca >= 8) destaques.push("Cabeça");
                    if (avaliacao.pontuacaoPescoco >= 8) destaques.push("Pescoço");
                    if (avaliacao.pontuacaoGarupa >= 8) destaques.push("Garupa");
                    if (avaliacao.pontuacaoAndamento >= 8) destaques.push("Andamento");

                    return (
                      <TableRow key={avaliacao.id}>
                        <TableCell>
                          {avaliacao.dataMedicao ? 
                            format(new Date(avaliacao.dataMedicao), "dd/MM/yyyy", {
                              locale: ptBR,
                            }) : 
                            "Data não informada"
                          }
                        </TableCell>
                        <TableCell>{avaliacao.avaliador}</TableCell>
                        <TableCell>
                          <span className="font-medium">
                            {avaliacao.pontuacaoTotal}/80
                          </span>
                        </TableCell>
                        <TableCell>
                          {destaques.length > 0
                            ? destaques.join(", ")
                            : "Nenhum destaque"}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleVisualizarGrafico(avaliacao)}
                          >
                            <BarChart3 className="h-4 w-4 mr-1" />
                            Gráfico
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-10 text-muted-foreground">
                <p>Nenhuma avaliação morfológica registrada para este cavalo.</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={handleAdicionarAvaliacao}
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Adicionar Primeira Avaliação
                </Button>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="medicao" className="mt-6">
            <MedidasMorfologicas 
              cavalo={selectedHorse}
              onSalvar={handleSalvarMedidas}
            />
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}