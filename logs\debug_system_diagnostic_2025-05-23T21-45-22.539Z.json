{"timestamp": "2025-05-23T21:45:22.538Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 394760192, "heapTotal": 110845952, "heapUsed": 71142568, "external": 8327370, "arrayBuffers": 235533}, "uptime": 3.60199894, "cpuUsage": {"user": 3183349, "system": 431905}, "resourceUsage": {"userCPUTime": 3183406, "systemCPUTime": 431912, "maxRSS": 385508, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102872, "majorPageFault": 0, "swappedOut": 0, "fsRead": 18880, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9098, "involuntaryContextSwitches": 13216}}