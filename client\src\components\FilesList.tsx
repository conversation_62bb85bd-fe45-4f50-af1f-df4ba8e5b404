import React, { useState, useEffect } from 'react';
import { 
  Download, 
  FileIcon, 
  FileImage, 
  FileVideo, 
  File, 
  Trash2,
  AlertCircle
} from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, Card<PERSON>itle } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface Arquivo {
  id: number;
  fileName: string;
  filePath: string;
  fileType: string;
  description: string;
  horseId: number;
  userId: number;
  createdAt?: string;
}

interface FilesListProps {
  horseId: number;
  onRefresh?: () => void;
  className?: string;
}

export function FilesList({ horseId, onRefresh, className }: FilesListProps) {
  const [files, setFiles] = useState<Arquivo[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedFileId, setSelectedFileId] = useState<number | null>(null);
  
  const fetchFiles = async () => {
    try {
      setLoading(true);
      
      // Obter o usuário do localStorage
      let user = null;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          user = JSON.parse(userStr);
        }
      } catch (e) {
        console.error("Erro ao obter usuário do localStorage:", e);
      }
      
      const userId = user?.id || localStorage.getItem('userId');
      
      if (!userId) {
        throw new Error("Usuário não autenticado. Faça login novamente.");
      }
      
      const response = await fetch(`/api/cavalos/${horseId}/arquivos`, {
        headers: {
          'user-id': userId.toString()
        }
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar arquivos: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      setFiles(data);
    } catch (error) {
      console.error('Erro ao buscar arquivos:', error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro desconhecido ao buscar arquivos",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };
  
  const handleDelete = async (id: number) => {
    try {
      // Obter o usuário do localStorage
      let user = null;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          user = JSON.parse(userStr);
        }
      } catch (e) {
        console.error("Erro ao obter usuário do localStorage:", e);
      }
      
      const userId = user?.id || localStorage.getItem('userId');
      
      if (!userId) {
        throw new Error("Usuário não autenticado. Faça login novamente.");
      }
      
      const response = await fetch(`/api/arquivos/${id}`, {
        method: 'DELETE',
        headers: {
          'user-id': userId.toString()
        }
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao excluir arquivo: ${response.status} ${response.statusText}`);
      }
      
      toast({
        title: "Sucesso",
        description: "Arquivo excluído com sucesso",
      });
      
      // Atualiza a lista
      fetchFiles();
      
      // Callback de atualização
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Erro ao excluir arquivo:', error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro desconhecido ao excluir arquivo",
        variant: "destructive"
      });
    }
  };
  
  const confirmDelete = (id: number) => {
    setSelectedFileId(id);
    setDeleteDialogOpen(true);
  };
  
  const executeDelete = () => {
    if (selectedFileId !== null) {
      handleDelete(selectedFileId);
      setDeleteDialogOpen(false);
      setSelectedFileId(null);
    }
  };
  
  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image':
        return <FileImage className="h-8 w-8 text-blue-500" />;
      case 'video':
        return <FileVideo className="h-8 w-8 text-green-500" />;
      case 'pdf':
        return <File className="h-8 w-8 text-red-500" />;
      default:
        return <FileIcon className="h-8 w-8 text-gray-500" />;
    }
  };
  
  // Carregar arquivos na montagem do componente
  useEffect(() => {
    if (horseId) {
      fetchFiles();
    }
  }, [horseId]);
  
  if (loading) {
    return <div className="py-4 text-center">Carregando arquivos...</div>;
  }
  
  if (files.length === 0) {
    return (
      <div className="py-8 text-center border rounded-md">
        <AlertCircle className="h-10 w-10 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-500">Nenhum arquivo encontrado para este cavalo</p>
      </div>
    );
  }
  
  return (
    <div className={`grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 ${className}`}>
      {files.map((file) => (
        <Card key={file.id} className="overflow-hidden">
          <CardHeader className="p-4 pb-2 flex items-center">
            {getFileIcon(file.fileType)}
            <CardTitle className="text-md ml-2 truncate">{file.fileName}</CardTitle>
          </CardHeader>
          
          <CardContent className="p-4 pt-2">
            <p className="text-sm text-gray-600 line-clamp-2">{file.description || 'Sem descrição'}</p>
            {file.createdAt && (
              <p className="text-xs text-gray-400 mt-1">
                Enviado em {new Date(file.createdAt).toLocaleDateString('pt-BR')}
              </p>
            )}
          </CardContent>
          
          <CardFooter className="p-4 pt-0 flex justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(`/api/arquivos/${file.id}/download`, '_blank')}
            >
              <Download className="h-4 w-4 mr-1" /> Abrir
            </Button>
            
            <Button
              variant="destructive"
              size="sm"
              onClick={() => confirmDelete(file.id)}
            >
              <Trash2 className="h-4 w-4 mr-1" /> Excluir
            </Button>
          </CardFooter>
        </Card>
      ))}
      
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este arquivo? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={executeDelete}>Excluir</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}