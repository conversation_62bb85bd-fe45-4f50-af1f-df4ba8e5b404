{"timestamp": "2025-05-16T23:22:46.937Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 263155712, "heapTotal": 118267904, "heapUsed": 73477584, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.454103785, "cpuUsage": {"user": 3154830, "system": 390925}, "resourceUsage": {"userCPUTime": 3154884, "systemCPUTime": 390932, "maxRSS": 295624, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105068, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 112, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8518, "involuntaryContextSwitches": 7936}}