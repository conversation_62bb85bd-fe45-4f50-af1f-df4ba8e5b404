{"timestamp": "2025-05-15T01:01:19.689Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 295845888, "heapTotal": 104992768, "heapUsed": 62348624, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.400510718, "cpuUsage": {"user": 2136286, "system": 319290}, "resourceUsage": {"userCPUTime": 2136339, "systemCPUTime": 319298, "maxRSS": 288912, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99268, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6438, "involuntaryContextSwitches": 1454}}