{"timestamp": "2025-05-26T21:28:37.491Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 400171008, "heapTotal": 106995712, "heapUsed": 79743920, "external": 8415357, "arrayBuffers": 235533}, "uptime": 2.365689338, "cpuUsage": {"user": 3272139, "system": 350702}, "resourceUsage": {"userCPUTime": 3272228, "systemCPUTime": 350702, "maxRSS": 390792, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105126, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 40, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8663, "involuntaryContextSwitches": 6908}}