{"timestamp": "2025-05-26T20:25:15.151Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 400269312, "heapTotal": 111452160, "heapUsed": 74482744, "external": 8227674, "arrayBuffers": 251917}, "uptime": 6.51117944, "cpuUsage": {"user": 2980625, "system": 429836}, "resourceUsage": {"userCPUTime": 2980697, "systemCPUTime": 429836, "maxRSS": 390888, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105628, "majorPageFault": 7, "swappedOut": 0, "fsRead": 59488, "fsWrite": 992, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9180, "involuntaryContextSwitches": 4995}}