{"timestamp": "2025-05-15T20:57:13.493Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404180992, "heapTotal": 113811456, "heapUsed": 72332472, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.824209072, "cpuUsage": {"user": 2809393, "system": 405440}, "resourceUsage": {"userCPUTime": 2809435, "systemCPUTime": 405446, "maxRSS": 394708, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103389, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8146, "involuntaryContextSwitches": 1877}}