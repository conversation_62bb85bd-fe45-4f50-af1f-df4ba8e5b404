import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'wouter';
import { Bell, AlertCircle, Clock, CheckCircle2 } from 'lucide-react';
import { format, isAfter, isBefore, parseISO, differenceInDays, formatDistanceToNow } from 'date-fns';
import { pt } from 'date-fns/locale';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { apiRequest } from '@/lib/queryClient';
import { Manejo, Cavalo } from '@shared/schema';

interface NotificacoesManejoProps {
  manejos?: Manejo[];
  cavalos?: Cavalo[];
  loading?: boolean;
  onUpdateStatus?: (manejoId: number, newStatus: string) => Promise<void>;
}

/**
 * Componente de Notificações de Manejos
 * 
 * Exibe um ícone de sino com as notificações de manejos vencidos ou 
 * que estão próximos do vencimento, permitindo marcar como concluídos
 */
export function NotificacoesManejo({ 
  manejos = [], 
  cavalos = [], 
  loading = false,
  onUpdateStatus 
}: NotificacoesManejoProps) {
  const [open, setOpen] = useState(false);
  const [notificacoes, setNotificacoes] = useState<{
    vencidos: Array<Manejo & { cavaloNome?: string }>;
    proximosVencimento: Array<Manejo & { cavaloNome?: string }>;
  }>({
    vencidos: [],
    proximosVencimento: []
  });

  // Processar manejos para categorizar por status
  useEffect(() => {
    if (!manejos.length) return;

    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);
    
    // Adicionar nome do cavalo aos manejos
    const manejosComCavalo = manejos.map(manejo => {
      let cavaloNome = 'Cavalo não identificado';
      
      if (manejo.horseId) {
        const cavalo = cavalos.find(c => c.id === manejo.horseId);
        if (cavalo) {
          cavaloNome = cavalo.name;
        }
      }
      
      return {
        ...manejo,
        cavaloNome
      };
    });
    
    // Filtrar manejos vencidos (data de vencimento antes de hoje e status pendente)
    const vencidos = manejosComCavalo.filter(manejo => {
      if (manejo.status !== 'pendente') return false;
      
      const dataVencimento = manejo.dataVencimento 
        ? parseISO(manejo.dataVencimento)
        : null;
        
      if (!dataVencimento) return false;
      
      return isBefore(dataVencimento, hoje);
    });
    
    // Filtrar manejos próximos do vencimento (data de vencimento em até 7 dias)
    const proximosVencimento = manejosComCavalo.filter(manejo => {
      if (manejo.status !== 'pendente') return false;
      
      const dataVencimento = manejo.dataVencimento 
        ? parseISO(manejo.dataVencimento)
        : null;
        
      if (!dataVencimento) return false;
      
      if (isBefore(dataVencimento, hoje)) return false; // já está nos vencidos
      
      const diasAteVencimento = differenceInDays(dataVencimento, hoje);
      return diasAteVencimento <= 7; // até 7 dias para vencer
    });
    
    setNotificacoes({
      vencidos,
      proximosVencimento
    });
  }, [manejos, cavalos]);

  // Calcular número total de notificações
  const totalNotificacoes = notificacoes.vencidos.length + notificacoes.proximosVencimento.length;

  // Obter a cor e o texto com base no status e prioridade
  const getStatusInfo = (manejo: Manejo) => {
    // Verificar se está vencido
    if (manejo.dataVencimento) {
      const dataVencimento = parseISO(manejo.dataVencimento);
      const hoje = new Date();
      hoje.setHours(0, 0, 0, 0);
      
      if (isBefore(dataVencimento, hoje) && manejo.status === 'pendente') {
        return {
          cor: 'bg-red-500',
          texto: 'Vencido',
          corTexto: 'text-red-500',
        };
      }
    }
    
    // Verificar status
    const statusMap: Record<string, { cor: string; texto: string; corTexto: string }> = {
      'pendente': {
        cor: 'bg-yellow-500',
        texto: 'Pendente',
        corTexto: 'text-yellow-500',
      },
      'concluido': {
        cor: 'bg-green-500',
        texto: 'Concluído',
        corTexto: 'text-green-500',
      },
      'cancelado': {
        cor: 'bg-gray-500',
        texto: 'Cancelado',
        corTexto: 'text-gray-500',
      }
    };
    
    // Verificar prioridade se o status for pendente
    if (manejo.status === 'pendente' && manejo.prioridade) {
      const prioridadeMap: Record<string, { cor: string; texto: string; corTexto: string }> = {
        'baixa': {
          cor: 'bg-blue-500',
          texto: 'Baixa Prioridade',
          corTexto: 'text-blue-500',
        },
        'normal': {
          cor: 'bg-yellow-500',
          texto: 'Prioridade Normal',
          corTexto: 'text-yellow-500',
        },
        'alta': {
          cor: 'bg-orange-500',
          texto: 'Alta Prioridade',
          corTexto: 'text-orange-500',
        },
        'urgente': {
          cor: 'bg-red-500',
          texto: 'Urgente',
          corTexto: 'text-red-500',
        }
      };
      
      return prioridadeMap[manejo.prioridade.toLowerCase()] || statusMap[manejo.status];
    }
    
    return statusMap[manejo.status] || {
      cor: 'bg-gray-500',
      texto: 'Desconhecido',
      corTexto: 'text-gray-500',
    };
  };

  // Retornar o nome do tipo de manejo
  const getTipoNome = (tipo: string) => {
    const tipos: Record<string, string> = {
      'veterinary': 'Consulta Veterinária',
      'farrier': 'Ferrageamento',
      'vaccination': 'Vacinação',
      'deworming': 'Vermifugação',
      'dental': 'Tratamento Dentário',
      'training': 'Treinamento',
      'other': 'Outro'
    };
    
    return tipos[tipo] || tipo.charAt(0).toUpperCase() + tipo.slice(1);
  };

  // Marcar um manejo como concluído
  const handleMarkAsCompleted = async (manejoId: number) => {
    if (!onUpdateStatus) return;
    
    try {
      await onUpdateStatus(manejoId, 'concluido');
      
      // Atualizar notificações localmente para feedback imediato
      setNotificacoes(prev => {
        const vencidos = prev.vencidos.filter(m => m.id !== manejoId);
        const proximosVencimento = prev.proximosVencimento.filter(m => m.id !== manejoId);
        
        return {
          vencidos,
          proximosVencimento
        };
      });
    } catch (error) {
      console.error('Erro ao atualizar status do manejo:', error);
    }
  };

  // Renderiza um manejo único na lista de notificações
  const renderManejo = (manejo: Manejo & { cavaloNome?: string }, index: number, tipo: 'vencido' | 'proximo') => {
    const statusInfo = getStatusInfo(manejo);
    const isVencido = tipo === 'vencido';
    
    return (
      <div 
        key={`${tipo}-${manejo.id || index}`}
        className="mb-2 p-3 border rounded-md hover:bg-muted/40 transition-colors"
      >
        <div className="flex items-start">
          <div className="flex-shrink-0 pt-0.5">
            {isVencido ? (
              <AlertCircle className="h-5 w-5 text-red-500" />
            ) : (
              <Clock className="h-5 w-5 text-yellow-500" />
            )}
          </div>
          <div className="ml-3 flex-grow">
            <div className="flex justify-between">
              <Badge variant="outline" className={`${statusInfo.corTexto} border-current`}>
                {getTipoNome(manejo.tipo)}
              </Badge>
              <span className="text-xs text-gray-500">
                {manejo.dataVencimento ? formatDistanceToNow(parseISO(manejo.dataVencimento), { addSuffix: true, locale: pt }) : ''}
              </span>
            </div>
            <h4 className="font-medium text-sm mt-1">{manejo.observacoes || `Manejo #${manejo.id}`}</h4>
            {manejo.cavaloNome && (
              <p className="text-sm text-gray-600">Cavalo: {manejo.cavaloNome}</p>
            )}
            {manejo.dataVencimento && (
              <p className="text-xs text-gray-500 mt-1">
                Vencimento: {format(parseISO(manejo.dataVencimento), 'dd/MM/yyyy', { locale: pt })}
              </p>
            )}
            <div className="mt-2 flex items-center space-x-2">
              <Button 
                size="sm" 
                variant="ghost" 
                className="h-7 px-2 text-xs text-green-600 hover:text-green-700 hover:bg-green-50"
                onClick={() => handleMarkAsCompleted(manejo.id!)}
              >
                <CheckCircle2 className="h-3.5 w-3.5 mr-1" />
                Marcar como concluído
              </Button>
              <Link href={`/manejos/${manejo.id}`}>
                <Button size="sm" variant="outline" className="h-7 px-2 text-xs">
                  Ver detalhes
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Button variant="ghost" size="sm" className="relative h-9 w-9 p-0" disabled>
        <Bell className="h-5 w-5" />
      </Button>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="relative h-9 w-9 p-0">
          <Bell className="h-5 w-5" />
          {totalNotificacoes > 0 && (
            <span className="absolute top-0 right-0 h-4 min-w-[16px] rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center px-1">
              {totalNotificacoes}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" side="bottom" align="end">
        <Card className="border-0">
          <CardHeader className="px-4 py-3 border-b">
            <CardTitle className="text-lg">Notificações</CardTitle>
            <CardDescription>
              {totalNotificacoes === 0
                ? 'Não há notificações a exibir.'
                : `${totalNotificacoes} ${totalNotificacoes === 1 ? 'notificação' : 'notificações'} pendentes.`}
            </CardDescription>
          </CardHeader>
          <ScrollArea className="h-[320px]">
            <CardContent className="px-4 py-3">
              {notificacoes.vencidos.length > 0 && (
                <div className="mb-4">
                  <h3 className="font-semibold text-red-600 text-sm flex items-center mb-2">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    Manejos Vencidos ({notificacoes.vencidos.length})
                  </h3>
                  {notificacoes.vencidos.map((manejo, i) => renderManejo(manejo, i, 'vencido'))}
                </div>
              )}
              
              {notificacoes.proximosVencimento.length > 0 && (
                <div className="mb-2">
                  <h3 className="font-semibold text-yellow-600 text-sm flex items-center mb-2">
                    <Clock className="h-4 w-4 mr-1" />
                    Manejos Próximos ({notificacoes.proximosVencimento.length})
                  </h3>
                  {notificacoes.proximosVencimento.map((manejo, i) => renderManejo(manejo, i, 'proximo'))}
                </div>
              )}
              
              {totalNotificacoes === 0 && (
                <div className="text-center py-6 text-gray-500">
                  <CheckCircle2 className="h-10 w-10 mx-auto mb-2 text-green-500" />
                  <p>Não há manejos vencidos ou próximos do vencimento.</p>
                </div>
              )}
            </CardContent>
          </ScrollArea>
          {totalNotificacoes > 0 && (
            <CardFooter className="flex justify-between px-4 py-3 border-t">
              <Link href="/dashboard/manejos">
                <Button variant="outline" className="w-full" onClick={() => setOpen(false)}>
                  Ver todos os manejos
                </Button>
              </Link>
            </CardFooter>
          )}
        </Card>
      </PopoverContent>
    </Popover>
  );
}