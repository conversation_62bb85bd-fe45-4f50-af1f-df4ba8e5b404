{"timestamp": "2025-05-15T00:21:51.041Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 373510144, "heapTotal": 91885568, "heapUsed": 70580240, "external": 6890744, "arrayBuffers": 106994}, "uptime": 1.559170684, "cpuUsage": {"user": 2340350, "system": 325640}, "resourceUsage": {"userCPUTime": 2340386, "systemCPUTime": 325645, "maxRSS": 364756, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 97460, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6531, "involuntaryContextSwitches": 3042}}