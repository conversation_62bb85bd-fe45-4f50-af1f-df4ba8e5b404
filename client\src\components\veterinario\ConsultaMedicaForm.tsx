import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Stethoscope, 
  Save, 
  Calendar,
  Clock,
  AlertTriangle,
  Thermometer,
  Heart,
  Activity
} from "lucide-react";
import { <PERSON><PERSON><PERSON> } from "@shared/schema";

// Schema de validação para consulta médica
const consultaMedicaSchema = z.object({
  horseId: z.number().min(1, "Selecione um cavalo"),
  veterinarioNome: z.string().min(2, "Nome do veterinário é obrigatório"),
  veterinarioContato: z.string().optional(),
  crmv: z.string().optional(),
  dataConsulta: z.string().min(1, "Data da consulta é obrigatória"),
  horaConsulta: z.string().optional(),
  motivoConsulta: z.string().min(5, "Descreva o motivo da consulta"),
  anamnese: z.string().optional(),
  exameFisico: z.string().optional(),
  sinaisVitais: z.string().optional(),
  diagnostico: z.string().optional(),
  diagnosticoDiferencial: z.string().optional(),
  tratamento: z.string().optional(),
  prescricao: z.string().optional(),
  examesComplementares: z.string().optional(),
  proximaConsulta: z.string().optional(),
  observacoes: z.string().optional(),
  custo: z.number().min(0).optional(),
  status: z.enum(["Agendada", "Em andamento", "Concluída", "Cancelada"]),
  urgencia: z.enum(["Baixa", "Normal", "Alta", "Crítica"]),
});

type ConsultaMedicaFormData = z.infer<typeof consultaMedicaSchema>;

interface ConsultaMedicaFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cavalos: Cavalo[];
  onSubmit: (data: ConsultaMedicaFormData) => Promise<void>;
  isSubmitting: boolean;
  consultaInicial?: Partial<ConsultaMedicaFormData>;
}

/**
 * Formulário Inteligente para Consultas Médicas Veterinárias
 * 
 * Oferece interface completa para registro de consultas médicas:
 * - Dados básicos da consulta
 * - Anamnese e exame físico
 * - Diagnóstico e tratamento
 * - Prescrições e recomendações
 * - Sistema de urgência e status
 */
export default function ConsultaMedicaForm({
  open,
  onOpenChange,
  cavalos,
  onSubmit,
  isSubmitting,
  consultaInicial
}: ConsultaMedicaFormProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  const form = useForm<ConsultaMedicaFormData>({
    resolver: zodResolver(consultaMedicaSchema),
    defaultValues: {
      status: "Agendada",
      urgencia: "Normal",
      dataConsulta: new Date().toISOString().split('T')[0],
      horaConsulta: "14:00",
      ...consultaInicial,
    },
  });

  const handleSubmit = async (data: ConsultaMedicaFormData) => {
    try {
      await onSubmit(data);
      form.reset();
      setCurrentStep(1);
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao salvar consulta:", error);
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepTitle = (step: number) => {
    const titles = {
      1: "Dados Básicos",
      2: "Anamnese e Exame",
      3: "Diagnóstico",
      4: "Tratamento e Prescrição"
    };
    return titles[step as keyof typeof titles];
  };

  const selectedHorse = cavalos.find(c => c.id === form.watch("horseId"));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Stethoscope className="h-5 w-5 text-blue-600" />
            Nova Consulta Médica
          </DialogTitle>
          <DialogDescription>
            Passo {currentStep} de {totalSteps}: {getStepTitle(currentStep)}
          </DialogDescription>
        </DialogHeader>

        {/* Indicador de Progresso */}
        <div className="flex items-center space-x-2 mb-6">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div key={i} className="flex items-center">
              <div 
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                  ${i + 1 <= currentStep 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-600'
                  }`}
              >
                {i + 1}
              </div>
              {i < totalSteps - 1 && (
                <div 
                  className={`w-12 h-1 mx-2 ${
                    i + 1 < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          ))}
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            
            {/* Passo 1: Dados Básicos */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="horseId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cavalo *</FormLabel>
                        <Select
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          value={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione o cavalo" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {cavalos.map((cavalo) => (
                              <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                                {cavalo.name} - {cavalo.breed}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="urgencia"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Urgência</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Baixa">
                              <Badge variant="secondary">Baixa</Badge>
                            </SelectItem>
                            <SelectItem value="Normal">
                              <Badge variant="outline">Normal</Badge>
                            </SelectItem>
                            <SelectItem value="Alta">
                              <Badge variant="destructive">Alta</Badge>
                            </SelectItem>
                            <SelectItem value="Crítica">
                              <Badge variant="destructive">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Crítica
                              </Badge>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="veterinarioNome"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Veterinário *</FormLabel>
                        <FormControl>
                          <Input placeholder="Dr. Nome do Veterinário" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="crmv"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CRMV</FormLabel>
                        <FormControl>
                          <Input placeholder="CRMV-XX 12345" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="veterinarioContato"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contato</FormLabel>
                        <FormControl>
                          <Input placeholder="Telefone ou email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="dataConsulta"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          Data *
                        </FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="horaConsulta"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          Hora
                        </FormLabel>
                        <FormControl>
                          <Input type="time" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Agendada">Agendada</SelectItem>
                            <SelectItem value="Em andamento">Em andamento</SelectItem>
                            <SelectItem value="Concluída">Concluída</SelectItem>
                            <SelectItem value="Cancelada">Cancelada</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="motivoConsulta"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Motivo da Consulta *</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Descreva o motivo da consulta, sintomas observados, etc."
                          className="min-h-[100px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {selectedHorse && (
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="font-medium text-blue-900 mb-2">Informações do Animal</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                      <div><span className="font-medium">Nome:</span> {selectedHorse.name}</div>
                      <div><span className="font-medium">Raça:</span> {selectedHorse.breed}</div>
                      <div><span className="font-medium">Sexo:</span> {selectedHorse.sexo}</div>
                      <div><span className="font-medium">Idade:</span> 
                        {selectedHorse.birthDate ? 
                          new Date().getFullYear() - new Date(selectedHorse.birthDate).getFullYear() + " anos" : 
                          "N/I"
                        }
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Passo 2: Anamnese e Exame */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="anamnese"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Anamnese</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Histórico clínico, sintomas relatados, comportamento, apetite, etc."
                          className="min-h-[120px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sinaisVitais"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Thermometer className="h-4 w-4" />
                        Sinais Vitais
                      </FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Temperatura: _°C, FC: _bpm, FR: _mpm, Mucosas: _, TPC: _s"
                          className="min-h-[80px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="exameFisico"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Activity className="h-4 w-4" />
                        Exame Físico
                      </FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Inspeção, palpação, auscultação, percussão. Descreva achados por sistema..."
                          className="min-h-[150px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Passo 3: Diagnóstico */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="diagnostico"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Diagnóstico</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Diagnóstico principal baseado nos achados clínicos"
                          className="min-h-[100px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="diagnosticoDiferencial"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Diagnóstico Diferencial</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Outras possibilidades diagnósticas a considerar"
                          className="min-h-[80px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="examesComplementares"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Exames Complementares</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Exames laboratoriais, radiografias, ultrassonografias solicitadas"
                          className="min-h-[80px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Passo 4: Tratamento */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="tratamento"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plano de Tratamento</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Descrição do tratamento proposto, procedimentos, cuidados"
                          className="min-h-[100px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="prescricao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Prescrição Médica</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Medicamentos prescritos: nome, dosagem, frequência, duração"
                          className="min-h-[120px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="proximaConsulta"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Próxima Consulta</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="custo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Custo da Consulta (R$)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            placeholder="0,00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="observacoes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Observações Gerais</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Observações adicionais, recomendações, etc."
                          className="min-h-[80px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <Separator />

            {/* Botões de Navegação */}
            <div className="flex justify-between">
              <div>
                {currentStep > 1 && (
                  <Button type="button" variant="outline" onClick={prevStep}>
                    Anterior
                  </Button>
                )}
              </div>
              
              <div className="flex gap-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => onOpenChange(false)}
                >
                  Cancelar
                </Button>
                
                {currentStep < totalSteps ? (
                  <Button type="button" onClick={nextStep}>
                    Próximo
                  </Button>
                ) : (
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2" />
                        Salvando...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Salvar Consulta
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}