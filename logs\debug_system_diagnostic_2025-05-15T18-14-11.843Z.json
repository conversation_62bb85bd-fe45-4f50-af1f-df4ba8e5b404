{"timestamp": "2025-05-15T18:14:11.843Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 393449472, "heapTotal": 108306432, "heapUsed": 72350552, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.525460531, "cpuUsage": {"user": 2563088, "system": 332393}, "resourceUsage": {"userCPUTime": 2563128, "systemCPUTime": 332393, "maxRSS": 384228, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102075, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8427, "involuntaryContextSwitches": 1195}}