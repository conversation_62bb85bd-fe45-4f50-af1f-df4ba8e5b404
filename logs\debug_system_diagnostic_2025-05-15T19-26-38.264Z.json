{"timestamp": "2025-05-15T19:26:38.263Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 393814016, "heapTotal": 111714304, "heapUsed": 72445488, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.988388312, "cpuUsage": {"user": 2820731, "system": 416162}, "resourceUsage": {"userCPUTime": 2820813, "systemCPUTime": 416162, "maxRSS": 384584, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102480, "majorPageFault": 0, "swappedOut": 0, "fsRead": 29464, "fsWrite": 16, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7932, "involuntaryContextSwitches": 5676}}