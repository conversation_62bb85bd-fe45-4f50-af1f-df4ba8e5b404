{"timestamp": "2025-05-16T17:18:21.353Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 307142656, "heapTotal": 115646464, "heapUsed": 72614480, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.86518938, "cpuUsage": {"user": 2796555, "system": 402590}, "resourceUsage": {"userCPUTime": 2796619, "systemCPUTime": 402590, "maxRSS": 299944, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102427, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8544, "involuntaryContextSwitches": 3155}}