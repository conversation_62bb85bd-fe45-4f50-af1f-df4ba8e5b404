{"timestamp": "2025-05-15T01:23:06.964Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 381349888, "heapTotal": 98439168, "heapUsed": 73045928, "external": 6852171, "arrayBuffers": 90610}, "uptime": 1.643004206, "cpuUsage": {"user": 2368217, "system": 331231}, "resourceUsage": {"userCPUTime": 2368284, "systemCPUTime": 331231, "maxRSS": 372412, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100849, "majorPageFault": 0, "swappedOut": 0, "fsRead": 32, "fsWrite": 128, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6204, "involuntaryContextSwitches": 2852}}