{"timestamp": "2025-05-15T18:13:54.383Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 330399744, "heapTotal": 192077824, "heapUsed": 169716960, "external": 14599753, "arrayBuffers": 1633291}, "uptime": 74.296872748, "cpuUsage": {"user": 16710514, "system": 1035436}, "resourceUsage": {"userCPUTime": 16710519, "systemCPUTime": 1035436, "maxRSS": 898956, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 262854, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 664, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 24354, "involuntaryContextSwitches": 12517}}