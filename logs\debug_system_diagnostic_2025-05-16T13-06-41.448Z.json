{"timestamp": "2025-05-16T13:06:41.448Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 420237312, "heapTotal": 128159744, "heapUsed": 102823000, "external": 8290836, "arrayBuffers": 257466}, "uptime": 2.350622255, "cpuUsage": {"user": 2995033, "system": 372777}, "resourceUsage": {"userCPUTime": 2995086, "systemCPUTime": 372777, "maxRSS": 410388, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105838, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 96, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8244, "involuntaryContextSwitches": 6028}}