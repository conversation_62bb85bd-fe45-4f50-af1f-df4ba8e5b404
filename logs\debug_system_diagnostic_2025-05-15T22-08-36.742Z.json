{"timestamp": "2025-05-15T22:08:36.742Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 394268672, "heapTotal": 102277120, "heapUsed": 79238296, "external": 8300372, "arrayBuffers": 257466}, "uptime": 3.476978328, "cpuUsage": {"user": 3108771, "system": 388258}, "resourceUsage": {"userCPUTime": 3108829, "systemCPUTime": 388258, "maxRSS": 385028, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101125, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7454, "involuntaryContextSwitches": 13261}}