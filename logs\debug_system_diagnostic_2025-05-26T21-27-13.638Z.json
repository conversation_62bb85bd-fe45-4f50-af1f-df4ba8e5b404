{"timestamp": "2025-05-26T21:27:13.638Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 408055808, "heapTotal": 121565184, "heapUsed": 95841248, "external": 8494992, "arrayBuffers": 278084}, "uptime": 1.801848157, "cpuUsage": {"user": 2777424, "system": 322091}, "resourceUsage": {"userCPUTime": 2777489, "systemCPUTime": 322099, "maxRSS": 398492, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106226, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8506, "involuntaryContextSwitches": 1905}}