import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import { CavaloFormValues } from '@/pages/CavaloFormPage';

/**
 * Hook para buscar todos os cavalos para seleção em dropdowns
 * Inclui cavalos do plantel e externos
 */
export function useCavalosDropdown() {
  const query = useQuery({
    queryKey: ['/api/cavalos-genealogia'],
    queryFn: async () => {
      try {
        const response = await fetch('/api/cavalos-genealogia', {
          headers: {
            'Content-Type': 'application/json',
            'User-Id': localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).id : '1'
          }
        });
        
        if (!response.ok) {
          throw new Error('Erro ao buscar cavalos');
        }
        
        return await response.json();
      } catch (error) {
        console.error('Erro ao buscar cavalos para dropdown:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false
  });
  
  return {
    cavalos: query.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error
  };
}

/**
 * Hook para buscar detalhes de um cavalo pelo ID
 */
export function useCavaloDetalhes(id: string | number | null) {
  const query = useQuery({
    queryKey: [`/api/cavalos/${id}`],
    queryFn: async () => {
      if (!id) return null;
      
      try {
        const response = await fetch(`/api/cavalos/${id}`, {
          headers: {
            'Content-Type': 'application/json',
            'User-Id': localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).id : '1'
          }
        });
        
        if (!response.ok) {
          throw new Error('Erro ao buscar detalhes do cavalo');
        }
        
        return await response.json();
      } catch (error) {
        console.error(`Erro ao buscar cavalo ${id}:`, error);
        throw error;
      }
    },
    enabled: !!id,
    staleTime: 60 * 1000, // 1 minuto
    refetchOnWindowFocus: false
  });
  
  return {
    cavalo: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error
  };
}

/**
 * Hook para criar um novo cavalo
 */
export function useCriarCavalo() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (dados: CavaloFormValues) => {
      const response = await fetch('/api/cavalos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Id': localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).id : '1'
        },
        body: JSON.stringify(dados)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao criar cavalo');
      }
      
      return await response.json();
    },
    onSuccess: () => {
      // Invalidar cache para recarregar dados
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia'] });
      
      toast({
        title: 'Sucesso!',
        description: 'Cavalo cadastrado com sucesso.',
      });
    },
    onError: (error: Error) => {
      console.error('Erro ao criar cavalo:', error);
      
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível cadastrar o cavalo. Tente novamente.',
        variant: 'destructive'
      });
    }
  });
}

/**
 * Hook para atualizar um cavalo existente
 */
export function useAtualizarCavalo() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, dados }: { id: number, dados: CavaloFormValues }) => {
      const response = await fetch(`/api/cavalos/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'User-Id': localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).id : '1'
        },
        body: JSON.stringify(dados)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao atualizar cavalo');
      }
      
      return await response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidar cache para recarregar dados
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${variables.id}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia'] });
      
      toast({
        title: 'Sucesso!',
        description: 'Cavalo atualizado com sucesso.',
      });
    },
    onError: (error: Error) => {
      console.error('Erro ao atualizar cavalo:', error);
      
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível atualizar o cavalo. Tente novamente.',
        variant: 'destructive'
      });
    }
  });
}