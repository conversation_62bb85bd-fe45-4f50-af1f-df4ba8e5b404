import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  <PERSON><PERSON>, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Settings, 
  Users, 
  Bell, 
  Shield, 
  Database, 
  Palette, 
  Share2, 
  Save, 
  Building, 
  Mail, 
  Smartphone, 
  MapPin,
  CloudUpload
} from 'lucide-react';
import { LayoutWrapper } from '@/components/Layout';
import { Separator } from '@/components/ui/separator';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";

/**
 * Interface para os dados da empresa/fazenda
 */
interface FazendaConfig {
  nome: string;
  razaoSocial: string;
  cnpj: string;
  inscricaoEstadual: string;
  logo: string | null;
  endereco: {
    rua: string;
    numero: string;
    complemento: string;
    bairro: string;
    cidade: string;
    estado: string;
    cep: string;
  };
  contato: {
    telefone: string;
    celular: string;
    email: string;
    site: string;
  };
}

/**
 * Interface para as configurações do sistema
 */
interface SystemConfig {
  theme: 'light' | 'dark' | 'system';
  language: string;
  dateFormat: string;
  numberFormat: string;
  timezone: string;
  notifications: {
    email: boolean;
    browser: boolean;
    agenda: boolean;
    tarefas: boolean;
    financeiro: boolean;
    animais: boolean;
  };
  backup: {
    automatic: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
    keepBackups: number;
  };
}

/**
 * Componente principal da página de Configurações
 */
export function ConfiguracoesPage() {
  // Estado para as configurações da fazenda
  const [fazendaConfig, setFazendaConfig] = useState<FazendaConfig>({
    nome: 'EquiGestor Haras',
    razaoSocial: 'EquiGestor Criação de Cavalos LTDA',
    cnpj: '12.345.678/0001-90',
    inscricaoEstadual: '123.456.789',
    logo: null,
    endereco: {
      rua: 'Estrada das Palmeiras',
      numero: '1000',
      complemento: 'Fazenda Km 5',
      bairro: 'Zona Rural',
      cidade: 'Campinas',
      estado: 'SP',
      cep: '13000-000'
    },
    contato: {
      telefone: '(19) 3333-4444',
      celular: '(19) 99999-8888',
      email: '<EMAIL>',
      site: 'www.equigestor.com.br'
    }
  });

  // Estado para as configurações do sistema
  const [systemConfig, setSystemConfig] = useState<SystemConfig>({
    theme: 'light',
    language: 'pt-BR',
    dateFormat: 'DD/MM/YYYY',
    numberFormat: 'pt-BR',
    timezone: 'America/Sao_Paulo',
    notifications: {
      email: true,
      browser: true,
      agenda: true,
      tarefas: true,
      financeiro: true,
      animais: true
    },
    backup: {
      automatic: true,
      frequency: 'daily',
      keepBackups: 7
    }
  });

  // Estado para indicar que houve alterações não salvas
  const [hasChanges, setHasChanges] = useState(false);

  // Handler para atualizar configurações da fazenda
  const updateFazendaConfig = (field: string, value: any) => {
    setFazendaConfig(prev => {
      // Verificar se o campo é um campo aninhado
      if (field.includes('.')) {
        const [section, key] = field.split('.');
        return {
          ...prev,
          [section]: {
            ...prev[section as keyof FazendaConfig],
            [key]: value
          }
        };
      }
      
      return {
        ...prev,
        [field]: value
      };
    });
    
    setHasChanges(true);
  };

  // Handler para atualizar configurações do sistema
  const updateSystemConfig = (field: string, value: any) => {
    setSystemConfig(prev => {
      // Verificar se o campo é um campo aninhado
      if (field.includes('.')) {
        const [section, key] = field.split('.');
        return {
          ...prev,
          [section]: {
            ...prev[section as keyof SystemConfig],
            [key]: value
          }
        };
      }
      
      return {
        ...prev,
        [field]: value
      };
    });
    
    setHasChanges(true);
  };

  // Função para salvar as configurações
  const saveConfigurations = () => {
    console.log('Salvando configurações:', { fazendaConfig, systemConfig });
    // Aqui seria implementada a lógica para salvar no backend
    setHasChanges(false);
    
    // Exibir notificação de sucesso (em uma implementação real)
    alert('Configurações salvas com sucesso!');
  };

  return (
    <LayoutWrapper pageTitle="Configurações">
      <div className="flex flex-col gap-6">
        {/* Cabeçalho com ações principais */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Configurações do Sistema</h1>
            <p className="text-muted-foreground">
              Personalize o EquiGestor AI de acordo com suas necessidades
            </p>
          </div>
          
          <Button 
            onClick={saveConfigurations}
            disabled={!hasChanges}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            <span>Salvar Alterações</span>
          </Button>
        </div>
        
        {/* Tabs de configurações */}
        <Tabs defaultValue="geral" className="w-full">
          <TabsList className="grid grid-cols-2 md:grid-cols-5 lg:w-[600px]">
            <TabsTrigger value="geral" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">Geral</span>
            </TabsTrigger>
            <TabsTrigger value="fazenda" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              <span className="hidden sm:inline">Fazenda</span>
            </TabsTrigger>
            <TabsTrigger value="notificacoes" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <span className="hidden sm:inline">Notificações</span>
            </TabsTrigger>
            <TabsTrigger value="usuarios" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span className="hidden sm:inline">Usuários</span>
            </TabsTrigger>
            <TabsTrigger value="sistema" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              <span className="hidden sm:inline">Sistema</span>
            </TabsTrigger>
          </TabsList>
          
          {/* Conteúdo da Tab Geral */}
          <TabsContent value="geral">
            <Card>
              <CardHeader>
                <CardTitle>Configurações Gerais</CardTitle>
                <CardDescription>
                  Configurações básicas do sistema e da interface
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Aparência */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Aparência</h3>
                  <div className="space-y-4">
                    <div className="grid gap-2">
                      <Label htmlFor="theme">Tema</Label>
                      <Select 
                        value={systemConfig.theme} 
                        onValueChange={(value) => updateSystemConfig('theme', value)}
                      >
                        <SelectTrigger id="theme">
                          <SelectValue placeholder="Selecione um tema" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Claro</SelectItem>
                          <SelectItem value="dark">Escuro</SelectItem>
                          <SelectItem value="system">Sistema</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-sm text-muted-foreground">
                        Escolha o tema de interface que mais lhe agrada
                      </p>
                    </div>
                    
                    <div className="grid gap-2">
                      <Label htmlFor="accent-color">Cor de Destaque</Label>
                      <div className="flex items-center gap-4">
                        <RadioGroup 
                          defaultValue="blue" 
                          className="flex gap-2" 
                          orientation="horizontal"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem 
                              value="blue" 
                              id="color-blue" 
                              className="border-blue-600" 
                            />
                            <Label htmlFor="color-blue">Azul</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem 
                              value="green" 
                              id="color-green" 
                              className="border-green-600" 
                            />
                            <Label htmlFor="color-green">Verde</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem 
                              value="purple" 
                              id="color-purple" 
                              className="border-purple-600" 
                            />
                            <Label htmlFor="color-purple">Roxo</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem 
                              value="orange" 
                              id="color-orange" 
                              className="border-orange-600" 
                            />
                            <Label htmlFor="color-orange">Laranja</Label>
                          </div>
                        </RadioGroup>
                      </div>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                {/* Localização */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Localização</h3>
                  <div className="space-y-4">
                    <div className="grid gap-2">
                      <Label htmlFor="language">Idioma</Label>
                      <Select 
                        value={systemConfig.language} 
                        onValueChange={(value) => updateSystemConfig('language', value)}
                      >
                        <SelectTrigger id="language">
                          <SelectValue placeholder="Selecione um idioma" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pt-BR">Português (Brasil)</SelectItem>
                          <SelectItem value="en-US">English (US)</SelectItem>
                          <SelectItem value="es">Español</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="grid gap-2">
                      <Label htmlFor="date-format">Formato de Data</Label>
                      <Select 
                        value={systemConfig.dateFormat} 
                        onValueChange={(value) => updateSystemConfig('dateFormat', value)}
                      >
                        <SelectTrigger id="date-format">
                          <SelectValue placeholder="Selecione um formato" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                          <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                          <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="grid gap-2">
                      <Label htmlFor="timezone">Fuso Horário</Label>
                      <Select 
                        value={systemConfig.timezone} 
                        onValueChange={(value) => updateSystemConfig('timezone', value)}
                      >
                        <SelectTrigger id="timezone">
                          <SelectValue placeholder="Selecione um fuso horário" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="America/Sao_Paulo">Brasília (GMT-3)</SelectItem>
                          <SelectItem value="America/Manaus">Manaus (GMT-4)</SelectItem>
                          <SelectItem value="America/Rio_Branco">Rio Branco (GMT-5)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Conteúdo da Tab Fazenda */}
          <TabsContent value="fazenda">
            <Card>
              <CardHeader>
                <CardTitle>Dados da Fazenda/Empresa</CardTitle>
                <CardDescription>
                  Configure as informações de cadastro da sua fazenda ou empresa
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Informações Básicas */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Informações Básicas</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="fazenda-nome">Nome da Fazenda</Label>
                      <Input 
                        id="fazenda-nome" 
                        value={fazendaConfig.nome}
                        onChange={(e) => updateFazendaConfig('nome', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="razao-social">Razão Social</Label>
                      <Input 
                        id="razao-social" 
                        value={fazendaConfig.razaoSocial}
                        onChange={(e) => updateFazendaConfig('razaoSocial', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="cnpj">CNPJ</Label>
                      <Input 
                        id="cnpj" 
                        value={fazendaConfig.cnpj}
                        onChange={(e) => updateFazendaConfig('cnpj', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="inscricao">Inscrição Estadual</Label>
                      <Input 
                        id="inscricao" 
                        value={fazendaConfig.inscricaoEstadual}
                        onChange={(e) => updateFazendaConfig('inscricaoEstadual', e.target.value)}
                      />
                    </div>
                  </div>
                  
                  <div className="mt-4 space-y-2">
                    <Label htmlFor="logo">Logo da Fazenda</Label>
                    <div className="flex items-center gap-4">
                      <div className="h-16 w-16 bg-muted rounded-md flex items-center justify-center">
                        {fazendaConfig.logo ? (
                          <img 
                            src={fazendaConfig.logo} 
                            alt="Logo" 
                            className="max-h-full max-w-full" 
                          />
                        ) : (
                          <Building className="h-8 w-8 text-muted-foreground" />
                        )}
                      </div>
                      <Button variant="outline" className="flex items-center gap-2">
                        <CloudUpload className="h-4 w-4" />
                        <span>Fazer Upload</span>
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                {/* Endereço */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Endereço</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="rua">Rua/Estrada</Label>
                      <Input 
                        id="rua" 
                        value={fazendaConfig.endereco.rua}
                        onChange={(e) => updateFazendaConfig('endereco.rua', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="numero">Número</Label>
                      <Input 
                        id="numero" 
                        value={fazendaConfig.endereco.numero}
                        onChange={(e) => updateFazendaConfig('endereco.numero', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="complemento">Complemento</Label>
                      <Input 
                        id="complemento" 
                        value={fazendaConfig.endereco.complemento}
                        onChange={(e) => updateFazendaConfig('endereco.complemento', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="bairro">Bairro</Label>
                      <Input 
                        id="bairro" 
                        value={fazendaConfig.endereco.bairro}
                        onChange={(e) => updateFazendaConfig('endereco.bairro', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="cidade">Cidade</Label>
                      <Input 
                        id="cidade" 
                        value={fazendaConfig.endereco.cidade}
                        onChange={(e) => updateFazendaConfig('endereco.cidade', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="estado">Estado</Label>
                      <Input 
                        id="estado" 
                        value={fazendaConfig.endereco.estado}
                        onChange={(e) => updateFazendaConfig('endereco.estado', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="cep">CEP</Label>
                      <Input 
                        id="cep" 
                        value={fazendaConfig.endereco.cep}
                        onChange={(e) => updateFazendaConfig('endereco.cep', e.target.value)}
                      />
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <Button variant="outline" className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span>Localizar no Mapa</span>
                    </Button>
                  </div>
                </div>
                
                <Separator />
                
                {/* Contato */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Contato</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="telefone">Telefone</Label>
                      <Input 
                        id="telefone" 
                        value={fazendaConfig.contato.telefone}
                        onChange={(e) => updateFazendaConfig('contato.telefone', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="celular">Celular</Label>
                      <Input 
                        id="celular" 
                        value={fazendaConfig.contato.celular}
                        onChange={(e) => updateFazendaConfig('contato.celular', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">E-mail</Label>
                      <Input 
                        id="email" 
                        type="email"
                        value={fazendaConfig.contato.email}
                        onChange={(e) => updateFazendaConfig('contato.email', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="site">Site</Label>
                      <Input 
                        id="site" 
                        value={fazendaConfig.contato.site}
                        onChange={(e) => updateFazendaConfig('contato.site', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={saveConfigurations} disabled={!hasChanges}>
                  Salvar Alterações
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          {/* Conteúdo da Tab Notificações */}
          <TabsContent value="notificacoes">
            <Card>
              <CardHeader>
                <CardTitle>Configurações de Notificações</CardTitle>
                <CardDescription>
                  Defina como e quando deseja receber notificações do sistema
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Canais de Notificação */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Canais de Notificação</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="notify-email" className="text-base">Notificações por E-mail</Label>
                        <p className="text-sm text-muted-foreground">
                          Receba notificações importantes por e-mail
                        </p>
                      </div>
                      <Switch 
                        id="notify-email"
                        checked={systemConfig.notifications.email}
                        onCheckedChange={(checked) => updateSystemConfig('notifications.email', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="notify-browser" className="text-base">Notificações no Navegador</Label>
                        <p className="text-sm text-muted-foreground">
                          Receba notificações em tempo real no navegador
                        </p>
                      </div>
                      <Switch 
                        id="notify-browser"
                        checked={systemConfig.notifications.browser}
                        onCheckedChange={(checked) => updateSystemConfig('notifications.browser', checked)}
                      />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                {/* Tipos de Notificação */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Tipos de Notificações</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="notify-agenda" className="text-base">Agenda e Calendário</Label>
                        <p className="text-sm text-muted-foreground">
                          Lembretes de eventos e compromissos agendados
                        </p>
                      </div>
                      <Switch 
                        id="notify-agenda"
                        checked={systemConfig.notifications.agenda}
                        onCheckedChange={(checked) => updateSystemConfig('notifications.agenda', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="notify-tarefas" className="text-base">Tarefas e Manejos</Label>
                        <p className="text-sm text-muted-foreground">
                          Alertas sobre tarefas pendentes e manejos programados
                        </p>
                      </div>
                      <Switch 
                        id="notify-tarefas"
                        checked={systemConfig.notifications.tarefas}
                        onCheckedChange={(checked) => updateSystemConfig('notifications.tarefas', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="notify-financeiro" className="text-base">Financeiro</Label>
                        <p className="text-sm text-muted-foreground">
                          Alertas sobre contas a pagar, receber e vencimentos
                        </p>
                      </div>
                      <Switch 
                        id="notify-financeiro"
                        checked={systemConfig.notifications.financeiro}
                        onCheckedChange={(checked) => updateSystemConfig('notifications.financeiro', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="notify-animais" className="text-base">Saúde dos Animais</Label>
                        <p className="text-sm text-muted-foreground">
                          Alertas sobre procedimentos veterinários e saúde animal
                        </p>
                      </div>
                      <Switch 
                        id="notify-animais"
                        checked={systemConfig.notifications.animais}
                        onCheckedChange={(checked) => updateSystemConfig('notifications.animais', checked)}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={saveConfigurations} disabled={!hasChanges}>
                  Salvar Alterações
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          {/* Conteúdo da Tab Usuários */}
          <TabsContent value="usuarios">
            <Card>
              <CardHeader>
                <CardTitle>Configurações de Usuários e Permissões</CardTitle>
                <CardDescription>
                  Gerencie usuários, permissões e controle de acesso ao sistema
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex flex-col gap-4">
                  <Button variant="outline" className="w-full sm:w-auto">
                    <Users className="mr-2 h-4 w-4" />
                    Gerenciar Usuários
                  </Button>
                  
                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="item-1">
                      <AccordionTrigger>Controle de Acesso</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4 py-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="acesso-restrito" />
                            <Label htmlFor="acesso-restrito">
                              Restringir acesso por endereço IP
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="verificacao-dois-fatores" />
                            <Label htmlFor="verificacao-dois-fatores">
                              Habilitar verificação em duas etapas
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="limite-tentativas" />
                            <Label htmlFor="limite-tentativas">
                              Limitar tentativas de login (3 tentativas)
                            </Label>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                    
                    <AccordionItem value="item-2">
                      <AccordionTrigger>Políticas de Senha</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4 py-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="senha-complexa" defaultChecked />
                            <Label htmlFor="senha-complexa">
                              Exigir senhas complexas
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="expiracao-senha" />
                            <Label htmlFor="expiracao-senha">
                              Expirar senhas a cada 90 dias
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="historico-senha" />
                            <Label htmlFor="historico-senha">
                              Manter histórico de senhas (impedir reutilização)
                            </Label>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                    
                    <AccordionItem value="item-3">
                      <AccordionTrigger>Gerenciamento de Sessões</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4 py-2">
                          <div className="grid gap-2">
                            <Label htmlFor="timeout-sessao">Tempo limite de sessão inativa</Label>
                            <Select defaultValue="30">
                              <SelectTrigger id="timeout-sessao">
                                <SelectValue placeholder="Selecione um tempo" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="15">15 minutos</SelectItem>
                                <SelectItem value="30">30 minutos</SelectItem>
                                <SelectItem value="60">1 hora</SelectItem>
                                <SelectItem value="120">2 horas</SelectItem>
                                <SelectItem value="0">Nunca expirar</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Checkbox id="unica-sessao" />
                            <Label htmlFor="unica-sessao">
                              Permitir apenas uma sessão ativa por usuário
                            </Label>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Conteúdo da Tab Sistema */}
          <TabsContent value="sistema">
            <Card>
              <CardHeader>
                <CardTitle>Configurações do Sistema</CardTitle>
                <CardDescription>
                  Configure detalhes técnicos e manutenção do sistema
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Backup e Restauração */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Backup e Restauração</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="backup-auto" className="text-base">Backup Automático</Label>
                        <p className="text-sm text-muted-foreground">
                          Realizar backups automáticos do sistema
                        </p>
                      </div>
                      <Switch 
                        id="backup-auto"
                        checked={systemConfig.backup.automatic}
                        onCheckedChange={(checked) => updateSystemConfig('backup.automatic', checked)}
                      />
                    </div>
                    
                    <div className="grid gap-2">
                      <Label htmlFor="backup-freq">Frequência de Backup</Label>
                      <Select 
                        value={systemConfig.backup.frequency} 
                        onValueChange={(value) => updateSystemConfig('backup.frequency', value)}
                        disabled={!systemConfig.backup.automatic}
                      >
                        <SelectTrigger id="backup-freq">
                          <SelectValue placeholder="Selecione uma frequência" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">Diário</SelectItem>
                          <SelectItem value="weekly">Semanal</SelectItem>
                          <SelectItem value="monthly">Mensal</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="grid gap-2">
                      <Label htmlFor="backup-keep">Manter Backups</Label>
                      <Select 
                        value={String(systemConfig.backup.keepBackups)} 
                        onValueChange={(value) => updateSystemConfig('backup.keepBackups', parseInt(value))}
                        disabled={!systemConfig.backup.automatic}
                      >
                        <SelectTrigger id="backup-keep">
                          <SelectValue placeholder="Selecione quantos backups manter" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="3">3 backups mais recentes</SelectItem>
                          <SelectItem value="7">7 backups mais recentes</SelectItem>
                          <SelectItem value="14">14 backups mais recentes</SelectItem>
                          <SelectItem value="30">30 backups mais recentes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                      <Button>
                        Fazer Backup Agora
                      </Button>
                      <Button variant="outline">
                        Restaurar Backup
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                {/* Manutenção */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Manutenção</h3>
                  <div className="space-y-4">
                    <div className="grid gap-2">
                      <Label>Limpeza de Dados</Label>
                      <div className="flex flex-wrap gap-2">
                        <Button variant="outline">
                          Limpar Cache
                        </Button>
                        <Button variant="outline">
                          Otimizar Banco de Dados
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid gap-2">
                      <Label>Versão do Sistema</Label>
                      <div className="p-2 bg-muted rounded-md">
                        <p className="text-sm">
                          EquiGestor AI v1.0.0 - Build 20250330
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Última atualização: 30 de março de 2025
                        </p>
                      </div>
                      <Button variant="outline" className="mt-2">
                        Verificar Atualizações
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                {/* Integrações */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Integrações e APIs</h3>
                  <div className="space-y-4">
                    <div className="grid gap-2">
                      <Label>APIs Disponíveis</Label>
                      <div className="grid gap-2">
                        <div className="flex items-center justify-between p-2 border rounded-md">
                          <div>
                            <p className="font-medium">API REST</p>
                            <p className="text-sm text-muted-foreground">
                              Para integração com outros sistemas
                            </p>
                          </div>
                          <Switch defaultChecked />
                        </div>
                        
                        <div className="flex items-center justify-between p-2 border rounded-md">
                          <div>
                            <p className="font-medium">Exportação para Excel</p>
                            <p className="text-sm text-muted-foreground">
                              Exportar dados para planilhas Excel
                            </p>
                          </div>
                          <Switch defaultChecked />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={saveConfigurations} disabled={!hasChanges}>
                  Salvar Alterações
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </LayoutWrapper>
  );
}

export default ConfiguracoesPage;