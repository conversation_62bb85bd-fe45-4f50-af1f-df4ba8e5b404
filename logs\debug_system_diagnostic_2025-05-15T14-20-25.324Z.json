{"timestamp": "2025-05-15T14:20:25.324Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 377106432, "heapTotal": 100274176, "heapUsed": 62214616, "external": 6815481, "arrayBuffers": 60485}, "uptime": 2.545576083, "cpuUsage": {"user": 2190579, "system": 303880}, "resourceUsage": {"userCPUTime": 2190629, "systemCPUTime": 303880, "maxRSS": 368268, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 98425, "majorPageFault": 0, "swappedOut": 0, "fsRead": 27496, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5980, "involuntaryContextSwitches": 3038}}