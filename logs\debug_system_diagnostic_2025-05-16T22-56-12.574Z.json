{"timestamp": "2025-05-16T22:56:12.573Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405561344, "heapTotal": 116432896, "heapUsed": 73081416, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.706919214, "cpuUsage": {"user": 2725164, "system": 334213}, "resourceUsage": {"userCPUTime": 2725201, "systemCPUTime": 334217, "maxRSS": 396056, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105536, "majorPageFault": 0, "swappedOut": 0, "fsRead": 24, "fsWrite": 216, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8275, "involuntaryContextSwitches": 1884}}