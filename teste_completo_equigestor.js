/**
 * Script de teste completo para o EquiGestor AI
 * Este script testa automaticamente todas as funcionalidades e cadastros do sistema
 */

const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');

// Configurações
const API_URL = 'http://localhost:3000/api'; // Ajuste conforme necessário
const USER_ID = 1;
const AUTH_HEADER = {'user-id': USER_ID.toString()};

// Função utilitária para aguardar um tempo específico
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Função utilitária para imprimir resultados
const printResult = (funcName, success, data = null, error = null) => {
  const status = success ? '✅ SUCESSO' : '❌ FALHA';
  console.log(`[${status}] ${funcName}`);
  if (data) console.log('  Dados:', JSON.stringify(data).substring(0, 150) + (JSON.stringify(data).length > 150 ? '...' : ''));
  if (error) console.log('  Erro:', error);
  console.log('-------------------------------------------');
};

// Função principal que executa todos os testes
async function executarTestesCompletos() {
  console.log('🔍 INICIANDO TESTES COMPLETOS DO EQUIGESTOR AI 🔍');
  console.log('================================================');
  
  try {
    // 1. MÓDULO DE CAVALOS
    await testarModuloCavalos();
    
    // 2. MÓDULO DE MANEJOS
    await testarModuloManejos();
    
    // 3. MÓDULO DE AGENDA
    await testarModuloAgenda();
    
    // 4. MÓDULO DE GENÉTICA
    await testarModuloGenetica();
    
    // 5. MÓDULO DE NUTRIÇÃO
    await testarModuloNutricao();
    
    // 6. MÓDULO DE VETERINÁRIO
    await testarModuloVeterinario();
    
    // 7. MÓDULO DE REPRODUÇÃO
    await testarModuloReproducao();
    
    // 8. MÓDULO DE ARQUIVOS
    await testarModuloArquivos();
    
    // 9. MÓDULO DO ASSISTENTE VIRTUAL
    await testarModuloAssistente();
    
    console.log('✨ TESTES CONCLUÍDOS COM SUCESSO ✨');
  } catch (error) {
    console.error('❌ ERRO DURANTE OS TESTES:', error.message);
  }
}

// 1. TESTES DO MÓDULO DE CAVALOS
async function testarModuloCavalos() {
  console.log('\n📋 TESTANDO MÓDULO DE CAVALOS');
  console.log('------------------------------------------------');
  
  // 1.1 Listar todos os cavalos
  try {
    const response = await axios.get(`${API_URL}/cavalos`, { headers: AUTH_HEADER });
    printResult('Listar cavalos', true, response.data);
  } catch (error) {
    printResult('Listar cavalos', false, null, error.message);
  }
  
  // 1.2 Criar um novo cavalo
  const novoCavalo = {
    name: `Teste Automatizado ${Date.now().toString().substring(9)}`,
    breed: 'Mangalarga Marchador',
    birth_date: '2022-05-01',
    sexo: 'Macho',
    peso: 450,
    altura: 1.52,
    notes: 'Cavalo criado pelo script de teste automatizado',
    pai: 'Hércules',
    mae: 'Aurora',
    avo_paterno: 'Zeus',
    avo_materno: 'Demeter',
    user_id: USER_ID
  };
  
  let cavaloId;
  try {
    const response = await axios.post(`${API_URL}/cavalos`, novoCavalo, { headers: AUTH_HEADER });
    cavaloId = response.data.id;
    printResult('Criar cavalo', true, response.data);
  } catch (error) {
    printResult('Criar cavalo', false, null, error.message);
  }
  
  if (cavaloId) {
    // 1.3 Obter detalhes de um cavalo
    try {
      const response = await axios.get(`${API_URL}/cavalos/${cavaloId}`, { headers: AUTH_HEADER });
      printResult('Obter detalhes do cavalo', true, response.data);
    } catch (error) {
      printResult('Obter detalhes do cavalo', false, null, error.message);
    }
    
    // 1.4 Atualizar um cavalo
    const dadosAtualizados = {
      name: `${novoCavalo.name} (Atualizado)`,
      peso: 455,
      altura: 1.53,
      notes: 'Cavalo atualizado pelo script de teste automatizado'
    };
    
    try {
      const response = await axios.put(`${API_URL}/cavalos/${cavaloId}`, dadosAtualizados, { headers: AUTH_HEADER });
      printResult('Atualizar cavalo', true, response.data);
    } catch (error) {
      printResult('Atualizar cavalo', false, null, error.message);
    }
    
    // 1.5 Adicionar medidas físicas para o cavalo
    const novasMedidas = {
      horse_id: cavaloId,
      data: new Date().toISOString().split('T')[0],
      peso: 455,
      altura: 1.53,
      condicao_corporal: 4,
      observacoes: 'Medidas registradas pelo script de teste automatizado',
      user_id: USER_ID
    };
    
    try {
      const response = await axios.post(`${API_URL}/medidas-fisicas`, novasMedidas, { headers: AUTH_HEADER });
      printResult('Adicionar medidas físicas', true, response.data);
    } catch (error) {
      printResult('Adicionar medidas físicas', false, null, error.message);
    }
  }
}

// 2. TESTES DO MÓDULO DE MANEJOS
async function testarModuloManejos() {
  console.log('\n📋 TESTANDO MÓDULO DE MANEJOS');
  console.log('------------------------------------------------');
  
  // 2.1 Listar todos os manejos
  try {
    const response = await axios.get(`${API_URL}/manejos`, { headers: AUTH_HEADER });
    printResult('Listar manejos', true, response.data);
  } catch (error) {
    printResult('Listar manejos', false, null, error.message);
  }
  
  // 2.2 Criar um novo manejo
  const novoManejo = {
    tipo: 'Vacinação',
    data: new Date().toISOString().split('T')[0],
    detalhes: 'Vacina contra Influenza Equina',
    responsavel: 'Dr. Carlos',
    horse_id: 1, // Cavalo Trovão
    user_id: USER_ID,
    status: 'realizado',
    observacoes: 'Manejo criado pelo script de teste automatizado'
  };
  
  let manejoId;
  try {
    const response = await axios.post(`${API_URL}/manejos`, novoManejo, { headers: AUTH_HEADER });
    manejoId = response.data.id;
    printResult('Criar manejo', true, response.data);
  } catch (error) {
    printResult('Criar manejo', false, null, error.message);
  }
  
  if (manejoId) {
    // 2.3 Obter detalhes de um manejo
    try {
      const response = await axios.get(`${API_URL}/manejos/${manejoId}`, { headers: AUTH_HEADER });
      printResult('Obter detalhes do manejo', true, response.data);
    } catch (error) {
      printResult('Obter detalhes do manejo', false, null, error.message);
    }
    
    // 2.4 Atualizar um manejo
    const dadosAtualizados = {
      detalhes: 'Vacina contra Influenza Equina (Atualizado)',
      observacoes: 'Manejo atualizado pelo script de teste automatizado'
    };
    
    try {
      const response = await axios.put(`${API_URL}/manejos/${manejoId}`, dadosAtualizados, { headers: AUTH_HEADER });
      printResult('Atualizar manejo', true, response.data);
    } catch (error) {
      printResult('Atualizar manejo', false, null, error.message);
    }
  }
}

// 3. TESTES DO MÓDULO DE AGENDA
async function testarModuloAgenda() {
  console.log('\n📋 TESTANDO MÓDULO DE AGENDA');
  console.log('------------------------------------------------');
  
  // 3.1 Listar todos os eventos
  try {
    const response = await axios.get(`${API_URL}/eventos`, { headers: AUTH_HEADER });
    printResult('Listar eventos', true, response.data);
  } catch (error) {
    printResult('Listar eventos', false, null, error.message);
  }
  
  // 3.2 Criar um novo evento
  const dataFutura = new Date();
  dataFutura.setDate(dataFutura.getDate() + 7);
  const dataEventoStr = dataFutura.toISOString().split('T')[0];
  
  const novoEvento = {
    titulo: `Teste Automatizado ${Date.now().toString().substring(9)}`,
    descricao: 'Evento criado pelo script de teste automatizado',
    data: dataEventoStr,
    hora_inicio: '09:00',
    hora_fim: '10:30',
    tipo: 'treinamento',
    status: 'pendente',
    horse_id: 1, // Cavalo Trovão
    responsavel: 'João Treinador',
    telefone_responsavel: '(11) 98765-4321',
    local: 'Pista de treinamento',
    observacoes: 'Evento criado pelo script de teste automatizado',
    recorrente: false,
    user_id: USER_ID,
    prioridade: 'média'
  };
  
  let eventoId;
  try {
    const response = await axios.post(`${API_URL}/eventos`, novoEvento, { headers: AUTH_HEADER });
    eventoId = response.data.id;
    printResult('Criar evento', true, response.data);
  } catch (error) {
    printResult('Criar evento', false, null, error.message);
  }
  
  if (eventoId) {
    // 3.3 Obter detalhes de um evento
    try {
      const response = await axios.get(`${API_URL}/eventos/${eventoId}`, { headers: AUTH_HEADER });
      printResult('Obter detalhes do evento', true, response.data);
    } catch (error) {
      printResult('Obter detalhes do evento', false, null, error.message);
    }
    
    // 3.4 Atualizar um evento
    const dadosAtualizados = {
      titulo: `${novoEvento.titulo} (Atualizado)`,
      hora_inicio: '09:30',
      observacoes: 'Evento atualizado pelo script de teste automatizado'
    };
    
    try {
      const response = await axios.put(`${API_URL}/eventos/${eventoId}`, dadosAtualizados, { headers: AUTH_HEADER });
      printResult('Atualizar evento', true, response.data);
    } catch (error) {
      printResult('Atualizar evento', false, null, error.message);
    }
  }
}

// 4. TESTES DO MÓDULO DE GENÉTICA
async function testarModuloGenetica() {
  console.log('\n📋 TESTANDO MÓDULO DE GENÉTICA');
  console.log('------------------------------------------------');
  
  // 4.1 Testar Morfologia
  await testarMorfologia();
  
  // 4.2 Testar Desempenho
  await testarDesempenho();
  
  // 4.3 Testar Genealogia
  await testarGenealogia();
  
  // 4.4 Testar Sugestões de Cruzamento
  await testarSugestoesCruzamento();
}

async function testarMorfologia() {
  // 4.1.1 Listar registros de morfologia para um cavalo
  try {
    const response = await axios.get(`${API_URL}/morfologia/cavalo/1`, { headers: AUTH_HEADER });
    printResult('Listar registros de morfologia', true, response.data);
  } catch (error) {
    printResult('Listar registros de morfologia', false, null, error.message);
  }
  
  // 4.1.2 Criar um novo registro de morfologia
  const novaMorfologia = {
    horse_id: 1,  // Cavalo Trovão
    data_medicao: new Date().toISOString().split('T')[0],
    altura_cernelha: 1.58,
    altura_dorso: 1.49,
    altura_garupa: 1.55,
    comprimento_corpo: 1.65,
    comprimento_pescoco: 0.70,
    largura_peito: 0.48,
    perimetro_toracico: 1.80,
    perimetro_pescoco: 0.90,
    perimetro_canela: 0.20,
    pontuacao_cabeca: 8,
    pontuacao_pescoco: 9,
    pontuacao_espalda: 8,
    pontuacao_dorso: 9,
    pontuacao_garupa: 8,
    pontuacao_membros: 8,
    pontuacao_aprumos: 7,
    pontuacao_andamento: 9,
    pontuacao_harmonia: 8,
    pontuacao_total: 8.2,
    observacoes: 'Registro criado pelo script de teste automatizado',
    responsavel_medicao: 'Dr. Carlos Santos',
    user_id: USER_ID
  };
  
  let morfologiaId;
  try {
    const response = await axios.post(`${API_URL}/morfologia`, novaMorfologia, { headers: AUTH_HEADER });
    morfologiaId = response.data.id;
    printResult('Criar registro de morfologia', true, response.data);
  } catch (error) {
    printResult('Criar registro de morfologia', false, null, error.message);
  }
}

async function testarDesempenho() {
  // 4.2.1 Listar registros de desempenho para um cavalo
  try {
    const response = await axios.get(`${API_URL}/desempenho/cavalo/1`, { headers: AUTH_HEADER });
    printResult('Listar registros de desempenho', true, response.data);
  } catch (error) {
    printResult('Listar registros de desempenho', false, null, error.message);
  }
  
  // 4.2.2 Criar um novo registro de desempenho
  const novoDesempenho = {
    horse_id: 1,  // Cavalo Trovão
    data: new Date().toISOString().split('T')[0],
    tipo_evento: 'Competição',
    nome_evento: 'Campeonato Regional de Marcha - Teste',
    categoria: 'Mangalarga Marchador Adulto',
    resultado: '1º Lugar',
    observacoes: 'Registro criado pelo script de teste automatizado',
    premiacao: 'Medalha de Ouro',
    desempenho_detalhes: 'Excelente desempenho na marcha. Avaliação do juiz: 9.8/10.',
    user_id: USER_ID
  };
  
  let desempenhoId;
  try {
    const response = await axios.post(`${API_URL}/desempenho`, novoDesempenho, { headers: AUTH_HEADER });
    desempenhoId = response.data.id;
    printResult('Criar registro de desempenho', true, response.data);
  } catch (error) {
    printResult('Criar registro de desempenho', false, null, error.message);
  }
}

async function testarGenealogia() {
  // 4.3.1 Obter genealogia de um cavalo
  try {
    const response = await axios.get(`${API_URL}/genealogia/cavalo/1`, { headers: AUTH_HEADER });
    printResult('Obter genealogia', true, response.data);
  } catch (error) {
    printResult('Obter genealogia', false, null, error.message);
  }
  
  // 4.3.2 Calcular coeficiente de consanguinidade
  try {
    const response = await axios.get(`${API_URL}/genealogia/consanguinidade/1`, { headers: AUTH_HEADER });
    printResult('Calcular consanguinidade', true, response.data);
  } catch (error) {
    printResult('Calcular consanguinidade', false, null, error.message);
  }
}

async function testarSugestoesCruzamento() {
  // 4.4.1 Obter sugestões de cruzamento para um cavalo
  try {
    const response = await axios.get(`${API_URL}/sugestoes-cruzamento/cavalo/1?objetivo=morfologia`, { headers: AUTH_HEADER });
    printResult('Obter sugestões de cruzamento', true, response.data);
  } catch (error) {
    printResult('Obter sugestões de cruzamento', false, null, error.message);
  }
  
  // 4.4.2 Gerar novas sugestões de cruzamento
  try {
    const dados = {
      horse_id_base: 1,
      objetivo: 'desempenho',
      user_id: USER_ID
    };
    const response = await axios.post(`${API_URL}/sugestoes-cruzamento/gerar`, dados, { headers: AUTH_HEADER });
    printResult('Gerar sugestões de cruzamento', true, response.data);
  } catch (error) {
    printResult('Gerar sugestões de cruzamento', false, null, error.message);
  }
}

// 5. TESTES DO MÓDULO DE NUTRIÇÃO
async function testarModuloNutricao() {
  console.log('\n📋 TESTANDO MÓDULO DE NUTRIÇÃO');
  console.log('------------------------------------------------');
  
  // 5.1 Listar registros de nutrição para um cavalo
  try {
    const response = await axios.get(`${API_URL}/nutricao/cavalo/1`, { headers: AUTH_HEADER });
    printResult('Listar registros de nutrição', true, response.data);
  } catch (error) {
    printResult('Listar registros de nutrição', false, null, error.message);
  }
  
  // 5.2 Criar um novo registro de nutrição
  const novaNutricao = {
    horse_id: 1,  // Cavalo Trovão
    data: new Date().toISOString().split('T')[0],
    tipo_alimentacao: 'Suplemento',
    nome_alimento: 'Suplemento Energético',
    quantidade: 0.1,
    unidade_medida: 'kg',
    frequencia_diaria: 1,
    horarios: '07:30',
    observacoes: 'Registro criado pelo script de teste automatizado',
    custo_unitario: 95.00,
    custo_mensal: 142.50,
    fornecedor: 'Nutrição Equina Brasil',
    recomendacao: 'Suplementação para período de competição',
    status: 'ativo',
    user_id: USER_ID
  };
  
  let nutricaoId;
  try {
    const response = await axios.post(`${API_URL}/nutricao`, novaNutricao, { headers: AUTH_HEADER });
    nutricaoId = response.data.id;
    printResult('Criar registro de nutrição', true, response.data);
  } catch (error) {
    printResult('Criar registro de nutrição', false, null, error.message);
  }
  
  if (nutricaoId) {
    // 5.3 Atualizar um registro de nutrição
    const dadosAtualizados = {
      nome_alimento: 'Suplemento Energético Premium',
      quantidade: 0.08,
      observacoes: 'Registro atualizado pelo script de teste automatizado'
    };
    
    try {
      const response = await axios.put(`${API_URL}/nutricao/${nutricaoId}`, dadosAtualizados, { headers: AUTH_HEADER });
      printResult('Atualizar registro de nutrição', true, response.data);
    } catch (error) {
      printResult('Atualizar registro de nutrição', false, null, error.message);
    }
  }
}

// 6. TESTES DO MÓDULO VETERINÁRIO
async function testarModuloVeterinario() {
  console.log('\n📋 TESTANDO MÓDULO VETERINÁRIO');
  console.log('------------------------------------------------');
  
  // 6.1 Listar procedimentos veterinários para um cavalo
  try {
    const response = await axios.get(`${API_URL}/cavalos/1/procedimentos-veterinarios`, { headers: AUTH_HEADER });
    printResult('Listar procedimentos veterinários', true, response.data);
  } catch (error) {
    printResult('Listar procedimentos veterinários', false, null, error.message);
  }
  
  // 6.2 Criar um novo procedimento veterinário
  const novoProcedimento = {
    tipo: 'Exame de Sangue',
    descricao: 'Hemograma completo',
    data: new Date().toISOString().split('T')[0],
    veterinario: 'Dra. Ana Costa',
    crmv: 'CRMV-SP 45678',
    medicamentos: 'Nenhum',
    dosagem: 'N/A',
    resultado: 'Exames dentro dos parâmetros normais',
    recomendacoes: 'Manter dieta e exercícios habituais',
    data_proximo: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    horse_id: 1,  // Cavalo Trovão
    status: 'realizado',
    custo: 280.00,
    forma_pagamento: 'Cartão de Crédito',
    observacoes: 'Procedimento criado pelo script de teste automatizado',
    user_id: USER_ID
  };
  
  let procedimentoId;
  try {
    const response = await axios.post(`${API_URL}/procedimentos-veterinarios`, novoProcedimento, { headers: AUTH_HEADER });
    procedimentoId = response.data.id;
    printResult('Criar procedimento veterinário', true, response.data);
  } catch (error) {
    printResult('Criar procedimento veterinário', false, null, error.message);
  }
  
  if (procedimentoId) {
    // 6.3 Atualizar um procedimento veterinário
    const dadosAtualizados = {
      resultado: 'Todos os parâmetros sanguíneos em níveis ideais',
      observacoes: 'Procedimento atualizado pelo script de teste automatizado'
    };
    
    try {
      const response = await axios.put(`${API_URL}/procedimentos-veterinarios/${procedimentoId}`, dadosAtualizados, { headers: AUTH_HEADER });
      printResult('Atualizar procedimento veterinário', true, response.data);
    } catch (error) {
      printResult('Atualizar procedimento veterinário', false, null, error.message);
    }
  }
}

// 7. TESTES DO MÓDULO DE REPRODUÇÃO
async function testarModuloReproducao() {
  console.log('\n📋 TESTANDO MÓDULO DE REPRODUÇÃO');
  console.log('------------------------------------------------');
  
  // 7.1 Listar registros de reprodução para uma égua
  try {
    const response = await axios.get(`${API_URL}/reproducao/cavalo/2`, { headers: AUTH_HEADER });
    printResult('Listar registros de reprodução', true, response.data);
  } catch (error) {
    printResult('Listar registros de reprodução', false, null, error.message);
  }
  
  // 7.2 Criar um novo registro de reprodução
  const dataFutura = new Date();
  dataFutura.setDate(dataFutura.getDate() + 10);
  const dataCoberturaStr = dataFutura.toISOString().split('T')[0];
  
  const novaReproducao = {
    horse_id: 2,  // Égua Ventania
    padreiro_id: 8,  // Cavalo Zeus
    data_cobertura: dataCoberturaStr,
    tipo_cobertura: 'Inseminação Artificial',
    estado: 'Programada',
    data_prevista_inseminacao: dataCoberturaStr,
    data_prevista_embriao: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    data_prevista_parto: new Date(Date.now() + 340 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    observacoes: 'Registro criado pelo script de teste automatizado',
    user_id: USER_ID
  };
  
  let reproducaoId;
  try {
    const response = await axios.post(`${API_URL}/reproducao`, novaReproducao, { headers: AUTH_HEADER });
    reproducaoId = response.data.id;
    printResult('Criar registro de reprodução', true, response.data);
  } catch (error) {
    printResult('Criar registro de reprodução', false, null, error.message);
  }
  
  if (reproducaoId) {
    // 7.3 Atualizar um registro de reprodução
    const dadosAtualizados = {
      estado: 'Confirmada',
      observacoes: 'Registro atualizado pelo script de teste automatizado'
    };
    
    try {
      const response = await axios.put(`${API_URL}/reproducao/${reproducaoId}`, dadosAtualizados, { headers: AUTH_HEADER });
      printResult('Atualizar registro de reprodução', true, response.data);
    } catch (error) {
      printResult('Atualizar registro de reprodução', false, null, error.message);
    }
  }
}

// 8. TESTES DO MÓDULO DE ARQUIVOS
async function testarModuloArquivos() {
  console.log('\n📋 TESTANDO MÓDULO DE ARQUIVOS');
  console.log('------------------------------------------------');
  
  // 8.1 Listar arquivos para um cavalo
  try {
    const response = await axios.get(`${API_URL}/cavalos/1/arquivos`, { headers: AUTH_HEADER });
    printResult('Listar arquivos', true, response.data);
  } catch (error) {
    printResult('Listar arquivos', false, null, error.message);
  }
  
  // 8.2 Criar arquivo de texto simples para teste
  // Esse teste requer a criação física de um arquivo temporário
  // Vamos criar um arquivo simples para o upload
  try {
    // Criar um arquivo temporário
    const tempFilePath = './temp_arquivo_teste.txt';
    fs.writeFileSync(tempFilePath, 'Este é um arquivo de teste para o EquiGestor AI.');
    
    // Criar formData para o upload
    const formData = new FormData();
    formData.append('file', fs.createReadStream(tempFilePath));
    formData.append('tipo', 'documento');
    formData.append('descricao', 'Arquivo criado pelo script de teste automatizado');
    formData.append('horse_id', '1');
    formData.append('user_id', USER_ID.toString());
    
    // Fazer o upload
    try {
      const response = await axios.post(`${API_URL}/arquivos`, formData, { 
        headers: {
          ...AUTH_HEADER,
          ...formData.getHeaders()
        }
      });
      printResult('Upload de arquivo', true, response.data);
      
      // Limpar o arquivo temporário
      fs.unlinkSync(tempFilePath);
    } catch (error) {
      printResult('Upload de arquivo', false, null, error.message);
      // Limpar o arquivo temporário mesmo em caso de erro
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    }
  } catch (error) {
    printResult('Criar arquivo temporário', false, null, error.message);
  }
}

// 9. TESTES DO MÓDULO DO ASSISTENTE VIRTUAL
async function testarModuloAssistente() {
  console.log('\n📋 TESTANDO MÓDULO DO ASSISTENTE VIRTUAL');
  console.log('------------------------------------------------');
  
  // 9.1 Testar envio de mensagem ao assistente
  const mensagemAssistente = {
    message: 'Quais são os próximos eventos na agenda?',
    user_id: USER_ID
  };
  
  try {
    const response = await axios.post(`${API_URL}/assistente/mensagem`, mensagemAssistente, { headers: AUTH_HEADER });
    printResult('Enviar mensagem ao assistente', true, response.data);
  } catch (error) {
    printResult('Enviar mensagem ao assistente', false, null, error.message);
  }
  
  // 9.2 Testar detecção de intenção
  const mensagemIntencao = {
    message: 'Quero agendar uma vacinação para o Trovão amanhã às 10h',
    user_id: USER_ID
  };
  
  try {
    const response = await axios.post(`${API_URL}/assistente/detectar-intencao`, mensagemIntencao, { headers: AUTH_HEADER });
    printResult('Detectar intenção', true, response.data);
  } catch (error) {
    printResult('Detectar intenção', false, null, error.message);
  }
  
  // 9.3 Testar agendamento via assistente
  const mensagemAgendamento = {
    message: 'Agende uma vacinação para o Trovão amanhã às 14h com o Dr. Carlos',
    user_id: USER_ID
  };
  
  try {
    const response = await axios.post(`${API_URL}/assistente/mensagem`, mensagemAgendamento, { headers: AUTH_HEADER });
    printResult('Agendamento via assistente', true, response.data);
  } catch (error) {
    printResult('Agendamento via assistente', false, null, error.message);
  }
}

// Executar todos os testes
executarTestesCompletos();