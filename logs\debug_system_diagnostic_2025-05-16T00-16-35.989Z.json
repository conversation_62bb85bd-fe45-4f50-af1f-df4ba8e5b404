{"timestamp": "2025-05-16T00:16:35.989Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399441920, "heapTotal": 112238592, "heapUsed": 72421760, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.768753793, "cpuUsage": {"user": 3181242, "system": 416844}, "resourceUsage": {"userCPUTime": 3181299, "systemCPUTime": 416844, "maxRSS": 390080, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102699, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 16, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8191, "involuntaryContextSwitches": 10407}}