{"timestamp": "2025-05-23T20:25:40.088Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404434944, "heapTotal": 113287168, "heapUsed": 85505360, "external": 8521181, "arrayBuffers": 235533}, "uptime": 1.842958992, "cpuUsage": {"user": 2792743, "system": 362533}, "resourceUsage": {"userCPUTime": 2792787, "systemCPUTime": 362539, "maxRSS": 394956, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103821, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8017, "involuntaryContextSwitches": 2722}}