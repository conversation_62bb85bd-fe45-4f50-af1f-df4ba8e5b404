{"timestamp": "2025-05-15T21:19:42.030Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 400891904, "heapTotal": 115908608, "heapUsed": 72449024, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.774760541, "cpuUsage": {"user": 2727090, "system": 362971}, "resourceUsage": {"userCPUTime": 2727154, "systemCPUTime": 362971, "maxRSS": 391496, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103699, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8578, "involuntaryContextSwitches": 3877}}