{"timestamp": "2025-05-26T21:24:56.703Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 395591680, "heapTotal": 116170752, "heapUsed": 74534984, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.003886823, "cpuUsage": {"user": 3129647, "system": 362542}, "resourceUsage": {"userCPUTime": 3129689, "systemCPUTime": 362547, "maxRSS": 386320, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102676, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8697, "involuntaryContextSwitches": 2672}}