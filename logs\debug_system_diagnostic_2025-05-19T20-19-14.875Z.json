{"timestamp": "2025-05-19T20:19:14.874Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 272871424, "heapTotal": 121057280, "heapUsed": 94648480, "external": 8448320, "arrayBuffers": 285620}, "uptime": 1.816555265, "cpuUsage": {"user": 2690454, "system": 343971}, "resourceUsage": {"userCPUTime": 2690510, "systemCPUTime": 343978, "maxRSS": 288652, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105430, "majorPageFault": 0, "swappedOut": 0, "fsRead": 4136, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8390, "involuntaryContextSwitches": 2197}}