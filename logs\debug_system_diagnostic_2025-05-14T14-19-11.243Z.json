{"timestamp": "2025-05-14T14:19:11.243Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 325828608, "heapTotal": 144740352, "heapUsed": 122170664, "external": 8789055, "arrayBuffers": 85061}, "uptime": 2.081047873, "cpuUsage": {"user": 3303294, "system": 453813}, "resourceUsage": {"userCPUTime": 3303343, "systemCPUTime": 453820, "maxRSS": 318192, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 111633, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 88, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 13217, "involuntaryContextSwitches": 4743}}