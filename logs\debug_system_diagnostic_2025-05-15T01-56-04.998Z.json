{"timestamp": "2025-05-15T01:56:04.997Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 387698688, "heapTotal": 101339136, "heapUsed": 73175552, "external": 6839806, "arrayBuffers": 50319}, "uptime": 1.906142764, "cpuUsage": {"user": 2324795, "system": 341822}, "resourceUsage": {"userCPUTime": 2324864, "systemCPUTime": 341822, "maxRSS": 378612, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 100962, "majorPageFault": 0, "swappedOut": 0, "fsRead": 32, "fsWrite": 56, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6038, "involuntaryContextSwitches": 5588}}