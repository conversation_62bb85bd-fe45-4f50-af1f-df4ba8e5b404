Assuma o papel de engenheiro sênior de software.
Implemente uma funcionalidade adicional no sistema EquiHorse já existente, sem modificar ou impactar a lógica dos módulos atuais, apenas estendendo e integrando as novas rotinas a seguir:

Funcionalidade: Importação automática de registro genealógico de cavalo via PDF (padrão ABCCC)
Crie um endpoint no backend (Node.js + TypeScript + Express) chamado /api/importar-pdf-crioulo, que:

Recebe uploads de PDF de certificado de registro genealógico oficial da ABCCC.

Extrai, via parser robusto (usando bibliotecas como pdf-parse), todos os dados relevantes do animal e de toda a sua árvore genealógica apresentada no documento:

Nome, registro (SBB), RP, sexo, nascimento, pelagem.

Nome e registro de pai, mãe, avôs, avós, bisavôs, bisavós — seguindo a estrutura vertical típica do documento.

Nome/registro do criador, proprietário, cabanha/origem.

Demais títulos ou notas (ex: REG.MÉRITO) associando ao animal correto.

Para cada animal identificado (principal, pais, avós, etc):

Verifique por registro ou nome se já existe no banco:

Se sim, atualize os dados.

Se não, crie um novo cavalo, conectando via ID a seus ascendentes diretos (genealogia relacional).

Certifique-se de nunca sobrescrever dados críticos já existentes — só atualizar campos extraídos do PDF e não mexer em lógicas de outros módulos.

Retorne ao frontend um resumo da operação, mostrando a árvore criada/atualizada, e quais registros foram processados.

Gere logs detalhados de todas as operações, erros de parsing, conflitos e updates, usando um sistema de debug acoplado ao módulo de importação.

Integre um sistema de debug visual ao EquiHorse:

Permita consultar os logs gerados pela rotina de importação (acessível apenas a usuários admin/dev).

Exiba mensagens detalhadas sobre cada etapa do processo: extração, parsing, cadastro/update, conflitos, e erros.

Garanta que o sistema de debug não afete performance ou segurança em produção.

No frontend React:

Implemente um componente para upload do PDF, que envie para o novo endpoint.

Exiba o resumo dos dados extraídos e da árvore genealógica antes de salvar.

Permita ao usuário confirmar/cancelar a importação.

Não altere componentes já existentes — apenas acople a nova funcionalidade.

A interface deve ser responsiva, clara e segura.

Padronização e segurança:

Tipagem forte, código modular, documentação interna via comentários.

Não modifique lógicas já existentes nos módulos principais — implemente tudo como extensões/acoplamentos.

Use middlewares de validação, logging e autenticação já presentes no sistema.

Demonstração de uso:

Forneça exemplos de chamadas HTTP, de uso do componente React, e como acessar o debug visual.

Explique como rodar localmente e como manter o sistema atualizado sem risco de quebrar os fluxos já implementados.

Reforce:
Apenas acople a funcionalidade ao sistema já existente, sem alterar lógica central ou módulos já testados.
A implementação deve ser eficaz, segura, extensível e facilmente monitorada pelo debug acoplado.
Comente as decisões técnicas no código e instrua como atualizar só esta parte do sistema.

