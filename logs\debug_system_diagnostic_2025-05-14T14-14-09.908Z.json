{"timestamp": "2025-05-14T14:14:09.907Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 416284672, "heapTotal": 123023360, "heapUsed": 74466016, "external": 7016254, "arrayBuffers": 60485}, "uptime": 1.904606542, "cpuUsage": {"user": 3423113, "system": 393191}, "resourceUsage": {"userCPUTime": 3423174, "systemCPUTime": 393191, "maxRSS": 406528, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106929, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 12736, "involuntaryContextSwitches": 3727}}