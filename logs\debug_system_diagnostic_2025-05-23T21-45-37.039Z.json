{"timestamp": "2025-05-23T21:45:37.039Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 283852800, "heapTotal": 115142656, "heapUsed": 92938608, "external": 8340820, "arrayBuffers": 257466}, "uptime": 2.961325161, "cpuUsage": {"user": 3101929, "system": 435419}, "resourceUsage": {"userCPUTime": 3102024, "systemCPUTime": 435422, "maxRSS": 293824, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 108225, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8551, "involuntaryContextSwitches": 10376}}