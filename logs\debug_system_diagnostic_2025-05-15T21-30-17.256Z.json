{"timestamp": "2025-05-15T21:30:17.256Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397656064, "heapTotal": 116170752, "heapUsed": 72429328, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.578028435, "cpuUsage": {"user": 2583020, "system": 324343}, "resourceUsage": {"userCPUTime": 2583098, "systemCPUTime": 324343, "maxRSS": 388336, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105120, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 152, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7947, "involuntaryContextSwitches": 1020}}