{"timestamp": "2025-05-15T14:30:12.721Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 380395520, "heapTotal": 91623424, "heapUsed": 70956448, "external": 6898936, "arrayBuffers": 115186}, "uptime": 1.303052993, "cpuUsage": {"user": 2084545, "system": 269356}, "resourceUsage": {"userCPUTime": 2084600, "systemCPUTime": 269363, "maxRSS": 371480, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99903, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 328, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5976, "involuntaryContextSwitches": 1061}}