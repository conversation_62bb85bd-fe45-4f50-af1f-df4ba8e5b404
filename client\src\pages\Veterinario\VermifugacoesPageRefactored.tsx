import React, { useState } from 'react';
import { useVeterinario } from '@/hooks/use-veterinario';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { useAuth } from '@/hooks/use-auth';
import { FilterControls } from '@/components/veterinario/FilterControls';
import { ProcedimentoList } from '@/components/veterinario/ProcedimentoList';
import { ProcedimentoVetForm, ProcedimentoFormValues } from '@/components/veterinario/ProcedimentoVetForm';
import { ProcedimentoVet } from '@shared/schema';
import { AuthenticationErrorHandler, isAuthenticationError } from '@/components/auth/AuthenticationErrorHandler';

/**
 * Página de Vermifugações Refatorada
 * 
 * Versão melhorada da página de vermifugações usando os novos componentes
 * modulares e o hook personalizado useVeterinario para uma melhor
 * estrutura de código, reutilização e manutenibilidade.
 */
export default function VermifugacoesPageRefactored() {
  const veterinario = useVeterinario();
  const { user } = useAuth();
  
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedProcedimentoId, setSelectedProcedimentoId] = useState<number | null>(null);
  
  // Encontrar o procedimento selecionado para edição
  const selectedProcedimento = selectedProcedimentoId 
    ? veterinario.procedimentosQuery.data?.find((p: ProcedimentoVet) => p.id === selectedProcedimentoId) 
    : null;
  
  // Handler para visualizar detalhes do procedimento
  const handleView = (id: number) => {
    // Implementar navegação para página de detalhes
    console.log('View procedimento', id);
  };
  
  // Handler para abrir o diálogo de edição
  const handleEdit = (id: number) => {
    setSelectedProcedimentoId(id);
    setIsEditDialogOpen(true);
  };
  
  // Handler para agendar próxima vermifugação
  const handleSchedule = (id: number) => {
    const procedimento = veterinario.procedimentosQuery.data?.find((p: ProcedimentoVet) => p.id === selectedProcedimentoId);
    if (procedimento) {
      // Lógica para agendar próxima vermifugação
      console.log('Schedule next deworming for', id);
    }
  };
  
  // Handler para excluir procedimento
  const handleDelete = (id: number) => {
    veterinario.deleteProcedimentoMutation.mutate(id);
  };
  
  // Handler para o envio do formulário de adição
  const handleAddSubmit = (values: ProcedimentoFormValues) => {
    veterinario.addProcedimentoMutation.mutate({
      ...values,
      userId: user?.id
    });
    setIsAddDialogOpen(false);
  };
  
  // Handler para o envio do formulário de edição
  const handleEditSubmit = (values: ProcedimentoFormValues) => {
    if (selectedProcedimentoId) {
      veterinario.updateProcedimentoMutation.mutate({
        ...values,
        id: selectedProcedimentoId
      });
      setIsEditDialogOpen(false);
      setSelectedProcedimentoId(null);
    }
  };
  
  // Configurar opções de status para o filtro
  const statusOptions = [
    { value: 'atrasado', label: 'Atrasados' },
    { value: 'próximo', label: 'Próximos' },
    { value: 'agendado', label: 'Agendados' },
    { value: 'completo', label: 'Completos' }
  ];
  
  // Valores padrão para o formulário de edição
  const getEditDefaultValues = () => {
    if (!selectedProcedimento) return {};
    
    return {
      horseId: selectedProcedimento.horseId,
      tipo: selectedProcedimento.tipo,
      descricao: selectedProcedimento.descricao,
      data: selectedProcedimento.data,
      veterinario: selectedProcedimento.veterinario || '',
      crmv: selectedProcedimento.crmv || '',
      medicamentos: selectedProcedimento.medicamentos || '',
      dosagem: selectedProcedimento.dosagem || '',
      resultado: selectedProcedimento.resultado || '',
      recomendacoes: selectedProcedimento.recomendacoes || '',
      dataProximoProcedimento: selectedProcedimento.dataProximoProcedimento || '',
      observacoes: selectedProcedimento.observacoes || '',
      custo: selectedProcedimento.custo || 0,
      formaPagamento: selectedProcedimento.formaPagamento || '',
      status: selectedProcedimento.status || 'realizado',
      userId: selectedProcedimento.userId,
    };
  };
  
  // Verificar erros de autenticação
  if (
    isAuthenticationError(veterinario.procedimentosQuery.error) ||
    isAuthenticationError(veterinario.cavalosQuery.error)
  ) {
    return (
      <AuthenticationErrorHandler 
        error={veterinario.procedimentosQuery.error || veterinario.cavalosQuery.error} 
        message="Sua sessão expirou ou você não está autenticado. Por favor, faça login novamente para acessar as vermifugações."
      />
    );
  }

  // Mostrar estado de loading enquanto as consultas carregam
  if (veterinario.procedimentosQuery.isLoading || veterinario.cavalosQuery.isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-6">
      <FilterControls
        title="Filtros"
        description="Filtre as vermifugações por cavalo ou status"
        searchTerm={veterinario.searchTerm}
        setSearchTerm={veterinario.setSearchTerm}
        filterCavalo={veterinario.filterCavalo}
        setFilterCavalo={veterinario.setFilterCavalo}
        cavalos={veterinario.cavalosQuery.data || []}
        filterStatus={veterinario.filterStatus}
        setFilterStatus={veterinario.setFilterStatus}
        statusOptions={statusOptions}
      />
      
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Vermifugações</h2>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Nova Vermifugação
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[700px]">
            <DialogHeader>
              <DialogTitle>Adicionar Nova Vermifugação</DialogTitle>
              <DialogDescription>
                Registre uma nova aplicação de vermífugo em um animal.
              </DialogDescription>
            </DialogHeader>
            
            <ProcedimentoVetForm
              onSubmit={handleAddSubmit}
              isSubmitting={veterinario.addProcedimentoMutation.isPending}
              cavalos={veterinario.cavalosQuery.data || []}
              userId={user?.id || 0}
              procedimentoTipo="vermifugacao"
            />
            
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancelar
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      {veterinario.vermifugacoesFiltradas.length > 0 ? (
        <ProcedimentoList
          procedimentos={veterinario.vermifugacoesFiltradas}
          getCavaloName={veterinario.getCavaloName}
          onView={handleView}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onSchedule={handleSchedule}
          formatarData={veterinario.formatarData}
          getStatus={veterinario.getStatusVermifugacao}
          statusType="vermifugacao"
          truncarTexto={veterinario.truncarTexto}
          colunas={['data', 'cavalo', 'tipo', 'medicamento', 'dosagem', 'veterinario', 'proximaData', 'status', 'acoes']}
        />
      ) : (
        <div className="text-center py-10">
          <p className="text-muted-foreground">Nenhuma vermifugação encontrada.</p>
          <Button variant="outline" className="mt-4" onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Registrar Primeira Vermifugação
          </Button>
        </div>
      )}
      
      {/* Dialog para editar vermifugação */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Editar Vermifugação</DialogTitle>
            <DialogDescription>
              Atualize os dados da vermifugação selecionada.
            </DialogDescription>
          </DialogHeader>
          
          <ProcedimentoVetForm
            onSubmit={handleEditSubmit}
            defaultValues={getEditDefaultValues()}
            isSubmitting={veterinario.updateProcedimentoMutation.isPending}
            cavalos={veterinario.cavalosQuery.data || []}
            userId={user?.id || 0}
            procedimentoTipo="vermifugacao"
          />
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => {
                setIsEditDialogOpen(false);
                setSelectedProcedimentoId(null);
              }}
            >
              Cancelar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}