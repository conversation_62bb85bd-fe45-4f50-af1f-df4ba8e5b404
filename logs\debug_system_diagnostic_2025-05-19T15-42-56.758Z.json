{"timestamp": "2025-05-19T15:42:56.758Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 256212992, "heapTotal": 111190016, "heapUsed": 74267544, "external": 8227674, "arrayBuffers": 251917}, "uptime": 2.498763178, "cpuUsage": {"user": 2797829, "system": 426129}, "resourceUsage": {"userCPUTime": 2797893, "systemCPUTime": 426129, "maxRSS": 295892, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101225, "majorPageFault": 3, "swappedOut": 0, "fsRead": 26904, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8587, "involuntaryContextSwitches": 4971}}