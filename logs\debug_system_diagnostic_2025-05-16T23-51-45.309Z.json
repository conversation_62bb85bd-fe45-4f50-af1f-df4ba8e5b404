{"timestamp": "2025-05-16T23:51:45.308Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 269348864, "heapTotal": 118808576, "heapUsed": 93424816, "external": 8405476, "arrayBuffers": 257466}, "uptime": 3.07389388, "cpuUsage": {"user": 3132783, "system": 453300}, "resourceUsage": {"userCPUTime": 3132839, "systemCPUTime": 453308, "maxRSS": 325452, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106592, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 104, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8945, "involuntaryContextSwitches": 10031}}