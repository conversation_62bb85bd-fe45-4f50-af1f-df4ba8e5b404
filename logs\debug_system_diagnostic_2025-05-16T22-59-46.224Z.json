{"timestamp": "2025-05-16T22:59:46.224Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405401600, "heapTotal": 118530048, "heapUsed": 73336168, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.645509479, "cpuUsage": {"user": 2696196, "system": 309758}, "resourceUsage": {"userCPUTime": 2696247, "systemCPUTime": 309758, "maxRSS": 395900, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105882, "majorPageFault": 3, "swappedOut": 0, "fsRead": 0, "fsWrite": 96, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7802, "involuntaryContextSwitches": 1254}}