{"timestamp": "2025-05-23T17:44:18.899Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 256675840, "heapTotal": 109617152, "heapUsed": 74409440, "external": 8211290, "arrayBuffers": 235533}, "uptime": 8.748776822, "cpuUsage": {"user": 3210484, "system": 455304}, "resourceUsage": {"userCPUTime": 3210548, "systemCPUTime": 455313, "maxRSS": 295412, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103296, "majorPageFault": 6, "swappedOut": 0, "fsRead": 55640, "fsWrite": 960, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9293, "involuntaryContextSwitches": 5277}}