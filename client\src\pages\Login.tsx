import { useState } from "react";
import { Link, useLocation } from "wouter";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

// Esquema para o login com nome de usuário em vez de email
const loginSchema = z.object({
  username: z.string().min(3, "Nome de usuário deve ter pelo menos 3 caracteres"),
  password: z.string().min(6, "Senha deve ter pelo menos 6 caracteres"),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

// Contas válidas do banco de dados
const VALID_ACCOUNTS = [
  { username: "fernando", password: "fer@2025", id: 8, email: "<EMAIL>", accessLevel: "user", isActive: true },
  { username: "admin", password: "hello", id: 1, email: "<EMAIL>", accessLevel: "admin", isActive: true },
  { username: "teste", password: "senha123", id: 1, email: "<EMAIL>", accessLevel: "admin", isActive: true },
  { username: "teste4", password: "123456", id: 4, email: "<EMAIL>", accessLevel: "user", isActive: true },
  { username: "test_user", password: "password123", id: 2, email: "<EMAIL>", accessLevel: "user", isActive: true }
];

const Login = () => {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    setLoading(true);
    
    try {
      console.log("Login: Tentando fazer login com", data.username);
      
      // Verificar credenciais contra as contas válidas
      const account = VALID_ACCOUNTS.find(
        acc => acc.username.toLowerCase() === data.username.toLowerCase() && 
              acc.password === data.password
      );
      
      if (account) {
        // Salvar os dados do usuário com níveis de acesso no localStorage
        localStorage.setItem('user', JSON.stringify({
          id: account.id,
          username: account.username,
          email: account.email,
          accessLevel: account.accessLevel,
          isActive: account.isActive
        }));
        
        toast({
          title: "Login realizado",
          description: "Você entrou com sucesso!",
        });
        
        // Redirecionamento bem-sucedido
        navigate("/");
        return;
      }
      
      // Se chegar aqui, significa que as credenciais são inválidas
      throw new Error("Credenciais inválidas");
    } catch (error: any) {
      console.error("Login: Falha no login:", error);
      toast({
        title: "Falha no login",
        description: `Não foi possível autenticar com as credenciais fornecidas. Erro: ${error.message || 'Desconhecido'}`,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mb-6">
            <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Bem-vindo de volta</h2>
          <p className="text-gray-600">Entre na sua conta para continuar</p>
        </div>
        
        <div className="bg-white/80 backdrop-blur-sm py-8 px-6 shadow-xl rounded-2xl border border-white/20">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">Nome de usuário</FormLabel>
                    <FormControl>
                      <Input 
                        type="text" 
                        placeholder="Digite seu usuário" 
                        autoComplete="username"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">Senha</FormLabel>
                    <FormControl>
                      <Input 
                        type="password" 
                        placeholder="Digite sua senha"
                        autoComplete="current-password"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rememberMe"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm text-gray-600">
                        Lembrar de mim
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <Button 
                type="submit"
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 rounded-lg transition-all duration-200 shadow-lg" 
                disabled={loading}
              >
                {loading ? "Entrando..." : "Entrar"}
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default Login;