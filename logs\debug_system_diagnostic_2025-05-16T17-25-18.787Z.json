{"timestamp": "2025-05-16T17:25:18.787Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403005440, "heapTotal": 115646464, "heapUsed": 72620160, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.692955604, "cpuUsage": {"user": 2731550, "system": 331028}, "resourceUsage": {"userCPUTime": 2731618, "systemCPUTime": 331028, "maxRSS": 393560, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103577, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7865, "involuntaryContextSwitches": 1690}}