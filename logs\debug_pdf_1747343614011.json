{"dados": "preview_1747343614010", "logs": "{\n  \"dados\": {\n    \"cavalo\": {\n      \"nome\": \"RZ CONDOLEZA DA CARAPUÇA\",\n      \"registro\": \"B358314\",\n      \"rp\": \"1174\",\n      \"sexo\": \"FEMEA\",\n      \"nascimento\": \"29/10/2010\",\n      \"pelagem\": \"TOSTADA REQUEIMADA RABICANA\",\n      \"criador\": \"RUBENS ELIAS ZOGBI\",\n      \"proprietario\": \"CAMILA WEBER\",\n      \"inspetor\": \"CARLOS MARQUES GONÇALVES NETO\"\n    },\n    \"pai\": {\n      \"nome\": \"RZ RAMPLA DA CARAPUÇA\",\n      \"registro\": \"B230516\"\n    },\n    \"mae\": {\n      \"nome\": \"RZ LUNA VIEJA DA CARAPUÇA\"\n    },\n    \"avoPai\": {\n      \"nome\": \"LA INVERNADA HORNERO\"\n    },\n    \"avaMae\": {\n      \"nome\": \"BT QUERELA\"\n    },\n    \"avoPai2\": {\n      \"nome\": \"TREN TREN ARREBOL\"\n    },\n    \"avaMae2\": {\n      \"nome\": \"ACULEO NOCHE BUENA\"\n    }\n  },\n  \"log\": \"[2025-05-15T21:13:30.692Z] [INFO] Iniciando processamento com OpenAI\\n[2025-05-15T21:13:34.010Z] [INFO] Recebido resposta da OpenAI\\n[2025-05-15T21:13:34.010Z] [DEBUG] Dados extraídos: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"RZ CONDOLEZA DA CARAPUÇA\\\",\\n    \\\"registro\\\": \\\"B358314\\\",\\n    \\\"rp\\\": \\\"1174\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"29/10/2010\\\",\\n    \\\"pelagem\\\": \\\"TOSTADA REQUEIMADA RABICANA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"RUBENS ELIAS ZOGBI\\\",\\n    \\\"inspetor_tecnico\\\": \\\"CARLOS MARQUES GONÇALVES NETO\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"RZ RAMPLA DA CARAPUÇA\\\",\\n    \\\"registro\\\": \\\"B230516\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"RZ LUNA VIEJA DA CARAPUÇA\\\"\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"LA INVERNADA HORNERO\\\"\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"BT QUERELA\\\"\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"TREN TREN ARREBOL\\\"\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"ACULEO NOCHE BUENA\\\"\\n  }\\n}\\n[2025-05-15T21:13:34.010Z] [DEBUG] Resposta completa da OpenAI: {\\n  \\\"cavalo_principal\\\": {\\n    \\\"nome\\\": \\\"RZ CONDOLEZA DA CARAPUÇA\\\",\\n    \\\"registro\\\": \\\"B358314\\\",\\n    \\\"rp\\\": \\\"1174\\\",\\n    \\\"sexo\\\": \\\"FEMEA\\\",\\n    \\\"nascimento\\\": \\\"29/10/2010\\\",\\n    \\\"pelagem\\\": \\\"TOSTADA REQUEIMADA RABICANA\\\",\\n    \\\"proprietario\\\": \\\"CAMILA WEBER\\\",\\n    \\\"criador\\\": \\\"RUBENS ELIAS ZOGBI\\\",\\n    \\\"inspetor_tecnico\\\": \\\"CARLOS MARQUES GONÇALVES NETO\\\"\\n  },\\n  \\\"pai\\\": {\\n    \\\"nome\\\": \\\"RZ RAMPLA DA CARAPUÇA\\\",\\n    \\\"registro\\\": \\\"B230516\\\"\\n  },\\n  \\\"mae\\\": {\\n    \\\"nome\\\": \\\"RZ LUNA VIEJA DA CARAPUÇA\\\"\\n  },\\n  \\\"avo_paterno\\\": {\\n    \\\"nome\\\": \\\"LA INVERNADA HORNERO\\\"\\n  },\\n  \\\"ava_paterna\\\": {\\n    \\\"nome\\\": \\\"BT QUERELA\\\"\\n  },\\n  \\\"avo_materno\\\": {\\n    \\\"nome\\\": \\\"TREN TREN ARREBOL\\\"\\n  },\\n  \\\"ava_materna\\\": {\\n    \\\"nome\\\": \\\"ACULEO NOCHE BUENA\\\"\\n  }\\n}\\n[2025-05-15T21:13:34.010Z] [INFO] Dados do cavalo principal extraídos: RZ CONDOLEZA DA CARAPUÇA (B358314)\\n[2025-05-15T21:13:34.010Z] [INFO] Pai: RZ RAMPLA DA CARAPUÇA (B230516)\\n[2025-05-15T21:13:34.010Z] [INFO] Mãe: RZ LUNA VIEJA DA CARAPUÇA (sem registro)\\n[2025-05-15T21:13:34.010Z] [INFO] Dados convertidos com sucesso\"\n}", "timestamp": "2025-05-15T21:13:34.011Z"}