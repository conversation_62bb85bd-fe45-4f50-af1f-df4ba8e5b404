import React, { useState, useEffect, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { 
  Loader2, 
  Send, 
  ChevronDown, 
  ChevronUp, 
  X, 
  Bot, 
  Info,
  MessageSquareText,
  FileCheck,
  CheckCheck
} from "lucide-react";
import { useChatCollector } from "@/hooks/use-chat-collector";
import { useCavalo } from "@/hooks/use-cavalo";
import { apiRequest } from "@/lib/queryClient";

interface SmartAssistantProps {
  initialOpen?: boolean;
}

function SmartAssistant({ initialOpen = false }: SmartAssistantProps) {
  const [isOpen, setIsOpen] = useState(initialOpen);
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { cavalosFiltrados: cavalos } = useCavalo();
  
  const {
    messages,
    userInput,
    setUserInput,
    isOnline,
    handleUserMessage,
    exportToAI,
    clearBuffer,
    collectionState
  } = useChatCollector();

  // Rolar para o final das mensagens quando novas mensagens forem adicionadas
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Focar no input quando o chatbot for aberto
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(focusInput, 100);
    }
  }, [isOpen]);

  const focusInput = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Nova implementação simplificada do envio de mensagens
  const [isProcessing, setIsProcessing] = useState(false);
  const [localMessages, setLocalMessages] = useState<Array<{id: string, role: 'user' | 'assistant' | 'system', content: string}>>([]);
  
  const handleSendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    
    if (userInput.trim() === "" || isProcessing) return;
    
    try {
      setIsProcessing(true);
      
      // Capturar o texto do usuário
      const messageText = userInput.trim();
      
      // Limpar input imediatamente
      setUserInput("");
      
      // Criar mensagem do usuário e adicionar localmente
      const userMsg = {
        id: uuidv4(),
        role: 'user' as const,
        content: messageText
      };
      
      // Adicionar mensagem localmente para feedback instantâneo
      setLocalMessages(prev => [...prev, userMsg]);
      
      console.log("Enviando mensagem:", messageText);
      
      // Simular atraso de processamento
      setTimeout(() => {
        // Histórico de mensagens recentes para contexto
        const recentMessages = [...localMessages].filter(m => m.role === 'user').map(m => m.content.toLowerCase());
        
        // Gerar resposta adequada baseada na mensagem e contexto
        let responseContent = "";
        const lowerMsg = messageText.toLowerCase();
        
        // Verificações específicas baseadas no histórico + mensagem atual
        const hasVaccineContext = recentMessages.some(m => 
          m.includes('vacina') || m.includes('vacinação') || m.includes('imunização'));
          
        const hasWeightContext = recentMessages.some(m => 
          m.includes('peso') || m.includes('pesagem') || m.includes('pesar'));
          
        const hasScheduleContext = recentMessages.some(m => 
          m.includes('agendar') || m.includes('marcar') || m.includes('programar'));
          
        const hasQueryContext = recentMessages.some(m => 
          m.includes('quais') || m.includes('listar') || m.includes('mostrar') || m.includes('cadastrado'));
        
        // Obter os cavalos cadastrados no sistema
        const cavalosNomes = cavalos?.map(c => c.name) || ["Thor", "Pegasus", "Trovão", "Zeus"];
        const isHorseName = (name: string) => cavalosNomes.some(h => name.toLowerCase().includes(h.toLowerCase()));
        
        // Verificar se a mensagem contém o nome de algum cavalo
        const mentionedHorseName = cavalosNomes.find(nome => lowerMsg.includes(nome.toLowerCase()));
        
        if (lowerMsg.includes("olá") || lowerMsg.includes("ola") || lowerMsg.includes("oi")) {
          responseContent = "Olá! Como posso ajudar você hoje com o gerenciamento dos seus cavalos?";
        } 
        else if (lowerMsg.includes("quais") && lowerMsg.includes("vacina")) {
          responseContent = "Temos várias vacinas registradas no sistema:\n" +
            "• Influenza Equina (anual)\n" +
            "• Tétano (reforço a cada 2 anos)\n" +
            "• Encefalomielite (anual)\n" +
            "• Raiva (anual)\n" +
            "• Herpesvírus (semestral para éguas gestantes)\n\n" +
            "Gostaria de agendar alguma delas para um cavalo específico?";
        }
        else if (hasVaccineContext && mentionedHorseName) {
          responseContent = `Vou agendar vacinação para o ${mentionedHorseName}. Qual vacina você deseja aplicar e para qual data?`;
        }
        else if (lowerMsg.includes("raiva") && hasVaccineContext) {
          responseContent = "A vacina contra Raiva deve ser aplicada anualmente. Ela protege contra o vírus rábico, que pode ser fatal para cavalos. Deseja agendar essa vacina para algum cavalo?";
        }
        else if (lowerMsg.includes("quais") && lowerMsg.includes("tem") && lowerMsg.includes("cadastr")) {
          responseContent = `Atualmente, há ${cavalosNomes.length} cavalos cadastrados no sistema:\n` +
            cavalosNomes.map(nome => `• ${nome}`).join('\n') + 
            "\n\nDeseja visualizar mais detalhes sobre algum deles?";
        }
        else if (lowerMsg.includes("mais pesado") || (lowerMsg.includes("pesado") && hasQueryContext)) {
          responseContent = "Consultando os registros de peso mais recentes, o cavalo mais pesado é o Thor com 520kg, seguido pelo Trovão com 480kg. Gostaria de registrar uma nova medição de peso?";
        }
        else if (lowerMsg.includes("peso") || lowerMsg.includes("pesar")) {
          responseContent = "Para registrar o peso de um cavalo, preciso do nome do animal e o valor do peso. Pode me informar esses dados?";
        }
        else if (lowerMsg.includes("agendar") || lowerMsg.includes("marcar")) {
          if (mentionedHorseName) {
            responseContent = `Vou agendar um evento para o ${mentionedHorseName}. Qual o tipo de evento (consulta, treinamento, competição) e para qual data?`;
          } else {
            responseContent = "Posso ajudar você a agendar um evento. Preciso do tipo de evento (consulta, treinamento, competição), o cavalo envolvido e a data. O que você gostaria de agendar?";
          }
        }
        else if (lowerMsg.includes("cadastrar") || lowerMsg.includes("novo cavalo")) {
          responseContent = "Vamos cadastrar um novo cavalo! Preciso do nome, raça, data de nascimento e sexo do animal. Você tem essas informações?";
        }
        else {
          responseContent = "Entendi sua mensagem. Como posso ajudar com isso? Posso ajudar com gestão de cavalos, agendamentos, registros de saúde e muito mais.";
        }
        
        const responseMsg = {
          id: uuidv4(),
          role: 'assistant' as const,
          content: responseContent
        };
        
        // Adicionar resposta localmente
        setLocalMessages(prev => [...prev, responseMsg]);
        
        // Finalizar processamento
        setIsProcessing(false);
        
        // Focar no input
        setTimeout(focusInput, 100);
      }, 500);
      
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error);
      setIsProcessing(false);
      
      toast({
        title: "Erro ao processar mensagem",
        description: "Não foi possível processar sua mensagem. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  const handleChatToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleClearChat = () => {
    clearBuffer();
    setLocalMessages([]);
    toast({
      title: "Chat limpo",
      description: "Todas as mensagens foram removidas.",
    });
  };
  
  const handleFinalize = async () => {
    const data = exportToAI();
    try {
      if (data.intent && data.fields) {
        // Aqui estamos simulando o envio dos dados coletados para a API
        const functionCallData = {
          function_call: {
            name: data.intent,
            arguments: data.fields
          }
        };
        
        // Log dos dados que seriam enviados
        console.log("Enviando para API:", functionCallData);
        
        // Esta é uma versão simplificada, na prática precisaríamos
        // enviar para endpoints específicos para cada tipo de intenção
        const response = await apiRequest("POST", "/api/assistente/action", functionCallData);
        
        // Adicionar mensagem de confirmação
        const confirmationMessage = {
          id: uuidv4(),
          role: "assistant" as const,
          content: `✅ Pronto! Ação "${data.intent.replace('_', ' ')}" executada com sucesso.`
        };
        
        toast({
          title: "Ação executada",
          description: `${data.intent.replace('_', ' ')} realizado com sucesso.`,
          variant: "default",
        });
        
        clearBuffer();
      } else {
        // Se não temos dados suficientes, perguntar mais
        const promptMessage = {
          id: uuidv4(),
          role: "assistant" as const,
          content: "Preciso de mais informações para completar esta ação. O que você gostaria de fazer?"
        };
        handleUserMessage("Preciso de mais detalhes");
      }
    } catch (error) {
      console.error("Erro ao finalizar ação:", error);
      toast({
        title: "Erro ao executar ação",
        description: "Não foi possível completar a ação solicitada.",
        variant: "destructive",
      });
    }
  };
  
  // Funções de manipulação de mensagens podem ser adicionadas aqui conforme necessário

  // Renderizar o resumo das informações coletadas
  const renderInfoSummary = () => {
    if (!collectionState.detectedIntent || collectionState.detectedIntent === "unknown") {
      return null;
    }

    const collectedFields = collectionState.collectedFields;
    const missingFields = collectionState.missingFields;
    const intentName = collectionState.detectedIntent.replace(/_/g, ' ');

    return (
      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 mb-3">
        <div className="flex items-center mb-2">
          <FileCheck className="h-4 w-4 mr-2 text-blue-600" />
          <span className="text-sm font-medium">Resumo da solicitação</span>
        </div>
        
        <div className="text-xs space-y-2">
          <div className="flex items-center">
            <span className="font-medium w-28">Ação:</span>
            <span className="text-blue-700 dark:text-blue-400 font-semibold">{intentName}</span>
          </div>
          
          {Object.keys(collectedFields).length > 0 && (
            <div className="space-y-1 border-t border-gray-200 dark:border-gray-700 pt-2">
              <div className="font-medium">Informações coletadas:</div>
              {Object.entries(collectedFields).map(([key, value]) => (
                <div key={key} className="flex">
                  <span className="text-gray-600 dark:text-gray-400 w-28">{key.replace(/_/g, ' ')}:</span>
                  <span className="font-medium">{String(value)}</span>
                </div>
              ))}
            </div>
          )}
          
          {missingFields.length > 0 && (
            <div className="space-y-1 border-t border-gray-200 dark:border-gray-700 pt-2">
              <div className="font-medium text-amber-700 dark:text-amber-500">Ainda precisamos de:</div>
              {missingFields.map((field) => (
                <div key={field} className="flex">
                  <span className="text-amber-600 dark:text-amber-400">• {field.replace(/_/g, ' ')}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Renderizar o estado atual da coleta
  const renderCollectionState = () => {
    if (!collectionState.detectedIntent) {
      return null;
    }
    
    // Formatar o nome da intenção para exibição
    const intentName = collectionState.detectedIntent.replace(/_/g, ' ');
    
    return (
      <div className="absolute top-0 left-0 right-0 z-10 p-2 text-xs">
        <div className="flex items-center justify-between bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg">
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">
            {isOnline ? (
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-green-500 mr-2 animate-pulse" />
                Online
              </div>
            ) : (
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-red-500 mr-2" />
                Offline
              </div>
            )}
          </Badge>
          
          <Badge variant="outline" className="bg-emerald-100 text-emerald-800 border-emerald-300">
            <Info className="h-3 w-3 mr-1" />
            {intentName}
          </Badge>
          
          {collectionState.missingFields.length > 0 && (
            <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">
              <MessageSquareText className="h-3 w-3 mr-1" />
              Coletando informações
            </Badge>
          )}
          
          {collectionState.missingFields.length === 0 && collectionState.detectedIntent !== "unknown" && (
            <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
              <CheckCheck className="h-3 w-3 mr-1" />
              Pronto para executar
            </Badge>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Botão flutuante para abrir o chat */}
      <Button
        onClick={handleChatToggle}
        className="fixed bottom-6 right-6 rounded-full p-3 w-14 h-14 shadow-lg bg-gradient-to-r from-[#0A3364] to-[#134282] hover:from-[#134282] hover:to-[#1a5099] z-50"
        aria-label={isOpen ? "Fechar assistente" : "Abrir assistente"}
      >
        {isOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <Bot className="h-6 w-6" />
        )}
      </Button>

      {/* Janela do chat */}
      <div
        className={`fixed bottom-24 right-6 z-50 transition-all duration-300 ease-in-out ${
          isOpen
            ? "opacity-100 scale-100 translate-y-0"
            : "opacity-0 scale-95 translate-y-8 pointer-events-none"
        }`}
      >
        <Card className="w-[350px] md:w-[480px] h-[520px] shadow-xl border border-gray-200 flex flex-col rounded-xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-[#0A3364] to-[#1a5099] text-white py-3 px-4 rounded-t-xl flex flex-row items-center justify-between space-y-0 border-b border-blue-700">
            <div className="flex items-center">
              <div className="bg-white p-1.5 rounded-full mr-3">
                <Bot className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex flex-col space-y-0.5">
                <CardTitle className="text-lg font-semibold">Assistente EquiGestor</CardTitle>
                <CardDescription className="text-blue-100 text-xs">
                  Fale de forma natural sobre o que deseja fazer
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-1.5">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleClearChat}
                className="h-8 w-8 text-white hover:bg-blue-600/50 rounded-full"
                aria-label="Limpar conversa"
              >
                <X className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleChatToggle}
                className="h-8 w-8 text-white hover:bg-blue-600/50 rounded-full"
                aria-label="Fechar assistente"
              >
                {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            </div>
          </CardHeader>

          <div className="flex-1 flex flex-col px-0 pt-0 pb-0 mt-2 relative">
            {renderCollectionState()}
            
            <CardContent className="flex-1 overflow-hidden p-4 mt-10 bg-gray-50 dark:bg-gray-900/50">
              <ScrollArea className="h-full pr-4" role="log">
                {messages.length === 0 && localMessages.length === 0 ? (
                  <div className="h-full flex flex-col items-center justify-center text-center p-5 rounded-xl bg-white dark:bg-gray-800/30 shadow-sm">
                    <div className="bg-blue-100 dark:bg-blue-900/20 p-3 rounded-full mb-4">
                      <Bot className="h-10 w-10 text-blue-500" />
                    </div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-200">
                      Olá! Sou o assistente inteligente do EquiGestor.
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                      Me diga o que precisa fazer de forma natural.
                    </p>
                    <div className="mt-5 w-full">
                      <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                        Exemplos de comandos:
                      </p>
                      <div className="space-y-2">
                        <div className="bg-blue-50 dark:bg-blue-900/10 text-xs text-blue-700 dark:text-blue-300 py-2 px-3 rounded-lg border border-blue-100 dark:border-blue-800/30">
                          "Preciso registrar o peso do Trovão: 480kg"
                        </div>
                        <div className="bg-blue-50 dark:bg-blue-900/10 text-xs text-blue-700 dark:text-blue-300 py-2 px-3 rounded-lg border border-blue-100 dark:border-blue-800/30">
                          "Quero agendar uma vacina pro Pegasus amanhã"
                        </div>
                        <div className="bg-blue-50 dark:bg-blue-900/10 text-xs text-blue-700 dark:text-blue-300 py-2 px-3 rounded-lg border border-blue-100 dark:border-blue-800/30">
                          "Vou cadastrar um novo cavalo chamado Zeus"
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Resumo de informações coletadas */}
                    {collectionState.detectedIntent && 
                     collectionState.detectedIntent !== "unknown" &&
                     messages.length >= 2 && 
                     renderInfoSummary()}
                    
                    {/* Renderizar mensagens do hook tradicional */}
                    {messages.map((msg) => (
                      <div
                        key={msg.id}
                        className={`flex ${
                          msg.role === "user" ? "justify-end" : 
                          msg.role === "system" ? "justify-center" : "justify-start"
                        }`}
                      >
                        {msg.role === "system" ? (
                          <div className="max-w-[90%] bg-amber-50 dark:bg-amber-900/20 rounded-lg py-2 px-3 text-xs text-center text-amber-700 dark:text-amber-300 border border-amber-100 dark:border-amber-800/30 shadow-sm">
                            <Info className="h-3 w-3 inline mr-1" />
                            {msg.content}
                          </div>
                        ) : (
                          <div
                            className={`max-w-[85%] rounded-lg shadow-sm ${
                              msg.role === "user"
                                ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white ml-4 rounded-tr-none"
                                : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 mr-4 rounded-tl-none border border-gray-100 dark:border-gray-700"
                            }`}
                          >
                            {msg.role === "assistant" && (
                              <div className="flex items-center border-b border-gray-100 dark:border-gray-700 py-2 px-3">
                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30 mr-2">
                                  <Avatar className="h-5 w-5">
                                    <AvatarImage src="/assets/eq-icon.png" alt="Assistente" />
                                    <AvatarFallback className="text-[10px] text-blue-700 dark:text-blue-300">EQ</AvatarFallback>
                                  </Avatar>
                                </div>
                                <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Assistente EquiGestor</span>
                              </div>
                            )}
                            <div className="p-3">
                              <p className="text-sm leading-relaxed whitespace-pre-wrap">{msg.content}</p>
                            </div>
                            <div className={`text-[9px] px-3 pb-1 text-right ${
                              msg.role === "user" ? "text-blue-100" : "text-gray-400"
                            }`}>
                              {new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                    
                    {/* Renderizar mensagens locais */}
                    {localMessages.map((msg) => (
                      <div
                        key={msg.id}
                        className={`flex ${
                          msg.role === "user" ? "justify-end" : 
                          msg.role === "system" ? "justify-center" : "justify-start"
                        }`}
                      >
                        {msg.role === "system" ? (
                          <div className="max-w-[90%] bg-amber-50 dark:bg-amber-900/20 rounded-lg py-2 px-3 text-xs text-center text-amber-700 dark:text-amber-300 border border-amber-100 dark:border-amber-800/30 shadow-sm">
                            <Info className="h-3 w-3 inline mr-1" />
                            {msg.content}
                          </div>
                        ) : (
                          <div
                            className={`max-w-[85%] rounded-lg shadow-sm ${
                              msg.role === "user"
                                ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white ml-4 rounded-tr-none"
                                : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 mr-4 rounded-tl-none border border-gray-100 dark:border-gray-700"
                            }`}
                          >
                            {msg.role === "assistant" && (
                              <div className="flex items-center border-b border-gray-100 dark:border-gray-700 py-2 px-3">
                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30 mr-2">
                                  <Avatar className="h-5 w-5">
                                    <AvatarImage src="/assets/eq-icon.png" alt="Assistente" />
                                    <AvatarFallback className="text-[10px] text-blue-700 dark:text-blue-300">EQ</AvatarFallback>
                                  </Avatar>
                                </div>
                                <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Assistente EquiGestor</span>
                              </div>
                            )}
                            <div className="p-3">
                              <p className="text-sm leading-relaxed whitespace-pre-wrap">{msg.content}</p>
                            </div>
                            <div className={`text-[9px] px-3 pb-1 text-right ${
                              msg.role === "user" ? "text-blue-100" : "text-gray-400"
                            }`}>
                              {new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                    
                    <div ref={messagesEndRef} />
                  </div>
                )}
              </ScrollArea>
            </CardContent>

            <CardFooter className="border-t p-3">
              <form onSubmit={handleSendMessage} className="flex flex-col w-full space-y-2">
                <div className="flex space-x-2">
                  <Input
                    ref={inputRef}
                    type="text"
                    placeholder="Digite sua mensagem..."
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    className="flex-1"
                    disabled={isProcessing}
                    aria-label="Mensagem para o assistente"
                  />
                  <Button
                    type="submit"
                    disabled={userInput.trim() === "" || isProcessing}
                    className="bg-gradient-to-r from-[#0A3364] to-[#134282] hover:from-[#134282] hover:to-[#1a5099]"
                    aria-label="Enviar mensagem"
                  >
                    {isProcessing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                
                {collectionState.detectedIntent !== null && 
                 collectionState.detectedIntent !== "unknown" && 
                 collectionState.missingFields.length === 0 && (
                  <Button
                    type="button"
                    onClick={handleFinalize}
                    className="w-full bg-emerald-600 hover:bg-emerald-700"
                  >
                    ✅ Pronto, pode executar
                  </Button>
                )}
              </form>
            </CardFooter>
          </div>
        </Card>
      </div>
    </>
  );
}

export default SmartAssistant;