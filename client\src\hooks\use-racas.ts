import { useQuery } from '@tanstack/react-query';

export interface Raca {
  id: number;
  nome: string;
  descricao?: string;
  origem?: string;
  caracteristicas?: string;
}

/**
 * Hook para buscar raças de cavalos do banco de dados
 */
export function useRacas() {
  const query = useQuery({
    queryKey: ['/api/racas'],
    queryFn: async ({ queryKey }) => {
      const response = await fetch(queryKey[0], {
        headers: {
          'Content-Type': 'application/json',
          'User-Id': '1' // Para autenticação
        }
      });
      
      if (!response.ok) {
        throw new Error('Erro ao buscar raças');
      }
      
      return response.json();
    },
    staleTime: 60 * 1000, // 1 minuto
    refetchOnWindowFocus: false
  });

  return {
    racas: Array.isArray(query.data) ? query.data : [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
  };
}