
-- <PERSON><PERSON> criar um usuário de teste se não existir
INSERT INTO users (username, password, email)
VALUES ('admin', '$2b$10$K8ZpdrjwzUWoFs7YYxOOz.qt7DAIUXuwht', '<EMAIL>')
ON CONFLICT (username) DO NOTHING;

-- Inserir 10 cavalos com dados completos
INSERT INTO cavalos (name, breed, birth_date, peso, altura, sexo, status, data_entrada, pai, mae, avo_paterno, avo_materno, notes, user_id)
VALUES 
('Zeus', '<PERSON>galar<PERSON> Marchador', '2020-03-15', 480.5, 1.52, 'Macho', 'ativo', '2023-01-10', '<PERSON>rov<PERSON> do Vale', 'Princesa Negra', '<PERSON><PERSON> do <PERSON>', 'Estrela Negra', '<PERSON><PERSON><PERSON><PERSON> de excelente linhagem, temperamento dócil', 1),
('<PERSON>', '<PERSON>ua<PERSON><PERSON> <PERSON>', '2019-06-22', 450.2, 1.48, 'Fê<PERSON><PERSON>', 'ativo', '2023-02-15', 'Gold Runner', '<PERSON> Queen', '<PERSON> Gold', 'Queen of Hearts', 'Égua premiada em provas de ta<PERSON>r', 1),
('<PERSON>', '<PERSON><PERSON><PERSON> Ing<PERSON><PERSON>s', '2021-01-30', 495.0, 1.62, '<PERSON><PERSON>', 'at<PERSON>', '2023-03-20', '<PERSON> <PERSON>', '<PERSON> <PERSON>', '<PERSON> <PERSON>', '<PERSON> <PERSON>', '<PERSON>ten<PERSON> para co<PERSON><PERSON>, m<PERSON><PERSON> ve<PERSON>z', 1),
('<PERSON><PERSON>ra', '<PERSON><PERSON><PERSON><PERSON>', '2018-09-12', 440.8, 1.51, 'Fêmea', 'ativo', '2023-04-05', 'Spotted King', 'Diamond Girl', 'Crown Spot', 'Pearl Beauty', 'Pelagem única, ótima para reprodução', 1),
('Thor', 'Friesian', '2017-11-25', 520.3, 1.68, 'Macho', 'ativo', '2023-05-12', 'Black Thunder', 'Night Queen', 'Storm King', 'Dark Lady', 'Cavalo de exposição, temperamento excepcional', 1),
('Bella', 'Andaluz', '2019-04-18', 465.7, 1.55, 'Fêmea', 'ativo', '2023-06-20', 'Spanish Pride', 'Andalusian Beauty', 'Royal Pride', 'Spanish Rose', 'Égua de adestramento, muito disciplinada', 1),
('Apache', 'Paint Horse', '2020-08-03', 470.4, 1.53, 'Macho', 'ativo', '2023-07-15', 'Color Master', 'Paint Princess', 'Art Master', 'Color Queen', 'Excelente para provas de conformação', 1),
('Estrela', 'Lusitano', '2018-12-10', 455.6, 1.54, 'Fêmea', 'ativo', '2023-08-22', 'Portuguese Star', 'Lusitanian Moon', 'Star King', 'Moon Light', 'Premiada em provas de adestramento', 1),
('Trovão', 'Crioulo', '2019-02-28', 445.9, 1.47, 'Macho', 'ativo', '2023-09-30', 'Pampa Negro', 'Prenda Linda', 'Negro Rey', 'Linda Flor', 'Ótimo para lida no campo', 1),
('Pérola', 'Campolina', '2020-05-15', 460.2, 1.56, 'Fêmea', 'ativo', '2023-10-05', 'Diamante Negro', 'Joia Rara', 'Negro Real', 'Rara Beleza', 'Marcha picada de excelente qualidade', 1);

-- Inserir registros de manejos
INSERT INTO manejos (tipo, data, observacoes, dataVencimento, status, prioridade, horseId, userId)
VALUES
('Vermifugação', '2025-03-15', 'Ivermectina aplicada', '2025-06-15', 'concluído', 'normal', 1, 1),
('Casqueamento', '2025-03-20', 'Realizado casqueamento preventivo', '2025-04-20', 'pendente', 'alta', 2, 1),
('Vacinação', '2025-03-25', 'Vacina contra Influenza', '2025-09-25', 'concluído', 'alta', 3, 1),
('Odontologia', '2025-04-01', 'Tratamento dentário de rotina', '2025-10-01', 'pendente', 'normal', 4, 1),
('Banho', '2025-03-28', 'Banho medicamentoso', null, 'concluído', 'baixa', 5, 1);

-- Inserir registros veterinários
INSERT INTO procedimentos_vet (tipo, data, descricao, veterinario, status, horseId, userId)
VALUES
('Exame Clínico', '2025-03-10', 'Avaliação geral de rotina', 'Dr. Carlos Silva', 'concluído', 1, 1),
('Cirurgia', '2025-03-12', 'Artroscopia no posterior direito', 'Dra. Maria Santos', 'em andamento', 2, 1),
('Vacinação', '2025-03-15', 'Protocolo completo de vacinação', 'Dr. João Oliveira', 'concluído', 3, 1),
('Exame Radiográfico', '2025-03-18', 'Raio-X dos membros anteriores', 'Dra. Ana Paula', 'pendente', 4, 1),
('Tratamento', '2025-03-20', 'Tratamento de ferida no boleto', 'Dr. Pedro Costa', 'em andamento', 5, 1);

-- Inserir dados de reprodução
INSERT INTO reproducao (tipo_evento, data, observacoes, status, horseId, userId)
VALUES
('Cobertura', '2025-03-01', 'Monta natural bem sucedida', 'confirmado', 2, 1),
('Inseminação', '2025-03-05', 'Inseminação artificial com sêmen fresco', 'aguardando', 4, 1),
('Parto', '2025-03-10', 'Parto normal sem complicações', 'concluído', 6, 1),
('Diagnóstico', '2025-03-15', 'Confirmação de prenhez 45 dias', 'positivo', 8, 1),
('Cobertura', '2025-03-20', 'Segunda tentativa de monta natural', 'aguardando', 10, 1);

-- Inserir medidas físicas
INSERT INTO medidas_fisicas (data, peso, altura, perimetro_toracico, horseId, userId)
VALUES
('2025-03-01', 480.5, 1.52, 180.0, 1, 1),
('2025-03-01', 450.2, 1.48, 175.5, 2, 1),
('2025-03-01', 495.0, 1.62, 185.0, 3, 1),
('2025-03-01', 440.8, 1.51, 172.0, 4, 1),
('2025-03-01', 520.3, 1.68, 190.0, 5, 1);

-- Inserir registros de nutrição
INSERT INTO nutricao (tipo_alimento, quantidade_diaria, unidade, horario_alimentacao, observacoes, horseId, userId)
VALUES
('Ração Performance', 4.0, 'kg', '06:00,12:00,18:00', 'Dividir em 3 refeições', 1, 1),
('Feno de Alfafa', 8.0, 'kg', '08:00,14:00,20:00', 'Fornecer após a ração', 2, 1),
('Ração Maintenance', 3.5, 'kg', '07:00,19:00', 'Dividir em 2 refeições', 3, 1),
('Feno de Coast Cross', 7.0, 'kg', '09:00,15:00,21:00', 'Deixar disponível', 4, 1),
('Ração Sport', 5.0, 'kg', '06:00,18:00', 'Adicionar óleo de linhaça', 5, 1);

-- Inserir eventos no calendário
INSERT INTO eventos (titulo, data_inicio, data_fim, tipo, descricao, status, horseId, userId)
VALUES
('Treinamento', '2025-04-01 09:00', '2025-04-01 11:00', 'treino', 'Treino de doma', 'agendado', 1, 1),
('Competição', '2025-04-15 08:00', '2025-04-17 18:00', 'evento', 'Campeonato Estadual', 'confirmado', 2, 1),
('Veterinário', '2025-04-05 14:00', '2025-04-05 15:00', 'veterinario', 'Consulta de rotina', 'agendado', 3, 1),
('Ferrageamento', '2025-04-10 10:00', '2025-04-10 11:00', 'manejo', 'Troca de ferraduras', 'pendente', 4, 1),
('Transporte', '2025-04-20 07:00', '2025-04-20 12:00', 'transporte', 'Transporte para exposição', 'agendado', 5, 1);

-- Inserir registros financeiros
INSERT INTO financeiro (data, tipo, categoria, descricao, valor, status, horseId, userId)
VALUES
('2025-03-01', 'despesa', 'Alimentação', 'Compra de ração', 1500.00, 'pago', 1, 1),
('2025-03-05', 'receita', 'Hospedagem', 'Mensalidade de pensão', 2000.00, 'recebido', 2, 1),
('2025-03-10', 'despesa', 'Veterinário', 'Consulta e medicamentos', 800.00, 'pago', 3, 1),
('2025-03-15', 'despesa', 'Ferrageamento', 'Troca de ferraduras', 400.00, 'pendente', 4, 1),
('2025-03-20', 'receita', 'Reprodução', 'Taxa de cobertura', 3000.00, 'recebido', 5, 1);

-- Inserir documentos/arquivos
INSERT INTO arquivos (fileName, filePath, fileType, description, horseId, userId)
VALUES
('Pedigree_Zeus.pdf', '/uploads/horses/pedigree_zeus.pdf', 'pdf', 'Registro de genealogia', 1, 1),
('Exame_Luna.jpg', '/uploads/horses/exame_luna.jpg', 'image', 'Radiografia do posterior', 2, 1),
('Vacinas_Flash.pdf', '/uploads/horses/vacinas_flash.pdf', 'pdf', 'Carteira de vacinação', 3, 1),
('Foto_Safira.jpg', '/uploads/horses/foto_safira.jpg', 'image', 'Foto para exposição', 4, 1),
('Video_Thor.mp4', '/uploads/horses/video_thor.mp4', 'video', 'Vídeo de desempenho', 5, 1);

-- Inserir dados morfológicos
INSERT INTO morfologia (data_avaliacao, caracteristicas, pontuacao, observacoes, horseId, userId)
VALUES
('2025-03-01', 'Cabeça e pescoço bem proporcionais', 85, 'Excelente conformação', 1, 1),
('2025-03-02', 'Membros anteriores fortes', 88, 'Boa estrutura óssea', 2, 1),
('2025-03-03', 'Garupa bem desenvolvida', 90, 'Morfologia ideal para sua raça', 3, 1),
('2025-03-04', 'Bom alinhamento dorso-lombar', 87, 'Necessita desenvolvimento muscular', 4, 1),
('2025-03-05', 'Estrutura corporal harmoniosa', 92, 'Exemplar de alta qualidade', 5, 1);

-- Inserir histórico de desempenho
INSERT INTO desempenho_historico (data_evento, tipo_evento, resultado, observacoes, horseId, userId)
VALUES
('2025-02-01', 'Prova de Marcha', '1º Lugar', 'Excelente desempenho', 1, 1),
('2025-02-15', 'Prova de Tambor', '2º Lugar', 'Tempo: 17.5 segundos', 2, 1),
('2025-03-01', 'Exposição', 'Grande Campeão', 'Melhor da raça', 3, 1),
('2025-03-15', 'Adestramento', '3º Lugar', 'Nível Elementar', 4, 1),
('2025-03-30', 'Enduro', 'Completou', '80km em 6 horas', 5, 1);

-- Inserir dados de genealogia
INSERT INTO genealogia (geracao, ancestrais, coeficiente_consanguinidade, observacoes, horseId, userId)
VALUES
('G1', 'Dados completos até 3ª geração', 0.025, 'Linhagem tradicional', 1, 1),
('G2', 'Registro de campeões', 0.018, 'Forte herança genética', 2, 1),
('G1', 'Ancestrais premiados', 0.015, 'Potencial reprodutivo', 3, 1),
('G3', 'Genealogia verificada', 0.022, 'Linhagem pura', 4, 1),
('G2', 'Histórico comprovado', 0.020, 'Excelente base genética', 5, 1);

-- Inserir sugestões de cruzamento
INSERT INTO sugestoes_cruzamento (data_analise, potenciais_parceiros, objetivo_melhoramento, probabilidade_sucesso, observacoes, horseId, userId)
VALUES
('2025-03-01', 'Zeus x Luna', 'Melhoramento de marcha', 0.85, 'Alta compatibilidade genética', 1, 1),
('2025-03-02', 'Flash x Safira', 'Velocidade e resistência', 0.78, 'Complementaridade morfológica', 2, 1),
('2025-03-03', 'Thor x Bella', 'Conformação e temperamento', 0.92, 'Excelente combinação', 3, 1),
('2025-03-04', 'Apache x Estrela', 'Características raciais', 0.88, 'Potencial para exposições', 4, 1),
('2025-03-05', 'Trovão x Pérola', 'Versatilidade', 0.82, 'Bom prognóstico', 5, 1);
