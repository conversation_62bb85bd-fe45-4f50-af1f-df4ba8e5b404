import { useQuery } from '@tanstack/react-query';

export interface Pelagem {
  id: number;
  nome: string;
  descricao?: string;
  fonte?: string;
  userId?: number;
}

/**
 * Hook para buscar pelagens cadastradas no sistema
 * @returns Pelagens cadastradas no sistema
 */
export function usePelagens() {
  const query = useQuery({
    queryKey: ['/api/pelagens'],
    queryFn: async ({ queryKey }) => {
      const response = await fetch(queryKey[0], {
        headers: {
          'Content-Type': 'application/json',
          'User-Id': '1' // Para autenticação
        }
      });
      
      if (!response.ok) {
        throw new Error('Erro ao buscar pelagens');
      }
      
      return response.json();
    },
    staleTime: 60 * 1000, // 1 minuto
    refetchOnWindowFocus: false
  });

  return {
    pelagens: Array.isArray(query.data) ? query.data : [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
  };
}