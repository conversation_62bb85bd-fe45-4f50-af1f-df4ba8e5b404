{"timestamp": "2025-05-26T21:24:46.436Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 395976704, "heapTotal": 103591936, "heapUsed": 84040408, "external": 8298765, "arrayBuffers": 282042}, "uptime": 1.840373925, "cpuUsage": {"user": 2873191, "system": 363178}, "resourceUsage": {"userCPUTime": 2873238, "systemCPUTime": 363184, "maxRSS": 386696, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101464, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7959, "involuntaryContextSwitches": 2318}}