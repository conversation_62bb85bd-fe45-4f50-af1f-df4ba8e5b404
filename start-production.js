#!/usr/bin/env node

// Script de inicialização para produção compatível com Unix/Linux
// Substitui o comando "set NODE_ENV=production" do Windows

console.log('🚀 Iniciando EquiGestor AI em modo produção...');

// Define a variável de ambiente corretamente para Unix/Linux
process.env.NODE_ENV = 'production';

// Configurar porta para deploy do Replit
if (process.env.REPL_ID) {
  process.env.PORT = process.env.PORT || '5000';
}

console.log('✅ NODE_ENV definido como:', process.env.NODE_ENV);
console.log('✅ Porta configurada:', process.env.PORT || '5000');

// Verifica se o arquivo de build existe
import { existsSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const buildFile = join(__dirname, 'dist', 'index.js');

if (!existsSync(buildFile)) {
  console.error('❌ Erro: Arquivo de build não encontrado em:', buildFile);
  console.error('Execute "npm run build" antes de iniciar em produção');
  process.exit(1);
}

console.log('✅ Arquivo de build encontrado, iniciando aplicação...');

// Configurar tratamento de saída limpa
process.on('SIGTERM', () => {
  console.log('📤 Recebido SIGTERM, encerrando aplicação...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('📤 Recebido SIGINT, encerrando aplicação...');
  process.exit(0);
});

// Importa e executa o arquivo principal
import('./dist/index.js').catch((error) => {
  console.error('❌ Erro ao iniciar aplicação em produção:', error);
  console.error('Detalhes do erro:', error.stack);
  process.exit(1);
});