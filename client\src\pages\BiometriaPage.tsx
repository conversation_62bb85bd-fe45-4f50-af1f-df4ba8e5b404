import { useEffect, useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { LayoutWrapper } from '@/components/Layout';
import { AcompanhamentoFisico } from '@/components/AcompanhamentoFisico';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/hooks/use-toast';
import { Cavalo, MedidaFisica } from '@shared/schema';
import { Database, BarChart3, Ruler, Scale } from 'lucide-react';

/**
 * Página de Dados Biométricos
 * 
 * Esta página permite visualizar e registrar dados biométricos dos cavalos,
 * como peso, altura e condição corporal.
 */
export default function BiometriaPage() {
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(null);
  const [selectedTab, setSelectedTab] = useState<string>('geral');

  // Consulta de cavalos
  const { data: cavalos = [], isLoading: isLoadingCavalos } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      return await apiRequest<Cavalo[]>('GET', '/api/cavalos');
    }
  });

  // Consulta de medidas físicas
  const { data: medidasFisicas = [], isLoading: isLoadingMedidas } = useQuery({
    queryKey: ['/api/medidas-fisicas', selectedHorseId],
    enabled: !!selectedHorseId,
    queryFn: async () => {
      return await apiRequest<MedidaFisica[]>('GET', `/api/medidas-fisicas/horse/${selectedHorseId}`);
    }
  });

  // Selecionar primeiro cavalo quando a lista for carregada
  useEffect(() => {
    if (cavalos.length > 0 && !selectedHorseId) {
      setSelectedHorseId(cavalos[0].id);
    }
  }, [cavalos, selectedHorseId]);

  // Encontrar o cavalo selecionado
  const selectedHorse = cavalos.find(c => c.id === selectedHorseId);

  return (
    <LayoutWrapper pageTitle="Dados Biométricos">
      <div className="space-y-4">
        <div className="flex flex-col md:flex-row gap-4 md:items-center justify-between">
          <div className="flex items-center gap-2">
            <Database className="h-5 w-5 text-blue-500" />
            <h1 className="text-2xl font-bold">Acompanhamento Biométrico</h1>
          </div>
          
          {/* Seletor de Cavalo */}
          <div className="w-full md:w-64">
            <Select
              value={selectedHorseId?.toString() || ''}
              onValueChange={(value) => setSelectedHorseId(Number(value))}
              disabled={isLoadingCavalos}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione um cavalo" />
              </SelectTrigger>
              <SelectContent>
                {cavalos.map((cavalo) => (
                  <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                    {cavalo.name} ({cavalo.register || 'Sem registro'})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {isLoadingCavalos ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
          </div>
        ) : selectedHorse ? (
          <div className="grid gap-4">
            {/* Tabs de navegação */}
            <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
              <TabsList className="grid grid-cols-3 w-full md:w-auto">
                <TabsTrigger value="geral" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>Geral</span>
                </TabsTrigger>
                <TabsTrigger value="medidas" className="flex items-center gap-2">
                  <Ruler className="h-4 w-4" />
                  <span>Medidas</span>
                </TabsTrigger>
                <TabsTrigger value="peso" className="flex items-center gap-2">
                  <Scale className="h-4 w-4" />
                  <span>Peso e Condição</span>
                </TabsTrigger>
              </TabsList>

              {/* Seção Geral */}
              <TabsContent value="geral" className="space-y-4">
                <AcompanhamentoFisico 
                  horseId={selectedHorseId} 
                  horseName={selectedHorse.name}
                />
              </TabsContent>

              {/* Seção de Medidas */}
              <TabsContent value="medidas" className="space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-xl">Histórico de Altura</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {isLoadingMedidas ? (
                      <div className="flex justify-center py-8">
                        <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
                      </div>
                    ) : medidasFisicas.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="border-b">
                              <th className="py-2 px-3 text-left">Data</th>
                              <th className="py-2 px-3 text-right">Altura (cm)</th>
                              <th className="py-2 px-3 text-left">Observações</th>
                            </tr>
                          </thead>
                          <tbody>
                            {medidasFisicas
                              .filter(m => m.altura !== null)
                              .sort((a, b) => new Date(b.data).getTime() - new Date(a.data).getTime())
                              .map((medida) => (
                                <tr key={medida.id} className="border-b hover:bg-gray-50">
                                  <td className="py-2 px-3">{new Date(medida.data).toLocaleDateString()}</td>
                                  <td className="py-2 px-3 text-right font-medium">{medida.altura}</td>
                                  <td className="py-2 px-3 text-gray-600">{medida.observacoes || '-'}</td>
                                </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        Nenhum registro de altura encontrado para este animal.
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Seção de Peso e Condição */}
              <TabsContent value="peso" className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl">Histórico de Peso</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {isLoadingMedidas ? (
                        <div className="flex justify-center py-8">
                          <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
                        </div>
                      ) : medidasFisicas.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse">
                            <thead>
                              <tr className="border-b">
                                <th className="py-2 px-3 text-left">Data</th>
                                <th className="py-2 px-3 text-right">Peso (kg)</th>
                              </tr>
                            </thead>
                            <tbody>
                              {medidasFisicas
                                .filter(m => m.peso !== null)
                                .sort((a, b) => new Date(b.data).getTime() - new Date(a.data).getTime())
                                .map((medida) => (
                                  <tr key={medida.id} className="border-b hover:bg-gray-50">
                                    <td className="py-2 px-3">{new Date(medida.data).toLocaleDateString()}</td>
                                    <td className="py-2 px-3 text-right font-medium">{medida.peso}</td>
                                  </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          Nenhum registro de peso encontrado para este animal.
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl">Condição Corporal</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {isLoadingMedidas ? (
                        <div className="flex justify-center py-8">
                          <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
                        </div>
                      ) : medidasFisicas.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse">
                            <thead>
                              <tr className="border-b">
                                <th className="py-2 px-3 text-left">Data</th>
                                <th className="py-2 px-3 text-right">Escore (1-9)</th>
                                <th className="py-2 px-3 text-left">Status</th>
                              </tr>
                            </thead>
                            <tbody>
                              {medidasFisicas
                                .filter(m => m.condicaoCorporal !== null)
                                .sort((a, b) => new Date(b.data).getTime() - new Date(a.data).getTime())
                                .map((medida) => {
                                  // Determinar status baseado no escore
                                  let status = "Normal";
                                  let statusClass = "text-green-600";
                                  
                                  if (medida.condicaoCorporal! < 4) {
                                    status = "Abaixo do peso";
                                    statusClass = "text-red-600";
                                  } else if (medida.condicaoCorporal! > 7) {
                                    status = "Acima do peso";
                                    statusClass = "text-orange-600";
                                  }
                                  
                                  return (
                                    <tr key={medida.id} className="border-b hover:bg-gray-50">
                                      <td className="py-2 px-3">{new Date(medida.data).toLocaleDateString()}</td>
                                      <td className="py-2 px-3 text-right font-medium">{medida.condicaoCorporal}</td>
                                      <td className={`py-2 px-3 ${statusClass} font-medium`}>{status}</td>
                                    </tr>
                                  );
                                })}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          Nenhum registro de condição corporal encontrado para este animal.
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        ) : (
          <div className="text-center py-16 bg-gray-50 rounded-lg border">
            <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-600">Nenhum cavalo encontrado</h2>
            <p className="mt-2 text-gray-500">Cadastre um cavalo para iniciar o acompanhamento biométrico</p>
            <Button className="mt-4" variant="outline" onClick={() => window.location.href = '/cavalo/cadastro'}>
              Cadastrar Novo Cavalo
            </Button>
          </div>
        )}
      </div>
    </LayoutWrapper>
  );
}