{"timestamp": "2025-05-15T00:16:20.635Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 387543040, "heapTotal": 104468480, "heapUsed": 62017704, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.695662162, "cpuUsage": {"user": 2525656, "system": 303712}, "resourceUsage": {"userCPUTime": 2525714, "systemCPUTime": 303719, "maxRSS": 378460, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99773, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6373, "involuntaryContextSwitches": 2831}}