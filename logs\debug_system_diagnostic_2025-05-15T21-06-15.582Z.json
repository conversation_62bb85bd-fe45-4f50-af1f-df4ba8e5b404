{"timestamp": "2025-05-15T21:06:15.581Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 400011264, "heapTotal": 114335744, "heapUsed": 72469608, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.98998899, "cpuUsage": {"user": 2879759, "system": 401592}, "resourceUsage": {"userCPUTime": 2879805, "systemCPUTime": 401598, "maxRSS": 390636, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102300, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8501, "involuntaryContextSwitches": 4078}}