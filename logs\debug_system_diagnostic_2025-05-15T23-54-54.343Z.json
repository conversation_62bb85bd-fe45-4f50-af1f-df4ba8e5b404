{"timestamp": "2025-05-15T23:54:54.343Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 304091136, "heapTotal": 169840640, "heapUsed": 144930696, "external": 14145602, "arrayBuffers": 1179876}, "uptime": 75.987926496, "cpuUsage": {"user": 16292796, "system": 1173620}, "resourceUsage": {"userCPUTime": 16292802, "systemCPUTime": 1173620, "maxRSS": 843140, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 243292, "majorPageFault": 0, "swappedOut": 0, "fsRead": 8, "fsWrite": 592, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 23077, "involuntaryContextSwitches": 22513}}