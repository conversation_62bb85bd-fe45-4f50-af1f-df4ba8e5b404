{"timestamp": "2025-05-16T22:42:36.455Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 407085056, "heapTotal": 118530048, "heapUsed": 72606896, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.972447452, "cpuUsage": {"user": 2977941, "system": 380875}, "resourceUsage": {"userCPUTime": 2977976, "systemCPUTime": 380879, "maxRSS": 397544, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105481, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 112, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8191, "involuntaryContextSwitches": 4149}}