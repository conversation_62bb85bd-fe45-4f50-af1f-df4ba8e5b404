{"timestamp": "2025-05-15T21:24:32.851Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405442560, "heapTotal": 115646464, "heapUsed": 72499584, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.916722556, "cpuUsage": {"user": 2802765, "system": 376248}, "resourceUsage": {"userCPUTime": 2802812, "systemCPUTime": 376254, "maxRSS": 395940, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105076, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8821, "involuntaryContextSwitches": 4237}}