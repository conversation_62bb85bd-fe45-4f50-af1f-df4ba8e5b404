{"timestamp": "2025-05-14T23:59:54.301Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 795889664, "heapTotal": 463892480, "heapUsed": 432323920, "external": 19425280, "arrayBuffers": 7680315}, "uptime": 16.76352809, "cpuUsage": {"user": 14843888, "system": 867322}, "resourceUsage": {"userCPUTime": 14843902, "systemCPUTime": 867323, "maxRSS": 777236, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 209160, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 248, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 17191, "involuntaryContextSwitches": 19616}}