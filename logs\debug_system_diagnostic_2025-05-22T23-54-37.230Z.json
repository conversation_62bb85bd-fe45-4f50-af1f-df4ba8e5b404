{"timestamp": "2025-05-22T23:54:37.229Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 286371840, "heapTotal": 113811456, "heapUsed": 74185536, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.181341723, "cpuUsage": {"user": 2951800, "system": 392235}, "resourceUsage": {"userCPUTime": 2951845, "systemCPUTime": 392241, "maxRSS": 280736, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102782, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7761, "involuntaryContextSwitches": 4722}}