{"timestamp": "2025-05-16T13:01:31.321Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 283422720, "heapTotal": 107782144, "heapUsed": 72847928, "external": 8219482, "arrayBuffers": 243725}, "uptime": 6.757458776, "cpuUsage": {"user": 2818389, "system": 400444}, "resourceUsage": {"userCPUTime": 2818448, "systemCPUTime": 400453, "maxRSS": 276780, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103777, "majorPageFault": 6, "swappedOut": 0, "fsRead": 53712, "fsWrite": 672, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8897, "involuntaryContextSwitches": 4901}}