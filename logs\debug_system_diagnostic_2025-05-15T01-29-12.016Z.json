{"timestamp": "2025-05-15T01:29:12.016Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 431325184, "heapTotal": 296845312, "heapUsed": 220141400, "external": 13191605, "arrayBuffers": 1446640}, "uptime": 35.530625853, "cpuUsage": {"user": 16508824, "system": 1021600}, "resourceUsage": {"userCPUTime": 16508838, "systemCPUTime": 1021601, "maxRSS": 849032, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 245554, "majorPageFault": 0, "swappedOut": 0, "fsRead": 32, "fsWrite": 312, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 18409, "involuntaryContextSwitches": 33985}}