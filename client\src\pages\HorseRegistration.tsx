import { useForm } from "react-hook-form";
import { useState } from "react";
import { useLocation } from "wouter";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CalendarIcon, ChevronLeft, Loader2, Search } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { usePelagens } from "@/hooks/use-pelagens";
import { useABCCCImport } from "@/hooks/use-abccc-import";
import { pino } from "pino";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format, isValid, parse } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  GenealogiaSelector,
  TipoEntradaGenealogica,
} from "./GenealogiaSelector";

// Logger
const logger = pino({ enabled: process.env.NODE_ENV !== "production" });

// Form validation schema (aligned with shared/schema.ts)
const horseSchema = z.object({
  nome: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  breed: z.string().min(1, "Por favor selecione uma raça"),
  birthDate: z
    .string()
    .min(1, "Data de nascimento é obrigatória")
    .transform((val) => parse(val, "dd/MM/yyyy", new Date()))
    .refine((val) => isValid(val), { message: "Data inválida" }),
  peso: z
    .number()
    .positive()
    .max(2000, "Peso deve ser até 2000kg")
    .optional()
    .nullable(),
  altura: z
    .number()
    .min(0.5)
    .max(2.5, "Altura deve ser entre 0.5m e 2.5m")
    .optional()
    .nullable(),
  sexo: z
    .enum(["Macho", "Fêmea", "Castrado", "Garanhão", "Égua", "nao_informado"])
    .optional(),
  pelagem: z.string().optional(),
  status: z
    .enum(["ativo", "vendido", "falecido", "doado", "emprestado"])
    .default("ativo"),
  dataEntrada: z
    .string()
    .optional()
    .transform((val) => (val ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  dataSaida: z
    .string()
    .optional()
    .transform((val) => (val ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  motivoSaida: z.string().optional(),
  pai: z
    .object({
      tipo: z.enum(["sistema", "externo", "nao_informado"]),
      cavaloSistemaId: z.string().nullable(),
      cavaloNome: z.string().nullable(),
    })
    .optional(),
  mae: z
    .object({
      tipo: z.enum(["sistema", "externo", "nao_informado"]),
      cavaloSistemaId: z.string().nullable(),
      cavaloNome: z.string().nullable(),
    })
    .optional(),
  numeroRegistro: z.string().optional(),
  origem: z.string().optional(),
  criador: z.string().optional(),
  proprietario: z.string().optional(),
  inspetor: z.string().optional(),
  valorCompra: z
    .number()
    .nonnegative("Valor deve ser positivo")
    .optional()
    .nullable(),
  dataCompra: z
    .string()
    .optional()
    .transform((val) => (val ? parse(val, "dd/MM/yyyy", new Date()) : null))
    .refine((val) => !val || isValid(val), { message: "Data inválida" }),
  observacoes: z.string().optional(),
  userId: z.number(),
});

type HorseFormData = z.infer<typeof horseSchema>;

const HorseRegistration = () => {
  const { userId } = useAuth();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const { pelagens, isLoading: isPelagensLoading } = usePelagens();
  const { importData, isLoading: isABCCCLoading } = useABCCCImport();
  const [loading, setLoading] = useState(false);
  const [isConfirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [formValues, setFormValues] = useState<HorseFormData | null>(null);
  const [cavalos, setCavalos] = useState<any[]>([]); // Fetch dynamically if needed

  const form = useForm<HorseFormData>({
    resolver: zodResolver(horseSchema),
    defaultValues: {
      nome: "",
      breed: "",
      birthDate: null,
      peso: null,
      altura: null,
      sexo: "nao_informado",
      pelagem: "nao_informado",
      status: "ativo",
      dataEntrada: null,
      dataSaida: null,
      motivoSaida: "",
      pai: { tipo: "nao_informado", cavaloSistemaId: null, cavaloNome: null },
      mae: { tipo: "nao_informado", cavaloSistemaId: null, cavaloNome: null },
      numeroRegistro: "",
      origem: "",
      criador: "",
      proprietario: "",
      inspetor: "",
      valorCompra: null,
      dataCompra: null,
      observacoes: "",
      userId,
    },
  });

  // Fetch authenticated API requests
  const fetchWithAuth = async (url: string, options: RequestInit = {}) => {
    if (!userId) {
      throw new Error("Usuário não autenticado");
    }

    const headers = new Headers(options.headers);
    headers.set("user-id", userId.toString());

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `Erro na requisição: ${response.status}`,
      );
    }

    return response.json();
  };

  // Handle ABCCC search
  const handleBuscarABCCC = async (registro: string) => {
    try {
      const data = await importData(registro);
      logger.info(`Dados ABCCC recebidos para registro ${registro}`);

      form.setValue("nome", data.nome);
      form.setValue("sexo", data.sexo === "M" ? "Macho" : "Fêmea");
      form.setValue(
        "birthDate",
        parse(data.nascimento, "dd/MM/yyyy", new Date()),
      );
      form.setValue("pelagem", data.pelagem);
      form.setValue("criador", data.criador);
      form.setValue("proprietario", data.proprietario);
      form.setValue("numeroRegistro", data.registro);
      form.setValue("pai", {
        tipo: "externo",
        cavaloSistemaId: null,
        cavaloNome: data.pai,
      });
      form.setValue("mae", {
        tipo: "externo",
        cavaloSistemaId: null,
        cavaloNome: data.mae,
      });
    } catch (error) {
      logger.error(`Erro ao buscar ABCCC: ${error.message}`);
      // Errors handled by useABCCCImport via toast
    }
  };

  const onSubmit = async (data: HorseFormData) => {
    try {
      logger.info("Processando dados do formulário", data);

      const valuesAjustados = {
        ...data,
        birthDate: data.birthDate ? format(data.birthDate, "yyyy-MM-dd") : null,
        dataEntrada: data.dataEntrada
          ? format(data.dataEntrada, "yyyy-MM-dd")
          : null,
        dataSaida: data.dataSaida ? format(data.dataSaida, "yyyy-MM-dd") : null,
        dataCompra: data.dataCompra
          ? format(data.dataCompra, "yyyy-MM-dd")
          : null,
        paiId:
          data.pai?.tipo === "sistema" && data.pai.cavaloSistemaId
            ? parseInt(data.pai.cavaloSistemaId)
            : null,
        paiNome: data.pai?.tipo === "externo" ? data.pai.cavaloNome : null,
        maeId:
          data.mae?.tipo === "sistema" && data.mae.cavaloSistemaId
            ? parseInt(data.mae.cavaloSistemaId)
            : null,
        maeNome: data.mae?.tipo === "externo" ? data.mae.cavaloNome : null,
      };

      delete valuesAjustados.pai;
      delete valuesAjustados.mae;

      setFormValues(valuesAjustados);
      setConfirmDialogOpen(true);
    } catch (error) {
      logger.error(`Erro ao processar formulário: ${error.message}`);
      toast({
        title: "Erro ao processar dados",
        description: "Não foi possível processar os dados do formulário.",
        variant: "destructive",
      });
    }
  };

  const confirmSubmit = async () => {
    if (!formValues) return;

    setLoading(true);
    try {
      const result = await fetchWithAuth("/api/cavalos", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formValues),
      });

      form.reset();
      setConfirmDialogOpen(false);
      toast({
        title: "Cavalo cadastrado",
        description: `${formValues.nome} foi adicionado ao seu estábulo!`,
      });
      navigate("/");
    } catch (error) {
      logger.error(`Erro ao salvar cavalo: ${error.message}`);
      toast({
        title: "Falha no cadastro",
        description: error.message || "Não foi possível cadastrar o cavalo.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/");
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={handleCancel}
          className="inline-flex items-center mb-4 text-gray-500 hover:text-gray-700"
          aria-label="Voltar para o dashboard"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Voltar para o Dashboard
        </Button>
        <h1 className="text-2xl font-bold text-gray-900">
          Cadastrar Novo Cavalo
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Adicione um novo cavalo ao seu estábulo.
        </p>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Required Fields */}
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <FormField
                  control={form.control}
                  name="nome"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Nome *</FormLabel>
                      <FormControl>
                        <Input placeholder="Nome do cavalo" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="breed"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Raça *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger aria-label="Selecionar raça">
                            <SelectValue placeholder="Selecione a raça" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Crioulo">Crioulo</SelectItem>
                          <SelectItem value="Quarto de Milha">
                            Quarto de Milha
                          </SelectItem>
                          <SelectItem value="Mangalarga">Mangalarga</SelectItem>
                          <SelectItem value="Mangalarga Marchador">
                            Mangalarga Marchador
                          </SelectItem>
                          <SelectItem value="Árabe">Árabe</SelectItem>
                          <SelectItem value="Paint Horse">
                            Paint Horse
                          </SelectItem>
                          <SelectItem value="Appaloosa">Appaloosa</SelectItem>
                          <SelectItem value="Campolina">Campolina</SelectItem>
                          <SelectItem value="Lusitano">Lusitano</SelectItem>
                          <SelectItem value="Pônei">Pônei</SelectItem>
                          <SelectItem value="Puro Sangue Inglês">
                            Puro Sangue Inglês
                          </SelectItem>
                          <SelectItem value="Outros">Outros</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="birthDate"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Data de Nascimento *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground",
                              )}
                              aria-label="Selecionar data de nascimento"
                            >
                              {field.value ? (
                                format(field.value, "dd/MM/yyyy", {
                                  locale: ptBR,
                                })
                              ) : (
                                <span>Selecione uma data</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date > new Date() || date < new Date("1970-01-01")
                            }
                            initialFocus
                            locale={ptBR}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Optional Fields in Tabs */}
              <div className="mt-8">
                <Tabs defaultValue="fisico" className="w-full" role="tablist">
                  <TabsList className="mb-4 grid grid-cols-5 w-full">
                    <TabsTrigger value="fisico" role="tab" aria-selected={true}>
                      Características Físicas
                    </TabsTrigger>
                    <TabsTrigger value="genealogia" role="tab">
                      Genealogia
                    </TabsTrigger>
                    <TabsTrigger value="rastreamento" role="tab">
                      Identificação
                    </TabsTrigger>
                    <TabsTrigger value="status" role="tab">
                      Status
                    </TabsTrigger>
                    <TabsTrigger value="observacoes" role="tab">
                      Observações
                    </TabsTrigger>
                  </TabsList>

                  {/* Physical Characteristics */}
                  <TabsContent value="fisico">
                    <Card>
                      <CardHeader>
                        <CardTitle>Características Físicas</CardTitle>
                        <CardDescription>
                          Informações sobre o porte e características físicas do
                          cavalo (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="peso"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Peso (kg)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.1"
                                    min="0"
                                    placeholder="Ex: 450.5"
                                    value={field.value ?? ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? parseFloat(e.target.value)
                                          : null,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="altura"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Altura (m)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    placeholder="Ex: 1.68"
                                    value={field.value ?? ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? parseFloat(e.target.value)
                                          : null,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="sexo"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Sexo</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger aria-label="Selecionar sexo">
                                      <SelectValue placeholder="Selecione o sexo" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="nao_informado">
                                      Não informado
                                    </SelectItem>
                                    <SelectItem value="Macho">Macho</SelectItem>
                                    <SelectItem value="Fêmea">Fêmea</SelectItem>
                                    <SelectItem value="Castrado">
                                      Castrado
                                    </SelectItem>
                                    <SelectItem value="Garanhão">
                                      Garanhão
                                    </SelectItem>
                                    <SelectItem value="Égua">Égua</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="pelagem"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Pelagem</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                  disabled={isPelagensLoading}
                                >
                                  <FormControl>
                                    <SelectTrigger aria-label="Selecionar pelagem">
                                      <SelectValue
                                        placeholder={
                                          isPelagensLoading
                                            ? "Carregando pelagens..."
                                            : "Selecione a pelagem"
                                        }
                                      />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="nao_informado">
                                      Não informado
                                    </SelectItem>
                                    {pelagens.map((pelagem) => (
                                      <SelectItem
                                        key={pelagem.id}
                                        value={pelagem.nome}
                                      >
                                        {pelagem.nome}
                                      </SelectItem>
                                    ))}
                                    {!isPelagensLoading &&
                                      pelagens.length === 0 && (
                                        <SelectItem value="outros" disabled>
                                          Nenhuma pelagem cadastrada
                                        </SelectItem>
                                      )}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Genealogy */}
                  <TabsContent value="genealogia">
                    <Card>
                      <CardHeader>
                        <CardTitle>Genealogia</CardTitle>
                        <CardDescription>
                          Informações sobre a linhagem e pedigree do cavalo
                          (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="pai"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Pai</FormLabel>
                                <FormControl>
                                  <GenealogiaSelector
                                    tipoEntrada={TipoEntradaGenealogica.Pai}
                                    valorInicial={field.value}
                                    cavalos={cavalos.filter((c) =>
                                      ["Macho", "Garanhão"].includes(c.sexo),
                                    )}
                                    onChange={field.onChange}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="mae"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Mãe</FormLabel>
                                <FormControl>
                                  <GenealogiaSelector
                                    tipoEntrada={TipoEntradaGenealogica.Mae}
                                    valorInicial={field.value}
                                    cavalos={cavalos.filter((c) =>
                                      ["Fêmea", "Égua"].includes(c.sexo),
                                    )}
                                    onChange={field.onChange}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Identification */}
                  <TabsContent value="rastreamento">
                    <Card>
                      <CardHeader>
                        <CardTitle>Identificação e Rastreamento</CardTitle>
                        <CardDescription>
                          Informações de registro, origem e identificação
                          oficial do cavalo (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="numeroRegistro"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>
                                  Número de Registro (ABCCC)
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Ex: B123456"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="origem"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Haras/Fazenda de Origem</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome do haras ou fazenda"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="criador"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Criador</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome do criador"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="proprietario"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Proprietário</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome do proprietário atual"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="inspetor"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Inspetor Técnico</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Inspetor técnico da ABCCC"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="valorCompra"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Valor de Compra (R$)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.01"
                                    placeholder="Valor em reais"
                                    value={field.value ?? ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? parseFloat(e.target.value)
                                          : null,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="dataCompra"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Data de Compra</FormLabel>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <FormControl>
                                      <Button
                                        variant="outline"
                                        className={cn(
                                          "w-full pl-3 text-left font-normal",
                                          !field.value &&
                                            "text-muted-foreground",
                                        )}
                                        aria-label="Selecionar data de compra"
                                      >
                                        {field.value ? (
                                          format(field.value, "dd/MM/yyyy", {
                                            locale: ptBR,
                                          })
                                        ) : (
                                          <span>Selecione uma data</span>
                                        )}
                                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                      </Button>
                                    </FormControl>
                                  </PopoverTrigger>
                                  <PopoverContent
                                    className="w-auto p-0"
                                    align="start"
                                  >
                                    <Calendar
                                      mode="single"
                                      selected={field.value}
                                      onSelect={field.onChange}
                                      disabled={(date) =>
                                        date > new Date() ||
                                        date < new Date("1970-01-01")
                                      }
                                      initialFocus
                                      locale={ptBR}
                                    />
                                  </PopoverContent>
                                </Popover>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Status */}
                  <TabsContent value="status">
                    <Card>
                      <CardHeader>
                        <CardTitle>Status</CardTitle>
                        <CardDescription>
                          Situação atual do cavalo no plantel (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="status"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Status</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger aria-label="Selecionar status">
                                      <SelectValue placeholder="Selecione o status" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="ativo">Ativo</SelectItem>
                                    <SelectItem value="vendido">
                                      Vendido
                                    </SelectItem>
                                    <SelectItem value="falecido">
                                      Falecido
                                    </SelectItem>
                                    <SelectItem value="doado">Doado</SelectItem>
                                    <SelectItem value="emprestado">
                                      Emprestado
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="dataEntrada"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Data de Entrada</FormLabel>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <FormControl>
                                      <Button
                                        variant="outline"
                                        className={cn(
                                          "w-full pl-3 text-left font-normal",
                                          !field.value &&
                                            "text-muted-foreground",
                                        )}
                                        aria-label="Selecionar data de entrada"
                                      >
                                        {field.value ? (
                                          format(field.value, "dd/MM/yyyy", {
                                            locale: ptBR,
                                          })
                                        ) : (
                                          <span>Selecione uma data</span>
                                        )}
                                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                      </Button>
                                    </FormControl>
                                  </PopoverTrigger>
                                  <PopoverContent
                                    className="w-auto p-0"
                                    align="start"
                                  >
                                    <Calendar
                                      mode="single"
                                      selected={field.value}
                                      onSelect={field.onChange}
                                      disabled={(date) =>
                                        date > new Date() ||
                                        date < new Date("1970-01-01")
                                      }
                                      initialFocus
                                      locale={ptBR}
                                    />
                                  </PopoverContent>
                                </Popover>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="dataSaida"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Data de Saída</FormLabel>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <FormControl>
                                      <Button
                                        variant="outline"
                                        className={cn(
                                          "w-full pl-3 text-left font-normal",
                                          !field.value &&
                                            "text-muted-foreground",
                                        )}
                                        aria-label="Selecionar data de saída"
                                      >
                                        {field.value ? (
                                          format(field.value, "dd/MM/yyyy", {
                                            locale: ptBR,
                                          })
                                        ) : (
                                          <span>Selecione uma data</span>
                                        )}
                                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                      </Button>
                                    </FormControl>
                                  </PopoverTrigger>
                                  <PopoverContent
                                    className="w-auto p-0"
                                    align="start"
                                  >
                                    <Calendar
                                      mode="single"
                                      selected={field.value}
                                      onSelect={field.onChange}
                                      disabled={(date) =>
                                        date > new Date() ||
                                        date < new Date("1970-01-01")
                                      }
                                      initialFocus
                                      locale={ptBR}
                                    />
                                  </PopoverContent>
                                </Popover>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="motivoSaida"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-6">
                                <FormLabel>Motivo da Saída</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Explique o motivo da saída, se aplicável"
                                    rows={2}
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Observations */}
                  <TabsContent value="observacoes">
                    <Card>
                      <CardHeader>
                        <CardTitle>Observações</CardTitle>
                        <CardDescription>
                          Informações adicionais, histórico ou particularidades
                          (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <FormField
                          control={form.control}
                          name="observacoes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Observações Gerais</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Informações adicionais sobre o cavalo, como histórico médico, comportamento, necessidades especiais, etc."
                                  rows={5}
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>

              <div className="pt-5 border-t border-gray-200">
                <div className="flex justify-between">
                  <p className="text-sm text-gray-500">* Campos obrigatórios</p>
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      className="mr-3"
                      aria-label="Cancelar cadastro"
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      disabled={loading}
                      aria-label="Cadastrar cavalo"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Cadastrando...
                        </>
                      ) : (
                        "Cadastrar Cavalo"
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Cadastro</DialogTitle>
            <DialogDescription>
              Por favor, confirme os dados do cavalo antes de salvar:
              <ul className="mt-2 space-y-1">
                <li>
                  <strong>Nome:</strong> {formValues?.nome}
                </li>
                <li>
                  <strong>Raça:</strong> {formValues?.breed}
                </li>
                <li>
                  <strong>Registro:</strong>{" "}
                  {formValues?.numeroRegistro || "Não informado"}
                </li>
                <li>
                  <strong>Pai:</strong>{" "}
                  {formValues?.paiNome || formValues?.paiId || "Não informado"}
                </li>
                <li>
                  <strong>Mãe:</strong>{" "}
                  {formValues?.maeNome || formValues?.maeId || "Não informado"}
                </li>
              </ul>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button onClick={confirmSubmit}>Confirmar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default HorseRegistration;
