import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Trash2, Edit, PlusCircle, Search, CheckCircle, AlertCircle, 
  Calendar as CalendarIcon, FileText, Stethoscope 
} from 'lucide-react';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useLocation } from 'wouter';

interface ProcedimentoVeterinario {
  id: number;
  data: string;
  tipo: string;
  descricao: string;
  veterinario: string;
  diagnostico?: string;
  tratamento?: string;
  medicamentos?: string;
  observacoes?: string;
  horseId?: number;
  manejoId?: number;
  eventoId?: number;
  userId: number;
  createdAt: string;
  cavaloNome?: string;
}

interface Cavalo {
  id: number;
  name: string;
  breed: string;
}

// Esquema Zod para validação do formulário
const procedimentoSchema = z.object({
  data: z.string().nonempty('Data é obrigatória'),
  tipo: z.string().nonempty('Tipo é obrigatório'),
  descricao: z.string().nonempty('Descrição é obrigatória'),
  veterinario: z.string().nonempty('Nome do veterinário é obrigatório'),
  diagnostico: z.string().optional(),
  tratamento: z.string().optional(),
  medicamentos: z.string().optional(),
  observacoes: z.string().optional(),
  horseId: z.number().nullable().optional(),
  manejoId: z.number().nullable().optional(),
  eventoId: z.number().nullable().optional(),
});

type ProcedimentoFormData = z.infer<typeof procedimentoSchema>;

function ProcedimentosVeterinarios() {
  const { toast } = useToast();
  const [user, setUser] = useState<any>(null);
  
  // Obter usuário do localStorage
  useEffect(() => {
    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
    } catch (error) {
      console.error("Erro ao obter usuário:", error);
    }
  }, []);
  const [_, navigate] = useLocation();
  const [procedimentos, setProcedimentos] = useState<ProcedimentoVeterinario[]>([]);
  const [cavalos, setCavalos] = useState<Cavalo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [openModal, setOpenModal] = useState(false);
  const [editingProcedimento, setEditingProcedimento] = useState<ProcedimentoVeterinario | null>(null);
  const [formData, setFormData] = useState<ProcedimentoFormData>({
    data: format(new Date(), 'yyyy-MM-dd'),
    tipo: '',
    descricao: '',
    veterinario: '',
    diagnostico: '',
    tratamento: '',
    medicamentos: '',
    observacoes: '',
    horseId: null,
  });
  const [filtro, setFiltro] = useState('');
  const [cavaloFiltro, setCavaloFiltro] = useState<number | null>(null);

  useEffect(() => {
    if (!user) return;
    
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const headers = {
          'Content-Type': 'application/json',
          'user-id': user.id.toString()
        };
        
        // Buscar cavalos
        const cavaloRes = await fetch('/api/cavalos', { headers });
        if (cavaloRes.ok) {
          const cavalosData = await cavaloRes.json();
          setCavalos(cavalosData);
        }
        
        // Buscar procedimentos veterinários
        const procRes = await fetch('/api/procedimentos-vet', { headers });
        if (procRes.ok) {
          const procedimentosData = await procRes.json();
          
          // Adicionar nomes dos cavalos aos procedimentos
          const procedimentosComNomes = await Promise.all(
            procedimentosData.map(async (proc: ProcedimentoVeterinario) => {
              if (proc.horseId) {
                const cavalo = cavalos.find((c: Cavalo) => c.id === proc.horseId);
                return { ...proc, cavaloNome: cavalo ? cavalo.name : 'Cavalo não encontrado' };
              }
              return proc;
            })
          );
          
          setProcedimentos(procedimentosComNomes);
        }
      } catch (error) {
        console.error('Erro ao buscar dados:', error);
        toast({
          variant: 'destructive',
          title: 'Erro',
          description: 'Não foi possível carregar os procedimentos veterinários.',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [user, toast]);
  
  const resetForm = () => {
    setFormData({
      data: format(new Date(), 'yyyy-MM-dd'),
      tipo: '',
      descricao: '',
      veterinario: '',
      diagnostico: '',
      tratamento: '',
      medicamentos: '',
      observacoes: '',
      horseId: null,
    });
    setEditingProcedimento(null);
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSelectChange = (name: string, value: string) => {
    if (name === 'horseId') {
      setFormData(prev => ({ 
        ...prev, 
        [name]: value && value !== 'none' ? parseInt(value) : null 
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast({
        variant: 'destructive',
        title: 'Erro',
        description: 'Você precisa estar logado para realizar esta ação.',
      });
      return;
    }
    
    try {
      // Validar dados do formulário
      procedimentoSchema.parse(formData);
      
      const headers = {
        'Content-Type': 'application/json',
        'user-id': user.id.toString()
      };
      
      const procedimentoData = {
        ...formData,
        userId: user.id,
      };
      
      let response;
      
      if (editingProcedimento) {
        // Atualizar procedimento existente
        response = await fetch(`/api/procedimentos-vet/${editingProcedimento.id}`, {
          method: 'PUT',
          headers,
          body: JSON.stringify(procedimentoData),
        });
      } else {
        // Criar novo procedimento
        response = await fetch('/api/procedimentos-vet', {
          method: 'POST',
          headers,
          body: JSON.stringify(procedimentoData),
        });
      }
      
      if (response.ok) {
        const novoOuAtualizado = await response.json();
        
        // Atualizar lista de procedimentos
        if (editingProcedimento) {
          setProcedimentos(prev => 
            prev.map(proc => proc.id === editingProcedimento.id ? 
              { ...novoOuAtualizado, cavaloNome: novoOuAtualizado.horseId ? 
                cavalos.find(c => c.id === novoOuAtualizado.horseId)?.name || 'Cavalo não encontrado' : undefined 
              } : proc)
          );
          toast({
            title: 'Procedimento atualizado',
            description: 'Procedimento veterinário atualizado com sucesso.',
          });
        } else {
          setProcedimentos(prev => [
            ...prev, 
            { 
              ...novoOuAtualizado, 
              cavaloNome: novoOuAtualizado.horseId ? 
                cavalos.find(c => c.id === novoOuAtualizado.horseId)?.name || 'Cavalo não encontrado' : undefined 
            }
          ]);
          toast({
            title: 'Procedimento adicionado',
            description: 'Novo procedimento veterinário adicionado com sucesso.',
          });
        }
        
        // Resetar formulário e fechar modal
        resetForm();
        setOpenModal(false);
      } else {
        const erro = await response.json();
        throw new Error(erro.message || 'Erro ao salvar procedimento');
      }
    } catch (error) {
      console.error('Erro ao salvar procedimento:', error);
      
      if (error instanceof z.ZodError) {
        // Erros de validação do formulário
        const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
        toast({
          variant: 'destructive',
          title: 'Dados inválidos',
          description: errorMessages,
        });
      } else {
        toast({
          variant: 'destructive',
          title: 'Erro',
          description: error instanceof Error ? error.message : 'Erro ao salvar procedimento veterinário',
        });
      }
    }
  };
  
  const handleEdit = (procedimento: ProcedimentoVeterinario) => {
    setEditingProcedimento(procedimento);
    setFormData({
      data: procedimento.data,
      tipo: procedimento.tipo,
      descricao: procedimento.descricao,
      veterinario: procedimento.veterinario,
      diagnostico: procedimento.diagnostico || '',
      tratamento: procedimento.tratamento || '',
      medicamentos: procedimento.medicamentos || '',
      observacoes: procedimento.observacoes || '',
      horseId: procedimento.horseId || null,
      manejoId: procedimento.manejoId || null,
      eventoId: procedimento.eventoId || null,
    });
    setOpenModal(true);
  };
  
  const handleDelete = async (id: number) => {
    if (!user) return;
    
    if (confirm('Tem certeza que deseja excluir este procedimento veterinário?')) {
      try {
        const headers = {
          'Content-Type': 'application/json',
          'user-id': user.id.toString()
        };
        
        const response = await fetch(`/api/procedimentos-vet/${id}`, {
          method: 'DELETE',
          headers,
        });
        
        if (response.ok) {
          setProcedimentos(prev => prev.filter(proc => proc.id !== id));
          toast({
            title: 'Procedimento excluído',
            description: 'Procedimento veterinário excluído com sucesso.',
          });
        } else {
          const erro = await response.json();
          throw new Error(erro.message || 'Erro ao excluir procedimento');
        }
      } catch (error) {
        console.error('Erro ao excluir procedimento:', error);
        toast({
          variant: 'destructive',
          title: 'Erro',
          description: error instanceof Error ? error.message : 'Erro ao excluir procedimento veterinário',
        });
      }
    }
  };
  
  const handleVerDetalhes = (id: number) => {
    navigate(`/procedimentos-vet/${id}`);
  };
  
  const procedimentosFiltrados = procedimentos
    .filter(proc => 
      (filtro === '' || 
        proc.descricao.toLowerCase().includes(filtro.toLowerCase()) ||
        proc.veterinario.toLowerCase().includes(filtro.toLowerCase()) ||
        proc.tipo.toLowerCase().includes(filtro.toLowerCase()) ||
        (proc.cavaloNome && proc.cavaloNome.toLowerCase().includes(filtro.toLowerCase()))
      ) &&
      (cavaloFiltro === null || cavaloFiltro === 0 || cavaloFiltro === undefined || proc.horseId === cavaloFiltro)
    )
    .sort((a, b) => new Date(b.data).getTime() - new Date(a.data).getTime());
  
  // Renderizar lista de procedimentos veterinários
  const renderProcedimentos = () => {
    if (isLoading) {
      return <div className="flex justify-center items-center h-40">Carregando procedimentos veterinários...</div>;
    }
    
    if (procedimentosFiltrados.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-40 gap-2">
          <p className="text-muted-foreground">Nenhum procedimento veterinário encontrado.</p>
          <Button onClick={() => setOpenModal(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Adicionar Procedimento
          </Button>
        </div>
      );
    }
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {procedimentosFiltrados.map(procedimento => (
          <Card key={procedimento.id} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg font-medium">{procedimento.tipo}</CardTitle>
                  <CardDescription className="text-xs">
                    {format(new Date(procedimento.data), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button variant="ghost" size="icon" onClick={() => handleEdit(procedimento)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" onClick={() => handleDelete(procedimento.id)}>
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
              </div>
              {procedimento.cavaloNome && (
                <Badge variant="outline" className="mt-1">
                  {procedimento.cavaloNome}
                </Badge>
              )}
            </CardHeader>
            <CardContent>
              <p className="text-sm mb-2 font-semibold">{procedimento.descricao}</p>
              <p className="text-xs mb-2">Veterinário: {procedimento.veterinario}</p>
              {procedimento.diagnostico && (
                <p className="text-xs truncate">Diagnóstico: {procedimento.diagnostico}</p>
              )}
              <div className="mt-4">
                <Button variant="outline" size="sm" onClick={() => handleVerDetalhes(procedimento.id)}>
                  Ver detalhes
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Procedimentos Veterinários</h1>
          <p className="text-muted-foreground">
            Gerencie os procedimentos e consultas veterinárias de seus cavalos.
          </p>
        </div>
        <Button onClick={() => setOpenModal(true)}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Novo Procedimento
        </Button>
      </div>
      
      <Separator />
      
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Filtrar por descrição, veterinário, tipo..."
            className="pl-8"
            value={filtro}
            onChange={(e) => setFiltro(e.target.value)}
          />
        </div>
        
        <Select value={cavaloFiltro?.toString() || 'all'} onValueChange={(value) => setCavaloFiltro(value === 'all' ? null : parseInt(value))}>
          <SelectTrigger className="w-full sm:w-[200px]">
            <SelectValue placeholder="Filtrar por cavalo" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos os cavalos</SelectItem>
            {cavalos.map(cavalo => (
              <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                {cavalo.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="mt-6">
        {renderProcedimentos()}
      </div>
      
      {/* Modal para adicionar/editar procedimento veterinário */}
      <Dialog open={openModal} onOpenChange={setOpenModal}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{editingProcedimento ? 'Editar Procedimento Veterinário' : 'Novo Procedimento Veterinário'}</DialogTitle>
            <DialogDescription>
              Preencha os dados do procedimento veterinário abaixo.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="data">Data *</Label>
                <Input
                  id="data"
                  name="data"
                  type="date"
                  value={formData.data}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="tipo">Tipo de Procedimento *</Label>
                <Select 
                  value={formData.tipo} 
                  onValueChange={(value) => handleSelectChange('tipo', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="consulta">Consulta de Rotina</SelectItem>
                    <SelectItem value="vacina">Vacinação</SelectItem>
                    <SelectItem value="cirurgia">Cirurgia</SelectItem>
                    <SelectItem value="exame">Exame Específico</SelectItem>
                    <SelectItem value="tratamento">Tratamento</SelectItem>
                    <SelectItem value="emergencia">Emergência</SelectItem>
                    <SelectItem value="outro">Outro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="descricao">Descrição *</Label>
                <Input
                  id="descricao"
                  name="descricao"
                  value={formData.descricao}
                  onChange={handleChange}
                  placeholder="Breve descrição do procedimento"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="veterinario">Veterinário *</Label>
                <Input
                  id="veterinario"
                  name="veterinario"
                  value={formData.veterinario}
                  onChange={handleChange}
                  placeholder="Nome do veterinário responsável"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="horseId">Cavalo</Label>
                <Select 
                  value={formData.horseId?.toString() || ''} 
                  onValueChange={(value) => handleSelectChange('horseId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um cavalo (opcional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Nenhum</SelectItem>
                    {cavalos.map(cavalo => (
                      <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                        {cavalo.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="diagnostico">Diagnóstico</Label>
                <Input
                  id="diagnostico"
                  name="diagnostico"
                  value={formData.diagnostico}
                  onChange={handleChange}
                  placeholder="Diagnóstico (opcional)"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="tratamento">Tratamento Recomendado</Label>
              <Textarea
                id="tratamento"
                name="tratamento"
                value={formData.tratamento}
                onChange={handleChange}
                placeholder="Detalhes do tratamento recomendado (opcional)"
                rows={2}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="medicamentos">Medicamentos</Label>
              <Textarea
                id="medicamentos"
                name="medicamentos"
                value={formData.medicamentos}
                onChange={handleChange}
                placeholder="Lista de medicamentos prescritos (opcional)"
                rows={2}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="observacoes">Observações</Label>
              <Textarea
                id="observacoes"
                name="observacoes"
                value={formData.observacoes}
                onChange={handleChange}
                placeholder="Observações adicionais (opcional)"
                rows={2}
              />
            </div>
            
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline" onClick={resetForm}>Cancelar</Button>
              </DialogClose>
              <Button type="submit">{editingProcedimento ? 'Atualizar' : 'Adicionar'}</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default ProcedimentosVeterinarios;