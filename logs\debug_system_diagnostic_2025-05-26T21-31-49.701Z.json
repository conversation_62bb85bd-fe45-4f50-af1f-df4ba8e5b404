{"timestamp": "2025-05-26T21:31:49.701Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402194432, "heapTotal": 114601984, "heapUsed": 90910248, "external": 8507741, "arrayBuffers": 273850}, "uptime": 2.159665182, "cpuUsage": {"user": 3012593, "system": 432829}, "resourceUsage": {"userCPUTime": 3012642, "systemCPUTime": 432835, "maxRSS": 392768, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105052, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9404, "involuntaryContextSwitches": 5037}}