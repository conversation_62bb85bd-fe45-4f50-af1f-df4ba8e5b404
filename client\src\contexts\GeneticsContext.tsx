import { createContext, useContext, useState, ReactNode } from 'react';

interface GeneticsContextType {
  selectedHorseId: number | null;
  setSelectedHorseId: (horseId: number | null) => void;
}

const GeneticsContext = createContext<GeneticsContextType | undefined>(undefined);

export function GeneticsProvider({ children }: { children: ReactNode }) {
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(null);

  return (
    <GeneticsContext.Provider value={{ selectedHorseId, setSelectedHorseId }}>
      {children}
    </GeneticsContext.Provider>
  );
}

export function useGeneticsContext() {
  const context = useContext(GeneticsContext);
  if (context === undefined) {
    throw new Error('useGeneticsContext must be used within a GeneticsProvider');
  }
  return context;
}