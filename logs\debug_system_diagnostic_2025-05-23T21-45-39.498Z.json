{"timestamp": "2025-05-23T21:45:39.498Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 302813184, "heapTotal": 132657152, "heapUsed": 106151544, "external": 8691554, "arrayBuffers": 610523}, "uptime": 5.420712093, "cpuUsage": {"user": 3689789, "system": 492000}, "resourceUsage": {"userCPUTime": 3689796, "systemCPUTime": 492001, "maxRSS": 295716, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 113462, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 56, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 10244, "involuntaryContextSwitches": 11417}}