{"timestamp": "2025-05-15T01:03:08.036Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 383098880, "heapTotal": 101322752, "heapUsed": 62051328, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.305133456, "cpuUsage": {"user": 2081759, "system": 270217}, "resourceUsage": {"userCPUTime": 2081812, "systemCPUTime": 270224, "maxRSS": 374120, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 98386, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5980, "involuntaryContextSwitches": 1194}}