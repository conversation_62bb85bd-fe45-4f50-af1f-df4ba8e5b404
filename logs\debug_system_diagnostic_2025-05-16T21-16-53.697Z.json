{"timestamp": "2025-05-16T21:16:53.697Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 326201344, "heapTotal": 129736704, "heapUsed": 110621280, "external": 8315040, "arrayBuffers": 282042}, "uptime": 1.69631915, "cpuUsage": {"user": 2604731, "system": 337607}, "resourceUsage": {"userCPUTime": 2604784, "systemCPUTime": 337607, "maxRSS": 318556, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105474, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 40, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8716, "involuntaryContextSwitches": 1707}}