{"timestamp": "2025-05-15T23:50:49.181Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402071552, "heapTotal": 114335744, "heapUsed": 72492400, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.287246137, "cpuUsage": {"user": 2953904, "system": 373225}, "resourceUsage": {"userCPUTime": 2953943, "systemCPUTime": 373230, "maxRSS": 392648, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103582, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7693, "involuntaryContextSwitches": 4396}}