{"timestamp": "2025-05-26T21:29:47.966Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 394407936, "heapTotal": 111976448, "heapUsed": 75343768, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.545703444, "cpuUsage": {"user": 3212891, "system": 423349}, "resourceUsage": {"userCPUTime": 3212959, "systemCPUTime": 423358, "maxRSS": 385164, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105137, "majorPageFault": 1, "swappedOut": 0, "fsRead": 0, "fsWrite": 40, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8852, "involuntaryContextSwitches": 9560}}