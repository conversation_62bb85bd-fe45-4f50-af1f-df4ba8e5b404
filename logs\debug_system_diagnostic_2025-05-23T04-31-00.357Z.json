{"timestamp": "2025-05-23T04:31:00.357Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403218432, "heapTotal": 111603712, "heapUsed": 84667096, "external": 8620500, "arrayBuffers": 235533}, "uptime": 2.502702523, "cpuUsage": {"user": 2639445, "system": 387649}, "resourceUsage": {"userCPUTime": 2639483, "systemCPUTime": 387655, "maxRSS": 393768, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104048, "majorPageFault": 1, "swappedOut": 0, "fsRead": 31408, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7655, "involuntaryContextSwitches": 3323}}