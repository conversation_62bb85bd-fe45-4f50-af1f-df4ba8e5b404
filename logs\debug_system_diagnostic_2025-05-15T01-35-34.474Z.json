{"timestamp": "2025-05-15T01:35:34.474Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 377577472, "heapTotal": 91623424, "heapUsed": 67742360, "external": 6861143, "arrayBuffers": 90610}, "uptime": 1.566538995, "cpuUsage": {"user": 2244916, "system": 316147}, "resourceUsage": {"userCPUTime": 2244972, "systemCPUTime": 316155, "maxRSS": 368728, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 97397, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6123, "involuntaryContextSwitches": 3735}}