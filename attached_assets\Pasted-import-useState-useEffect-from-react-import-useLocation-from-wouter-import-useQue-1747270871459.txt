import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { <PERSON><PERSON><PERSON> } from '@shared/schema';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ChevronLeft } from 'lucide-react';

// 1. Schema do formulário
const horseSchema = z.object({
  name: z.string().min(2, "Nome deve ter no mínimo 2 caracteres"),
  breed: z.string().min(1, "Selecione uma raça"),
  birthDate: z.string().min(1, "Data de nascimento é obrigatória"),
  peso: z.string().optional(),
  altura: z.string().optional(),
  sexo: z.string().optional(),
  status: z.string().optional(),
  dataEntrada: z.string().optional(),
  dataSaida: z.string().optional(),
  motivoSaida: z.string().optional(),
  paiId: z.string().optional(),    // Sempre trabalhar com IDs no form
  maeId: z.string().optional(),
  avoPaterno: z.string().optional(),
  avoMaterno: z.string().optional(),
  notes: z.string().optional(),
  cor: z.string().optional(),
});

type HorseFormData = z.infer<typeof horseSchema>;

const EditHorsePage = () => {
  const [user, setUser] = useState(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch {
        return null;
      }
    }
    return null;
  });

  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  // ID do cavalo na URL: /cavalo/123/editar
  const location = useLocation()[0];
  const horseId = location.split('/')[2];

  // Buscar cavalo atual
  const { data: horse, isLoading, error } = useQuery({
    queryKey: [`/api/cavalos/${horseId}`],
    enabled: !!horseId && !!user,
    queryFn: async () => {
      try {
        return await apiRequest<Cavalo>('GET', `/api/cavalos/${horseId}`);
      } catch (error) {
        toast({
          title: "Erro",
          description: "Não foi possível carregar os dados do cavalo",
          variant: "destructive",
        });
        return null;
      }
    }
  });

  // Buscar todos cavalos para montar os selects de pai/mãe
  const { data: cavalosLista } = useQuery({
    queryKey: ['/api/cavalos'],
    enabled: !!user,
    queryFn: async () => {
      try {
        return await apiRequest<Cavalo[]>('GET', '/api/cavalos');
      } catch {
        return [];
      }
    }
  });

  // Formulário controlado
  const form = useForm<HorseFormData>({
    resolver: zodResolver(horseSchema),
    defaultValues: {
      name: "",
      breed: "",
      birthDate: "",
      peso: "",
      altura: "",
      sexo: "",
      status: "ativo",
      dataEntrada: "",
      dataSaida: "",
      motivoSaida: "",
      paiId: "",
      maeId: "",
      avoPaterno: "",
      avoMaterno: "",
      notes: "",
      cor: "",
    },
  });

  // Preencher formulário com dados do cavalo (ID do pai/mãe!)
  useEffect(() => {
    if (horse) {
      form.reset({
        name: horse.name || "",
        breed: horse.breed || "",
        birthDate: horse.birthDate || "",
        peso: horse.peso ? horse.peso.toString() : "",
        altura: horse.altura ? horse.altura.toString() : "",
        sexo: horse.sexo || "",
        status: horse.status || "ativo",
        dataEntrada: horse.dataEntrada || "",
        dataSaida: horse.dataSaida || "",
        motivoSaida: horse.motivoSaida || "",
        paiId: horse.pai || "", // <- Pega o ID do pai
        maeId: horse.mae || "", // <- Pega o ID da mãe
        avoPaterno: horse.avoPaterno || "",
        avoMaterno: horse.avoMaterno || "",
        notes: horse.notes || "",
        cor: horse.cor || "",
      });
    }
  }, [horse, form]);

  // 2. Submit: Envia só os campos no padrão correto!
  const onSubmit = async (data: HorseFormData) => {
    if (!user) return;
    setLoading(true);

    try {
      const updatePayload = {
        name: data.name,
        breed: data.breed,
        birthDate: data.birthDate,
        peso: data.peso ? parseFloat(data.peso) : null,
        altura: data.altura ? parseFloat(data.altura) : null,
        sexo: data.sexo || null,
        cor: data.cor || null,
        status: data.status || "ativo",
        dataEntrada: data.dataEntrada || null,
        dataSaida: data.dataSaida || null,
        motivoSaida: data.motivoSaida || null,
        pai: data.paiId || null,  // <- Envia ID do pai!
        mae: data.maeId || null,  // <- Envia ID da mãe!
        avoPaterno: data.avoPaterno || null,
        avoMaterno: data.avoMaterno || null,
        notes: data.notes || null,
      };
      await apiRequest('PUT', `/api/cavalos/${horseId}`, updatePayload);

      toast({
        title: "Cavalo atualizado",
        description: `${data.name} foi atualizado com sucesso!`,
      });

      navigate(`/cavalo/${horseId}`);
    } catch (error: any) {
      toast({
        title: "Falha na atualização",
        description: error.message || "Não foi possível atualizar o cavalo",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(`/cavalo/${horseId}`);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (error || !horse) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div>
              <h3 className="text-red-800 font-medium">Erro ao carregar</h3>
              <p className="text-red-700 mt-1">
                Não foi possível carregar os dados do cavalo. Por favor, tente novamente.
              </p>
              <Button 
                variant="outline" 
                onClick={() => navigate('/')}
                className="mt-4"
              >
                Voltar para o Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="mb-6">
        <button onClick={handleCancel} className="inline-flex items-center mb-4 text-gray-500 hover:text-gray-700">
          <ChevronLeft className="h-4 w-4 mr-1" />
          Voltar para detalhes do cavalo
        </button>
        <h1 className="text-2xl font-bold text-gray-900">Editar Cavalo: {horse.name}</h1>
        <p className="mt-1 text-sm text-gray-500">Atualize as informações do cavalo.</p>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Campos obrigatórios */}
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                {/* ...demais campos... */}
              </div>

              {/* Abas */}
              <div className="mt-8">
                <Tabs defaultValue="fisico" className="w-full">
                  <TabsList className="mb-4 grid grid-cols-4 w-full">
                    <TabsTrigger value="fisico">Características Físicas</TabsTrigger>
                    <TabsTrigger value="genealogia">Genealogia</TabsTrigger>
                    <TabsTrigger value="status">Status</TabsTrigger>
                    <TabsTrigger value="observacoes">Observações</TabsTrigger>
                  </TabsList>

                  {/* Genealogia */}
                  <TabsContent value="genealogia">
                    <Card>
                      <CardHeader>
                        <CardTitle>Genealogia</CardTitle>
                        <CardDescription>
                          Informações sobre a linhagem e pedigree do cavalo.
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="paiId"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Pai</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione o pai" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="">Nenhum</SelectItem>
                                    {cavalosLista &&
                                      cavalosLista
                                        .filter(cav => cav.id !== horse.id)
                                        .map(cav => (
                                          <SelectItem key={cav.id} value={cav.id}>
                                            {cav.name}
                                          </SelectItem>
                                        ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="maeId"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Mãe</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione a mãe" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="">Nenhuma</SelectItem>
                                    {cavalosLista &&
                                      cavalosLista
                                        .filter(cav => cav.id !== horse.id)
                                        .map(cav => (
                                          <SelectItem key={cav.id} value={cav.id}>
                                            {cav.name}
                                          </SelectItem>
                                        ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          {/* Outros campos genealogia... */}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                  {/* ...demais abas... */}
                </Tabs>
              </div>

              <div className="pt-5 border-t border-gray-200">
                <div className="flex justify-between">
                  <p className="text-sm text-gray-500">* Campos obrigatórios</p>
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      className="mr-3"
                    >
                      Cancelar
                    </Button>
                    <Button 
                      type="submit" 
                      disabled={loading}
                    >
                      {loading ? "Salvando..." : "Salvar Alterações"}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default EditHorsePage;
