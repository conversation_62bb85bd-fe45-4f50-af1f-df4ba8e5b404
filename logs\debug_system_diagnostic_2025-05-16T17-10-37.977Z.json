{"timestamp": "2025-05-16T17:10:37.976Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404381696, "heapTotal": 117219328, "heapUsed": 72565120, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.75829635, "cpuUsage": {"user": 2773826, "system": 320666}, "resourceUsage": {"userCPUTime": 2773890, "systemCPUTime": 320666, "maxRSS": 394904, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104210, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 136, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7890, "involuntaryContextSwitches": 1652}}