{"timestamp": "2025-05-16T00:23:13.552Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 403881984, "heapTotal": 113041408, "heapUsed": 88021032, "external": 8282731, "arrayBuffers": 257466}, "uptime": 2.761765772, "cpuUsage": {"user": 3105772, "system": 396010}, "resourceUsage": {"userCPUTime": 3105835, "systemCPUTime": 396018, "maxRSS": 394416, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104430, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 80, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8079, "involuntaryContextSwitches": 10291}}