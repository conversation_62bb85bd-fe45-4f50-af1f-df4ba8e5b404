{"timestamp": "2025-05-15T18:13:54.387Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 330399744, "heapTotal": 192077824, "heapUsed": 169941944, "external": 14617674, "arrayBuffers": 1651212}, "uptime": 74.301527488, "cpuUsage": {"user": 16713195, "system": 1037292}, "resourceUsage": {"userCPUTime": 16713200, "systemCPUTime": 1037292, "maxRSS": 898956, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 262860, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 704, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 24362, "involuntaryContextSwitches": 12519}}