import React, { useState } from 'react';
import { 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormMessage 
} from "@/components/ui/form";
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Control } from "react-hook-form";
// Definindo o tipo Cavalo localmente para evitar problemas de importação
interface Cavalo {
  id: number;
  name: string;
  sexo?: string;
  [key: string]: any;
}

interface GenealogyFieldProps {
  control: Control<any>;
  name: string;
  label: string;
  cavalos: Cavalo[];
  gender: 'Macho' | 'Fêmea';
}

const GenealogyField: React.FC<GenealogyFieldProps> = ({ 
  control, 
  name, 
  label, 
  cavalos, 
  gender 
}) => {
  const [method, setMethod] = useState<'select' | 'input'>('select');
  const filteredCavalos = cavalos.filter(cavalo => cavalo.sexo === gender);

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          
          <Tabs 
            defaultValue="select" 
            onValueChange={(v) => setMethod(v as 'select' | 'input')}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="select">Selecionar do Sistema</TabsTrigger>
              <TabsTrigger value="input">Cavalo Externo</TabsTrigger>
            </TabsList>
            
            <TabsContent value="select">
              <FormControl>
                <Select
                  onValueChange={(value) => {
                    // Se for "nao_informado", limpa o campo
                    if (value === 'nao_informado') {
                      field.onChange({
                        id: null,
                        name: null,
                        type: 'none'
                      });
                    } else {
                      // Selecionar um cavalo do sistema
                      const selectedCavalo = cavalos.find(c => c.id.toString() === value);
                      if (selectedCavalo) {
                        field.onChange({
                          id: selectedCavalo.id,
                          name: selectedCavalo.name,
                          type: 'internal'
                        });
                      }
                    }
                  }}
                  value={field.value?.type === 'internal' ? field.value.id?.toString() : 'nao_informado'}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={`Selecione ${gender === 'Macho' ? 'o pai' : 'a mãe'}`} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="nao_informado">Não informado</SelectItem>
                    {filteredCavalos.map((cavalo) => (
                      <SelectItem 
                        key={cavalo.id} 
                        value={cavalo.id.toString()}>
                          {cavalo.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
            </TabsContent>
            
            <TabsContent value="input">
              <FormControl>
                <Input
                  placeholder={`Nome ${gender === 'Macho' ? 'do pai' : 'da mãe'} externo`}
                  value={field.value?.type === 'external' ? field.value.name || '' : ''}
                  onChange={(e) => {
                    field.onChange({
                      id: null,
                      name: e.target.value,
                      type: 'external'
                    });
                  }}
                />
              </FormControl>
            </TabsContent>
          </Tabs>
          
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default GenealogyField;