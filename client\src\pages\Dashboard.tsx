import { useState, useMemo } from "react";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Eye, Plus, ClipboardList, Calendar, CheckSquare, ActivitySquare, 
  AlertCircle, Clock, AlertTriangle, CalendarCheck2, CalendarDays,
  ChevronDown, FileText
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Cavalo, Manejo as ManejoType } from "../../../shared/schema";
import { DashboardOverview } from "@/components/DashboardOverview";

interface Manejo extends ManejoType {
  // Interface estendida para adicionar campos calculados
  isVencido?: boolean;
  diasParaVencimento?: number | null;
}

const Dashboard = () => {
  const [user, setUser] = useState(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch {
        return null;
      }
    }
    return null;
  });
  
  const { toast } = useToast();

  // Usar React Query para buscar dados de cavalos
  const { 
    data: horses = [], 
    isLoading: loading 
  } = useQuery({
    queryKey: ['/api/cavalos'],
    enabled: !!user,
    queryFn: async () => {
      try {
        console.log("Buscando cavalos para o usuário:", user);
        const cavalos = await apiRequest<Cavalo[]>('GET', '/api/cavalos') || [];
        console.log("Cavalos recebidos:", cavalos);
        return cavalos;
      } catch (error) {
        console.error("Error fetching horses:", error);
        console.log("Detalhes completos do erro:", JSON.stringify(error));
        toast({
          title: "Erro",
          description: "Não foi possível buscar os cavalos",
          variant: "destructive"
        });
        return [] as Cavalo[];
      }
    }
  });

  // Usar React Query para buscar dados de manejos
  const { 
    data: allManejos = [], 
    isLoading: loadingManejos 
  } = useQuery({
    queryKey: ['/api/manejos'],
    enabled: !!user,
    queryFn: async () => {
      try {
        console.log("Buscando manejos para o usuário:", user);
        const manejos = await apiRequest<Manejo[]>('GET', '/api/manejos');
        console.log("Manejos recebidos:", manejos);
        
        if (!manejos) return [] as Manejo[];
        
        // Processar os manejos para adicionar informações de vencimento
        const today = new Date();
        
        return manejos.map(manejo => {
          const dataVencimento = manejo.dataVencimento ? new Date(manejo.dataVencimento) : null;
          let isVencido = false;
          let diasParaVencimento = null;
          
          if (dataVencimento) {
            isVencido = dataVencimento < today;
            
            // Calcular dias para vencimento
            const diffTime = Math.abs(dataVencimento.getTime() - today.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            diasParaVencimento = isVencido ? -diffDays : diffDays;
          }
          
          return {
            ...manejo,
            isVencido,
            diasParaVencimento
          };
        });
      } catch (error) {
        console.error("Error fetching manejos:", error);
        toast({
          title: "Erro",
          description: "Não foi possível buscar os manejos",
          variant: "destructive"
        });
        return [] as Manejo[];
      }
    }
  });
  
  // Derivar os manejos recentes dos manejos totais
  const recentManejos = useMemo(() => {
    if (!allManejos || allManejos.length === 0) return [];
    
    return [...allManejos]
      .sort((a, b) => new Date(b.data).getTime() - new Date(a.data).getTime())
      .slice(0, 3);
  }, [allManejos]);
  
  // Derivar os manejos vencidos ou próximos do vencimento
  const alertManejos = useMemo(() => {
    if (!allManejos || allManejos.length === 0) return [];
    
    return allManejos
      .filter(manejo => 
        manejo.dataVencimento && 
        (manejo.isVencido || (manejo.diasParaVencimento !== undefined && manejo.diasParaVencimento !== null && manejo.diasParaVencimento <= 7))
      )
      .sort((a, b) => {
        // Ordenar primeiro por vencidos, depois por proximidade do vencimento
        if (a.isVencido && !b.isVencido) return -1;
        if (!a.isVencido && b.isVencido) return 1;
        
        // Se ambos estão na mesma categoria (vencidos ou não), ordenar por proximidade
        if (a.diasParaVencimento !== undefined && a.diasParaVencimento !== null && 
            b.diasParaVencimento !== undefined && b.diasParaVencimento !== null) {
          return a.diasParaVencimento - b.diasParaVencimento;
        }
        
        return 0;
      })
      .slice(0, 5); // Mostrar 5 alertas mais urgentes
  }, [allManejos]);

  // Calculate age from birth date
  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return `${age} ${age === 1 ? 'ano' : 'anos'}`;
  };
  
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };
  
  // Get display name for task type
  const getTaskTypeName = (tipo: string) => {
    const types: Record<string, string> = {
      'veterinary': 'Visita Veterinária',
      'farrier': 'Ferrageamento',
      'vaccination': 'Vacinação',
      'deworming': 'Vermifugação',
      'dental': 'Cuidado Dentário',
      'training': 'Treinamento',
      'other': 'Outro'
    };
    
    return types[tipo] || tipo.charAt(0).toUpperCase() + tipo.slice(1);
  };
  
  // Mapa de IDs de cavalo para seus nomes
  const horseMap = useMemo(() => {
    const map = new Map();
    horses.forEach((horse) => {
      map.set(horse.id, horse.name);
    });
    return map;
  }, [horses]);

  // Função para formatar a exibição de tempo restante
  const formatTempoRestante = (dias: number | null) => {
    if (dias === null) return '';
    
    if (dias < 0) {
      const diasAtraso = Math.abs(dias);
      return `Atrasado em ${diasAtraso} ${diasAtraso === 1 ? 'dia' : 'dias'}`;
    } else if (dias === 0) {
      return 'Vence hoje';
    } else {
      return `Em ${dias} ${dias === 1 ? 'dia' : 'dias'}`;
    }
  };
  
  // Função para obter a classe CSS com base no status da tarefa
  const getAlertStatusClass = (manejo: Manejo) => {
    if (!manejo.dataVencimento) return 'bg-blue-50 text-blue-800';
    
    if (manejo.isVencido) {
      return 'bg-red-50 text-red-800 border-red-200';
    }
    
    if (manejo.diasParaVencimento !== undefined && manejo.diasParaVencimento !== null) {
      if (manejo.diasParaVencimento <= 3) {
        return 'bg-orange-50 text-orange-800 border-orange-200';
      }
      if (manejo.diasParaVencimento <= 7) {
        return 'bg-yellow-50 text-yellow-800 border-yellow-200';
      }
    }
    
    return 'bg-green-50 text-green-800 border-green-200';
  };

  // Calcular estatísticas de status dos manejos
  const manejoStats = useMemo(() => {
    if (!allManejos || allManejos.length === 0) {
      return { pendentes: 0, concluidos: 0, atrasados: 0, emDia: 0 };
    }
    
    let pendentes = 0;
    let concluidos = 0;
    let atrasados = 0;
    let emDia = 0;
    
    allManejos.forEach(manejo => {
      if (manejo.status === 'concluido') {
        concluidos++;
      } else {
        pendentes++;
        
        if (manejo.isVencido) {
          atrasados++;
        } else {
          emDia++;
        }
      }
    });
    
    return { pendentes, concluidos, atrasados, emDia };
  }, [allManejos]);

  // Estatísticas por tipo de manejo
  const tipoStats = useMemo(() => {
    const stats: Record<string, number> = {};
    
    if (allManejos && allManejos.length > 0) {
      allManejos.forEach(manejo => {
        if (!stats[manejo.tipo]) {
          stats[manejo.tipo] = 0;
        }
        stats[manejo.tipo]++;
      });
    }
    
    return stats;
  }, [allManejos]);

  return (
    <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
      <div className="mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-blue-700 tracking-tight">Painel de Controle</h1>
            <p className="mt-2 text-gray-600">Gerencie seus cavalos e atividades de manejo.</p>
          </div>
          <div className="flex space-x-3">
            {/* Menu dropdown para adicionar novo cavalo ou importar da ABCCC */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button className="bg-green-600 text-white hover:bg-green-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Novo Animal
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href="/cavalo/cadastro" className="flex items-center">
                    <Plus className="mr-2 h-4 w-4" />
                    <span>Cadastro Manual</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/genetica/importar-abccc" className="flex items-center">
                    <FileText className="mr-2 h-4 w-4" />
                    <span>Importar de PDF ABCCC</span>
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <Link href="/manejos">
              <Button className="bg-blue-600 text-white hover:bg-blue-700">
                <ClipboardList className="h-4 w-4 mr-2" />
                Manejos
              </Button>
            </Link>
          </div>
        </div>
      </div>
      
      {/* Nova interface de Dashboard - Sempre visível e otimizada */}
      {user && <DashboardOverview userId={user.id} />}
      <div className="mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card className="border-none shadow-sm bg-gradient-to-br from-primary-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-primary-600">Cavalos</p>
                <h3 className="mt-1 text-2xl font-semibold">{horses.length}</h3>
              </div>
              <div className="p-2 bg-primary-100 rounded-full">
                <CheckSquare className="h-6 w-6 text-primary-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-none shadow-sm bg-gradient-to-br from-blue-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Manejos Totais</p>
                <h3 className="mt-1 text-2xl font-semibold">{allManejos.length}</h3>
              </div>
              <div className="p-2 bg-blue-100 rounded-full">
                <ClipboardList className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-none shadow-sm bg-gradient-to-br from-green-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Manejos Concluídos</p>
                <h3 className="mt-1 text-2xl font-semibold">{manejoStats.concluidos}</h3>
              </div>
              <div className="p-2 bg-green-100 rounded-full">
                <CalendarCheck2 className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className={`border-none shadow-sm ${manejoStats.atrasados > 0 ? 'bg-gradient-to-br from-red-50 to-white' : 'bg-gradient-to-br from-gray-50 to-white'}`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${manejoStats.atrasados > 0 ? 'text-red-600' : 'text-gray-600'}`}>
                  Manejos Atrasados
                </p>
                <h3 className="mt-1 text-2xl font-semibold">{manejoStats.atrasados}</h3>
              </div>
              <div className={`p-2 rounded-full ${manejoStats.atrasados > 0 ? 'bg-red-100' : 'bg-gray-100'}`}>
                <AlertCircle className={`h-6 w-6 ${manejoStats.atrasados > 0 ? 'text-red-600' : 'text-gray-600'}`} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Alertas Section */}
        <Card className="overflow-hidden border-none shadow-md">
          <CardHeader className="bg-gradient-to-r from-amber-50 to-amber-100 pb-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-6 w-6 text-amber-600" />
                <CardTitle className="text-xl text-amber-700">Alertas de Manejos</CardTitle>
              </div>
              <Link href="/manejos">
                <Button size="sm" variant="outline" className="border-amber-200 bg-white text-amber-700 hover:bg-amber-50">
                  <CalendarDays className="h-4 w-4 mr-1" />
                  Gerenciar
                </Button>
              </Link>
            </div>
            <CardDescription className="text-amber-600/70">
              Manejos que requerem atenção
            </CardDescription>
          </CardHeader>
          
          <CardContent className="p-0">
            {loadingManejos ? (
              <div className="p-8 flex justify-center">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-amber-500"></div>
              </div>
            ) : alertManejos.length === 0 ? (
              <div className="py-10 px-4 text-center">
                <AlertCircle className="mx-auto h-12 w-12 text-gray-300" />
                <p className="mt-4 text-gray-500 font-medium">Nenhum manejo com data de vencimento</p>
                <p className="text-xs text-gray-400 mt-1">
                  Adicione datas de vencimento aos seus manejos para receber alertas.
                </p>
                <Link href="/manejos">
                  <Button variant="link" className="mt-2 text-amber-600">
                    Gerenciar manejos
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {alertManejos.map((manejo) => (
                  <div key={manejo.id} className="p-4 hover:bg-gray-50 transition-colors duration-150">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="text-base font-semibold text-gray-800">{getTaskTypeName(manejo.tipo)}</h3>
                          <Badge variant="outline" className={getAlertStatusClass(manejo)}>
                            {manejo.dataVencimento ? formatTempoRestante(manejo.diasParaVencimento || null) : 'Sem prazo'}
                          </Badge>
                        </div>
                        
                        {/* Nome do cavalo */}
                        <p className="text-sm text-gray-600 mt-1">
                          {manejo.horseId 
                            ? `Cavalo: ${horseMap.get(manejo.horseId) || "Não especificado"}`
                            : "Manejo geral"}
                        </p>
                        
                        {/* Data e informação de vencimento */}
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-gray-500">
                            Data: {formatDate(manejo.data)}
                          </span>
                          {manejo.dataVencimento && (
                            <span className="text-xs text-gray-500">
                              Vencimento: {formatDate(manejo.dataVencimento)}
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center">
                        {manejo.isVencido ? (
                          <Clock className="h-5 w-5 text-red-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-amber-500" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
          
          {alertManejos.length > 0 && (
            <CardFooter className="bg-gray-50 px-4 py-3 flex justify-end">
              <Link href="/manejos">
                <Button variant="ghost" size="sm" className="text-amber-700 hover:bg-amber-50">
                  <Calendar className="mr-2 h-4 w-4" />
                  Ver Todos os Manejos
                </Button>
              </Link>
            </CardFooter>
          )}
        </Card>
        
        {/* Cavalos Section */}
        <Card className="overflow-hidden border-none shadow-md">
          <CardHeader className="bg-gradient-to-r from-primary-50 to-primary-100 pb-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <CheckSquare className="h-6 w-6 text-primary-600" />
                <CardTitle className="text-xl text-primary-700">Seus Cavalos</CardTitle>
              </div>
              <Link href="/cadastro">
                <Button size="sm" variant="outline" className="border-primary-200 bg-white text-primary-700 hover:bg-primary-50">
                  <Plus className="h-4 w-4 mr-1" />
                  Adicionar Cavalo
                </Button>
              </Link>
            </div>
            <CardDescription className="text-primary-600/70">
              Informações sobre seus cavalos registrados
            </CardDescription>
          </CardHeader>
          
          <CardContent className="p-0">
            {loading ? (
              <div className="p-8 flex justify-center">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary-500"></div>
              </div>
            ) : horses.length === 0 ? (
              <div className="py-10 px-4 text-center">
                <CheckSquare className="mx-auto h-12 w-12 text-gray-300" />
                <p className="mt-4 text-gray-500 font-medium">Nenhum cavalo cadastrado ainda</p>
                <Link href="/cadastro">
                  <Button variant="link" className="mt-2 text-primary-600">
                    Registre seu primeiro cavalo
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {horses.map((horse) => (
                  <div key={horse.id} className="p-4 hover:bg-gray-50 transition-colors duration-150">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-base font-semibold text-primary-700">{horse.name}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs bg-primary-50 text-primary-700 border-primary-200 rounded-full">
                            {horse.breed}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {horse.birthDate ? calculateAge(horse.birthDate) : "Sem data de nascimento"}
                          </span>
                        </div>
                      </div>
                      <Link href={`/cavalo/${horse.id}`}>
                        <Button variant="ghost" size="icon" className="rounded-full text-gray-500 hover:text-primary-600 hover:bg-primary-50">
                          <Eye className="h-5 w-5" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
          
          {horses.length > 0 && (
            <CardFooter className="bg-gray-50 px-4 py-3 flex justify-end">
              <Link href="/cavalos">
                <Button variant="ghost" size="sm" className="text-primary-700 hover:bg-primary-50">
                  Ver Todos os Cavalos
                </Button>
              </Link>
            </CardFooter>
          )}
        </Card>
      </div>
      
      {/* Segunda linha de cards */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 mt-8">
        {/* Manejos Recentes Section */}
        <Card className="overflow-hidden border-none shadow-md">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 pb-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <ActivitySquare className="h-6 w-6 text-blue-600" />
                <CardTitle className="text-xl text-blue-700">Manejos Recentes</CardTitle>
              </div>
              <Link href="/manejos">
                <Button size="sm" variant="outline" className="border-blue-200 bg-white text-blue-700 hover:bg-blue-50">
                  <Plus className="h-4 w-4 mr-1" />
                  Adicionar Manejo
                </Button>
              </Link>
            </div>
            <CardDescription className="text-blue-600/70">
              Atividades de cuidado e manejo dos seus cavalos
            </CardDescription>
          </CardHeader>
          
          <CardContent className="p-0">
            {loadingManejos ? (
              <div className="p-8 flex justify-center">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : recentManejos.length === 0 ? (
              <div className="py-10 px-4 text-center">
                <ClipboardList className="mx-auto h-12 w-12 text-gray-300" />
                <p className="mt-4 text-gray-500 font-medium">Nenhum manejo registrado ainda</p>
                <p className="text-xs text-gray-400 mt-1">
                  Registre consultas veterinárias, ferrageamento, vacinações e muito mais.
                </p>
                <Link href="/manejos">
                  <Button variant="link" className="mt-2 text-blue-600">
                    Adicione seu primeiro manejo
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {recentManejos.map((manejo) => (
                  <div key={manejo.id} className="p-4 hover:bg-gray-50 transition-colors duration-150">
                    <div className="flex justify-between">
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="text-base font-semibold text-blue-700">{getTaskTypeName(manejo.tipo)}</h3>
                          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 border-none">
                            {formatDate(manejo.data)}
                          </Badge>
                        </div>
                        
                        {/* Nome do cavalo */}
                        <p className="text-sm text-gray-600 mt-1">
                          {manejo.horseId 
                            ? `Cavalo: ${horseMap.get(manejo.horseId) || "Cavalo não encontrado"}`
                            : "Manejo geral (sem cavalo específico)"}
                        </p>
                        
                        {/* Observações (se existirem) */}
                        {manejo.observacoes && (
                          <p className="mt-1 text-xs text-gray-500 truncate max-w-xs">
                            {manejo.observacoes}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
          
          <CardFooter className="bg-gray-50 px-4 py-3 flex justify-end">
            <Link href="/manejos">
              <Button variant="ghost" size="sm" className="text-blue-700 hover:bg-blue-50">
                <Calendar className="mr-2 h-4 w-4" />
                Ver Todos os Manejos
              </Button>
            </Link>
          </CardFooter>
        </Card>
        
        {/* Estatísticas de Manejos */}
        <Card className="overflow-hidden border-none shadow-md">
          <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 pb-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <CalendarCheck2 className="h-6 w-6 text-green-600" />
                <CardTitle className="text-xl text-green-700">Estatísticas</CardTitle>
              </div>
            </div>
            <CardDescription className="text-green-600/70">
              Resumo de atividades de manejo
            </CardDescription>
          </CardHeader>
          
          <CardContent className="p-6">
            {loadingManejos ? (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-green-500"></div>
              </div>
            ) : allManejos.length === 0 ? (
              <div className="text-center">
                <p className="text-gray-500 font-medium">Sem dados para exibir</p>
                <p className="text-xs text-gray-400 mt-1">
                  Adicione manejos para visualizar estatísticas.
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Status de Manejos */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-700">Status de Manejos</h3>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-xs text-gray-600 mb-1">
                        <span>Pendentes</span>
                        <span>{manejoStats.pendentes} de {allManejos.length}</span>
                      </div>
                      <Progress 
                        value={(manejoStats.pendentes / allManejos.length) * 100} 
                        className="h-2 bg-gray-100" 
                      />
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-xs text-gray-600 mb-1">
                        <span>Concluídos</span>
                        <span>{manejoStats.concluidos} de {allManejos.length}</span>
                      </div>
                      <Progress 
                        value={(manejoStats.concluidos / allManejos.length) * 100} 
                        className="h-2 bg-gray-100" 
                      />
                    </div>
                    
                    {manejoStats.atrasados > 0 && (
                      <div>
                        <div className="flex justify-between text-xs text-gray-600 mb-1">
                          <span className="text-red-600">Atrasados</span>
                          <span>{manejoStats.atrasados} de {allManejos.length}</span>
                        </div>
                        <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-red-500 rounded-full" 
                            style={{ width: `${(manejoStats.atrasados / allManejos.length) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Tipos de Manejos */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-700">Manejos por Tipo</h3>
                  <div className="space-y-2">
                    {Object.entries(tipoStats).map(([tipo, count]) => (
                      <div key={tipo} className="flex justify-between items-center text-sm">
                        <span>{getTaskTypeName(tipo)}</span>
                        <Badge variant="outline" className="bg-gray-50">
                          {count}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
