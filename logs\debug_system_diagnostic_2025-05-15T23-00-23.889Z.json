{"timestamp": "2025-05-15T23:00:23.888Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 397479936, "heapTotal": 110403584, "heapUsed": 72559384, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.526027853, "cpuUsage": {"user": 3141023, "system": 408925}, "resourceUsage": {"userCPUTime": 3141089, "systemCPUTime": 408925, "maxRSS": 388164, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101925, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8054, "involuntaryContextSwitches": 9106}}