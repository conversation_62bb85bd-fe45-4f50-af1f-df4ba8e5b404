{"timestamp": "2025-05-15T21:16:40.909Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404701184, "heapTotal": 116170752, "heapUsed": 72449680, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.689654214, "cpuUsage": {"user": 2645231, "system": 312376}, "resourceUsage": {"userCPUTime": 2645275, "systemCPUTime": 312376, "maxRSS": 395216, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104467, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 64, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7973, "involuntaryContextSwitches": 2152}}