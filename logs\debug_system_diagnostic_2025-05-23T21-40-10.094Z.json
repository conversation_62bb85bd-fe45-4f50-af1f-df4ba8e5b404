{"timestamp": "2025-05-23T21:40:10.094Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399142912, "heapTotal": 116326400, "heapUsed": 92220304, "external": 8616688, "arrayBuffers": 249274}, "uptime": 2.610771548, "cpuUsage": {"user": 3136042, "system": 418863}, "resourceUsage": {"userCPUTime": 3136112, "systemCPUTime": 418872, "maxRSS": 389788, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 112529, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 32, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8557, "involuntaryContextSwitches": 7935}}