{"timestamp": "2025-05-16T13:54:07.341Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 392351744, "heapTotal": 110927872, "heapUsed": 72482144, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.855109197, "cpuUsage": {"user": 2821752, "system": 350939}, "resourceUsage": {"userCPUTime": 2821801, "systemCPUTime": 350945, "maxRSS": 383156, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102490, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8204, "involuntaryContextSwitches": 2760}}