{"timestamp": "2025-05-15T23:48:46.751Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405958656, "heapTotal": 114335744, "heapUsed": 72555696, "external": 8219482, "arrayBuffers": 243725}, "uptime": 2.809007783, "cpuUsage": {"user": 3127437, "system": 409402}, "resourceUsage": {"userCPUTime": 3127503, "systemCPUTime": 409405, "maxRSS": 396444, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103994, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8485, "involuntaryContextSwitches": 8999}}