import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { <PERSON><PERSON>lo } from '@shared/schema';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';
import { HorseAvatarWithUpload } from '@/components/horse/HorseAvatarWithUpload';
import { GenealogiaSelector, EntradaGenealogica } from '@/components/cavalo/GenealogiaSelector';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ExpandedGenealogy } from '@/components/genetics/ExpandedGenealogy';

export default function EditHorseSimple() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [, navigate] = useLocation();
  
  // ID do cavalo na URL
  const location = useLocation()[0];
  const horseId = location.split("/")[2];

  // Estados do formulário - incluindo todos os campos da página de detalhes
  const [formData, setFormData] = useState({
    name: '',
    breed: '',
    birthDate: '',
    sexo: '',
    cor: '',
    peso: '',
    altura: '',
    numeroRegistro: '',
    criador: '',
    proprietario: '',
    inspetor: '',
    origem: '',
    notes: '',
    status: '',
    dataEntrada: '',
    dataSaida: '',
    dataCompra: '',
    motivoSaida: '',
    paiNome: '',
    maeNome: '',
    avoPaterno: '',
    avoMaterno: ''
  });

  // Estados para genealogia usando o sistema EntradaGenealogica
  const [pai, setPai] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  const [mae, setMae] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  
  // Estados para avós
  const [avoPaterno, setAvoPaterno] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  const [avoPaterna, setAvoPaterna] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  const [avoMaterno, setAvoMaterno] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  const [avoMaterna, setAvoMaterna] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  
  // Estados para bisavós paternos
  const [bisavoPaternoPaterno, setBisavoPaternoPaterno] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  const [bisavoPaternaPaterno, setBisavoPaternaPaterno] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  const [bisavoMaternoPaterno, setBisavoMaternoPaterno] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  const [bisavoMaternaPaterno, setBisavoMaternaPaterno] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  
  // Estados para bisavós maternos
  const [bisavoPaternoMaterno, setBisavoPaternoMaterno] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  const [bisavoPaternaMaterno, setBisavoPaternaMaterno] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  const [bisavoMaternoMaterno, setBisavoMaternoMaterno] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });
  const [bisavoMaternaMaterno, setBisavoMaternaMaterno] = useState<EntradaGenealogica>({ tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null });

  // Buscar dados do cavalo
  const {
    data: horse,
    isLoading,
    error,
  } = useQuery({
    queryKey: [`/api/cavalos/${horseId}`],
    enabled: !!horseId && !!user,
    queryFn: async () => {
      const result = await apiRequest<Cavalo>("GET", `/api/cavalos/${horseId}`);
      console.log("Dados do cavalo carregados:", result);
      return result;
    },
  });

  // Buscar todos os cavalos (incluindo externos) para genealogia
  const {
    data: todosCavalos = [],
    isLoading: isLoadingCavalos,
  } = useQuery({
    queryKey: ['/api/cavalos', 'incluir_externos', true],
    enabled: !!user,
    queryFn: async () => {
      const result = await apiRequest<Cavalo[]>("GET", "/api/cavalos?incluir_externos=true");
      console.log("Todos os cavalos carregados para genealogia:", result.length);
      return result;
    },
  });

  // Preencher formulário quando dados chegarem
  useEffect(() => {
    if (horse) {
      console.log("Preenchendo formulário com:", horse);
      
      // Converter data se necessário
      let formattedBirthDate = "";
      if (horse.birthDate) {
        if (horse.birthDate.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
          const parts = horse.birthDate.split('/');
          formattedBirthDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
        } else {
          formattedBirthDate = horse.birthDate;
        }
      }

      // Função para converter datas
      const convertDate = (dateStr: string) => {
        if (!dateStr) return '';
        
        // Se já está no formato ISO (YYYY-MM-DD), manter
        if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
          return dateStr;
        }
        
        // Se está no formato brasileiro (DD/MM/YYYY), converter
        if (dateStr.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
          const parts = dateStr.split('/');
          return `${parts[2]}-${parts[1]}-${parts[0]}`;
        }
        
        // Se é uma data ISO completa, extrair apenas a parte da data
        if (dateStr.includes('T')) {
          return dateStr.split('T')[0];
        }
        
        // Para outros formatos, tentar converter usando Date
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          return date.toISOString().split('T')[0];
        }
        
        return '';
      };

      setFormData({
        name: horse.name || '',
        breed: horse.breed || '',
        birthDate: convertDate(horse.birthDate || ''),
        sexo: horse.sexo || '',
        cor: horse.cor || '',
        peso: horse.peso ? horse.peso.toString() : '',
        altura: horse.altura ? horse.altura.toString() : '',
        numeroRegistro: horse.numeroRegistro || '',
        criador: horse.criador || '',
        proprietario: horse.proprietario || '',
        inspetor: horse.inspetor || '',
        origem: horse.origem || '',
        notes: horse.notes || '',
        status: horse.status || 'ativo',
        dataEntrada: convertDate(horse.dataEntrada || ''),
        dataSaida: convertDate(horse.dataSaida || ''),
        dataCompra: convertDate(horse.dataCompra || ''),
        motivoSaida: horse.motivoSaida || '',
        paiNome: horse.paiNome || '',
        maeNome: horse.maeNome || '',
        avoPaterno: horse.avoPaterno || '',
        avoMaterno: horse.avoMaterno || ''
      });

      // Inicializar dados de genealogia baseado nos dados reais do console
      console.log("Dados completos do cavalo:", horse);
      
      // Verificar pai usando os campos corretos do banco
      if (horse.paiId || horse.paiNome) {
        const paiConfig = {
          tipo: horse.paiId ? 'sistema' : 'externo',
          cavaloSistemaId: horse.paiId ? horse.paiId.toString() : null,
          cavaloNome: horse.paiNome || null
        };
        setPai(paiConfig);
        console.log("✓ Pai configurado:", paiConfig);
      }

      // Verificar mãe usando os campos corretos do banco
      if (horse.maeId || horse.maeNome) {
        const maeConfig = {
          tipo: horse.maeId ? 'sistema' : 'externo',
          cavaloSistemaId: horse.maeId ? horse.maeId.toString() : null,
          cavaloNome: horse.maeNome || null
        };
        setMae(maeConfig);
        console.log("✓ Mãe configurada:", maeConfig);
      }

      // Inicializar avós usando os campos corretos do banco
      if (horse.avoPaterno) {
        const avoPatConfig = {
          tipo: 'externo',
          cavaloSistemaId: null,
          cavaloNome: horse.avoPaterno
        };
        setAvoPaterno(avoPatConfig);
        console.log("✓ Avô paterno configurado:", avoPatConfig);
      }

      if (horse.avoMaterno) {
        const avoMatConfig = {
          tipo: 'externo',
          cavaloSistemaId: null,
          cavaloNome: horse.avoMaterno
        };
        setAvoMaterno(avoMatConfig);
        console.log("✓ Avô materno configurado:", avoMatConfig);
      }
    }
  }, [horse]);

  // Mutation para salvar alterações
  const updateMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      // Função para converter datas de volta para formato brasileiro
      const convertDateToAPI = (dateStr: string) => {
        if (!dateStr) return null;
        if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
          const parts = dateStr.split('-');
          return `${parts[2]}/${parts[1]}/${parts[0]}`;
        }
        return dateStr;
      };

      const payload = {
        name: data.name,
        breed: data.breed,
        birthDate: convertDateToAPI(data.birthDate),
        sexo: data.sexo || null,
        cor: data.cor || null,
        peso: data.peso ? parseFloat(data.peso) : null,
        altura: data.altura ? parseFloat(data.altura) : null,
        numeroRegistro: data.numeroRegistro || null,
        criador: data.criador || null,
        proprietario: data.proprietario || null,
        inspetor: data.inspetor || null,
        origem: data.origem || null,
        notes: data.notes || null,
        status: data.status || 'ativo',
        dataEntrada: convertDateToAPI(data.dataEntrada),
        dataSaida: convertDateToAPI(data.dataSaida),
        dataCompra: convertDateToAPI(data.dataCompra),
        motivoSaida: data.motivoSaida || null,
        pai: pai,
        mae: mae,
        avoPaterno: avoPaterno,
        avoPaterna: avoPaterna,
        avoMaterno: avoMaterno,
        avoMaterna: avoMaterna,
        bisavoPaternoPaterno: bisavoPaternoPaterno,
        bisavoPaternaPaterno: bisavoPaternaPaterno,
        bisavoMaternoPaterno: bisavoMaternoPaterno,
        bisavoMaternaPaterno: bisavoMaternaPaterno,
        bisavoPaternoMaterno: bisavoPaternoMaterno,
        bisavoPaternaMaterno: bisavoPaternaMaterno,
        bisavoMaternoMaterno: bisavoMaternoMaterno,
        bisavoMaternaMaterno: bisavoMaternaMaterno,
      };
      
      console.log("Payload sendo enviado para API:", payload);
      
      const response = await apiRequest("PATCH", `/api/cavalos/${horseId}`, payload);
      console.log("Resposta da API:", response);
      
      return response;
    },
    onSuccess: async () => {
      // Invalidar e forçar refetch dos dados
      await queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${horseId}`] });
      await queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      
      // Remover da cache para forçar nova busca
      queryClient.removeQueries({ queryKey: [`/api/cavalos/${horseId}`] });
      
      toast({
        title: "Sucesso",
        description: "Cavalo atualizado com sucesso!",
      });
      
      // Aguardar um pouco antes de navegar
      setTimeout(() => {
        navigate(`/cavalo/${horseId}`);
      }, 300);
    },
    onError: (error: any) => {
      toast({
        title: "Erro",
        description: error.message || "Erro ao atualizar cavalo",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Dados sendo enviados:", formData);
    updateMutation.mutate(formData);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !horse) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Cavalo não encontrado
              </h2>
              <p className="text-gray-600 mb-4">
                Não foi possível encontrar este cavalo. Ele pode ter sido excluído ou você não tem permissão para acessá-lo.
              </p>
              <Button onClick={() => navigate('/cavalos')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar para o Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => navigate(`/cavalo/${horseId}`)}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar aos Detalhes
        </Button>
        
        <h1 className="text-3xl font-bold text-gray-900">
          Editar {horse.name}
        </h1>
        <p className="text-gray-600">
          Altere as informações do cavalo abaixo
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Upload de Foto */}
        <Card>
          <CardHeader>
            <CardTitle>Foto do Cavalo</CardTitle>
            <CardDescription>
              Faça upload de uma foto do cavalo
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HorseAvatarWithUpload
              horseId={parseInt(horseId)}
              horseName={horse.name}
              size="lg"
            />
          </CardContent>
        </Card>

        {/* Informações Básicas */}
        <Card>
          <CardHeader>
            <CardTitle>Informações Básicas</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Nome *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="breed">Raça</Label>
                <Input
                  id="breed"
                  value={formData.breed}
                  onChange={(e) => handleInputChange('breed', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="birthDate">Data de Nascimento</Label>
                <Input
                  id="birthDate"
                  type="date"
                  value={formData.birthDate}
                  onChange={(e) => handleInputChange('birthDate', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="sexo">Sexo</Label>
                <Select value={formData.sexo} onValueChange={(value) => handleInputChange('sexo', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o sexo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MACHO">Macho</SelectItem>
                    <SelectItem value="FÊMEA">Fêmea</SelectItem>
                    <SelectItem value="CASTRADO">Castrado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="cor">Pelagem/Cor</Label>
                <Input
                  id="cor"
                  value={formData.cor}
                  onChange={(e) => handleInputChange('cor', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="numeroRegistro">Número de Registro</Label>
                <Input
                  id="numeroRegistro"
                  value={formData.numeroRegistro}
                  onChange={(e) => handleInputChange('numeroRegistro', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Medidas */}
        <Card>
          <CardHeader>
            <CardTitle>Medidas</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="peso">Peso (kg)</Label>
                <Input
                  id="peso"
                  type="number"
                  step="0.1"
                  value={formData.peso}
                  onChange={(e) => handleInputChange('peso', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="altura">Altura (m)</Label>
                <Input
                  id="altura"
                  type="number"
                  step="0.01"
                  value={formData.altura}
                  onChange={(e) => handleInputChange('altura', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Informações de Registro */}
        <Card>
          <CardHeader>
            <CardTitle>Informações de Registro</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="criador">Criador</Label>
                <Input
                  id="criador"
                  value={formData.criador}
                  onChange={(e) => handleInputChange('criador', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="proprietario">Proprietário</Label>
                <Input
                  id="proprietario"
                  value={formData.proprietario}
                  onChange={(e) => handleInputChange('proprietario', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="inspetor">Inspetor</Label>
                <Input
                  id="inspetor"
                  value={formData.inspetor}
                  onChange={(e) => handleInputChange('inspetor', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="origem">Origem</Label>
                <Input
                  id="origem"
                  value={formData.origem}
                  onChange={(e) => handleInputChange('origem', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status e Controle */}
        <Card>
          <CardHeader>
            <CardTitle>Status e Controle</CardTitle>
            <CardDescription>
              Informações sobre status e datas importantes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ativo">Ativo</SelectItem>
                    <SelectItem value="vendido">Vendido</SelectItem>
                    <SelectItem value="falecido">Falecido</SelectItem>
                    <SelectItem value="doado">Doado</SelectItem>
                    <SelectItem value="emprestado">Emprestado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="dataEntrada">Data de Entrada</Label>
                <Input
                  id="dataEntrada"
                  type="date"
                  value={formData.dataEntrada}
                  onChange={(e) => handleInputChange('dataEntrada', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="dataCompra">Data de Compra</Label>
                <Input
                  id="dataCompra"
                  type="date"
                  value={formData.dataCompra}
                  onChange={(e) => handleInputChange('dataCompra', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="dataSaida">Data de Saída</Label>
                <Input
                  id="dataSaida"
                  type="date"
                  value={formData.dataSaida}
                  onChange={(e) => handleInputChange('dataSaida', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Genealogia */}
        <Card>
          <CardHeader>
            <CardTitle>Genealogia</CardTitle>
            <CardDescription>
              Árvore genealógica completa e editável
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ExpandedGenealogy cavaloId={horse.id} editable={true} />
          </CardContent>
        </Card>

        {/* Observações */}
        <Card>
          <CardHeader>
            <CardTitle>Observações</CardTitle>
            <CardDescription>
              Anotações e observações sobre o cavalo
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="notes">Observações Gerais</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Observações adicionais sobre o cavalo..."
                rows={4}
              />
            </div>

            {formData.dataSaida && (
              <div>
                <Label htmlFor="motivoSaida">Motivo da Saída</Label>
                <Textarea
                  id="motivoSaida"
                  value={formData.motivoSaida}
                  onChange={(e) => handleInputChange('motivoSaida', e.target.value)}
                  placeholder="Descreva o motivo da saída do cavalo..."
                  rows={3}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Botões de Ação */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate(`/cavalo/${horseId}`)}
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            disabled={updateMutation.isPending}
          >
            {updateMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Salvando...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Salvar Alterações
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}