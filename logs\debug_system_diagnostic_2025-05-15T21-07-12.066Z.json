{"timestamp": "2025-05-15T21:07:12.066Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 405016576, "heapTotal": 115384320, "heapUsed": 72454192, "external": 8211290, "arrayBuffers": 235533}, "uptime": 1.718372191, "cpuUsage": {"user": 2901778, "system": 362488}, "resourceUsage": {"userCPUTime": 2901817, "systemCPUTime": 362493, "maxRSS": 395524, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105026, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 64, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7948, "involuntaryContextSwitches": 2581}}