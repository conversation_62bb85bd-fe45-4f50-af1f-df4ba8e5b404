/**
 * Serviço de Importação de Registros Genealógicos da ABCCC
 *
 * Este módulo fornece funções para processar registros da ABCCC e
 * importá-los para o banco de dados EquiGestor, atualizando ou
 * criando cavalos e suas relações genealógicas.
 *
 * O serviço atua como uma camada entre o parser de PDF e o banco de dados,
 * garantindo que as operações sejam realizadas com integridade e sem impacto
 * nos módulos existentes.
 */

import path from "path";
import { db } from "./db";
import { cavalos, genealogia, pelagens } from "../shared/schema";
import {
  CavaloInfo,
  processarPdfABCCC,
  salvarLogDebug,
} from "./abccc-pdf-parser";
import { processarPdfComOpenAI } from "./openai-pdf-service";
import { ABCCCScrapedData, getCriouloDataByRegistro } from "./abccc-scraper-service";
import { <PERSON><PERSON>lo, InsertCavalo } from "../shared/schema";
import { eq, sql } from "drizzle-orm";
import { getModuleLogger } from "./logger";

const logger = getModuleLogger("abccc-import");

/**
 * Resolve ou cria uma pelagem, retornando seu ID
 */
async function resolvePelagemId(nome: string | undefined, userId: number): Promise<number | null> {
  if (!nome) return null;
  // Busca exata
  const [exato] = await db.select().from(pelagens).where(eq(pelagens.nome, nome)).limit(1);
  if (exato) return exato.id;
  // Busca case-insensitive
  const [similar] = await db
    .select()
    .from(pelagens)
    .where(sql`LOWER(${pelagens.nome}) = LOWER(${nome})`)
    .limit(1);
  if (similar) return similar.id;
  // Cria nova pelagem
  const [nova] = await db
    .insert(pelagens)
    .values({
      nome,
      descricao: `Extraída via scraper em ${new Date().toISOString().slice(0, 10)}`,
      fonte: "scraper",
      userId,
    })
    .returning();
  return nova?.id ?? null;
}

export interface ResumoImportacao {
  cavaloPrincipal: {
    nome: string;
    registro: string;
    novo: boolean;
    id?: number;
    sexo?: string;
    nascimento?: string;
    pelagem?: string;
    criador?: string;
    proprietario?: string;
    inspetor?: string;
  };
  familiares: Array<{
    tipo: string;
    nome: string;
    registro?: string;
    novo: boolean;
    id?: number;
    sexo?: string;
    nascimento?: string;
    pelagem?: string;
    criador?: string;
    proprietario?: string;
    inspetor?: string;
  }>;
  detalhes: {
    nomeArquivo: string;
    dataImportacao: string;
    totalAnimais: number;
    novosAnimais: number;
    atualizados: number;
    erros: string[];
    dadosComplementares?: boolean;
  };
  logDebug: string;
  dadosOriginaisPdf?: unknown;
  dadosComplementares?: ABCCCScrapedData;
}

export async function previsualizarRegistroABCCC(
  filePath: string,
  userId: number
): Promise<ResumoImportacao> {
  if (!filePath || !userId) throw new Error("filePath e userId são obrigatórios");
  const nomeArquivo = path.basename(filePath);
  const dataAtual = new Date();
  logger.info(`Iniciando pré-visualização do arquivo ${nomeArquivo} para o usuário ${userId}`);
  const resultadoOpenAI = await processarPdfComOpenAI(filePath);
  if (!resultadoOpenAI.dados?.cavalo?.nome) {
    throw new Error("Não foi possível extrair os dados do cavalo principal do PDF");
  }
  const logId = `preview_${Date.now()}`;
  await salvarLogDebug(logId, JSON.stringify(resultadoOpenAI, null, 2));

  const resumo: ResumoImportacao = {
    cavaloPrincipal: {
      nome: resultadoOpenAI.dados.cavalo.nome,
      registro: resultadoOpenAI.dados.cavalo.registro || "",
      novo: true,
      sexo: resultadoOpenAI.dados.cavalo.sexo,
      nascimento: resultadoOpenAI.dados.cavalo.nascimento,
      pelagem: resultadoOpenAI.dados.cavalo.pelagem,
      criador: resultadoOpenAI.dados.cavalo.criador,
      proprietario: resultadoOpenAI.dados.cavalo.proprietario,
      inspetor: resultadoOpenAI.dados.cavalo.inspetor,
    },
    familiares: [],
    detalhes: {
      nomeArquivo,
      dataImportacao: dataAtual.toLocaleString(),
      totalAnimais: 1,
      novosAnimais: 0,
      atualizados: 0,
      erros: [],
    },
    logDebug: logId,
    dadosOriginaisPdf: resultadoOpenAI.dados,
  };

  // Apenas preview, sem scraping externo
  try {
    const existencia = resultadoOpenAI.dados.cavalo.registro
      ? await buscarCavaloPorRegistro(resultadoOpenAI.dados.cavalo.registro)
      : undefined;
    if (existencia) {
      resumo.cavaloPrincipal.novo = false;
      resumo.cavaloPrincipal.id = existencia.id;
    }
  } catch (e: any) {
    logger.warn(`Erro ao verificar existência: ${e.message}`);
    resumo.detalhes.erros.push(e.message);
  }

  // Processa familiares básicos
  for (const [tipo, info] of Object.entries(resultadoOpenAI.dados).filter(
    ([k]) => ["pai","mae","avoPai","avaMae","avoPai2","avaMae2"].includes(k)
  )) {
    if (info) {
      const fam = await prepararInfoFamiliar(info as CavaloInfo, tipo, resumo);
      resumo.familiares.push(fam);
      resumo.detalhes.totalAnimais++;
    }
  }

  resumo.detalhes.novosAnimais = resumo.familiares.filter(f => f.novo).length + (resumo.cavaloPrincipal.novo ? 1 : 0);
  resumo.detalhes.atualizados = resumo.detalhes.totalAnimais - resumo.detalhes.novosAnimais;
  logger.info(`Pré-visualização concluída: ${resumo.detalhes.totalAnimais} animais, ${resumo.detalhes.novosAnimais} novos`);
  return resumo;
}

async function prepararInfoFamiliar(
  info: CavaloInfo,
  tipo: string,
  resumo: ResumoImportacao
) {
  const fam: any = { tipo, nome: info.nome, registro: info.registro, novo: true, sexo: info.sexo, nascimento: info.nascimento, pelagem: info.pelagem, criador: info.criador, proprietario: info.proprietario, inspetor: info.inspetor };
  try {
    const existe = info.registro
      ? await buscarCavaloPorRegistro(info.registro)
      : info.nome
        ? await buscarCavaloPorNome(info.nome)
        : undefined;
    if (existe) { fam.novo = false; fam.id = existe.id; }
  } catch (e: any) {
    logger.warn(`Verificação falhou para ${info.nome}: ${e.message}`);
    resumo.detalhes.erros.push(e.message);
  }
  return fam;
}

export async function importarRegistroABCCC(
  filePath: string,
  userId: number
): Promise<ResumoImportacao> {
  if (!filePath || !userId) throw new Error("filePath e userId obrigatórios");
  const resumo: ResumoImportacao = {
    cavaloPrincipal: { nome:"", registro:"", novo:false },
    familiares: [],
    detalhes: { nomeArquivo: path.basename(filePath), dataImportacao: new Date().toISOString(), totalAnimais: 0, novosAnimais: 0, atualizados: 0, erros: [] },
    logDebug: "",
  };
  try {
    logger.info(`Processando PDF ${filePath}`);
    if (!process.env.OPENAI_API_KEY) throw new Error("OPENAI_API_KEY não configurada");
    let resultado;
    try {
      resultado = await processarPdfComOpenAI(filePath);
      resumo.logDebug = resultado.log;
    } catch {
      resultado = await processarPdfABCCC(filePath);
      const logId = `import_${Date.now()}`;
      await salvarLogDebug(logId, JSON.stringify(resultado, null,2));
      resumo.logDebug = logId;
    }
    const dados = resultado.dados;
    let total=0, novos=0, at=0;
    // Processa principal
    const cp = await processarCavaloPrincipal(dados.cavalo, userId, resumo);
    total++; cp.novo? novos++: at++;

    // Processa familiares
    const relacoes: Array<{ tipo:string;entity:any }> = [
      { tipo:'Pai', entity:dados.pai },
      { tipo:'Mãe', entity:dados.mae },
      { tipo:'Avô Paterno', entity:dados.avoPai },
      { tipo:'Avó Paterna', entity:dados.avaMae },
      { tipo:'Avô Materno', entity:dados.avoPai2 },
      { tipo:'Avó Materna', entity:dados.avaMae2 },
    ];
    const idsMap: any = { cavaloId: cp.id };
    for (const r of relacoes) {
      if (r.entity) {
        const fam = await processarCavaloFamiliar(r.entity, userId, r.tipo, resumo);
        total++; fam.novo? novos++: at++;
        resumo.familiares.push(fam);
        // guarda para genealogia
        if (r.tipo==='Pai') { idsMap.paiId = fam.id; idsMap.paiNome = fam.nome; }
        if (r.tipo==='Mãe') { idsMap.maeId = fam.id; idsMap.maeNome = fam.nome; }
        if (r.tipo==='Avô Paterno') { idsMap.avoPaternoId = fam.id; idsMap.avoPaternoNome = fam.nome; }
        if (r.tipo==='Avó Materna') { idsMap.avoMaternaId = fam.id; idsMap.avoMaternaNome = fam.nome; }
      }
    }
    resumo.detalhes.totalAnimais = total;
    resumo.detalhes.novosAnimais = novos;
    resumo.detalhes.atualizados = at;

    // Atualiza genealogia completa
    await atualizarGenealogiaCompleta({ ...idsMap, userId });

    return resumo;
  } catch (e: any) {
    logger.error(`Importação falhou: ${e.message}`);
    resumo.detalhes.erros.push(e.message);
    return resumo;
  }
}

async function processarCavaloPrincipal(
  info: CavaloInfo,
  userId: number,
  resumo: ResumoImportacao
): Promise<Cavalo> {
  if (!info.nome) throw new Error("Nome do cavalo é obrigatório");
  let cavalo = info.registro
    ? await buscarCavaloPorRegistro(info.registro)
    : undefined;
  if (!cavalo) cavalo = await buscarCavaloPorNome(info.nome) as Cavalo;

  // Enriquecer pelagem
  const pelagemId = await resolvePelagemId(info.pelagem, userId);

  if (cavalo) {
    const at: Partial<Cavalo> = { name: info.nome, numeroRegistro: info.registro, sexo: info.sexo, birthDate: info.nascimento? new Date(info.nascimento):undefined, cor: info.pelagem, pelagemId };
    const [upd] = await db.update(cavalos).set(at).where(eq(cavalos.id, cavalo.id)).returning();
    resumo.cavaloPrincipal = { nome:upd.name, registro:upd.numeroRegistro||"", novo:false, id:upd.id };
    return upd;
  } else {
    const dados: InsertCavalo = { userId, name:info.nome, breed:"Crioulo", birthDate: info.nascimento? new Date(info.nascimento):new Date(), status:"ativo", altura:null, peso:null, cor:info.pelagem||null, pelagemId, sexo:info.sexo||null, numeroRegistro:info.registro||null, origem:info.origem||null, criador:info.criador||null, proprietario:info.proprietario||null, inspetor:info.inspetor||null, notes:info.observacoes||null, isExternal:false };
    const [novo] = await db.insert(cavalos).values(dados).returning();
    resumo.cavaloPrincipal = { nome:novo.name, registro:novo.numeroRegistro||"", novo:true, id:novo.id };
    return novo;
  }
}

async function processarCavaloFamiliar(
  info: CavaloInfo,
  userId: number,
  tipo: string,
  resumo: ResumoImportacao
): Promise<Cavalo> {
  if (!info.nome) throw new Error("Nome do familiar é obrigatório");
  let cavalo = info.registro
    ? await buscarCavaloPorRegistro(info.registro)
    : undefined;
  if (!cavalo) cavalo = await buscarCavaloPorNome(info.nome) as Cavalo;

  const pelagemId = await resolvePelagemId(info.pelagem, userId);

  if (cavalo) {
    await db.update(cavalos).set({ isExternal:true }).where(eq(cavalos.id,cavalo.id));
    const at: Partial<Cavalo> = { name:info.nome, numeroRegistro:info.registro, sexo:info.sexo, birthDate:info.nascimento? new Date(info.nascimento):undefined, cor:info.pelagem, pelagemId };
    const [upd] = await db.update(cavalos).set(at).where(eq(cavalos.id, cavalo.id)).returning();
    const fam = upd; fam.novo=false;
    return fam;
  } else {
    const dados: InsertCavalo = { userId, name:info.nome, breed:"Crioulo", birthDate: info.nascimento? new Date(info.nascimento):new Date(), status:"ativo", altura:null, peso:null, cor:info.pelagem||null, pelagemId, sexo:info.sexo||null, numeroRegistro:info.registro||null, origem:info.origem||null, criador:info.criador||null, proprietario:info.proprietario||null, inspetor:info.inspetor||null, notes:info.observacoes||null, isExternal:true };
    const [novo] = await db.insert(cavalos).values(dados).returning();
    return novo;
  }
}

async function buscarCavaloPorRegistro(registro: string): Promise<Cavalo | undefined> {
  const [res] = await db.select().from(cavalos).where(eq(cavalos.numeroRegistro, registro)).limit(1);
  return res;
}
async function buscarCavaloPorNome(nome: string): Promise<Cavalo | undefined> {
  const [res] = await db.select().from(cavalos).where(eq(cavalos.name, nome)).limit(1);
  return res;
}

async function atualizarGenealogiaCompleta(dados: Record<string, any> & { userId: number }): Promise<void> {
  const { cavaloId, userId, ...rest } = dados;
  const existente = await db.select().from(genealogia).where(eq(genealogia.horseId, cavaloId));
  if (existente.length > 0) {
    const upd: any = {};
    for (const [k,v] of Object.entries(rest)) if (v!==undefined) upd[k]=v;
    if (Object.keys(upd).length) {
      await db.update(genealogia).set(upd).where(eq(genealogia.horseId,cavaloId));
    }
  } else {
    await db.insert(genealogia).values({ horseId:cavaloId, userId, ...rest });
  }
}
