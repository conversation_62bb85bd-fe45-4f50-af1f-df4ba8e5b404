// src/components/SmartChatBot.jsx
import React, { useState, useEffect, useRef } from 'react';
import { useChatCollector } from '../hooks/useChatCollector';

const detectIntent = (text) => {
  if (/peso|kg|quilos/i.test(text)) return '📏 Registro de peso';
  if (/vacina|vacinar|injeção/i.test(text)) return '💉 Vacinação';
  if (/treino|treinar/i.test(text)) return '🏇 Treinamento';
  if (/alimentação|comida|ração/i.test(text)) return '🥕 Alimentação';
  if (/ferrageamento|casqueamento/i.test(text)) return '🦶 Ferrageamento';
  if (/veterinário|consulta|exame/i.test(text)) return '🩺 Veterinária';
  if (/cadastrar|novo cavalo|potro/i.test(text)) return '📋 Cadastro de cavalo';
  return null;
};

const SmartChatBot = ({ onSendToAI }) => {
  const {
    messages,
    userInput,
    setUserInput,
    isListening,
    startListening,
    stopListening,
    handleUserMessage,
    clearBuffer
  } = useChatCollector();

  const [detectedIntent, setDetectedIntent] = useState(null);

  const handleSend = () => {
    if (userInput.trim()) {
      handleUserMessage(userInput);
      const intent = detectIntent(userInput);
      if (intent) setDetectedIntent(intent);
      setUserInput('');
    }
  };

  const handleUndoLast = () => {
    if (messages.length > 0) {
      messages.pop();
    }
  };

  const handleFinalize = () => {
    const fullContext = messages.map(m => m.content).join('\n');
    onSendToAI(fullContext);
    clearBuffer();
    setDetectedIntent(null);
  };

  const renderPreview = () => {
    return (
      <div className="text-sm text-gray-700 bg-gray-50 p-3 rounded-xl border border-gray-300 mb-3">
        <strong>📝 Resumo:</strong>
        <ul className="list-disc list-inside mt-1">
          {messages.map((msg, i) => (
            <li key={i}>{msg.content}</li>
          ))}
        </ul>
        {detectedIntent && (
          <div className="mt-2 text-blue-600">🔍 Intenção detectada: <strong>{detectedIntent}</strong></div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full max-w-md mx-auto p-4 border rounded-2xl shadow-xl bg-white">
      <div className="h-80 overflow-y-auto border-b mb-4 pb-2">
        {messages.map((msg, i) => (
          <div
            key={i}
            className={`mb-2 text-sm ${msg.role === 'user' ? 'text-right' : 'text-left text-gray-500'}`}
          >
            {msg.content}
          </div>
        ))}
        {messages.length > 0 && (
          <div className="text-xs text-gray-400 text-center mt-2">
            🧠 Pronto para entender o que você quer fazer. Clique em "✅ Pronto" quando terminar.
          </div>
        )}
      </div>

      <div className="flex items-center gap-2">
        <input
          value={userInput}
          onChange={e => setUserInput(e.target.value)}
          placeholder="Digite sua mensagem..."
          className="flex-grow border p-2 rounded-xl"
        />
        <button onClick={handleSend} className="p-2 rounded-xl bg-blue-500 text-white">
          Enviar
        </button>
        <button
          onClick={isListening ? stopListening : startListening}
          className={`p-2 rounded-xl ${isListening ? 'bg-red-500' : 'bg-green-500'} text-white`}
        >
          🎤
        </button>
      </div>

      {messages.length >= 2 && (
        <div className="mt-4 space-y-2">
          {renderPreview()}
          <button
            onClick={handleFinalize}
            className="w-full p-2 bg-emerald-600 text-white rounded-xl"
          >
            ✅ Pronto, pode mandar
          </button>
          <button
            onClick={handleUndoLast}
            className="w-full p-2 bg-yellow-500 text-white rounded-xl"
          >
            ↩️ Corrigir última mensagem
          </button>
        </div>
      )}
    </div>
  );
};

export default SmartChatBot;
