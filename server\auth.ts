import type { Express, Request, Response, NextFunction } from "express";
import { 
  hashPassword, 
  comparePassword, 
  generateToken, 
  isValidToken,
  formatAuthResponse
} from "./security";
import { storage } from "./storage";
import { insertUserSchema } from "@shared/schema";
import { z } from "zod";
import { validate, authValidation } from "./validation";
import { createError } from "./error-handler";
import { validarSenha, calcularForcaSenha } from './senha-validator';
import { recordLoginAttempt, isUserBlocked, isIPBlocked } from './login-protection';

/**
 * Módulo de autenticação para o EquiGestor
 * 
 * Este módulo configura as rotas de autenticação e exporta um middleware
 * para proteger rotas que requerem login.
 */

/**
 * Middleware de autenticação para proteger rotas
 * @param req Requisição Express
 * @param res Resposta Express
 * @param next Próxima função na cadeia de middleware
 */
export const authenticateUser = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Verificar se o userId foi enviado no cabeçalho ou no corpo
    let userId: number | undefined;
    
    // Primeiro tentar obter do cabeçalho (mais seguro)
    const userIdHeader = req.headers["user-id"];
    if (userIdHeader) {
      // Validar que o valor é um número
      const parsedUserId = parseInt(userIdHeader as string);
      if (!isNaN(parsedUserId) && parsedUserId > 0) {
        userId = parsedUserId;
      } else {
        console.error(`Cabeçalho user-id inválido: ${userIdHeader}`);
      }
    }
    
    // Se não estiver no cabeçalho ou for inválido, verificar no corpo da requisição
    if ((!userId || isNaN(userId)) && req.body && req.body.userId) {
      // Validar que o valor é um número
      const parsedBodyUserId = parseInt(req.body.userId.toString());
      if (!isNaN(parsedBodyUserId) && parsedBodyUserId > 0) {
        userId = parsedBodyUserId;
      } else {
        console.error(`Corpo userId inválido: ${req.body.userId}`);
      }
    }
    
    // Log da rota acessada para diagnóstico
    console.log('Rota autenticada acessada:', req.path);
    
    if (!userId || isNaN(userId) || userId <= 0) {
      return next(createError.authentication(
        "ID de usuário não fornecido ou inválido", 
        {
          headerProvided: !!userIdHeader,
          bodyProvided: !!req.body?.userId
        }
      ));
    }
    
    // Garantir que o userId esteja no corpo da requisição e na req.user para uso futuro
    req.body = req.body || {};
    req.body.userId = userId;
    
    // Adicionar o user object para compatibilidade
    (req as any).user = { id: userId };
    
    // Verificar token de autenticação
    const apiKey = req.headers["x-api-key"] as string;
    
    // Se houver um token, verificar estrutura e validade
    if (apiKey) {
      try {
        // Validar formato do token usando nosso esquema de validação aprimorado
        const tokenValidation = authValidation.token.safeParse({ token: apiKey });
        
        if (!tokenValidation.success) {
          return next(createError.authentication(
            "Formato de token inválido",
            { details: tokenValidation.error.format() }
          ));
        }
        
        // Verificar se o token é válido
        const validToken = isValidToken(apiKey);
        
        if (!validToken) {
          return next(createError.authentication(
            "Token de autenticação inválido ou expirado",
            { token: apiKey.substring(0, 4) + "..." }
          ));
        }
      } catch (tokenError) {
        return next(createError.authentication(
          "Erro ao processar token de autenticação",
          { error: tokenError instanceof Error ? tokenError.message : String(tokenError) }
        ));
      }
    }
    
    next();
  } catch (error) {
    // Usar nosso sistema de tratamento de erros centralizado
    next(createError.authentication(
      "Falha no processo de autenticação",
      { error: error instanceof Error ? error.message : String(error) }
    ));
  }
};

/**
 * Configura as rotas de autenticação na aplicação Express
 * @param app Aplicação Express
 */
export function setupAuth(app: Express) {
  // Rota para criar usuário (signup) com validação aprimorada e validação forte de senha
  app.post("/api/auth/signup", async (req, res, next) => {
    try {
      // Validar dados de entrada usando o schema do Zod
      const userData = insertUserSchema.parse(req.body);
      
      // Verificar se o usuário já existe
      const existingUser = await storage.getUserByUsername(userData.username);
      
      if (existingUser) {
        return next(createError.duplicate("Usuário", "username", userData.username));
      }
      
      // Validação forte de senha
      const validacaoSenha = validarSenha(userData.password);
      
      if (!validacaoSenha.valida) {
        return next(createError.validation(
          "A senha não atende aos requisitos de segurança", 
          { 
            detalhes: validacaoSenha.erros,
            dica: "Use uma combinação de letras maiúsculas, minúsculas, números e símbolos."
          }
        ));
      }
      
      // Hash da senha antes de salvar
      const hashedPassword = await hashPassword(userData.password);
      
      // Substituir a senha em texto plano pela hash
      const secureUserData = {
        ...userData,
        password: hashedPassword
      };
      
      // Salvar usuário com senha hasheada
      const user = await storage.createUser(secureUserData);
      
      // Gerar resposta formatada com token de autenticação
      const authResponse = formatAuthResponse(user);
      
      // Retornar status 201 (Created) com os dados do usuário e token
      res.status(201).json({ 
        user: authResponse.user,
        token: authResponse.token,
        expiration: authResponse.expiration
      });
    } catch (error) {
      // Tratar erros de validação do Zod
      if (error instanceof z.ZodError) {
        return next(createError.validation("Dados de cadastro inválidos", error.format()));
      }
      
      // Outros erros passam para o middleware de tratamento de erros
      next(createError.authentication(
        "Erro ao criar usuário", 
        { error: error instanceof Error ? error.message : String(error) }
      ));
    }
  });
  
  // Rota para login simplificada
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;
      
      // Validação básica
      if (!username || !password) {
        return res.status(400).json({ message: "Nome de usuário e senha são obrigatórios" });
      }
      
      // Buscar usuário pelo nome de usuário
      const user = await storage.getUserByUsername(username);
      
      // Verificar senha
      const isValidPassword = user ? await comparePassword(password, user.password) : false;
      
      if (!user || !isValidPassword) {
        return res.status(401).json({ message: "Credenciais inválidas" });
      }
      
      // Gerar resposta de autenticação
      const authResponse = formatAuthResponse(user);
      
      // Retornar dados do usuário com níveis de acesso
      res.json({ 
        user: authResponse.user,
        token: authResponse.token,
        expiration: authResponse.expiration,
        expirationFormatted: authResponse.expirationFormatted,
        security: authResponse.security
      });
    } catch (error) {
      console.error("Erro no login:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });
  
  // Rota para logout com middleware de autenticação
  app.post("/api/auth/logout", authenticateUser, (req, res) => {
    // Em um sistema real, invalidaríamos o token no banco de dados
    // Por enquanto, apenas confirmamos o logout ao cliente e registramos o evento
    console.log(`Logout realizado para o usuário ID: ${req.body.userId}`);
    res.json({ 
      message: "Logout realizado com sucesso",
      timestamp: new Date().toISOString()
    });
  });
}