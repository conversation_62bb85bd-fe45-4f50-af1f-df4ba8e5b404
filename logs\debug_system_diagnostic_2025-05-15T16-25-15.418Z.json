{"timestamp": "2025-05-15T16:25:15.418Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 296529920, "heapTotal": 116891648, "heapUsed": 95864448, "external": 7209124, "arrayBuffers": 90610}, "uptime": 1.451925512, "cpuUsage": {"user": 2217411, "system": 311652}, "resourceUsage": {"userCPUTime": 2217455, "systemCPUTime": 311658, "maxRSS": 289580, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101224, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 72, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7509, "involuntaryContextSwitches": 1561}}