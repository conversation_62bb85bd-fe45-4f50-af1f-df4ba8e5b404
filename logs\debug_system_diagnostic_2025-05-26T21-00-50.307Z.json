{"timestamp": "2025-05-26T21:00:50.306Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 412893184, "heapTotal": 120520704, "heapUsed": 96891248, "external": 8710652, "arrayBuffers": 274785}, "uptime": 1.999683056, "cpuUsage": {"user": 3061848, "system": 411648}, "resourceUsage": {"userCPUTime": 3061912, "systemCPUTime": 411656, "maxRSS": 403216, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105877, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9041, "involuntaryContextSwitches": 4734}}