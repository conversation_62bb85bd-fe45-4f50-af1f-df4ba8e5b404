{"timestamp": "2025-05-23T04:33:32.717Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 414916608, "heapTotal": 122089472, "heapUsed": 97205840, "external": 8707751, "arrayBuffers": 274785}, "uptime": 1.713063144, "cpuUsage": {"user": 2717356, "system": 320123}, "resourceUsage": {"userCPUTime": 2717406, "systemCPUTime": 320124, "maxRSS": 405192, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106698, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8316, "involuntaryContextSwitches": 1210}}