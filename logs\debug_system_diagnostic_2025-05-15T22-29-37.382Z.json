{"timestamp": "2025-05-15T22:29:37.381Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402014208, "heapTotal": 113025024, "heapUsed": 72471016, "external": 8211290, "arrayBuffers": 235533}, "uptime": 3.021231257, "cpuUsage": {"user": 2710134, "system": 392719}, "resourceUsage": {"userCPUTime": 2710209, "systemCPUTime": 392719, "maxRSS": 392592, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102965, "majorPageFault": 0, "swappedOut": 0, "fsRead": 23344, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7172, "involuntaryContextSwitches": 5102}}