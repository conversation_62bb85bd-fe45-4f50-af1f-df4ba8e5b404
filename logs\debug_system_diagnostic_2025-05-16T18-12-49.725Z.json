{"timestamp": "2025-05-16T18:12:49.725Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 404811776, "heapTotal": 116432896, "heapUsed": 72605712, "external": 8219482, "arrayBuffers": 243725}, "uptime": 1.875231631, "cpuUsage": {"user": 2807799, "system": 368912}, "resourceUsage": {"userCPUTime": 2807845, "systemCPUTime": 368919, "maxRSS": 395324, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 105424, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 48, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7779, "involuntaryContextSwitches": 2252}}