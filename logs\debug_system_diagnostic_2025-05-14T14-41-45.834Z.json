{"timestamp": "2025-05-14T14:41:45.834Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 388124672, "heapTotal": 100012032, "heapUsed": 76484488, "external": 6915182, "arrayBuffers": 82418}, "uptime": 1.749482547, "cpuUsage": {"user": 2335219, "system": 325539}, "resourceUsage": {"userCPUTime": 2335265, "systemCPUTime": 325546, "maxRSS": 379028, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99829, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6729, "involuntaryContextSwitches": 4423}}