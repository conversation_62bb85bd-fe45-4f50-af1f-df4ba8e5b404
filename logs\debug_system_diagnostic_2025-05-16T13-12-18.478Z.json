{"timestamp": "2025-05-16T13:12:18.477Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 398663680, "heapTotal": 104304640, "heapUsed": 79094504, "external": 8563341, "arrayBuffers": 257466}, "uptime": 3.146265896, "cpuUsage": {"user": 3168862, "system": 421871}, "resourceUsage": {"userCPUTime": 3168935, "systemCPUTime": 421871, "maxRSS": 389320, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104539, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 240, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7806, "involuntaryContextSwitches": 13040}}