{"timestamp": "2025-05-19T17:48:04.930Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399671296, "heapTotal": 120627200, "heapUsed": 74266160, "external": 8227674, "arrayBuffers": 251917}, "uptime": 5.493112399, "cpuUsage": {"user": 2829311, "system": 418343}, "resourceUsage": {"userCPUTime": 2829349, "systemCPUTime": 418346, "maxRSS": 390304, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 112598, "majorPageFault": 6, "swappedOut": 0, "fsRead": 53960, "fsWrite": 936, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9483, "involuntaryContextSwitches": 2970}}