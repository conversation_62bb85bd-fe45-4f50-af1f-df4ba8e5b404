// Configuração de inicialização otimizada para deploy no Replit
// Resolve o problema do comando Windows "set NODE_ENV=production"

console.log('🚀 EquiGestor AI - Iniciando em modo produção');

// Configurar ambiente de produção
process.env.NODE_ENV = 'production';

// Usar a porta padrão do Replit
const port = process.env.PORT || 5000;
process.env.PORT = port;

console.log(`✅ Ambiente: ${process.env.NODE_ENV}`);
console.log(`✅ Porta: ${port}`);

// Importar e iniciar aplicação
async function startApp() {
  try {
    // Verificar se existe build
    const fs = await import('fs');
    const path = await import('path');
    
    const buildPath = path.join(process.cwd(), 'dist', 'index.js');
    
    if (!fs.existsSync(buildPath)) {
      console.log('📦 Build não encontrado, usando modo desenvolvimento...');
      // Fallback para modo desenvolvimento se build não existir
      const { spawn } = await import('child_process');
      const dev = spawn('npm', ['run', 'dev'], { stdio: 'inherit' });
      
      dev.on('error', (error) => {
        console.error('❌ Erro ao iniciar em modo desenvolvimento:', error);
        process.exit(1);
      });
      
      return;
    }
    
    console.log('📁 Build encontrado, iniciando aplicação...');
    await import('./dist/index.js');
    
  } catch (error) {
    console.error('❌ Erro ao iniciar aplicação:', error.message);
    
    // Fallback para desenvolvimento se houver erro
    console.log('🔄 Tentando modo desenvolvimento como fallback...');
    const { spawn } = await import('child_process');
    const dev = spawn('npm', ['run', 'dev'], { stdio: 'inherit' });
    
    dev.on('error', (devError) => {
      console.error('❌ Erro crítico:', devError);
      process.exit(1);
    });
  }
}

startApp();