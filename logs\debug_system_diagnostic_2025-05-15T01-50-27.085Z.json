{"timestamp": "2025-05-15T01:50:27.084Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 381816832, "heapTotal": 96882688, "heapUsed": 78327912, "external": 6918574, "arrayBuffers": 131570}, "uptime": 2.14012504, "cpuUsage": {"user": 2477993, "system": 324962}, "resourceUsage": {"userCPUTime": 2478049, "systemCPUTime": 324969, "maxRSS": 372868, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 102066, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 104, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6870, "involuntaryContextSwitches": 7422}}