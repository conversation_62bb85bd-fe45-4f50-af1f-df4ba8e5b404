{"timestamp": "2025-05-15T01:46:52.910Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 373039104, "heapTotal": 91623424, "heapUsed": 68150280, "external": 6845689, "arrayBuffers": 90610}, "uptime": 1.653333586, "cpuUsage": {"user": 2351065, "system": 265374}, "resourceUsage": {"userCPUTime": 2351120, "systemCPUTime": 265380, "maxRSS": 364296, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 97681, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6539, "involuntaryContextSwitches": 4181}}