{"timestamp": "2025-05-23T21:47:14.575Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 399904768, "heapTotal": 119054336, "heapUsed": 74590256, "external": 8211290, "arrayBuffers": 235533}, "uptime": 2.102823226, "cpuUsage": {"user": 3136302, "system": 384208}, "resourceUsage": {"userCPUTime": 3136435, "systemCPUTime": 384224, "maxRSS": 390532, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 106733, "majorPageFault": 0, "swappedOut": 0, "fsRead": 32, "fsWrite": 40, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8434, "involuntaryContextSwitches": 3270}}