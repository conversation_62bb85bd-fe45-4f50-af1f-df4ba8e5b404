{"timestamp": "2025-05-14T17:52:47.522Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 388939776, "heapTotal": 105779200, "heapUsed": 62058584, "external": 6815481, "arrayBuffers": 60485}, "uptime": 1.493874734, "cpuUsage": {"user": 2310100, "system": 299217}, "resourceUsage": {"userCPUTime": 2310160, "systemCPUTime": 299225, "maxRSS": 379824, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 99830, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 6266, "involuntaryContextSwitches": 1911}}