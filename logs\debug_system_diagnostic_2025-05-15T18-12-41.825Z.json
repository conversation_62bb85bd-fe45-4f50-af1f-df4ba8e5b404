{"timestamp": "2025-05-15T18:12:41.824Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 390557696, "heapTotal": 102539264, "heapUsed": 79293792, "external": 8295104, "arrayBuffers": 257466}, "uptime": 1.738617497, "cpuUsage": {"user": 2663578, "system": 303883}, "resourceUsage": {"userCPUTime": 2663618, "systemCPUTime": 303888, "maxRSS": 381404, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101273, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8035, "involuntaryContextSwitches": 2106}}