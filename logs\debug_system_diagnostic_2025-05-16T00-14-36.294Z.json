{"timestamp": "2025-05-16T00:14:36.294Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 410546176, "heapTotal": 118288384, "heapUsed": 90906392, "external": 8258824, "arrayBuffers": 233559}, "uptime": 2.54439791, "cpuUsage": {"user": 2983376, "system": 416315}, "resourceUsage": {"userCPUTime": 2983442, "systemCPUTime": 416315, "maxRSS": 400924, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104764, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7732, "involuntaryContextSwitches": 6158}}