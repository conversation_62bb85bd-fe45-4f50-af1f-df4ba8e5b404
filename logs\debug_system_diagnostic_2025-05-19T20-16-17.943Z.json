{"timestamp": "2025-05-19T20:16:17.943Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 402345984, "heapTotal": 117637120, "heapUsed": 93293384, "external": 9055382, "arrayBuffers": 269390}, "uptime": 2.690191192, "cpuUsage": {"user": 2861474, "system": 408821}, "resourceUsage": {"userCPUTime": 2861530, "systemCPUTime": 408829, "maxRSS": 392916, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 104743, "majorPageFault": 1, "swappedOut": 0, "fsRead": 47640, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 9944, "involuntaryContextSwitches": 6652}}