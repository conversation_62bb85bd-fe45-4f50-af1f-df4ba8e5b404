{"timestamp": "2025-05-23T03:28:03.091Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 295964672, "heapTotal": 117374976, "heapUsed": 97219008, "external": 8717000, "arrayBuffers": 299361}, "uptime": 1.680064792, "cpuUsage": {"user": 2644986, "system": 335623}, "resourceUsage": {"userCPUTime": 2645030, "systemCPUTime": 335624, "maxRSS": 289028, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 103378, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 8781, "involuntaryContextSwitches": 1476}}