{"timestamp": "2025-05-14T14:16:14.873Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 436150272, "heapTotal": 145788928, "heapUsed": 123992536, "external": 8912326, "arrayBuffers": 85061}, "uptime": 2.453026656, "cpuUsage": {"user": 3515612, "system": 458605}, "resourceUsage": {"userCPUTime": 3515680, "systemCPUTime": 458605, "maxRSS": 425928, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 111113, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 8, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 13077, "involuntaryContextSwitches": 7191}}