{"timestamp": "2025-05-15T01:28:38.319Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 380809216, "heapTotal": 92147712, "heapUsed": 72949720, "external": 6898646, "arrayBuffers": 115186}, "uptime": 1.833610869, "cpuUsage": {"user": 2350635, "system": 268534}, "resourceUsage": {"userCPUTime": 2350679, "systemCPUTime": 268539, "maxRSS": 371884, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 97989, "majorPageFault": 0, "swappedOut": 0, "fsRead": 0, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 5350, "involuntaryContextSwitches": 3914}}