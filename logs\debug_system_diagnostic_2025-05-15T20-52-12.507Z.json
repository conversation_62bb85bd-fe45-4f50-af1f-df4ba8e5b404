{"timestamp": "2025-05-15T20:52:12.507Z", "environment": "development", "nodeVersion": "v20.18.1", "platform": "linux", "architecture": "x64", "memoryUsage": {"rss": 395513856, "heapTotal": 107782144, "heapUsed": 72548592, "external": 8211290, "arrayBuffers": 235533}, "uptime": 3.756847517, "cpuUsage": {"user": 2654455, "system": 404020}, "resourceUsage": {"userCPUTime": 2654491, "systemCPUTime": 404026, "maxRSS": 386244, "sharedMemorySize": 0, "unsharedDataSize": 0, "unsharedStackSize": 0, "minorPageFault": 101620, "majorPageFault": 2, "swappedOut": 0, "fsRead": 45920, "fsWrite": 0, "ipcSent": 0, "ipcReceived": 0, "signalsCount": 0, "voluntaryContextSwitches": 7583, "involuntaryContextSwitches": 3597}}